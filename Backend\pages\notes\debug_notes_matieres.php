<?php
/**
 * Script de diagnostic pour le problème des matières dans les notes
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Notes - Matières</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { background: white; padding: 20px; border-radius: 8px; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Debug Notes - Problème Matières</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='container'>";
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
    
    // 1. Vérifier l'existence des tables
    echo "<h2>🔍 Vérification des tables</h2>";
    
    $tables = ['notes', 'matieres', 'etudiants', 'devoirs', 'utilisateurs', 'classes'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<div class='info'>📋 Table '$table' : " . $count['count'] . " enregistrements</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Table '$table' : " . $e->getMessage() . "</div>";
        }
    }
    
    // 2. Tester la requête des notes avec jointures
    echo "<h2>🧪 Test de la requête Notes avec jointures</h2>";
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                n.id,
                n.etudiant_id,
                n.devoir_id,
                n.matiere_id,
                n.note,
                n.date_enregistrement,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                d.titre as devoir_titre,
                d.description as devoir_description,
                d.date_remise,
                m.nom as matiere_nom,
                c.nom as classe_nom,
                DATE_FORMAT(n.date_enregistrement, '%d/%m/%Y') as date_formatted
            FROM notes n
            LEFT JOIN etudiants e ON n.etudiant_id = e.id
            LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN devoirs d ON n.devoir_id = d.id
            LEFT JOIN matieres m ON n.matiere_id = m.id
            LEFT JOIN classes c ON d.classe_id = c.id
            LIMIT 5
        ");
        
        $stmt->execute();
        $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>✅ Requête réussie - " . count($notes) . " notes récupérées</div>";
        
        if (count($notes) > 0) {
            echo "<h3>📊 Exemple de données (première note)</h3>";
            echo "<pre>" . json_encode($notes[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            
            // Analyser les champs manquants
            $premiere_note = $notes[0];
            $champs_manquants = [];
            
            if (empty($premiere_note['matiere_nom'])) $champs_manquants[] = 'matiere_nom';
            if (empty($premiere_note['etudiant_nom'])) $champs_manquants[] = 'etudiant_nom';
            if (empty($premiere_note['devoir_titre'])) $champs_manquants[] = 'devoir_titre';
            if (empty($premiere_note['classe_nom'])) $champs_manquants[] = 'classe_nom';
            
            if (count($champs_manquants) > 0) {
                echo "<div class='warning'>⚠️ Champs manquants détectés : " . implode(', ', $champs_manquants) . "</div>";
            } else {
                echo "<div class='success'>✅ Tous les champs sont présents</div>";
            }
            
            // Afficher un tableau des notes
            echo "<h3>📋 Tableau des notes</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Étudiant</th><th>Devoir</th><th>Matière</th><th>Note</th><th>Date</th></tr>";
            foreach ($notes as $note) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($note['id']) . "</td>";
                echo "<td>" . htmlspecialchars($note['etudiant_nom'] ?: 'MANQUANT') . "</td>";
                echo "<td>" . htmlspecialchars($note['devoir_titre'] ?: 'MANQUANT') . "</td>";
                echo "<td><strong>" . htmlspecialchars($note['matiere_nom'] ?: 'MANQUANT') . "</strong></td>";
                echo "<td>" . htmlspecialchars($note['note']) . "</td>";
                echo "<td>" . htmlspecialchars($note['date_formatted']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>⚠️ Aucune note trouvée dans la base de données</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur requête notes : " . $e->getMessage() . "</div>";
    }
    
    // 3. Vérifier les relations entre tables
    echo "<h2>🔗 Vérification des relations</h2>";
    
    try {
        // Notes sans matière
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM notes n 
            LEFT JOIN matieres m ON n.matiere_id = m.id 
            WHERE m.id IS NULL
        ");
        $notes_sans_matiere = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($notes_sans_matiere > 0) {
            echo "<div class='warning'>⚠️ $notes_sans_matiere notes sans matière associée</div>";
        } else {
            echo "<div class='success'>✅ Toutes les notes ont une matière associée</div>";
        }
        
        // Notes sans étudiant
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM notes n 
            LEFT JOIN etudiants e ON n.etudiant_id = e.id 
            WHERE e.id IS NULL
        ");
        $notes_sans_etudiant = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($notes_sans_etudiant > 0) {
            echo "<div class='warning'>⚠️ $notes_sans_etudiant notes sans étudiant associé</div>";
        } else {
            echo "<div class='success'>✅ Toutes les notes ont un étudiant associé</div>";
        }
        
        // Notes sans devoir
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM notes n 
            LEFT JOIN devoirs d ON n.devoir_id = d.id 
            WHERE d.id IS NULL
        ");
        $notes_sans_devoir = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($notes_sans_devoir > 0) {
            echo "<div class='warning'>⚠️ $notes_sans_devoir notes sans devoir associé</div>";
        } else {
            echo "<div class='success'>✅ Toutes les notes ont un devoir associé</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur vérification relations : " . $e->getMessage() . "</div>";
    }
    
    // 4. Test de l'API Notes
    echo "<h2>🔧 Test de l'API Notes</h2>";
    
    $roles = ['enseignant-token', 'admin-token', 'etudiant-token'];
    foreach ($roles as $token) {
        echo "<h3>Test avec $token</h3>";
        try {
            $url = "http://localhost/Project_PFE/Backend/pages/notes/api.php";
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => "Authorization: Bearer $token\r\nContent-Type: application/json\r\n",
                    'timeout' => 10
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);
            
            if ($data && isset($data['success']) && $data['success']) {
                $count = isset($data['data']) ? count($data['data']) : 0;
                echo "<div class='success'>✅ API fonctionne - $count notes retournées</div>";
                
                if ($count > 0 && isset($data['data'][0])) {
                    $premiere = $data['data'][0];
                    $matiere_ok = !empty($premiere['matiere_nom']) ? '✅' : '❌';
                    echo "<div class='info'>$matiere_ok Matière dans première note : " . ($premiere['matiere_nom'] ?? 'MANQUANT') . "</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ API retourne : " . substr($response, 0, 200) . "...</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Erreur API $token : " . $e->getMessage() . "</div>";
        }
    }
    
    echo "</div>";
    
    // 5. Recommandations
    echo "<div class='container'>";
    echo "<h2>💡 Recommandations</h2>";
    
    if ($notes_sans_matiere > 0 || $notes_sans_etudiant > 0 || $notes_sans_devoir > 0) {
        echo "<div class='warning'>";
        echo "<p><strong>⚠️ Problèmes détectés :</strong></p>";
        echo "<ul>";
        if ($notes_sans_matiere > 0) echo "<li>Notes sans matière : vérifier les IDs de matières</li>";
        if ($notes_sans_etudiant > 0) echo "<li>Notes sans étudiant : vérifier les IDs d'étudiants</li>";
        if ($notes_sans_devoir > 0) echo "<li>Notes sans devoir : vérifier les IDs de devoirs</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='success'>";
        echo "<p><strong>✅ Base de données saine :</strong></p>";
        echo "<p>Toutes les relations sont correctes. Le problème pourrait être dans le frontend.</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='container'>";
    echo "<div class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</div>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='container'>";
    echo "<div class='error'>❌ Erreur générale : " . $e->getMessage() . "</div>";
    echo "</div>";
}

echo "</body></html>";
?>
