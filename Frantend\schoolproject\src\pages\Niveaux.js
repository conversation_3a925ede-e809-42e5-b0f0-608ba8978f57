import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const NiveauCRUD = () => {
    const { user } = useContext(AuthContext);
    const [niveaux, setNiveaux] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingNiveau, setEditingNiveau] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        nom: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchNiveaux();
    }, []);

    const fetchNiveaux = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des niveaux...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/niveaux/niveau.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let niveauxData = response.data;

            // Vérifier si la réponse est un tableau ou contient une erreur
            if (Array.isArray(niveauxData)) {
                setNiveaux(niveauxData);
                console.log('✅ Niveaux chargés:', niveauxData.length, 'éléments');
            } else if (niveauxData && niveauxData.error) {
                console.error('❌ Erreur API:', niveauxData.error);
                throw new Error(niveauxData.error);
            } else {
                console.warn('⚠️ Réponse API inattendue:', niveauxData);
                setNiveaux([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des niveaux:', error);

            // Données de test
            const testNiveaux = [
                { id: 1, nom: 'Première année' },
                { id: 2, nom: 'Deuxième année' },
                { id: 3, nom: 'Troisième année' },
                { id: 4, nom: 'Quatrième année' },
                { id: 5, nom: 'Cinquième année' },
                { id: 6, nom: 'Sixième année' },
                { id: 7, nom: 'Septième année' },
                { id: 8, nom: 'Huitième année' },
                { id: 9, nom: 'Neuvième année' },
                { id: 10, nom: 'Baccalauréat 1ère année' },
                { id: 11, nom: 'Baccalauréat 2ème année' },
                { id: 12, nom: 'Licence 1ère année' },
                { id: 13, nom: 'Licence 2ème année' },
                { id: 14, nom: 'Licence 3ème année' },
                { id: 15, nom: 'Master 1ère année' },
                { id: 16, nom: 'Master 2ème année' }
            ];

            setNiveaux(testNiveaux);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testNiveaux.length} niveaux de test.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des niveaux', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/niveaux/niveau.php';
            const method = editingNiveau ? 'PUT' : 'POST';
            const data = editingNiveau ? { ...formData, id: editingNiveau.id } : formData;

            console.log('🔄 Envoi requête:', { method, url, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            // Vérifier si la réponse contient success: true
            if (response.data && response.data.success === true) {
                Swal.fire('Succès', `Niveau ${editingNiveau ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingNiveau(null);
                resetForm();
                fetchNiveaux();
            } else {
                // Si success n'est pas true, c'est une erreur
                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            const errorMessage = error.response?.data?.error ||
                               error.response?.data?.message ||
                               error.message ||
                               'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (niveau) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des niveaux', 'error');
            return;
        }

        setEditingNiveau(niveau);
        setFormData({
            nom: niveau.nom
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des niveaux', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/niveaux/niveau.php';

                console.log('🔄 Suppression niveau:', { id, url });

                const response = await axios.delete(url, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data && response.data.success === true) {
                    Swal.fire('Supprimé!', 'Le niveau a été supprimé.', 'success');
                    fetchNiveaux();
                } else {
                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error ||
                                   error.response?.data?.message ||
                                   error.message ||
                                   'Impossible de supprimer le niveau';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            nom: ''
        });
    };

    // Filtrage des données
    const filteredNiveaux = niveaux.filter(niveau => {
        return niveau.nom?.toLowerCase().includes(searchTerm.toLowerCase());
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentNiveaux = filteredNiveaux.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredNiveaux.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des niveaux...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>📊 Gestion des Niveaux</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredNiveaux.length} niveau(x) trouvé(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouveau Niveau
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les niveaux en mode lecture seule.
                        Seul l'administrateur peut créer, modifier ou supprimer des niveaux.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher un niveau..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
            </div>

            <div className="factures-grid">
                {filteredNiveaux.length === 0 ? (
                    <div className="no-data">
                        <img src="/level.png" alt="Aucun niveau" />
                        <p>Aucun niveau trouvé</p>
                        {searchTerm && (
                            <button
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📊 Nom du Niveau</th>
                                    <th>📈 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentNiveaux.map((niveau) => (
                                    <tr key={niveau.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{niveau.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{niveau.nom}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span className="badge badge-success">Actif</span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(niveau)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(niveau.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredNiveaux.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredNiveaux.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des niveaux</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredNiveaux.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Niveaux actifs</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {currentNiveaux.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un niveau */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingNiveau ? 'Modifier le niveau' : 'Nouveau niveau'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingNiveau(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Nom du niveau *</label>
                                <input
                                    type="text"
                                    value={formData.nom}
                                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                                    placeholder="Ex: Première année, Baccalauréat..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingNiveau(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingNiveau ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default NiveauCRUD;
