<?php
/**
 * Version temporaire de l'API Absences SANS authentification
 * À utiliser uniquement pour les tests et le développement
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Simulation utilisateur pour test (Admin)
$user_info = [
    'id' => 1,
    'role' => 'Admin'
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// Debug : Log de toutes les requêtes
error_log("DEBUG ABSENCE API: Method=$method, Input=" . json_encode($input));

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage(), 'debug' => 'Version temporaire sans auth']);
}

function handleGet($pdo, $user_info) {
    // Récupérer toutes les absences (version simplifiée)
    $stmt = $pdo->prepare("
        SELECT a.*, u.nom as etudiant_nom, u.prenom as etudiant_prenom, u.email as etudiant_email,
               m.nom as matiere_nom, ue.nom as enseignant_nom, ue.prenom as enseignant_prenom
        FROM Absences a 
        JOIN Etudiants e ON a.etudiant_id = e.id 
        JOIN Utilisateurs u ON e.utilisateur_id = u.id 
        LEFT JOIN Matieres m ON a.matiere_id = m.id
        LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
        LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
        ORDER BY a.date_absence DESC
    ");
    $stmt->execute();
    
    $absences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formater les données pour React
    $formatted_absences = [];
    foreach ($absences as $absence) {
        $formatted_absences[] = [
            'id' => $absence['id'],
            'etudiant_id' => $absence['etudiant_id'],
            'matiere_id' => $absence['matiere_id'],
            'enseignant_id' => $absence['enseignant_id'],
            'date_absence' => $absence['date_absence'],
            'justification' => $absence['justification'],
            'etudiant_nom' => $absence['etudiant_nom'] . ' ' . $absence['etudiant_prenom'],
            'etudiant_email' => $absence['etudiant_email'],
            'matiere_nom' => $absence['matiere_nom'],
            'enseignant_nom' => $absence['enseignant_nom'] ? 
                $absence['enseignant_nom'] . ' ' . $absence['enseignant_prenom'] : 
                null
        ];
    }
    
    echo json_encode($formatted_absences);
}

function handlePost($pdo, $user_info, $input) {
    // Debug : Afficher les données reçues
    error_log("DEBUG ABSENCE POST: " . json_encode($input));
    
    if (!isset($input['etudiant_id'], $input['date_absence'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes: etudiant_id et date_absence requis']);
        return;
    }
    
    // Nettoyer et valider les données
    $etudiant_id = (int)$input['etudiant_id'];
    $matiere_id = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
    $enseignant_id = !empty($input['enseignant_id']) ? (int)$input['enseignant_id'] : null;
    $date_absence = $input['date_absence'];
    $justification = !empty($input['justification']) ? $input['justification'] : null;
    
    // Validation
    if ($etudiant_id <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'ID étudiant invalide: ' . $input['etudiant_id'] . ' (converti en: ' . $etudiant_id . ')']);
        return;
    }
    
    // Vérifier que l'étudiant existe
    $stmt = $pdo->prepare("SELECT id FROM Etudiants WHERE id = ?");
    $stmt->execute([$etudiant_id]);
    if (!$stmt->fetch()) {
        http_response_code(400);
        echo json_encode(['error' => 'Étudiant non trouvé avec ID: ' . $etudiant_id]);
        return;
    }
    
    // Debug : Afficher les données nettoyées
    error_log("DEBUG ABSENCE CLEAN: etudiant_id=$etudiant_id, matiere_id=$matiere_id, enseignant_id=$enseignant_id, date=$date_absence");
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO Absences (etudiant_id, matiere_id, enseignant_id, date_absence, justification) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $etudiant_id,
            $matiere_id,
            $enseignant_id,
            $date_absence,
            $justification
        ]);
        
        if ($result) {
            $new_id = $pdo->lastInsertId();
            echo json_encode([
                'success' => true, 
                'id' => $new_id,
                'message' => 'Absence créée avec succès (version temporaire)',
                'debug' => [
                    'etudiant_id' => $etudiant_id,
                    'matiere_id' => $matiere_id,
                    'enseignant_id' => $enseignant_id,
                    'date_absence' => $date_absence,
                    'justification' => $justification
                ]
            ]);
        } else {
            throw new Exception('Échec de l\'insertion en base de données');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Erreur base de données: ' . $e->getMessage(),
            'debug_data' => [
                'etudiant_id' => $etudiant_id,
                'matiere_id' => $matiere_id,
                'enseignant_id' => $enseignant_id,
                'date_absence' => $date_absence,
                'justification' => $justification
            ]
        ]);
    }
}

function handlePut($pdo, $user_info, $input) {
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $fields = [];
    $values = [];
    
    if (isset($input['date_absence'])) {
        $fields[] = 'date_absence = ?';
        $values[] = $input['date_absence'];
    }
    if (isset($input['justification'])) {
        $fields[] = 'justification = ?';
        $values[] = $input['justification'];
    }
    if (isset($input['matiere_id'])) {
        $fields[] = 'matiere_id = ?';
        $values[] = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à modifier']);
        return;
    }
    
    $values[] = (int)$input['id'];
    $sql = "UPDATE Absences SET " . implode(', ', $fields) . " WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($values);
    
    echo json_encode(['success' => true, 'message' => 'Absence modifiée avec succès (version temporaire)']);
}

function handleDelete($pdo, $user_info, $input) {
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM Absences WHERE id = ?");
    $stmt->execute([(int)$input['id']]);
    
    echo json_encode(['success' => true, 'message' => 'Absence supprimée avec succès (version temporaire)']);
}
?>
