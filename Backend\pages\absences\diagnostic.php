<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔍 DIAGNOSTIC ABSENCES - PROBLÈMES IDENTIFIÉS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .sql-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; }
        .result-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .result-table th, .result-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .result-table th { background: #007bff; color: white; }
        .fix-button { background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .fix-button:hover { background: #218838; color: white; text-decoration: none; }
        .problem-box { background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 15px; margin: 15px 0; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🔍 Diagnostic des Problèmes d'Absences</h2>";
    echo "<p><strong>Problème 1 :</strong> Nom de l'enseignant ne s'affiche pas</p>";
    echo "<p><strong>Problème 2 :</strong> Boutons CRUD ne s'affichent pas</p>";
    echo "</div>";
    
    // 1. Vérifier la structure de la table Absences
    echo "<div class='step'>";
    echo "<h3>📋 1. Vérification Structure Table Absences</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE Absences");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table class='result-table'>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
        
        $has_enseignant_id = false;
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'enseignant_id') {
                $has_enseignant_id = true;
            }
        }
        echo "</table>";
        
        if ($has_enseignant_id) {
            echo "<p class='success'>✅ Colonne enseignant_id présente</p>";
        } else {
            echo "<p class='error'>❌ Colonne enseignant_id manquante</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur vérification structure : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Vérifier les données dans les tables liées
    echo "<div class='step'>";
    echo "<h3>👥 2. Vérification des Données</h3>";
    
    // Compter les absences
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Absences");
        $absence_count = $stmt->fetch()['count'];
        echo "<p class='info'>📊 <strong>$absence_count</strong> absence(s) dans la base</p>";
        
        // Compter les enseignants
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Enseignants");
        $enseignant_count = $stmt->fetch()['count'];
        echo "<p class='info'>👨‍🏫 <strong>$enseignant_count</strong> enseignant(s) dans la base</p>";
        
        // Compter les étudiants
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Etudiants");
        $etudiant_count = $stmt->fetch()['count'];
        echo "<p class='info'>👨‍🎓 <strong>$etudiant_count</strong> étudiant(s) dans la base</p>";
        
        // Compter les matières
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Matieres");
        $matiere_count = $stmt->fetch()['count'];
        echo "<p class='info'>📚 <strong>$matiere_count</strong> matière(s) dans la base</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur comptage données : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. Test de la requête API
    echo "<div class='step'>";
    echo "<h3>🔌 3. Test de la Requête API</h3>";
    
    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Absences a 
            JOIN Etudiants e ON a.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON a.matiere_id = m.id
            LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            ORDER BY a.date_absence DESC
            LIMIT 5
        ");
        $stmt->execute();
        $absences = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 Résultat de la requête API :</h4>";
        
        if (empty($absences)) {
            echo "<p class='warning'>⚠️ Aucune absence trouvée</p>";
        } else {
            echo "<table class='result-table'>";
            echo "<tr><th>ID</th><th>Étudiant</th><th>Matière</th><th>Enseignant</th><th>Date</th><th>Justification</th></tr>";
            
            foreach ($absences as $absence) {
                echo "<tr>";
                echo "<td>{$absence['id']}</td>";
                echo "<td>{$absence['etudiant_nom']}</td>";
                echo "<td>" . ($absence['matiere_nom'] ?: '-') . "</td>";
                echo "<td>" . ($absence['enseignant_nom'] ?: '<span style=\"color: red;\">NULL</span>') . "</td>";
                echo "<td>{$absence['date_absence']}</td>";
                echo "<td>" . ($absence['justification'] ?: '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Analyser les problèmes
            $enseignants_null = 0;
            foreach ($absences as $absence) {
                if (empty($absence['enseignant_nom'])) {
                    $enseignants_null++;
                }
            }
            
            if ($enseignants_null > 0) {
                echo "<div class='problem-box'>";
                echo "<h4>❌ PROBLÈME IDENTIFIÉ</h4>";
                echo "<p><strong>$enseignants_null</strong> absence(s) sur " . count($absences) . " n'ont pas de nom d'enseignant</p>";
                echo "<p><strong>Causes possibles :</strong></p>";
                echo "<ul>";
                echo "<li>enseignant_id est NULL dans la table Absences</li>";
                echo "<li>L'enseignant référencé n'existe pas dans la table Enseignants</li>";
                echo "<li>L'utilisateur lié à l'enseignant n'existe pas</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<p class='success'>✅ Tous les enseignants sont correctement affichés</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur requête API : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 4. Analyse détaillée des relations
    echo "<div class='step'>";
    echo "<h3>🔗 4. Analyse des Relations</h3>";
    
    try {
        // Vérifier les absences avec enseignant_id NULL
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Absences WHERE enseignant_id IS NULL");
        $null_enseignant = $stmt->fetch()['count'];
        
        if ($null_enseignant > 0) {
            echo "<p class='warning'>⚠️ <strong>$null_enseignant</strong> absence(s) avec enseignant_id NULL</p>";
        } else {
            echo "<p class='success'>✅ Toutes les absences ont un enseignant_id</p>";
        }
        
        // Vérifier les enseignants orphelins
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM Absences a 
            LEFT JOIN Enseignants e ON a.enseignant_id = e.id 
            WHERE a.enseignant_id IS NOT NULL AND e.id IS NULL
        ");
        $orphan_enseignant = $stmt->fetch()['count'];
        
        if ($orphan_enseignant > 0) {
            echo "<p class='error'>❌ <strong>$orphan_enseignant</strong> absence(s) référencent un enseignant inexistant</p>";
        } else {
            echo "<p class='success'>✅ Toutes les références enseignant sont valides</p>";
        }
        
        // Vérifier les utilisateurs enseignants orphelins
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM Enseignants e 
            LEFT JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            WHERE u.id IS NULL
        ");
        $orphan_user = $stmt->fetch()['count'];
        
        if ($orphan_user > 0) {
            echo "<p class='error'>❌ <strong>$orphan_user</strong> enseignant(s) sans utilisateur associé</p>";
        } else {
            echo "<p class='success'>✅ Tous les enseignants ont un utilisateur associé</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur analyse relations : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 5. Test de l'endpoint API
    echo "<div class='step'>";
    echo "<h3>🌐 5. Test Endpoint API</h3>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/absences/";
    
    echo "<h4>🔗 URL de l'API :</h4>";
    echo "<p><a href='$api_url' target='_blank'>$api_url</a></p>";
    
    echo "<h4>🧪 Test avec cURL :</h4>";
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API accessible</p>";
            
            $data = json_decode($response, true);
            if ($data) {
                echo "<p class='success'>✅ Réponse JSON valide</p>";
                echo "<p><strong>Nombre d'absences retournées :</strong> " . count($data) . "</p>";
                
                if (!empty($data)) {
                    $first_absence = $data[0];
                    echo "<h5>📋 Premier enregistrement :</h5>";
                    echo "<div class='sql-block'>";
                    echo "<pre>" . json_encode($first_absence, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    echo "</div>";
                    
                    if (empty($first_absence['enseignant_nom'])) {
                        echo "<p class='error'>❌ enseignant_nom est vide dans la réponse API</p>";
                    } else {
                        echo "<p class='success'>✅ enseignant_nom présent : " . $first_absence['enseignant_nom'] . "</p>";
                    }
                }
            } else {
                echo "<p class='error'>❌ Réponse JSON invalide</p>";
                echo "<div class='sql-block'><pre>" . htmlspecialchars($response) . "</pre></div>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
            echo "<div class='sql-block'><pre>" . htmlspecialchars($response) . "</pre></div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test cURL : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 6. Solutions recommandées
    echo "<div class='step'>";
    echo "<h3>🔧 6. Solutions Recommandées</h3>";
    
    echo "<div class='problem-box'>";
    echo "<h4>🎯 PROBLÈME 1 : Nom enseignant ne s'affiche pas</h4>";
    echo "<p><strong>Solutions :</strong></p>";
    echo "<ol>";
    echo "<li><strong>Corriger les données :</strong> Mettre à jour les enseignant_id NULL</li>";
    echo "<li><strong>Créer des données de test :</strong> Insérer des enseignants et absences</li>";
    echo "<li><strong>Vérifier les relations :</strong> S'assurer que les FK sont correctes</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='problem-box'>";
    echo "<h4>🎯 PROBLÈME 2 : Boutons CRUD ne s'affichent pas</h4>";
    echo "<p><strong>Causes possibles :</strong></p>";
    echo "<ul>";
    echo "<li>Rôle utilisateur incorrect (doit être Admin ou Enseignant)</li>";
    echo "<li>Variable canManageAbsences = false</li>";
    echo "<li>Problème d'authentification</li>";
    echo "</ul>";
    echo "<p><strong>Solutions :</strong></p>";
    echo "<ol>";
    echo "<li><strong>Vérifier le rôle :</strong> Console du navigateur (F12)</li>";
    echo "<li><strong>Forcer l'affichage :</strong> Temporairement mettre canManageAbsences = true</li>";
    echo "<li><strong>Vérifier l'auth :</strong> Token JWT valide</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h4>🚀 Actions Immédiates :</h4>";
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='fix_data.php' class='fix-button'>🔧 Corriger les Données</a>";
    echo "<a href='create_test_data.php' class='fix-button'>📊 Créer Données Test</a>";
    echo "<a href='$api_url' target='_blank' class='fix-button'>🧪 Tester API</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='fix-button'>🎯 Interface React</a>";
    echo "</div>";
    echo "</div>";
    
    // 7. Instructions de débogage
    echo "<div class='step'>";
    echo "<h3>🐛 7. Instructions de Débogage</h3>";
    
    echo "<h4>🔍 Pour le Frontend React :</h4>";
    echo "<ol>";
    echo "<li><strong>Ouvrir la console :</strong> F12 → Console</li>";
    echo "<li><strong>Vérifier les logs :</strong> Chercher 'User role', 'Can manage absences', 'Absences data'</li>";
    echo "<li><strong>Vérifier les requêtes :</strong> Onglet Network → Voir les appels API</li>";
    echo "<li><strong>Vérifier les données :</strong> Structure des objets absence</li>";
    echo "</ol>";
    
    echo "<h4>🔧 Pour le Backend PHP :</h4>";
    echo "<ol>";
    echo "<li><strong>Tester l'API :</strong> <a href='$api_url' target='_blank'>$api_url</a></li>";
    echo "<li><strong>Vérifier les logs :</strong> Logs d'erreur PHP</li>";
    echo "<li><strong>Tester les requêtes :</strong> Exécuter les SQL manuellement</li>";
    echo "<li><strong>Vérifier les relations :</strong> FK et jointures</li>";
    echo "</ol>";
    
    echo "<h4>📋 Checklist de Vérification :</h4>";
    echo "<ul>";
    echo "<li>☐ Table Absences existe avec enseignant_id</li>";
    echo "<li>☐ Table Enseignants a des données</li>";
    echo "<li>☐ Relations FK correctes</li>";
    echo "<li>☐ API retourne des données</li>";
    echo "<li>☐ enseignant_nom présent dans la réponse</li>";
    echo "<li>☐ Rôle utilisateur correct (Admin/Enseignant)</li>";
    echo "<li>☐ canManageAbsences = true</li>";
    echo "<li>☐ Boutons CRUD visibles</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
