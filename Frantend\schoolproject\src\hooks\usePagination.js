import { useState, useMemo } from 'react';

const usePagination = (data, itemsPerPage = 10) => {
    const [currentPage, setCurrentPage] = useState(1);

    // Calculer les données paginées
    const paginatedData = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return data.slice(startIndex, endIndex);
    }, [data, currentPage, itemsPerPage]);

    // Calculer le nombre total de pages
    const totalPages = Math.ceil(data.length / itemsPerPage);

    // Fonction pour changer de page
    const goToPage = (page) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    // Réinitialiser à la page 1 quand les données changent
    const resetPagination = () => {
        setCurrentPage(1);
    };

    // Informations de pagination
    const paginationInfo = {
        currentPage,
        totalPages,
        totalItems: data.length,
        itemsPerPage,
        startItem: (currentPage - 1) * itemsPerPage + 1,
        endItem: Math.min(currentPage * itemsPerPage, data.length),
        hasNextPage: currentPage < totalPages,
        hasPrevPage: currentPage > 1
    };

    return {
        paginatedData,
        currentPage,
        totalPages,
        goToPage,
        resetPagination,
        paginationInfo
    };
};

export default usePagination;
