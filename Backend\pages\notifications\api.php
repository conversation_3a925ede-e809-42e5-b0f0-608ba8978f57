<?php
// ============================================================================
// API NOTIFICATIONS - SYSTÈME INTÉGRÉ AVEC LA MESSAGERIE
// ============================================================================

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// ============================================================================
// SYSTÈME D'AUTHENTIFICATION
// ============================================================================

function getCurrentUser($pdo) {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        return null;
    }
    
    $token = $matches[1];
    
    // Simulation de l'authentification basée sur le token
    if (preg_match('/^(admin|enseignant|parent|etudiant)-token-?(\d*)$/', $token, $tokenMatches)) {
        $role = $tokenMatches[1];
        $userId = !empty($tokenMatches[2]) ? (int)$tokenMatches[2] : 1;
        
        // Vérifier que l'utilisateur existe
        $stmt = $pdo->prepare("
            SELECT u.id, u.nom, u.email, r.nom as role 
            FROM utilisateurs u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ? AND r.nom = ?
        ");
        $stmt->execute([$userId, $role]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $user ?: null;
    }
    
    return null;
}

// Vérification de l'authentification
$currentUser = getCurrentUser($pdo);
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['error' => 'Token d\'authentification invalide ou manquant']);
    exit();
}

// ============================================================================
// GESTION DES REQUÊTES API
// ============================================================================

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($pdo, $currentUser, $action);
            break;
        case 'PUT':
            handlePutRequest($pdo, $currentUser);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

// ============================================================================
// FONCTIONS DE GESTION DES REQUÊTES
// ============================================================================

function handleGetRequest($pdo, $currentUser, $action) {
    switch ($action) {
        case 'stats':
            getNotificationStats($pdo, $currentUser);
            break;
        case 'recent':
            getRecentNotifications($pdo, $currentUser);
            break;
        case 'all':
            getAllNotifications($pdo, $currentUser);
            break;
        default:
            getAllNotifications($pdo, $currentUser);
    }
}

function handlePutRequest($pdo, $currentUser) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de la notification requis']);
        return;
    }
    
    markNotificationAsRead($pdo, $currentUser, $input['id']);
}

// ============================================================================
// FONCTIONS MÉTIER
// ============================================================================

function getNotificationStats($pdo, $currentUser) {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN lu = FALSE THEN 1 END) as unread,
            COUNT(CASE WHEN lu = TRUE THEN 1 END) as read
        FROM notifications
        WHERE utilisateur_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'total' => (int)$stats['total'],
        'unread' => (int)$stats['unread'],
        'read' => (int)$stats['read']
    ]);
}

function getRecentNotifications($pdo, $currentUser) {
    $limit = $_GET['limit'] ?? 10;
    
    $stmt = $pdo->prepare("
        SELECT 
            n.*,
            exp.nom as expediteur_nom,
            exp.email as expediteur_email
        FROM notifications n
        LEFT JOIN utilisateurs exp ON n.expediteur_id = exp.id
        WHERE n.utilisateur_id = ?
        ORDER BY n.date_envoi DESC
        LIMIT ?
    ");
    $stmt->execute([$currentUser['id'], (int)$limit]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($notifications);
}

function getAllNotifications($pdo, $currentUser) {
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $offset = ((int)$page - 1) * (int)$limit;
    
    // Compter le total
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total
        FROM notifications
        WHERE utilisateur_id = ?
    ");
    $stmt->execute([$currentUser['id']]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Récupérer les notifications
    $stmt = $pdo->prepare("
        SELECT 
            n.*,
            exp.nom as expediteur_nom,
            exp.email as expediteur_email
        FROM notifications n
        LEFT JOIN utilisateurs exp ON n.expediteur_id = exp.id
        WHERE n.utilisateur_id = ?
        ORDER BY n.date_envoi DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$currentUser['id'], (int)$limit, $offset]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'pagination' => [
            'current_page' => (int)$page,
            'total_pages' => ceil($total / $limit),
            'total_items' => (int)$total,
            'items_per_page' => (int)$limit
        ]
    ]);
}

function markNotificationAsRead($pdo, $currentUser, $notificationId) {
    $stmt = $pdo->prepare("
        UPDATE notifications 
        SET lu = TRUE, date_lecture = NOW()
        WHERE id = ? AND utilisateur_id = ?
    ");
    $stmt->execute([$notificationId, $currentUser['id']]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'Notification marquée comme lue']);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Notification non trouvée']);
    }
}
