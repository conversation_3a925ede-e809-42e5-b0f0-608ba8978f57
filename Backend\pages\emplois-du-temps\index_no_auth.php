<?php
/**
 * API CRUD pour la gestion des Emplois du Temps - SANS AUTHENTIFICATION
 * MÊME LOGIQUE QUE LES ABSENCES QUI FONCTIONNENT
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données - MÊME LOGIQUE QUE ABSENCES
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Simulation utilisateur Admin pour les tests - MÊME LOGIQUE QUE ABSENCES
$user_info = [
    'id' => 1,
    'role' => 'Admin'
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * GET - Récupérer tous les emplois du temps - MÊME STRUCTURE QUE ABSENCES
 */
function handleGet($pdo, $user_info) {
    try {
        // CORRECTION : Requête adaptée à la vraie structure des tables
        $sql = "
            SELECT edt.id, edt.classe_id, edt.jour, edt.heure_debut, edt.heure_fin,
                   edt.matiere_id, edt.enseignant_id,
                   COALESCE(c.nom, CONCAT('Classe ID ', edt.classe_id, ' (non trouvée)')) as classe_nom,
                   COALESCE(CONCAT('Niveau ', c.niveau_id), '') as niveau,
                   COALESCE(m.nom, CONCAT('Matière ID ', edt.matiere_id, ' (non trouvée)')) as matiere_nom,
                   '' as matiere_code,
                   COALESCE(e.nom_prenom, CONCAT('Enseignant ID ', edt.enseignant_id, ' (non trouvé)')) as enseignant_nom,
                   COALESCE(e.nom_prenom, '') as enseignant_nom_complet,
                   '' as enseignant_prenom,
                   COALESCE(e.email, '') as enseignant_email,
                   COALESCE(e.specialite, '') as enseignant_specialite,
                   COALESCE(e.telephone, '') as enseignant_telephone,
                   -- Indicateurs de validité des jointures
                   CASE WHEN c.id IS NOT NULL THEN 1 ELSE 0 END as classe_valide,
                   CASE WHEN m.id IS NOT NULL THEN 1 ELSE 0 END as matiere_valide,
                   CASE WHEN e.id IS NOT NULL THEN 1 ELSE 0 END as enseignant_valide
            FROM EmploisDuTemps edt
            LEFT JOIN Classes c ON edt.classe_id = c.id
            LEFT JOIN Matieres m ON edt.matiere_id = m.id
            LEFT JOIN Enseignants e ON edt.enseignant_id = e.id
            ORDER BY
                CASE edt.jour
                    WHEN 'Lundi' THEN 1
                    WHEN 'Mardi' THEN 2
                    WHEN 'Mercredi' THEN 3
                    WHEN 'Jeudi' THEN 4
                    WHEN 'Vendredi' THEN 5
                    WHEN 'Samedi' THEN 6
                    ELSE 7
                END,
                edt.heure_debut
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $emplois = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format identique aux absences : tableau direct
        $result = [];
        foreach ($emplois as $emploi) {
            $result[] = [
                'id' => (int)$emploi['id'],
                'classe_id' => $emploi['classe_id'] ? (int)$emploi['classe_id'] : null,
                'jour' => $emploi['jour'],
                'heure_debut' => $emploi['heure_debut'],
                'heure_fin' => $emploi['heure_fin'],
                'matiere_id' => $emploi['matiere_id'] ? (int)$emploi['matiere_id'] : null,
                'enseignant_id' => $emploi['enseignant_id'] ? (int)$emploi['enseignant_id'] : null,
                'classe_nom' => $emploi['classe_nom'],
                'niveau' => $emploi['niveau'],
                'matiere_nom' => $emploi['matiere_nom'],
                'matiere_code' => $emploi['matiere_code'],
                'enseignant_nom' => $emploi['enseignant_nom'],
                'enseignant_prenom' => '', // Déjà inclus dans enseignant_nom
                'enseignant_email' => $emploi['enseignant_email']
            ];
        }
        
        // Retour identique aux absences : tableau direct
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération: ' . $e->getMessage()]);
    }
}

/**
 * POST - Créer un nouvel emploi du temps - MÊME LOGIQUE QUE ABSENCES
 */
function handlePost($pdo, $user_info, $input) {
    try {
        // Validation des données requises
        if (!isset($input['classe_id'], $input['jour'], $input['heure_debut'], $input['heure_fin'], $input['matiere_id'], $input['enseignant_id'])) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Données manquantes: classe_id, jour, heure_debut, heure_fin, matiere_id et enseignant_id requis',
                'received' => $input
            ]);
            return;
        }
        
        // Nettoyer et valider les données
        $classe_id = (int)$input['classe_id'];
        $jour = trim($input['jour']);
        $heure_debut = trim($input['heure_debut']);
        $heure_fin = trim($input['heure_fin']);
        $matiere_id = (int)$input['matiere_id'];
        $enseignant_id = (int)$input['enseignant_id'];
        
        // Validation des IDs
        if ($classe_id <= 0 || $matiere_id <= 0 || $enseignant_id <= 0) {
            http_response_code(400);
            echo json_encode(['error' => 'IDs invalides']);
            return;
        }
        
        // Validation du jour
        $jours_valides = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
        if (!in_array($jour, $jours_valides)) {
            http_response_code(400);
            echo json_encode(['error' => 'Jour invalide. Jours valides: ' . implode(', ', $jours_valides)]);
            return;
        }
        
        // Validation des heures
        if (empty($heure_debut) || empty($heure_fin)) {
            http_response_code(400);
            echo json_encode(['error' => 'Les heures de début et fin sont requises']);
            return;
        }
        
        // Vérifier que l'heure de fin est après l'heure de début
        if ($heure_debut >= $heure_fin) {
            http_response_code(400);
            echo json_encode(['error' => 'L\'heure de fin doit être après l\'heure de début']);
            return;
        }
        
        // Vérifier les conflits d'horaires pour la même classe
        $stmt = $pdo->prepare("
            SELECT id FROM EmploisDuTemps
            WHERE classe_id = ? AND jour = ?
            AND ((heure_debut <= ? AND heure_fin > ?) OR (heure_debut < ? AND heure_fin >= ?))
        ");
        $stmt->execute([$classe_id, $jour, $heure_debut, $heure_debut, $heure_fin, $heure_fin]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Conflit d\'horaires détecté pour cette classe']);
            return;
        }
        
        // Vérifier les conflits d'horaires pour le même enseignant
        $stmt = $pdo->prepare("
            SELECT id FROM EmploisDuTemps
            WHERE enseignant_id = ? AND jour = ?
            AND ((heure_debut <= ? AND heure_fin > ?) OR (heure_debut < ? AND heure_fin >= ?))
        ");
        $stmt->execute([$enseignant_id, $jour, $heure_debut, $heure_debut, $heure_fin, $heure_fin]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Conflit d\'horaires détecté pour cet enseignant']);
            return;
        }
        
        // Insérer l'emploi du temps
        $stmt = $pdo->prepare("
            INSERT INTO EmploisDuTemps (classe_id, jour, heure_debut, heure_fin, matiere_id, enseignant_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([$classe_id, $jour, $heure_debut, $heure_fin, $matiere_id, $enseignant_id]);
        
        if ($result) {
            $new_id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'id' => (int)$new_id,
                'message' => 'Emploi du temps créé avec succès'
            ]);
        } else {
            throw new Exception('Échec de l\'insertion en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * PUT - Modifier un emploi du temps existant - MÊME LOGIQUE QUE ABSENCES
 */
function handlePut($pdo, $user_info, $input) {
    try {
        if (!isset($input['id']) || !is_numeric($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID emploi du temps manquant ou invalide']);
            return;
        }
        
        $emploi_id = (int)$input['id'];
        
        // Vérifier que l'emploi du temps existe
        $stmt = $pdo->prepare("SELECT * FROM EmploisDuTemps WHERE id = ?");
        $stmt->execute([$emploi_id]);
        $existing_emploi = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existing_emploi) {
            http_response_code(404);
            echo json_encode(['error' => 'Emploi du temps non trouvé']);
            return;
        }
        
        // Préparer les champs à modifier
        $fields = [];
        $values = [];
        
        if (isset($input['jour'])) {
            $jour = trim($input['jour']);
            $jours_valides = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
            if (!in_array($jour, $jours_valides)) {
                http_response_code(400);
                echo json_encode(['error' => 'Jour invalide']);
                return;
            }
            $fields[] = 'jour = ?';
            $values[] = $jour;
        }
        
        if (isset($input['heure_debut'])) {
            $heure_debut = trim($input['heure_debut']);
            if (empty($heure_debut)) {
                http_response_code(400);
                echo json_encode(['error' => 'L\'heure de début ne peut pas être vide']);
                return;
            }
            $fields[] = 'heure_debut = ?';
            $values[] = $heure_debut;
        }
        
        if (isset($input['heure_fin'])) {
            $heure_fin = trim($input['heure_fin']);
            if (empty($heure_fin)) {
                http_response_code(400);
                echo json_encode(['error' => 'L\'heure de fin ne peut pas être vide']);
                return;
            }
            $fields[] = 'heure_fin = ?';
            $values[] = $heure_fin;
        }
        
        if (isset($input['classe_id'])) {
            $classe_id = (int)$input['classe_id'];
            if ($classe_id > 0) {
                $fields[] = 'classe_id = ?';
                $values[] = $classe_id;
            }
        }
        
        if (isset($input['matiere_id'])) {
            $matiere_id = (int)$input['matiere_id'];
            if ($matiere_id > 0) {
                $fields[] = 'matiere_id = ?';
                $values[] = $matiere_id;
            }
        }
        
        if (isset($input['enseignant_id'])) {
            $enseignant_id = (int)$input['enseignant_id'];
            if ($enseignant_id > 0) {
                $fields[] = 'enseignant_id = ?';
                $values[] = $enseignant_id;
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['error' => 'Aucune donnée à modifier']);
            return;
        }
        
        $values[] = $emploi_id;
        $sql = "UPDATE EmploisDuTemps SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Emploi du temps modifié avec succès'
            ]);
        } else {
            throw new Exception('Échec de la modification en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

/**
 * DELETE - Supprimer un emploi du temps - MÊME LOGIQUE QUE ABSENCES
 */
function handleDelete($pdo, $user_info, $input) {
    try {
        if (!isset($input['id']) || !is_numeric($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID emploi du temps manquant ou invalide']);
            return;
        }
        
        $emploi_id = (int)$input['id'];
        
        // Vérifier que l'emploi du temps existe
        $stmt = $pdo->prepare("SELECT id FROM EmploisDuTemps WHERE id = ?");
        $stmt->execute([$emploi_id]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Emploi du temps non trouvé']);
            return;
        }
        
        // Supprimer l'emploi du temps
        $stmt = $pdo->prepare("DELETE FROM EmploisDuTemps WHERE id = ?");
        $result = $stmt->execute([$emploi_id]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Emploi du temps supprimé avec succès'
            ]);
        } else {
            throw new Exception('Échec de la suppression en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
