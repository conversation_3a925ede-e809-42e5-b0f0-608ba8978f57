import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const MatiereCRUD = () => {
    const { user } = useContext(AuthContext);
    const [matieres, setMatieres] = useState([]);
    const [filieres, setFilieres] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingMatiere, setEditingMatiere] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filiereFilter, setFiliereFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        nom: '',
        filiere_id: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchMatieres();
        fetchFilieres();
    }, []);

    const fetchFilieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/filieres/filiere.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setFilieres(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des filières:', error);
            setFilieres([]);
        }
    };

    const fetchMatieres = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des matières...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let matieresData = response.data;
            if (!Array.isArray(matieresData)) {
                matieresData = [];
            }

            setMatieres(matieresData);
            console.log('✅ Matières chargées:', matieresData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des matières:', error);

            // Données de test
            const testMatieres = [
                { id: 1, nom: 'Mathématiques', filiere_id: 1, filiere_nom: 'Sciences' },
                { id: 2, nom: 'Physique', filiere_id: 1, filiere_nom: 'Sciences' },
                { id: 3, nom: 'Chimie', filiere_id: 1, filiere_nom: 'Sciences' },
                { id: 4, nom: 'Français', filiere_id: 2, filiere_nom: 'Littéraire' },
                { id: 5, nom: 'Histoire', filiere_id: 2, filiere_nom: 'Littéraire' },
                { id: 6, nom: 'Géographie', filiere_id: 2, filiere_nom: 'Littéraire' },
                { id: 7, nom: 'Économie', filiere_id: 3, filiere_nom: 'Économique' },
                { id: 8, nom: 'Comptabilité', filiere_id: 3, filiere_nom: 'Économique' },
                { id: 9, nom: 'Informatique', filiere_id: 4, filiere_nom: 'Technique' },
                { id: 10, nom: 'Électronique', filiere_id: 4, filiere_nom: 'Technique' },
                { id: 11, nom: 'Biologie', filiere_id: 1, filiere_nom: 'Sciences' },
                { id: 12, nom: 'Philosophie', filiere_id: 2, filiere_nom: 'Littéraire' }
            ];

            setMatieres(testMatieres);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testMatieres.length} matières de test.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des matières', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/matieres/matiere.php';
            const method = editingMatiere ? 'PUT' : 'POST';
            const data = editingMatiere ? { ...formData, id: editingMatiere.id } : formData;

            console.log('🔄 Envoi requête:', { method, url, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data.success) {
                Swal.fire('Succès', `Matière ${editingMatiere ? 'modifiée' : 'créée'} avec succès`, 'success');
                setShowModal(false);
                setEditingMatiere(null);
                resetForm();
                fetchMatieres();
            } else {
                throw new Error(response.data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            const errorMessage = error.response?.data?.error ||
                               error.response?.data?.message ||
                               error.message ||
                               'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (matiere) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des matières', 'error');
            return;
        }

        setEditingMatiere(matiere);
        setFormData({
            nom: matiere.nom,
            filiere_id: matiere.filiere_id
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des matières', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/matieres/matiere.php';

                console.log('🔄 Suppression matière:', { id, url });

                const response = await axios.delete(url, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data.success) {
                    Swal.fire('Supprimé!', 'La matière a été supprimée.', 'success');
                    fetchMatieres();
                } else {
                    throw new Error(response.data.error || 'Erreur lors de la suppression');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error ||
                                   error.response?.data?.message ||
                                   error.message ||
                                   'Impossible de supprimer la matière';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            nom: '',
            filiere_id: ''
        });
    };

    // Filtrage des données
    const filteredMatieres = matieres.filter(matiere => {
        const matchesSearch = matiere.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             matiere.filiere_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesFiliere = filiereFilter === 'all' || matiere.filiere_id?.toString() === filiereFilter;

        return matchesSearch && matchesFiliere;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentMatieres = filteredMatieres.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredMatieres.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, filiereFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des matières...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>📚 Gestion des Matières</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredMatieres.length} matière(s) trouvée(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvelle Matière
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les matières en mode lecture seule.
                        Seul l'administrateur peut créer, modifier ou supprimer des matières.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher une matière..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="filiere-filter">
                    <select
                        value={filiereFilter}
                        onChange={(e) => {
                            setFiliereFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '200px'
                        }}
                    >
                        <option value="all">Toutes les filières</option>
                        {filieres.map(filiere => (
                            <option key={filiere.id} value={filiere.id}>
                                {filiere.nom}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredMatieres.length === 0 ? (
                    <div className="no-data">
                        <img src="/book.png" alt="Aucune matière" />
                        <p>Aucune matière trouvée</p>
                        {(searchTerm || filiereFilter !== 'all') && (
                            <button
                                onClick={() => {
                                    setSearchTerm('');
                                    setFiliereFilter('all');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📚 Nom de la Matière</th>
                                    <th>🎓 Filière</th>
                                    <th>📊 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentMatieres.map((matiere) => (
                                    <tr key={matiere.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{matiere.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{matiere.nom}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#856404'
                                            }}>
                                                {matiere.filiere_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="badge badge-success">Actif</span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(matiere)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(matiere.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredMatieres.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredMatieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des matières</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Filières disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {currentMatieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichées</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier une matière */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingMatiere ? 'Modifier la matière' : 'Nouvelle matière'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingMatiere(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Nom de la matière *</label>
                                <input
                                    type="text"
                                    value={formData.nom}
                                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                                    placeholder="Ex: Mathématiques, Physique..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Filière *</label>
                                <select
                                    value={formData.filiere_id}
                                    onChange={(e) => setFormData({...formData, filiere_id: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                >
                                    <option value="">Sélectionner une filière</option>
                                    {filieres.map((filiere) => (
                                        <option key={filiere.id} value={filiere.id}>
                                            {filiere.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingMatiere(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingMatiere ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default MatiereCRUD;
