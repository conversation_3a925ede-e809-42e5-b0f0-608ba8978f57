/* Styles pour le modal de modification des utilisateurs */

.user-edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.user-edit-modal {
  background-color: white;
  border-radius: 15px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  animation: slideIn 0.3s ease-out;
}

.user-edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.user-edit-modal-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--cerulean);
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-edit-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.user-edit-modal-close:hover {
  background-color: #f5f5f5;
  color: #333;
}

.user-edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-edit-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-edit-label {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-edit-input,
.user-edit-select {
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.user-edit-input:focus,
.user-edit-select:focus {
  outline: none;
  border-color: var(--cerulean);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.user-edit-input.error,
.user-edit-select.error {
  border-color: #e53935;
}

.user-edit-error {
  color: #e53935;
  font-size: 12px;
  margin-top: 5px;
}

.user-edit-general-error {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 14px;
}

.user-edit-button-group {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 2px solid #f0f0f0;
}

.user-edit-button {
  padding: 12px 25px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.user-edit-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.user-edit-button-save {
  background-color: var(--cerulean);
  color: white;
}

.user-edit-button-save:hover:not(:disabled) {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.user-edit-button-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.user-edit-button-cancel:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #333;
}

.user-edit-button-loading {
  background-color: #ccc;
  cursor: not-allowed;
}

.user-edit-password-hint {
  color: #666;
  font-size: 12px;
  font-style: italic;
  margin-top: 5px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .user-edit-modal {
    width: 95%;
    padding: 20px;
    margin: 10px;
  }
  
  .user-edit-modal-title {
    font-size: 1.3rem;
  }
  
  .user-edit-button-group {
    flex-direction: column;
  }
  
  .user-edit-button {
    width: 100%;
    justify-content: center;
  }
}

/* Amélioration de l'accessibilité */
.user-edit-modal:focus {
  outline: none;
}

.user-edit-input:focus,
.user-edit-select:focus {
  outline: 2px solid var(--cerulean);
  outline-offset: 2px;
}

/* Style pour les champs requis */
.user-edit-label.required::after {
  content: " *";
  color: #e53935;
  font-weight: bold;
}

/* Style pour les messages de succès */
.user-edit-success {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 14px;
}

/* Style pour les champs en cours de chargement */
.user-edit-input:disabled,
.user-edit-select:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* Style pour les icônes dans les labels */
.user-edit-label svg {
  color: var(--cerulean);
  font-size: 16px;
}

/* Style pour le focus trap */
.user-edit-modal-overlay[data-focus-trap="true"] {
  /* Styles pour le piège de focus si nécessaire */
}
