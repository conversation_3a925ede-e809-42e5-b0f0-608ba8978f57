<?php
/**
 * Script de test pour l'API Classes
 * Utilisation: php test_classe_api.php
 */

echo "🧪 Test de l'API Classes\n";
echo "========================\n\n";

// Configuration
$baseUrl = 'http://localhost/Project_PFE/Backend/pages/classes/classe.php';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer test-token'
    ]);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: GET - Récupérer toutes les classes
echo "1️⃣ Test GET - Récupérer toutes les classes\n";
$response = makeRequest($baseUrl, 'GET');
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && is_array($response['data'])) {
    echo "✅ Succès: " . count($response['data']) . " classes trouvées\n";
} else {
    echo "❌ Erreur: Réponse invalide\n";
}
echo "\n";

// Test 2: POST - Créer une nouvelle classe
echo "2️⃣ Test POST - Créer une nouvelle classe\n";
$testData = [
    'nom' => 'Test Classe ' . date('H:i:s'),
    'filiere_id' => 1,  // Supposons que la filière 1 existe
    'niveau_id' => 1    // Supposons que le niveau 1 existe
];
$response = makeRequest($baseUrl, 'POST', $testData);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === true) {
    echo "✅ Succès: Classe créée avec ID " . $response['data']['id'] . "\n";
    $testId = $response['data']['id'];
} else {
    echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    $testId = null;
}
echo "\n";

// Test 3: PUT - Modifier la classe créée
if ($testId) {
    echo "3️⃣ Test PUT - Modifier la classe créée\n";
    $updateData = [
        'id' => $testId,
        'nom' => 'Test Classe Modifiée ' . date('H:i:s'),
        'filiere_id' => 1,
        'niveau_id' => 2  // Changer le niveau
    ];
    $response = makeRequest($baseUrl, 'PUT', $updateData);
    echo "Code HTTP: " . $response['code'] . "\n";
    echo "Réponse: " . $response['body'] . "\n";
    if ($response['data'] && $response['data']['success'] === true) {
        echo "✅ Succès: Classe modifiée\n";
    } else {
        echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    }
    echo "\n";
}

// Test 4: DELETE - Supprimer la classe créée
if ($testId) {
    echo "4️⃣ Test DELETE - Supprimer la classe créée\n";
    $deleteData = ['id' => $testId];
    $response = makeRequest($baseUrl, 'DELETE', $deleteData);
    echo "Code HTTP: " . $response['code'] . "\n";
    echo "Réponse: " . $response['body'] . "\n";
    if ($response['data'] && $response['data']['success'] === true) {
        echo "✅ Succès: Classe supprimée\n";
    } else {
        echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    }
    echo "\n";
}

// Test 5: Validation des erreurs - POST sans nom
echo "5️⃣ Test de validation - POST sans nom\n";
$response = makeRequest($baseUrl, 'POST', ['nom' => '', 'filiere_id' => 1, 'niveau_id' => 1]);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === false) {
    echo "✅ Succès: Validation fonctionne - " . $response['data']['error'] . "\n";
} else {
    echo "❌ Erreur: Validation ne fonctionne pas\n";
}
echo "\n";

// Test 6: Validation des erreurs - POST sans filiere_id
echo "6️⃣ Test de validation - POST sans filiere_id\n";
$response = makeRequest($baseUrl, 'POST', ['nom' => 'Test', 'niveau_id' => 1]);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === false) {
    echo "✅ Succès: Validation fonctionne - " . $response['data']['error'] . "\n";
} else {
    echo "❌ Erreur: Validation ne fonctionne pas\n";
}
echo "\n";

// Test 7: Validation des erreurs - POST avec filiere_id inexistant
echo "7️⃣ Test de validation - POST avec filiere_id inexistant\n";
$response = makeRequest($baseUrl, 'POST', ['nom' => 'Test', 'filiere_id' => 9999, 'niveau_id' => 1]);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === false) {
    echo "✅ Succès: Validation fonctionne - " . $response['data']['error'] . "\n";
} else {
    echo "❌ Erreur: Validation ne fonctionne pas\n";
}
echo "\n";

// Test 8: Test de méthode non autorisée
echo "8️⃣ Test PATCH - Méthode non autorisée\n";
$response = makeRequest($baseUrl, 'PATCH', ['test' => 'data']);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['code'] == 405) {
    echo "✅ Succès: Méthode non autorisée correctement gérée\n";
} else {
    echo "❌ Erreur: Méthode non autorisée mal gérée\n";
}
echo "\n";

echo "🏁 Tests terminés!\n";
echo "==================\n";
echo "Vérifiez les logs d'erreur PHP pour plus de détails.\n";
?>
