<?php

class Database
{
    private $host = "";
    private $user = "root";
    private $pswd = "";

    private function getconnexion()
    {
        try {
            return new PDO($this->host, $this->user, $this->pswd);
        } catch (PDOException $e) {
            die('Erreur:' . $e->getMessage());
        }
    }

    public function create(int $etudiant_id, string $mois, float $montant, string $date_paiement, string $statut)
    {
        $q = $this->getconnexion()->prepare("
            INSERT INTO factures (etudiant_id, mois, montant, date_paiement, statut) 
            VALUES (:etudiant_id, :mois, :montant, :date_paiement, :statut)
        ");
        return $q->execute([
            'etudiant_id'   => $etudiant_id,
            'mois'          => $mois,
            'montant'       => $montant,
            'date_paiement' => $date_paiement,
            'statut'        => $statut
        ]);
    }

    public function read()
    {
        return $this->getconnexion()->query("
            SELECT f.*, u.nom, u.email
            FROM factures f
            JOIN etudiants e ON f.etudiant_id = e.id
            JOIN utilisateurs u ON e.utilisateur_id = u.id
            ORDER BY f.id DESC
        ")->fetchAll(PDO::FETCH_OBJ);
    }

    public function countBills(): int
    {
        return (int)$this->getconnexion()->query("SELECT COUNT(id) as count FROM factures")->fetch()[0];
    }

    public function getSingleBill(int $id)
    {
        $q = $this->getconnexion()->prepare("SELECT * FROM factures WHERE id = :id");
        $q->execute(['id' => $id]);
        return $q->fetch(PDO::FETCH_OBJ);
    }

    public function update(int $id, int $etudiant_id, string $mois, float $montant, string $date_paiement, string $statut)
    {
        $q = $this->getconnexion()->prepare("
            UPDATE factures 
            SET etudiant_id = :etudiant_id, 
                mois = :mois, 
                montant = :montant, 
                date_paiement = :date_paiement, 
                statut = :statut 
            WHERE id = :id
        ");
        return $q->execute([
            'etudiant_id'   => $etudiant_id,
            'mois'          => $mois,
            'montant'       => $montant,
            'date_paiement' => $date_paiement,
            'statut'        => $statut,
            'id'            => $id
        ]);
    }

    public function delete(int $id): bool
    {
        $q = $this->getconnexion()->prepare("DELETE FROM factures WHERE id = :id");
        return $q->execute(['id' => $id]);
    }
}
