import React, { useState, useEffect } from "react";
import axios from "axios";
import "../css/Register.css"; 

export default function Register() {
  const [nom, setNom] = useState("");
  const [email, setEmail] = useState("");
  const [motDePasse, setMotDePasse] = useState("");
  const [roles, setRoles] = useState([]);
  const [roleId, setRoleId] = useState("");

  // Charger les rôles depuis la base de données
  useEffect(() => {
    axios.get("http://localhost/SchoolProject/Auth/role.php")
      .then(res => setRoles(res.data))
      .catch(err => console.error("Erreur lors du chargement des rôles:", err));
  }, []);

  // Gestion de l'envoi du formulaire
  const handleSubmit = (e) => {
    e.preventDefault();
    axios.post("http://localhost/SchoolProject/Auth/register.php", {
      nom,
      email,
      mot_de_passe: motDePasse,
      role_id: roleId
    })
    .then(res => alert(res.data.message))
    .catch(err => alert("Erreur lors de l'inscription: " + err));
  };

  return (
    <div className="register-container">
      <form className="register-form" onSubmit={handleSubmit}>
        <h2>Créer un compte</h2>

        <input
          type="text"
          placeholder="Nom complet"
          value={nom}
          onChange={e => setNom(e.target.value)}
          required
        />

        <input
          type="email"
          placeholder="Adresse e-mail"
          value={email}
          onChange={e => setEmail(e.target.value)}
          required
        />

        <input
          type="password"
          placeholder="Mot de passe"
          value={motDePasse}
          onChange={e => setMotDePasse(e.target.value)}
          required
        />

        <select value={roleId} onChange={e => setRoleId(e.target.value)} required>
          <option value="">-- Sélectionnez un rôle --</option>
          {roles.map(role => (
            <option key={role.id} value={role.id}>{role.nom}</option>
          ))}
        </select>

        <button type="submit">S'inscrire</button>
      </form>
    </div>
  );
}
