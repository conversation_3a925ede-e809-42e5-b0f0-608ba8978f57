<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Test 1: Compter les étudiants
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_etudiants FROM Etudiants");
        $stmt->execute();
        $etudiants_count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Test 2: Compter les diplômes
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_diplomes FROM Diplomes");
        $stmt->execute();
        $diplomes_count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Test 3: Récupérer quelques étudiants avec leurs infos complètes
        $stmt = $pdo->prepare("
            SELECT
                e.id as etudiant_id,
                u.id as numero_etudiant,
                u.nom,
                u.email,
                g.nom as groupe_nom,
                c.nom as classe_nom,
                f.nom as filiere_nom,
                n.nom as niveau_nom
            FROM Etudiants e
            JOIN Utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN Groupes g ON e.groupe_id = g.id
            LEFT JOIN Classes c ON g.classe_id = c.id
            LEFT JOIN Filieres f ON c.filiere_id = f.id
            LEFT JOIN Niveaux n ON c.niveau_id = n.id
            LIMIT 5
        ");
        $stmt->execute();
        $sample_etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Test 4: Récupérer quelques diplômes avec infos complètes
        $stmt = $pdo->prepare("
            SELECT 
                d.*,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                g.nom as groupe_nom,
                c.nom as classe_nom,
                f.nom as filiere_nom,
                n.nom as niveau_nom
            FROM Diplomes d
            JOIN Etudiants e ON d.etudiant_id = e.id
            JOIN Utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN Groupes g ON e.groupe_id = g.id
            LEFT JOIN Classes c ON g.classe_id = c.id
            LEFT JOIN Filieres f ON c.filiere_id = f.id
            LEFT JOIN Niveaux n ON c.niveau_id = n.id
            LIMIT 5
        ");
        $stmt->execute();
        $sample_diplomes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Tests des diplômes réussis',
            'data' => [
                'total_etudiants' => $etudiants_count['total_etudiants'],
                'total_diplomes' => $diplomes_count['total_diplomes'],
                'sample_etudiants' => $sample_etudiants,
                'sample_diplomes' => $sample_diplomes
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Créer des données de test
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['create_test_data']) && $input['create_test_data'] === true) {
            // Vérifier s'il y a des étudiants
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM Etudiants");
            $stmt->execute();
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($count['count'] > 0) {
                // Récupérer quelques étudiants
                $stmt = $pdo->prepare("SELECT id FROM Etudiants LIMIT 3");
                $stmt->execute();
                $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $diplomes_created = 0;
                $titres_diplomes = [
                    'Licence en Informatique',
                    'Master en Gestion',
                    'Diplôme Technicien Supérieur',
                    'Licence en Mathématiques',
                    'Master en Finance',
                    'Certificat en Marketing Digital'
                ];
                
                foreach ($etudiants as $index => $etudiant) {
                    // Créer 1-2 diplômes par étudiant
                    $nb_diplomes = rand(1, 2);
                    
                    for ($i = 0; $i < $nb_diplomes; $i++) {
                        $titre = $titres_diplomes[array_rand($titres_diplomes)];
                        $date_obtention = date('Y-m-d', strtotime('-' . rand(30, 365) . ' days'));
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO Diplomes (etudiant_id, titre, date_obtention) 
                            VALUES (?, ?, ?)
                        ");
                        $stmt->execute([
                            $etudiant['id'],
                            $titre,
                            $date_obtention
                        ]);
                        $diplomes_created++;
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "Données de test créées : $diplomes_created diplômes"
                ]);
            } else {
                echo json_encode([
                    'error' => 'Aucun étudiant trouvé. Créez d\'abord des étudiants.'
                ]);
            }
        } else {
            echo json_encode(['error' => 'Paramètre create_test_data manquant']);
        }
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la création des données : ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
