/* Container principal */
.notes-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* En-tête de page */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 5px solid #28a745;
}

.page-header h1 {
    margin: 0;
    color: #333;
    font-size: 28px;
    font-weight: 700;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-info .total-count {
    padding: 8px 16px;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-radius: 25px;
    font-size: 0.9em;
    font-weight: 600;
    border: 2px solid #b8daff;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

/* Bouton d'ajout principal */
.add-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.add-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.add-button:hover::before {
    left: 100%;
}

.add-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #28a745 100%);
}

.add-button img {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
}

/* Informations des boutons */
.button-info {
    margin-top: 4px;
    opacity: 0.9;
}

.button-info small {
    font-size: 10px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.3px;
    line-height: 1.2;
}

/* Filtres de recherche */
.search-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box img {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    opacity: 0.5;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

.filter-select:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Tableau */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    margin-bottom: 25px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    border: none;
}

.table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Informations spécifiques */
.student-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.student-info strong {
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.student-info small {
    color: #6c757d;
    font-size: 12px;
}

.devoir-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.devoir-info strong {
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.devoir-info small {
    color: #6c757d;
    font-size: 12px;
}

/* Affichage des notes */
.grade-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.grade-value {
    font-size: 18px;
    font-weight: 700;
    font-family: monospace;
}

.grade-label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Statistiques quiz */
.quiz-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stat-item {
    font-family: monospace;
    font-size: 14px;
    font-weight: 600;
    color: #28a745;
}

.quiz-stats small {
    font-size: 11px;
    color: #6c757d;
    text-align: center;
}

.incomplete {
    color: #ffc107 !important;
    font-weight: 500;
}

/* Date */
.date-display {
    font-family: monospace;
    font-size: 13px;
    color: #6c757d;
}

/* Badges */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.recalculate-button, .edit-button, .delete-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 80px;
    justify-content: center;
    flex-direction: column;
}

.recalculate-button {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.recalculate-button:hover {
    background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.edit-button {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.edit-button:hover {
    background: linear-gradient(135deg, #ffcd39 0%, #ffc107 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.delete-button {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-button:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.recalculate-button img, .edit-button img, .delete-button img {
    width: 12px;
    height: 12px;
    filter: brightness(0) invert(1);
}

.edit-button img {
    filter: brightness(0) saturate(100%) invert(13%) sepia(94%) saturate(7151%) hue-rotate(0deg) brightness(90%) contrast(119%);
}

.btn-text {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Informations des boutons d'action */
.btn-info-note {
    margin-top: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-info-note small {
    font-size: 8px;
    font-weight: 400;
    text-transform: none;
    letter-spacing: 0.2px;
    line-height: 1;
}

.recalculate-button .btn-info-note small {
    color: rgba(255, 255, 255, 0.8);
}

.edit-button .btn-info-note small {
    color: rgba(33, 37, 41, 0.7);
}

.delete-button .btn-info-note small {
    color: rgba(255, 255, 255, 0.8);
}

/* Afficher les infos au survol */
.recalculate-button:hover .btn-info-note,
.edit-button:hover .btn-info-note,
.delete-button:hover .btn-info-note {
    opacity: 1;
}

/* Ajustement de la taille des boutons pour les infos */
.recalculate-button, .edit-button, .delete-button {
    min-width: 80px;
    min-height: 40px;
    flex-direction: column;
    padding: 4px 8px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    margin-top: 20px;
}

.pagination-info {
    color: #6c757d;
    font-size: 14px;
}

.pagination {
    display: flex;
    gap: 8px;
    align-items: center;
}

.pagination-btn {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    border-color: #28a745;
    color: #28a745;
    background-color: #d4edda;
}

.pagination-btn.active {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.calculate-btn {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calculate-btn:hover {
    background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
    transform: translateY(-1px);
}

.calculated-note {
    margin-top: 10px;
    padding: 10px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    color: #155724;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #218838 0%, #28a745 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* États de chargement */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #28a745;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Message aucune donnée */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-data img {
    width: 64px;
    height: 64px;
    opacity: 0.5;
    margin-bottom: 15px;
}

.no-data p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notes-container {
        padding: 15px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 20px;
    }
    
    .header-info {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .add-button {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
        font-size: 16px;
    }
    
    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-group {
        justify-content: center;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .table th,
    .table td {
        padding: 8px 6px;
        font-size: 13px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
        align-items: center;
    }
    
    .recalculate-button, .edit-button, .delete-button {
        width: 100%;
        min-width: 70px;
        padding: 8px 6px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
