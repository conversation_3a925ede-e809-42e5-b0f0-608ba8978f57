<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🧪 CRÉATION DE DONNÉES DE TEST COHÉRENTES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Objectif</h2>";
    echo "<p>Créer des données de test respectant l'architecture :</p>";
    echo "<ul>";
    echo "<li>✅ Utilisateurs avec rôles appropriés</li>";
    echo "<li>✅ Parents uniquement dans table parents</li>";
    echo "<li>✅ Étudiants uniquement dans table etudiants</li>";
    echo "<li>✅ Enseignants uniquement dans table enseignants</li>";
    echo "<li>✅ Admin uniquement dans table utilisateurs</li>";
    echo "</ul>";
    echo "</div>";
    
    // Vérifier que les rôles existent
    echo "<div class='step'>";
    echo "<h3>1. 🔍 Vérification des rôles</h3>";
    
    $stmt = $pdo->query("SELECT id, nom FROM roles ORDER BY id");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $roleIds = [];
    foreach ($roles as $role) {
        $roleIds[$role['nom']] = $role['id'];
        echo "<p class='success'>✅ Rôle '{$role['nom']}' (ID: {$role['id']})</p>";
    }
    
    if (count($roles) < 4) {
        echo "<p class='error'>❌ Rôles manquants. Exécutez d'abord la restructuration.</p>";
        exit();
    }
    echo "</div>";
    
    // Créer un utilisateur admin
    echo "<div class='step'>";
    echo "<h3>2. 👨‍💼 Création de l'administrateur</h3>";
    
    try {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
            VALUES ('Admin', 'Système', '<EMAIL>', :password, :role_id)
        ");
        $stmt->execute([
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'role_id' => $roleIds['admin']
        ]);
        
        echo "<p class='success'>✅ Administrateur créé</p>";
        echo "<p style='margin-left: 20px;'>📧 Email: <EMAIL></p>";
        echo "<p style='margin-left: 20px;'>🔑 Mot de passe: admin123</p>";
    } catch (PDOException $e) {
        echo "<p class='warning'>⚠️ Admin existe déjà ou erreur: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Créer des utilisateurs parents
    echo "<div class='step'>";
    echo "<h3>3. 👨‍👩‍👧‍👦 Création des parents</h3>";
    
    $parentsData = [
        ['nom' => 'Dupont', 'prenom' => 'Jean', 'email' => '<EMAIL>', 'telephone' => '0123456789', 'adresse' => '123 Rue de la Paix, 75001 Paris', 'profession' => 'Ingénieur'],
        ['nom' => 'Martin', 'prenom' => 'Marie', 'email' => '<EMAIL>', 'telephone' => '0123456790', 'adresse' => '456 Avenue des Fleurs, 69000 Lyon', 'profession' => 'Médecin'],
        ['nom' => 'Bernard', 'prenom' => 'Pierre', 'email' => '<EMAIL>', 'telephone' => '0123456791', 'adresse' => '789 Boulevard Central, 13000 Marseille', 'profession' => 'Avocat'],
        ['nom' => 'Dubois', 'prenom' => 'Sophie', 'email' => '<EMAIL>', 'telephone' => '0123456792', 'adresse' => '321 Place du Marché, 31000 Toulouse', 'profession' => 'Professeur'],
        ['nom' => 'Moreau', 'prenom' => 'Luc', 'email' => '<EMAIL>', 'telephone' => '0123456793', 'adresse' => '654 Rue des Écoles, 44000 Nantes', 'profession' => 'Commerçant']
    ];
    
    $parentsCreated = 0;
    foreach ($parentsData as $parent) {
        try {
            // Créer l'utilisateur
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES (:nom, :prenom, :email, :password, :role_id)
            ");
            $result = $stmt->execute([
                'nom' => $parent['nom'],
                'prenom' => $parent['prenom'],
                'email' => $parent['email'],
                'password' => password_hash('parent123', PASSWORD_DEFAULT),
                'role_id' => $roleIds['parent']
            ]);
            
            if ($result) {
                // Récupérer l'ID de l'utilisateur
                $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = :email");
                $stmt->execute(['email' => $parent['email']]);
                $userId = $stmt->fetchColumn();
                
                if ($userId) {
                    // Créer l'entrée dans la table parents
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO parents (utilisateur_id, telephone, adresse, profession) 
                        VALUES (:utilisateur_id, :telephone, :adresse, :profession)
                    ");
                    $stmt->execute([
                        'utilisateur_id' => $userId,
                        'telephone' => $parent['telephone'],
                        'adresse' => $parent['adresse'],
                        'profession' => $parent['profession']
                    ]);
                    
                    echo "<p class='success'>✅ Parent créé: {$parent['nom']} {$parent['prenom']}</p>";
                    $parentsCreated++;
                }
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur parent {$parent['nom']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p class='info'>📊 Total parents créés: $parentsCreated</p>";
    echo "</div>";
    
    // Créer des classes et groupes
    echo "<div class='step'>";
    echo "<h3>4. 🏫 Création des classes et groupes</h3>";
    
    try {
        // Créer quelques classes
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO classes (nom, filiere_id, niveau_id) 
            VALUES 
            ('Informatique 1A', 1, 1),
            ('Informatique 2A', 1, 2),
            ('Gestion 1A', 2, 1),
            ('Commerce 1A', 3, 1)
        ");
        $stmt->execute();
        
        // Créer des groupes
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO groupes (nom, classe_id) 
            VALUES 
            ('Groupe A', 1),
            ('Groupe B', 1),
            ('Groupe A', 2),
            ('Groupe A', 3),
            ('Groupe A', 4)
        ");
        $stmt->execute();
        
        echo "<p class='success'>✅ Classes et groupes créés</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur classes/groupes: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Créer des utilisateurs étudiants
    echo "<div class='step'>";
    echo "<h3>5. 👨‍🎓 Création des étudiants</h3>";
    
    $etudiantsData = [
        ['nom' => 'Leroy', 'prenom' => 'Antoine', 'email' => '<EMAIL>', 'numero' => 'ETU001', 'groupe_id' => 1],
        ['nom' => 'Petit', 'prenom' => 'Camille', 'email' => '<EMAIL>', 'numero' => 'ETU002', 'groupe_id' => 1],
        ['nom' => 'Roux', 'prenom' => 'Thomas', 'email' => '<EMAIL>', 'numero' => 'ETU003', 'groupe_id' => 2],
        ['nom' => 'Blanc', 'prenom' => 'Emma', 'email' => '<EMAIL>', 'numero' => 'ETU004', 'groupe_id' => 3],
        ['nom' => 'Girard', 'prenom' => 'Lucas', 'email' => '<EMAIL>', 'numero' => 'ETU005', 'groupe_id' => 4]
    ];
    
    $etudiantsCreated = 0;
    foreach ($etudiantsData as $etudiant) {
        try {
            // Créer l'utilisateur
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES (:nom, :prenom, :email, :password, :role_id)
            ");
            $result = $stmt->execute([
                'nom' => $etudiant['nom'],
                'prenom' => $etudiant['prenom'],
                'email' => $etudiant['email'],
                'password' => password_hash('etudiant123', PASSWORD_DEFAULT),
                'role_id' => $roleIds['etudiant']
            ]);
            
            if ($result) {
                // Récupérer l'ID de l'utilisateur
                $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = :email");
                $stmt->execute(['email' => $etudiant['email']]);
                $userId = $stmt->fetchColumn();
                
                if ($userId) {
                    // Créer l'entrée dans la table etudiants
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO etudiants (utilisateur_id, groupe_id, numero_etudiant, date_inscription) 
                        VALUES (:utilisateur_id, :groupe_id, :numero_etudiant, CURDATE())
                    ");
                    $stmt->execute([
                        'utilisateur_id' => $userId,
                        'groupe_id' => $etudiant['groupe_id'],
                        'numero_etudiant' => $etudiant['numero']
                    ]);
                    
                    echo "<p class='success'>✅ Étudiant créé: {$etudiant['nom']} {$etudiant['prenom']} ({$etudiant['numero']})</p>";
                    $etudiantsCreated++;
                }
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur étudiant {$etudiant['nom']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p class='info'>📊 Total étudiants créés: $etudiantsCreated</p>";
    echo "</div>";
    
    // Créer des utilisateurs enseignants
    echo "<div class='step'>";
    echo "<h3>6. 👨‍🏫 Création des enseignants</h3>";
    
    $enseignantsData = [
        ['nom' => 'Professeur', 'prenom' => 'Jean', 'email' => '<EMAIL>', 'telephone' => '0123456800', 'specialite' => 'Informatique', 'salaire' => 3500.00],
        ['nom' => 'Leclerc', 'prenom' => 'Marie', 'email' => '<EMAIL>', 'telephone' => '0123456801', 'specialite' => 'Mathématiques', 'salaire' => 3200.00],
        ['nom' => 'Durand', 'prenom' => 'Paul', 'email' => '<EMAIL>', 'telephone' => '0123456802', 'specialite' => 'Gestion', 'salaire' => 3000.00],
        ['nom' => 'Rousseau', 'prenom' => 'Claire', 'email' => '<EMAIL>', 'telephone' => '0123456803', 'specialite' => 'Français', 'salaire' => 2800.00]
    ];
    
    $enseignantsCreated = 0;
    foreach ($enseignantsData as $enseignant) {
        try {
            // Créer l'utilisateur
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES (:nom, :prenom, :email, :password, :role_id)
            ");
            $result = $stmt->execute([
                'nom' => $enseignant['nom'],
                'prenom' => $enseignant['prenom'],
                'email' => $enseignant['email'],
                'password' => password_hash('enseignant123', PASSWORD_DEFAULT),
                'role_id' => $roleIds['enseignant']
            ]);
            
            if ($result) {
                // Récupérer l'ID de l'utilisateur
                $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = :email");
                $stmt->execute(['email' => $enseignant['email']]);
                $userId = $stmt->fetchColumn();
                
                if ($userId) {
                    // Créer l'entrée dans la table enseignants
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO enseignants (utilisateur_id, telephone, specialite, date_embauche, salaire) 
                        VALUES (:utilisateur_id, :telephone, :specialite, CURDATE(), :salaire)
                    ");
                    $stmt->execute([
                        'utilisateur_id' => $userId,
                        'telephone' => $enseignant['telephone'],
                        'specialite' => $enseignant['specialite'],
                        'salaire' => $enseignant['salaire']
                    ]);
                    
                    echo "<p class='success'>✅ Enseignant créé: {$enseignant['nom']} {$enseignant['prenom']} ({$enseignant['specialite']})</p>";
                    $enseignantsCreated++;
                }
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur enseignant {$enseignant['nom']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p class='info'>📊 Total enseignants créés: $enseignantsCreated</p>";
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>📊 RÉSUMÉ FINAL</h3>";
    
    // Compter les utilisateurs par rôle
    $stmt = $pdo->query("
        SELECT r.nom as role_nom, COUNT(u.id) as count
        FROM roles r
        LEFT JOIN utilisateurs u ON r.id = u.role_id
        GROUP BY r.id, r.nom
        ORDER BY r.id
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Rôle</th><th>Utilisateurs</th><th>Table spécialisée</th></tr>";
    foreach ($stats as $stat) {
        $specializedTable = '';
        $specializedCount = 0;
        
        switch ($stat['role_nom']) {
            case 'parent':
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM parents");
                $specializedCount = $countStmt->fetch()['count'];
                $specializedTable = "parents ($specializedCount)";
                break;
            case 'etudiant':
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM etudiants");
                $specializedCount = $countStmt->fetch()['count'];
                $specializedTable = "etudiants ($specializedCount)";
                break;
            case 'enseignant':
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM enseignants");
                $specializedCount = $countStmt->fetch()['count'];
                $specializedTable = "enseignants ($specializedCount)";
                break;
            case 'admin':
                $specializedTable = "utilisateurs uniquement";
                break;
        }
        
        echo "<tr>";
        echo "<td><strong>{$stat['role_nom']}</strong></td>";
        echo "<td>{$stat['count']}</td>";
        echo "<td>$specializedTable</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>🎉 DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !</h4>";
    echo "<p class='success'>✅ Architecture cohérente respectée</p>";
    echo "<p class='success'>✅ Triggers de sécurité fonctionnels</p>";
    echo "<p class='success'>✅ Données prêtes pour les tests</p>";
    
    echo "<h4>🔑 Comptes de test :</h4>";
    echo "<ul>";
    echo "<li><strong>Admin :</strong> <EMAIL> / admin123</li>";
    echo "<li><strong>Parent :</strong> <EMAIL> / parent123</li>";
    echo "<li><strong>Étudiant :</strong> <EMAIL> / etudiant123</li>";
    echo "<li><strong>Enseignant :</strong> <EMAIL> / enseignant123</li>";
    echo "</ul>";
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><a href='../pages/parents/debug_filtrage.php'>Tester le filtrage des parents</a></li>";
    echo "<li><a href='http://localhost:3000/parents'>Interface Parents</a></li>";
    echo "<li><a href='http://localhost:3000/login'>Se connecter avec un compte de test</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
