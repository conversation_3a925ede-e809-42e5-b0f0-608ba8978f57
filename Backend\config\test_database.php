<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔧 TEST CONNEXION BASE DE DONNÉES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🔍 DIAGNOSTIC CONNEXION DATABASE.PHP</h2>";
    echo "<p>Test de la configuration et connexion à la base de données</p>";
    echo "</div>";
    
    // Vérifier si le fichier database.php existe
    $database_file = __DIR__ . '/database.php';
    echo "<div class='info'>";
    echo "<h3>📁 Vérification du fichier database.php</h3>";
    echo "<p><strong>Chemin :</strong> $database_file</p>";
    
    if (file_exists($database_file)) {
        echo "<p style='color: green;'>✅ Fichier database.php trouvé</p>";
        echo "<p><strong>Taille :</strong> " . filesize($database_file) . " bytes</p>";
        echo "<p><strong>Dernière modification :</strong> " . date('Y-m-d H:i:s', filemtime($database_file)) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Fichier database.php non trouvé</p>";
        echo "<p>Le fichier doit être créé dans : $database_file</p>";
        echo "</div>";
        exit();
    }
    echo "</div>";
    
    // Inclure le fichier database.php
    echo "<div class='info'>";
    echo "<h3>📥 Inclusion du fichier database.php</h3>";
    
    try {
        require_once $database_file;
        echo "<p style='color: green;'>✅ Fichier database.php inclus avec succès</p>";
        
        // Vérifier si la classe Database existe
        if (class_exists('Database')) {
            echo "<p style='color: green;'>✅ Classe Database trouvée</p>";
        } else {
            echo "<p style='color: red;'>❌ Classe Database non trouvée</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erreur lors de l'inclusion : " . $e->getMessage() . "</p>";
        echo "</div>";
        exit();
    }
    echo "</div>";
    
    // Test de connexion
    echo "<div class='info'>";
    echo "<h3>🔌 Test de connexion à la base de données</h3>";
    
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        if ($conn !== null) {
            echo "<p style='color: green;'>✅ Connexion à la base de données réussie</p>";
            
            // Obtenir les informations de la base
            $info = $database->getDatabaseInfo();
            
            if (!isset($info['error'])) {
                echo "<table>";
                echo "<tr><th>Information</th><th>Valeur</th></tr>";
                echo "<tr><td>Version MySQL</td><td>{$info['mysql_version']}</td></tr>";
                echo "<tr><td>Base de données</td><td>{$info['database_name']}</td></tr>";
                echo "<tr><td>Charset</td><td>{$info['charset']}</td></tr>";
                echo "<tr><td>Nombre de tables</td><td>{$info['table_count']}</td></tr>";
                echo "</table>";
                
                if (!empty($info['tables'])) {
                    echo "<h4>📋 Tables disponibles :</h4>";
                    echo "<ul>";
                    foreach ($info['tables'] as $table) {
                        echo "<li>$table</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ Connexion réussie mais erreur lors de la récupération des infos : " . $info['error'] . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Échec de la connexion à la base de données</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erreur lors du test de connexion : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Test des tables nécessaires pour les notes
    echo "<div class='info'>";
    echo "<h3>📊 Vérification des tables pour les notes</h3>";
    
    $required_tables = [
        'notes' => 'Table principale des notes',
        'etudiants' => 'Table des étudiants',
        'utilisateurs' => 'Table des utilisateurs',
        'devoirs' => 'Table des devoirs',
        'matieres' => 'Table des matières',
        'classes' => 'Table des classes',
        'quiz' => 'Table des quiz (optionnel)',
        'reponsesquiz' => 'Table des réponses quiz (optionnel)'
    ];
    
    if (isset($info['tables'])) {
        echo "<table>";
        echo "<tr><th>Table</th><th>Description</th><th>Statut</th></tr>";
        
        foreach ($required_tables as $table => $description) {
            $exists = in_array($table, $info['tables']);
            $status = $exists ? "<span style='color: green;'>✅ Existe</span>" : "<span style='color: red;'>❌ Manquante</span>";
            echo "<tr><td><strong>$table</strong></td><td>$description</td><td>$status</td></tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Configuration recommandée
    echo "<div class='warning'>";
    echo "<h3>⚙️ Configuration Recommandée</h3>";
    echo "<p>Si vous avez des problèmes de connexion, vérifiez ces paramètres dans database.php :</p>";
    echo "</div>";
    
    echo "<div class='code'>";
    echo "// Configuration pour Laragon (par défaut)
private \$host = \"localhost\";
private \$db_name = \"school_management\"; // ← Changez selon votre DB
private \$username = \"root\";
private \$password = \"\";                 // ← Vide pour Laragon

// Configuration pour XAMPP
private \$host = \"localhost\";
private \$db_name = \"school_management\";
private \$username = \"root\";
private \$password = \"\";                 // ← Vide pour XAMPP

// Configuration pour WAMP
private \$host = \"localhost\";
private \$db_name = \"school_management\";
private \$username = \"root\";
private \$password = \"\";                 // ← Ou votre mot de passe";
    echo "</div>";
    
    // Test des APIs Notes
    echo "<div class='info'>";
    echo "<h3>🧪 Test des APIs Notes</h3>";
    echo "<p>Maintenant que la base de données fonctionne, testez les APIs :</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='../pages/notes/index_fixed.php' target='_blank' class='btn btn-success'>📊 API Notes Corrigée</a>";
    echo "<a href='../pages/notes/index_dynamic.php' target='_blank' class='btn btn-success'>🔄 API Notes Dynamique</a>";
    echo "<a href='../pages/notes/test_correction.php' target='_blank' class='btn btn-success'>🔧 Test Correction</a>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 DIAGNOSTIC TERMINÉ !</h2>";
    echo "<p><strong>✅ Fichier database.php créé et configuré</strong></p>";
    echo "<p><strong>✅ Chemins corrigés dans les APIs</strong></p>";
    echo "<p><strong>✅ Connexion à la base de données testée</strong></p>";
    echo "<p><strong>🚀 Les APIs Notes devraient maintenant fonctionner !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
