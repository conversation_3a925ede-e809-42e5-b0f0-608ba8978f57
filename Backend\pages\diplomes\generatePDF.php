<?php
require_once '../../vendor/autoload.php'; // Si vous utilisez Composer
// Sinon, téléchargez TCPDF et incluez-le
require_once '../../libs/tcpdf/tcpdf.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['diplome_id'])) {
    try {
        $diplome_id = $_GET['diplome_id'];
        
        // Récupérer les informations complètes du diplôme
        $stmt = $pdo->prepare("
            SELECT
                d.*,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                u.id as numero_etudiant,
                g.nom as groupe_nom,
                c.nom as classe_nom,
                f.nom as filiere_nom,
                n.nom as niveau_nom,
                DATE_FORMAT(d.date_obtention, '%d/%m/%Y') as date_obtention_fr
            FROM Diplomes d
            JOIN Etudiants e ON d.etudiant_id = e.id
            JOIN Utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN Groupes g ON e.groupe_id = g.id
            LEFT JOIN Classes c ON g.classe_id = c.id
            LEFT JOIN Filieres f ON c.filiere_id = f.id
            LEFT JOIN Niveaux n ON c.niveau_id = n.id
            WHERE d.id = ?
        ");
        $stmt->execute([$diplome_id]);
        $diplome = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$diplome) {
            echo json_encode(['error' => 'Diplôme non trouvé']);
            exit;
        }
        
        // Créer le PDF
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Informations du document
        $pdf->SetCreator('École de Gestion');
        $pdf->SetAuthor('Système de Gestion Scolaire');
        $pdf->SetTitle('Diplôme - ' . $diplome['etudiant_nom']);
        $pdf->SetSubject('Diplôme Officiel');
        
        // Supprimer header/footer par défaut
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        
        // Marges
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetAutoPageBreak(TRUE, 20);
        
        // Ajouter une page
        $pdf->AddPage();
        
        // Police
        $pdf->SetFont('helvetica', '', 12);
        
        // Contenu du diplôme
        $html = generateDiplomeHTML($diplome);
        
        // Écrire le HTML
        $pdf->writeHTML($html, true, false, true, false, '');
        
        // Nom du fichier
        $filename = 'Diplome_' . str_replace(' ', '_', $diplome['etudiant_nom']) . '_' . date('Y-m-d') . '.pdf';
        
        // Sortie du PDF
        $pdf->Output($filename, 'D'); // 'D' pour téléchargement direct
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Erreur lors de la génération du PDF: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['error' => 'ID du diplôme manquant']);
}

function generateDiplomeHTML($diplome) {
    $annee_scolaire = date('Y', strtotime($diplome['date_obtention'])) . '-' . (date('Y', strtotime($diplome['date_obtention'])) + 1);
    
    return '
    <style>
        .header { text-align: center; margin-bottom: 30px; }
        .logo { width: 100px; height: auto; }
        .title { font-size: 24px; font-weight: bold; color: #2c3e50; margin: 20px 0; }
        .subtitle { font-size: 18px; color: #34495e; margin: 10px 0; }
        .content { margin: 30px 0; line-height: 1.8; }
        .student-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .info-row { margin: 10px 0; }
        .label { font-weight: bold; color: #2c3e50; }
        .value { color: #34495e; }
        .signature-section { margin-top: 50px; }
        .signature-box { width: 200px; text-align: center; float: right; }
        .date-section { margin-top: 40px; text-align: center; }
        .border-decoration { border: 3px solid #3498db; padding: 20px; border-radius: 10px; }
    </style>
    
    <div class="border-decoration">
        <div class="header">
            <!-- Logo de l\'école (remplacez par votre logo) -->
            <img src="../../assets/logo-ecole.png" alt="Logo École" class="logo" style="width: 80px; height: 80px;">
            <h1 class="title">ÉCOLE DE GESTION SCOLAIRE</h1>
            <h2 class="subtitle">DIPLÔME OFFICIEL</h2>
        </div>
        
        <div class="content">
            <p style="text-align: center; font-size: 16px; margin: 30px 0;">
                <strong>Il est certifié que</strong>
            </p>
            
            <div class="student-info">
                <div class="info-row">
                    <span class="label">Nom de l\'étudiant :</span>
                    <span class="value" style="font-size: 18px; font-weight: bold; color: #2980b9;">
                        ' . strtoupper($diplome['etudiant_nom']) . '
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="label">Numéro d\'étudiant :</span>
                    <span class="value">' . ($diplome['numero_etudiant'] ?? 'N/A') . '</span>
                </div>
                
                <div class="info-row">
                    <span class="label">Filière :</span>
                    <span class="value">' . ($diplome['filiere_nom'] ?? 'N/A') . '</span>
                </div>
                
                <div class="info-row">
                    <span class="label">Niveau :</span>
                    <span class="value">' . ($diplome['niveau_nom'] ?? 'N/A') . '</span>
                </div>
                
                <div class="info-row">
                    <span class="label">Classe :</span>
                    <span class="value">' . ($diplome['classe_nom'] ?? 'N/A') . '</span>
                </div>
                
                <div class="info-row">
                    <span class="label">Année scolaire :</span>
                    <span class="value">' . $annee_scolaire . '</span>
                </div>
            </div>
            
            <p style="text-align: center; font-size: 16px; margin: 30px 0;">
                <strong>a obtenu avec succès le diplôme de :</strong>
            </p>
            
            <h3 style="text-align: center; font-size: 20px; color: #e74c3c; margin: 20px 0; padding: 15px; background-color: #fdf2f2; border-radius: 8px;">
                ' . strtoupper($diplome['titre']) . '
            </h3>
            
            <div class="date-section">
                <p><strong>Délivré le :</strong> ' . $diplome['date_obtention_fr'] . '</p>
            </div>
            
            <div class="signature-section">
                <div style="float: left; width: 200px; text-align: center;">
                    <p><strong>Le Directeur</strong></p>
                    <br><br>
                    <p>_____________________</p>
                    <p style="font-size: 10px;">Signature et cachet</p>
                </div>
                
                <div style="float: right; width: 200px; text-align: center;">
                    <p><strong>Le Responsable Académique</strong></p>
                    <br><br>
                    <p>_____________________</p>
                    <p style="font-size: 10px;">Signature et cachet</p>
                </div>
                
                <div style="clear: both;"></div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #7f8c8d;">
        <p>Ce diplôme est délivré par l\'École de Gestion Scolaire</p>
        <p>Document officiel - Toute reproduction est interdite</p>
        <p>Généré le ' . date('d/m/Y à H:i') . '</p>
    </div>';
}
?>
