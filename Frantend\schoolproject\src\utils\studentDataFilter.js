/**
 * 🔒 SYSTÈME DE FILTRAGE DE SÉCURITÉ POUR LES DONNÉES ÉTUDIANTS
 * 
 * Ce module garantit que chaque étudiant ne peut voir que ses propres informations
 * et les données liées à sa filière/classe uniquement.
 * 
 * RÈGLES DE SÉCURITÉ :
 * - Étudiants : Accès en lecture seule à leurs propres données uniquement
 * - Matières : Filtrées par filière de l'étudiant
 * - Cours : Filtrés par classe/groupe de l'étudiant
 * - Notes/Absences/Retards/Diplômes : Données personnelles uniquement
 * - Admin/Enseignants : Accès complet à toutes les données
 */

// ============================================================================
// FONCTIONS DE VÉRIFICATION DES RÔLES
// ============================================================================

export const isStudent = (user) => {
    if (!user || !user.role) return false;
    const role = user.role.toLowerCase();
    return role === 'etudiant' || role === 'student';
};

export const isAdmin = (user) => {
    if (!user || !user.role) return false;
    const role = user.role.toLowerCase();
    return role === 'admin' || role === 'administrateur';
};

export const isTeacher = (user) => {
    if (!user || !user.role) return false;
    const role = user.role.toLowerCase();
    return role === 'enseignant' || role === 'teacher' || role === 'professeur';
};

export const isParent = (user) => {
    if (!user || !user.role) return false;
    const role = user.role.toLowerCase();
    return role === 'parent';
};

export const canManageData = (user) => {
    return isAdmin(user) || isTeacher(user);
};

// ============================================================================
// FONCTION DE FILTRAGE GÉNÉRIQUE
// ============================================================================

export const filterStudentData = (data, user, userField = 'etudiant_email') => {
    if (!user || !Array.isArray(data)) {
        return [];
    }

    // Si l'utilisateur n'est pas un étudiant, retourner toutes les données
    if (!isStudent(user)) {
        return data;
    }

    // Filtrer les données pour ne garder que celles de l'étudiant connecté
    return data.filter(item => {
        const matchesEmail = item.etudiant_email === user.email;
        const matchesUserId = item.utilisateur_id === user.id;
        const matchesEtudiantUserId = item.etudiant_utilisateur_id === user.id;
        const matchesEtudiantId = item.etudiant_id === user.etudiant_id;
        
        return matchesEmail || matchesUserId || matchesEtudiantUserId || matchesEtudiantId;
    });
};

// ============================================================================
// FONCTIONS SPÉCIALISÉES PAR TYPE DE DONNÉES
// ============================================================================

export const filterAbsences = (absences, user) => {
    return filterStudentData(absences, user, 'etudiant_email');
};

export const filterRetards = (retards, user) => {
    return filterStudentData(retards, user, 'etudiant_email');
};

export const filterNotes = (notes, user) => {
    return filterStudentData(notes, user, 'etudiant_email');
};

export const filterDiplomes = (diplomes, user) => {
    return filterStudentData(diplomes, user, 'etudiant_email');
};

export const filterCours = (cours, user) => {
    // Pour les cours, on filtre par classe_id de l'étudiant
    if (!user || !Array.isArray(cours)) {
        return [];
    }

    // Si l'utilisateur n'est pas un étudiant, retourner tous les cours
    if (!isStudent(user)) {
        return cours;
    }

    // Filtrer les cours par classe de l'étudiant
    return cours.filter(coursItem => {
        const matchesClasse = coursItem.classe_id === user.classe_id;
        const matchesGroupe = coursItem.groupe_id === user.groupe_id;
        const matchesEtudiant = coursItem.etudiant_email === user.email;
        
        return matchesClasse || matchesGroupe || matchesEtudiant;
    });
};

export const filterMatieres = (matieres, user) => {
    // Pour les matières, on filtre par filière de l'étudiant
    if (!user || !Array.isArray(matieres)) {
        return [];
    }

    // Si l'utilisateur n'est pas un étudiant, retourner toutes les matières
    if (!isStudent(user)) {
        return matieres;
    }

    // Filtrer les matières par filière de l'étudiant
    return matieres.filter(matiere => {
        const matchesFiliere = matiere.filiere_id === user.filiere_id;
        const matchesFiliereNom = matiere.filiere_nom === user.filiere_nom;
        
        return matchesFiliere || matchesFiliereNom;
    });
};

export const filterDevoirs = (devoirs, user) => {
    // Pour les devoirs, on filtre par classe/matière de l'étudiant
    if (!user || !Array.isArray(devoirs)) {
        return [];
    }

    // Si l'utilisateur n'est pas un étudiant, retourner tous les devoirs
    if (!isStudent(user)) {
        return devoirs;
    }

    // Filtrer les devoirs par classe/matière de l'étudiant
    return devoirs.filter(devoir => {
        const matchesClasse = devoir.classe_id === user.classe_id;
        const matchesGroupe = devoir.groupe_id === user.groupe_id;
        const matchesFiliere = devoir.filiere_id === user.filiere_id;
        
        return matchesClasse || matchesGroupe || matchesFiliere;
    });
};

export const filterEmploisDuTemps = (emplois, user) => {
    // Pour les emplois du temps, on filtre par classe/groupe de l'étudiant
    if (!user || !Array.isArray(emplois)) {
        return [];
    }

    // Si l'utilisateur n'est pas un étudiant, retourner tous les emplois
    if (!isStudent(user)) {
        return emplois;
    }

    // Filtrer les emplois du temps par classe/groupe de l'étudiant
    return emplois.filter(emploi => {
        const matchesClasse = emploi.classe_id === user.classe_id;
        const matchesGroupe = emploi.groupe_id === user.groupe_id;
        
        return matchesClasse || matchesGroupe;
    });
};

// ============================================================================
// FONCTIONS DE LOGGING ET AUDIT DE SÉCURITÉ
// ============================================================================

export const logSecurityEvent = (action, user, data) => {
    const logEntry = {
        timestamp: new Date().toISOString(),
        action,
        user: { 
            id: user?.id, 
            email: user?.email, 
            role: user?.role 
        },
        data: { 
            id: data?.id, 
            type: data?.type,
            total: data?.total,
            filtered: data?.filtered
        }
    };
    
    console.log('🔐 AUDIT SÉCURITÉ:', logEntry);
    
    // En production, envoyer vers un système de logging centralisé
    if (process.env.NODE_ENV === 'production') {
        // sendToSecurityLog(logEntry);
    }
};

export const showSecurityAlert = (message = 'Accès refusé : Vous ne pouvez consulter que vos propres informations.') => {
    console.warn('🚨 TENTATIVE D\'ACCÈS NON AUTORISÉ DÉTECTÉE');
    alert(message);
};

// ============================================================================
// FONCTIONS D'AIDE POUR LA RÉCUPÉRATION DES INFORMATIONS ÉTUDIANT
// ============================================================================

export const getStudentInfo = async (userId, token) => {
    try {
        const response = await fetch(`/api/student-info/${userId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            return data.student;
        }
        
        return null;
    } catch (error) {
        console.error('Erreur lors de la récupération des informations étudiant:', error);
        return null;
    }
};

// ============================================================================
// FONCTIONS DE VALIDATION DES PERMISSIONS
// ============================================================================

export const canAccessData = (user, dataType, dataItem) => {
    if (!user) return false;
    
    // Admin et enseignants ont accès à tout
    if (canManageData(user)) return true;
    
    // Parents peuvent voir les données de leurs enfants
    if (isParent(user)) {
        return dataItem.parent_id === user.parent_id || 
               dataItem.etudiant_parent_id === user.id;
    }
    
    // Étudiants ne peuvent voir que leurs propres données
    if (isStudent(user)) {
        return dataItem.etudiant_email === user.email ||
               dataItem.utilisateur_id === user.id ||
               dataItem.etudiant_id === user.etudiant_id;
    }
    
    return false;
};

export const validateDataAccess = (user, data, dataType) => {
    if (!isStudent(user)) return data;
    
    const filteredData = data.filter(item => canAccessData(user, dataType, item));
    
    if (filteredData.length !== data.length) {
        logSecurityEvent(`${dataType.toUpperCase()}_ACCESS_FILTERED`, user, {
            type: dataType,
            total: data.length,
            filtered: filteredData.length
        });
    }
    
    return filteredData;
};

// Export par défaut pour faciliter l'importation
export default {
    isStudent,
    isAdmin,
    isTeacher,
    isParent,
    canManageData,
    filterStudentData,
    filterAbsences,
    filterRetards,
    filterNotes,
    filterDiplomes,
    filterCours,
    filterMatieres,
    filterDevoirs,
    filterEmploisDuTemps,
    logSecurityEvent,
    showSecurityAlert,
    getStudentInfo,
    canAccessData,
    validateDataAccess
};
