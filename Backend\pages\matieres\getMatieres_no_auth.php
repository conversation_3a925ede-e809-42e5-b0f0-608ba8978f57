<?php
/**
 * API pour récupérer les matières - SANS AUTHENTIFICATION
 * MÊME LOGIQUE QUE LES AUTRES APIs QUI FONCTIONNENT
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Connexion à la base de données - MÊME LOGIQUE QUE ABSENCES
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Récupérer les matières avec informations complètes
        $stmt = $pdo->prepare("
            SELECT m.id, m.nom, m.code, m.description, m.coefficient,
                   COUNT(DISTINCT edt.id) as nombre_cours_planifies,
                   COUNT(DISTINCT d.id) as nombre_devoirs
            FROM matieres m
            LEFT JOIN emploisdutemps edt ON m.id = edt.matiere_id
            LEFT JOIN devoirs d ON m.id = d.matiere_id
            GROUP BY m.id, m.nom, m.code, m.description, m.coefficient
            ORDER BY m.nom
        ");
        
        $stmt->execute();
        $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formater les données pour React - MÊME FORMAT QUE ABSENCES
        $result = [];
        foreach ($matieres as $matiere) {
            $result[] = [
                'id' => (int)$matiere['id'],
                'nom' => $matiere['nom'],
                'code' => $matiere['code'],
                'description' => $matiere['description'],
                'coefficient' => $matiere['coefficient'] ? (float)$matiere['coefficient'] : null,
                'nombre_cours_planifies' => (int)$matiere['nombre_cours_planifies'],
                'nombre_devoirs' => (int)$matiere['nombre_devoirs']
            ];
        }
        
        // Retour identique aux absences : success + data
        echo json_encode([
            'success' => true,
            'matieres' => $result,
            'total' => count($result),
            'message' => 'Matières récupérées avec succès'
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur de base de données: ' . $e->getMessage()
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur serveur: ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Méthode non autorisée'
    ]);
}
?>
