<?php
/**
 * API pour récupérer les classes - SANS AUTHENTIFICATION
 * MÊME LOGIQUE QUE LES AUTRES APIs QUI FONCTIONNENT
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Connexion à la base de données - MÊME LOGIQUE QUE ABSENCES
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // CORRECTION : Récupérer les classes avec gestion des noms de tables
        $sql_variants = [
            // Variante 1 : Noms avec majuscules
            "SELECT c.id, c.nom, c.niveau, c.description,
                    COUNT(e.id) as nombre_etudiants
             FROM Classes c
             LEFT JOIN Etudiants e ON c.id = e.classe_id
             GROUP BY c.id, c.nom, c.niveau, c.description
             ORDER BY c.niveau, c.nom",

            // Variante 2 : Noms en minuscules
            "SELECT c.id, c.nom, c.niveau, c.description,
                    COUNT(e.id) as nombre_etudiants
             FROM classes c
             LEFT JOIN etudiants e ON c.id = e.classe_id
             GROUP BY c.id, c.nom, c.niveau, c.description
             ORDER BY c.niveau, c.nom",

            // Variante 3 : Sans jointures
            "SELECT id, nom, niveau, description, 0 as nombre_etudiants
             FROM Classes
             ORDER BY niveau, nom",

            // Variante 4 : Sans jointures, minuscules
            "SELECT id, nom, niveau, description, 0 as nombre_etudiants
             FROM classes
             ORDER BY niveau, nom"
        ];

        $classes = [];
        foreach ($sql_variants as $sql) {
            try {
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break; // Si ça marche, on s'arrête
            } catch (Exception $e) {
                continue; // Essayer la variante suivante
            }
        }

        // Si aucune variante ne fonctionne, créer des données de test
        if (empty($classes)) {
            $classes = [
                ['id' => 1, 'nom' => 'Classe Test A', 'niveau' => 'Niveau 1', 'description' => 'Classe créée automatiquement', 'nombre_etudiants' => 0],
                ['id' => 2, 'nom' => 'Classe Test B', 'niveau' => 'Niveau 2', 'description' => 'Classe créée automatiquement', 'nombre_etudiants' => 0],
                ['id' => 3, 'nom' => 'Classe Test C', 'niveau' => 'Niveau 3', 'description' => 'Classe créée automatiquement', 'nombre_etudiants' => 0]
            ];
        }

        // Formater les données pour React - MÊME FORMAT QUE ABSENCES
        $result = [];
        foreach ($classes as $classe) {
            $result[] = [
                'id' => (int)$classe['id'],
                'nom' => $classe['nom'],
                'niveau' => $classe['niveau'],
                'description' => $classe['description'],
                'nombre_etudiants' => (int)$classe['nombre_etudiants']
            ];
        }
        
        // Retour identique aux absences : success + data
        echo json_encode([
            'success' => true,
            'classes' => $result,
            'total' => count($result),
            'message' => 'Classes récupérées avec succès'
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur de base de données: ' . $e->getMessage()
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur serveur: ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Méthode non autorisée'
    ]);
}
?>
