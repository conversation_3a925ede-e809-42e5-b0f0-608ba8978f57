<?php

// Autoriser les requêtes CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Récupérer l'ID utilisateur depuis les paramètres GET
    $userId = isset($_GET['id']) ? intval($_GET['id']) : null;
    $email = isset($_GET['email']) ? $_GET['email'] : null;

    if (!$userId && !$email) {
        echo json_encode(['error' => 'ID utilisateur ou email requis']);
        exit();
    }

    try {
        // Requête pour récupérer les détails complets de l'utilisateur
        $sql = "
            SELECT
                u.id,
                u.nom,
                u.email,
                u.role_id,
                r.nom as role_nom,
                -- Informations spécifiques selon le rôle
                CASE
                    WHEN r.nom = 'enseignant' THEN e.id
                    WHEN r.nom = 'etudiant' THEN et.id
                    WHEN r.nom = 'parent' THEN p.id
                    ELSE NULL
                END as profile_id,
                -- Informations additionnelles pour les parents
                p.telephone as parent_telephone,
                p.adresse as parent_adresse,
                -- Informations additionnelles pour les étudiants
                g.nom as groupe_nom,
                c.nom as classe_nom,
                f.nom as filiere_nom,
                n.nom as niveau_nom,
                -- Compter les relations
                (SELECT COUNT(*) FROM Enseignants WHERE utilisateur_id = u.id) as is_enseignant,
                (SELECT COUNT(*) FROM Etudiants WHERE utilisateur_id = u.id) as is_etudiant,
                (SELECT COUNT(*) FROM Parents WHERE utilisateur_id = u.id) as is_parent
            FROM Utilisateurs u
            LEFT JOIN Roles r ON u.role_id = r.id
            LEFT JOIN Enseignants e ON u.id = e.utilisateur_id
            LEFT JOIN Etudiants et ON u.id = et.utilisateur_id
            LEFT JOIN Parents p ON u.id = p.utilisateur_id
            LEFT JOIN Groupes g ON et.groupe_id = g.id
            LEFT JOIN Classes c ON g.classe_id = c.id
            LEFT JOIN Filieres f ON c.filiere_id = f.id
            LEFT JOIN Niveaux n ON c.niveau_id = n.id
            WHERE " . ($userId ? "u.id = :identifier" : "u.email = :identifier") . "
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute(['identifier' => $userId ?: $email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            echo json_encode(['error' => 'Utilisateur non trouvé']);
            exit();
        }

        // Ajouter une date de création par défaut (puisque la colonne n'existe pas dans le schéma)
        $user['created_at_formatted'] = 'Non disponible';

        // Ajouter des statistiques supplémentaires selon le rôle
        if ($user['role_nom'] === 'Enseignant' && $user['is_enseignant'] > 0) {
            // Statistiques pour les enseignants
            $statsStmt = $pdo->prepare("
                SELECT
                    (SELECT COUNT(*) FROM Cours c
                     JOIN Enseignements ens ON c.matiere_id = ens.matiere_id AND c.classe_id = ens.classe_id
                     WHERE ens.enseignant_id = e.id) as total_cours,
                    (SELECT COUNT(*) FROM Devoirs d
                     JOIN Enseignements ens ON d.matiere_id = ens.matiere_id AND d.classe_id = ens.classe_id
                     WHERE ens.enseignant_id = e.id) as total_devoirs,
                    (SELECT COUNT(*) FROM Enseignements WHERE enseignant_id = e.id) as total_enseignements,
                    (SELECT COUNT(DISTINCT matiere_id) FROM Enseignements WHERE enseignant_id = e.id) as total_matieres
                FROM Enseignants e WHERE e.utilisateur_id = :user_id
            ");
            $statsStmt->execute(['user_id' => $user['id']]);
            $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
            $user['stats'] = $stats;
        }

        if ($user['role_nom'] === 'Etudiant' && $user['is_etudiant'] > 0) {
            // Statistiques pour les étudiants
            $statsStmt = $pdo->prepare("
                SELECT
                    g.nom as groupe_nom,
                    c.nom as classe_nom,
                    f.nom as filiere_nom,
                    n.nom as niveau_nom,
                    (SELECT COUNT(*) FROM Devoirs d WHERE d.classe_id = c.id) as total_devoirs_disponibles,
                    (SELECT COUNT(*) FROM Notes no WHERE no.etudiant_id = et.id) as total_notes,
                    (SELECT AVG(no.note) FROM Notes no WHERE no.etudiant_id = et.id) as moyenne_generale,
                    (SELECT COUNT(*) FROM Absences a WHERE a.etudiant_id = et.id) as total_absences
                FROM Etudiants et
                LEFT JOIN Groupes g ON et.groupe_id = g.id
                LEFT JOIN Classes c ON g.classe_id = c.id
                LEFT JOIN Filieres f ON c.filiere_id = f.id
                LEFT JOIN Niveaux n ON c.niveau_id = n.id
                WHERE et.utilisateur_id = :user_id
            ");
            $statsStmt->execute(['user_id' => $user['id']]);
            $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
            $user['stats'] = $stats;
        }

        if ($user['role_nom'] === 'Parent' && $user['is_parent'] > 0) {
            // Récupérer les enfants du parent selon la nouvelle structure
            $enfantsStmt = $pdo->prepare("
                SELECT
                    u.nom as enfant_nom,
                    g.nom as groupe_nom,
                    c.nom as classe_nom,
                    f.nom as filiere_nom,
                    n.nom as niveau_nom,
                    pe.lien_parente
                FROM Parents p
                JOIN Parent_Etudiant pe ON p.id = pe.parent_id
                JOIN Etudiants e ON pe.etudiant_id = e.id
                JOIN Utilisateurs u ON e.utilisateur_id = u.id
                LEFT JOIN Groupes g ON e.groupe_id = g.id
                LEFT JOIN Classes c ON g.classe_id = c.id
                LEFT JOIN Filieres f ON c.filiere_id = f.id
                LEFT JOIN Niveaux n ON c.niveau_id = n.id
                WHERE p.utilisateur_id = :user_id
            ");
            $enfantsStmt->execute(['user_id' => $user['id']]);
            $enfants = $enfantsStmt->fetchAll(PDO::FETCH_ASSOC);
            $user['enfants'] = $enfants;
        }

        echo json_encode([
            'success' => true,
            'user' => $user
        ]);

    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}

?>
