<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Groupe API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Groupe API - Headers: " . json_encode(getallheaders()));
error_log("Groupe API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("POST Data: " . json_encode($data));

    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("POST Error: Nom du groupe requis");
        echo json_encode(['success' => false, 'error' => 'Nom du groupe requis']);
        exit;
    }
    if (!isset($data['classe_id']) || !is_numeric($data['classe_id'])) {
        error_log("POST Error: ID de la classe requis");
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis et doit être un nombre']);
        exit;
    }

    $nom = trim($data['nom']);
    $classe_id = intval($data['classe_id']);

    // Vérifier si la classe existe
    try {
        $checkClasseStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :classe_id");
        $checkClasseStmt->execute(['classe_id' => $classe_id]);
        if (!$checkClasseStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe error: " . $e->getMessage());
    }

    // Vérifier si le groupe existe déjà avec le même nom dans la même classe
    try {
        $checkGroupeStmt = $pdo->prepare("SELECT id FROM Groupes WHERE nom = :nom AND classe_id = :classe_id");
        $checkGroupeStmt->execute(['nom' => $nom, 'classe_id' => $classe_id]);
        if ($checkGroupeStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Ce groupe existe déjà dans cette classe']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check groupe error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Groupes (nom, classe_id) VALUES (:nom, :classe_id)");
        $stmt->execute(['nom' => $nom, 'classe_id' => $classe_id]);
        $newId = $pdo->lastInsertId();
        error_log("Groupe created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Groupe ajouté avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        // On récupère la liste des groupes avec le nom de leur classe (jointure)
        $stmt = $pdo->query("
            SELECT Groupes.id, Groupes.nom, Groupes.classe_id, Classes.nom AS classe_nom
            FROM Groupes
            LEFT JOIN Classes ON Groupes.classe_id = Classes.id
            ORDER BY Groupes.id DESC
        ");
        $groupes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("GET Success: " . count($groupes) . " groupes found");
        echo json_encode($groupes);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("PUT Data: " . json_encode($data));

    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom'])) || !isset($data['classe_id']) || !is_numeric($data['classe_id'])) {
        error_log("PUT Error: ID, nom du groupe et ID de la classe requis");
        echo json_encode(['success' => false, 'error' => 'ID, nom du groupe et ID de la classe requis']);
        exit;
    }

    $id = intval($data['id']);
    $nom = trim($data['nom']);
    $classe_id = intval($data['classe_id']);

    // Vérifier si le groupe existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Groupes WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Groupe non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check groupe exists error: " . $e->getMessage());
    }

    // Vérifier si la classe existe
    try {
        $checkClasseStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :classe_id");
        $checkClasseStmt->execute(['classe_id' => $classe_id]);
        if (!$checkClasseStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe error: " . $e->getMessage());
    }

    // Vérifier si le nom n'est pas déjà utilisé par un autre groupe dans la même classe
    try {
        $checkNameStmt = $pdo->prepare("SELECT id FROM Groupes WHERE nom = :nom AND classe_id = :classe_id AND id != :id");
        $checkNameStmt->execute(['nom' => $nom, 'classe_id' => $classe_id, 'id' => $id]);
        if ($checkNameStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Ce nom de groupe est déjà utilisé dans cette classe']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check groupe name error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("UPDATE Groupes SET nom = :nom, classe_id = :classe_id WHERE id = :id");
        $result = $stmt->execute(['nom' => $nom, 'classe_id' => $classe_id, 'id' => $id]);
        if ($result) {
            error_log("Groupe updated successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Groupe mis à jour avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Échec de la mise à jour']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("DELETE Data: " . json_encode($data));

    if (!isset($data['id'])) {
        error_log("DELETE Error: ID du groupe requis");
        echo json_encode(['success' => false, 'error' => 'ID du groupe requis']);
        exit;
    }

    $id = intval($data['id']);

    // Vérifier si le groupe existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Groupes WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Groupe non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check groupe exists error: " . $e->getMessage());
    }

    // Vérifier les dépendances (si le groupe est utilisé dans d'autres tables)
    try {
        // Exemple : vérifier si le groupe est utilisé dans la table Etudiants
        $checkDependencyStmt = $pdo->prepare("SELECT COUNT(*) as count FROM Etudiants WHERE groupe_id = :id");
        $checkDependencyStmt->execute(['id' => $id]);
        $dependency = $checkDependencyStmt->fetch();
        if ($dependency['count'] > 0) {
            echo json_encode(['success' => false, 'error' => 'Impossible de supprimer ce groupe car il est utilisé par des étudiants']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check dependencies error: " . $e->getMessage());
        // Continue même si la vérification des dépendances échoue
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM Groupes WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);
        if ($result && $stmt->rowCount() > 0) {
            error_log("Groupe deleted successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Groupe supprimé avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Aucun groupe supprimé']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
