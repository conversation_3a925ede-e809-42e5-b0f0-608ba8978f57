<?php
/**
 * API pour récupérer les devoirs - VERSION SANS AUTHENTIFICATION
 * Pour tests et développement uniquement
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Connexion à la base de données
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Requête pour récupérer tous les devoirs avec informations complètes
        $stmt = $pdo->prepare("
            SELECT d.id, d.titre, d.description, d.date_creation, d.date_echeance,
                   m.nom as matiere_nom, c.nom as classe_nom,
                   CONCAT(u.nom, ' ', u.prenom) as enseignant_nom
            FROM Devoirs d
            JOIN Matieres m ON d.matiere_id = m.id
            JOIN Classes c ON d.classe_id = c.id
            LEFT JOIN Enseignants e ON d.enseignant_id = e.id
            LEFT JOIN Utilisateurs u ON e.utilisateur_id = u.id
            ORDER BY d.date_creation DESC
        ");
        
        $stmt->execute();
        $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formater les données pour React
        $result = [];
        foreach ($devoirs as $devoir) {
            $result[] = [
                'id' => (int)$devoir['id'],
                'titre' => $devoir['titre'],
                'description' => $devoir['description'],
                'date_creation' => $devoir['date_creation'],
                'date_echeance' => $devoir['date_echeance'],
                'matiere_nom' => $devoir['matiere_nom'],
                'classe_nom' => $devoir['classe_nom'],
                'enseignant_nom' => $devoir['enseignant_nom'] ?: 'Non défini'
            ];
        }
        
        echo json_encode([
            'success' => true,
            'devoirs' => $result,
            'total' => count($result),
            'message' => 'Devoirs récupérés avec succès (version sans auth)'
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur base de données : ' . $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Méthode non autorisée'
    ]);
}
?>
