<?php
/**
 * Script de debug pour tester l'API Cours
 * Utilisation: php test_cours_debug.php
 */

echo "🧪 Test de Debug API Cours\n";
echo "==========================\n\n";

// Configuration
$baseUrl = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $files = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $headers = [
        'Authorization: Bearer test-token'
    ];
    
    if ($files) {
        // Pour les uploads de fichiers
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        $headers[] = 'Content-Type: application/json';
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: GET - Récupérer tous les cours
echo "1️⃣ Test GET - Récupérer tous les cours\n";
$response = makeRequest($baseUrl, 'GET');
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && is_array($response['data'])) {
    echo "✅ Succès: " . count($response['data']) . " cours trouvés\n";
    if (count($response['data']) > 0) {
        echo "Premier cours: " . json_encode($response['data'][0], JSON_PRETTY_PRINT) . "\n";
    }
} else {
    echo "❌ Erreur: Réponse invalide\n";
}
echo "\n";

// Test 2: Vérifier la structure de la table
echo "2️⃣ Test Structure BDD\n";
try {
    require_once('../../config/db.php');
    
    // Vérifier la structure de la table cours
    $stmt = $pdo->query("DESCRIBE cours");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Structure de la table 'cours':\n";
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
             ($column['Null'] === 'NO' ? " NOT NULL" : "") . 
             ($column['Default'] ? " DEFAULT " . $column['Default'] : "") . "\n";
    }
    
    // Vérifier les matières disponibles
    $stmt = $pdo->query("SELECT id, nom FROM matieres LIMIT 5");
    $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\nMatières disponibles:\n";
    foreach ($matieres as $matiere) {
        echo "- ID: " . $matiere['id'] . ", Nom: " . $matiere['nom'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur BDD: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: POST - Simuler l'ajout d'un cours (sans fichier)
echo "3️⃣ Test POST - Données uniquement\n";
$testData = [
    'titre' => 'Test Cours Debug ' . date('H:i:s'),
    'description' => 'Description de test',
    'matiere_id' => 1
];

// Simuler FormData en POST
$postData = http_build_query($testData);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer test-token',
    'Content-Type: application/x-www-form-urlencoded'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Code HTTP: " . $httpCode . "\n";
echo "Réponse: " . $response . "\n";
$data = json_decode($response, true);
if ($data && isset($data['success'])) {
    if ($data['success']) {
        echo "✅ Succès: " . $data['message'] . "\n";
    } else {
        echo "❌ Erreur: " . $data['error'] . "\n";
    }
} else {
    echo "❌ Erreur: Réponse invalide\n";
}
echo "\n";

// Test 4: Vérifier les logs d'erreur PHP
echo "4️⃣ Vérification des logs PHP\n";
$logFiles = [
    '/var/log/php_errors.log',
    '/var/log/apache2/error.log',
    '/tmp/php_errors.log',
    ini_get('error_log')
];

foreach ($logFiles as $logFile) {
    if ($logFile && file_exists($logFile)) {
        echo "Log trouvé: " . $logFile . "\n";
        $lines = file($logFile);
        $recentLines = array_slice($lines, -10); // 10 dernières lignes
        echo "Dernières lignes:\n";
        foreach ($recentLines as $line) {
            if (strpos($line, 'cours') !== false || strpos($line, 'Cours') !== false) {
                echo "  " . trim($line) . "\n";
            }
        }
        break;
    }
}
echo "\n";

// Test 5: Vérifier les permissions du dossier uploads
echo "5️⃣ Vérification dossier uploads\n";
$uploadDir = '../../uploads/cours/';
if (!file_exists($uploadDir)) {
    echo "❌ Dossier uploads n'existe pas: " . $uploadDir . "\n";
    echo "Tentative de création...\n";
    if (mkdir($uploadDir, 0777, true)) {
        echo "✅ Dossier créé avec succès\n";
    } else {
        echo "❌ Impossible de créer le dossier\n";
    }
} else {
    echo "✅ Dossier uploads existe: " . $uploadDir . "\n";
    $perms = fileperms($uploadDir);
    echo "Permissions: " . substr(sprintf('%o', $perms), -4) . "\n";
    
    if (is_writable($uploadDir)) {
        echo "✅ Dossier accessible en écriture\n";
    } else {
        echo "❌ Dossier non accessible en écriture\n";
    }
}

echo "\n🏁 Tests de debug terminés!\n";
echo "============================\n";
echo "Vérifiez les logs ci-dessus pour identifier les problèmes.\n";
?>
