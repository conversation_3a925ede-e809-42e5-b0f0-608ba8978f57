import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';

const EnseignantFormFixed = ({ enseignant, onSuccess, onCancel }) => {
    const [formData, setFormData] = useState({
        utilisateur_id: '',
        telephone: '',
        specialite: '',
        date_embauche: '',
        salaire: ''
    });
    
    const [utilisateurs, setUtilisateurs] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingUsers, setLoadingUsers] = useState(true);

    useEffect(() => {
        // Initialiser le formulaire avec les données de l'enseignant
        if (enseignant) {
            setFormData({
                utilisateur_id: enseignant.utilisateur_id || '',
                telephone: enseignant.telephone || '',
                specialite: enseignant.specialite || '',
                date_embauche: enseignant.date_embauche || '',
                salaire: enseignant.salaire || ''
            });
        }
        
        // Charger les utilisateurs
        fetchUtilisateurs();
    }, [enseignant]);

    const fetchUtilisateurs = async () => {
        try {
            setLoadingUsers(true);
            const token = localStorage.getItem('token');
            
            console.log('🔄 Chargement des utilisateurs enseignants...');
            
            // Récupérer tous les utilisateurs avec le rôle "enseignant"
            const responseUsers = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les enseignants existants
            const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            const tousUtilisateursEnseignants = Array.isArray(responseUsers.data) ? responseUsers.data : [];
            const enseignantsExistants = Array.isArray(responseEnseignants.data) 
                ? responseEnseignants.data.map(ens => ens.utilisateur_id)
                : [];

            console.log('📊 Utilisateurs enseignants:', tousUtilisateursEnseignants.length);
            console.log('📊 Enseignants existants:', enseignantsExistants);

            // Filtrer les utilisateurs disponibles
            const utilisateursDisponibles = tousUtilisateursEnseignants.filter(user => {
                const isEnseignantRole = user.role_nom === 'enseignant' || user.role === 'enseignant';
                const isNotAlreadyEnseignant = !enseignantsExistants.includes(user.id);
                
                // Exception : en mode modification, inclure l'utilisateur actuel
                const isCurrentUser = enseignant && user.id === enseignant.utilisateur_id;
                
                return isEnseignantRole && (isNotAlreadyEnseignant || isCurrentUser);
            });

            console.log('✅ Utilisateurs disponibles:', utilisateursDisponibles.length);
            setUtilisateurs(utilisateursDisponibles);

        } catch (error) {
            console.error('❌ Erreur chargement utilisateurs:', error);
            Swal.fire('Erreur', 'Impossible de charger les utilisateurs', 'error');
        } finally {
            setLoadingUsers(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            console.log('🔄 Soumission formulaire:', { enseignant, formData });

            // Validation
            if (!formData.utilisateur_id) {
                Swal.fire('Erreur', 'Veuillez sélectionner un utilisateur', 'error');
                return;
            }

            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php';
            
            let response;
            if (enseignant) {
                // Modification
                response = await axios.put(url, {
                    ...formData,
                    id: enseignant.id
                }, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            } else {
                // Création
                response = await axios.post(url, formData, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }

            console.log('✅ Réponse:', response.data);

            if (response.data.success) {
                Swal.fire('Succès', `Enseignant ${enseignant ? 'modifié' : 'créé'} avec succès`, 'success');
                if (onSuccess) onSuccess();
            } else {
                throw new Error(response.data.error || 'Erreur inconnue');
            }

        } catch (error) {
            console.error('❌ Erreur soumission:', error);
            
            let errorMessage = 'Une erreur est survenue';
            if (error.response?.data?.error) {
                errorMessage = error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            Swal.fire('Erreur', errorMessage, 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    if (loadingUsers) {
        return (
            <div style={{ padding: '20px', textAlign: 'center' }}>
                <p>🔄 Chargement des utilisateurs...</p>
            </div>
        );
    }

    return (
        <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
            <h2>{enseignant ? '✏️ Modifier' : '➕ Ajouter'} un Enseignant</h2>
            
            <form onSubmit={handleSubmit}>
                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                        Utilisateur Enseignant *
                    </label>
                    <select
                        name="utilisateur_id"
                        value={formData.utilisateur_id}
                        onChange={handleChange}
                        required
                        style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                    >
                        <option value="">Sélectionner un utilisateur</option>
                        {utilisateurs.map(user => (
                            <option key={user.id} value={user.id}>
                                {user.nom} ({user.email})
                            </option>
                        ))}
                    </select>
                    <small style={{ color: '#666' }}>
                        {utilisateurs.length} utilisateur(s) enseignant(s) disponible(s)
                    </small>
                </div>

                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                        Téléphone
                    </label>
                    <input
                        type="text"
                        name="telephone"
                        value={formData.telephone}
                        onChange={handleChange}
                        style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                    />
                </div>

                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                        Spécialité
                    </label>
                    <input
                        type="text"
                        name="specialite"
                        value={formData.specialite}
                        onChange={handleChange}
                        style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                    />
                </div>

                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                        Date d'embauche
                    </label>
                    <input
                        type="date"
                        name="date_embauche"
                        value={formData.date_embauche}
                        onChange={handleChange}
                        style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                    />
                </div>

                <div style={{ marginBottom: '15px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                        Salaire
                    </label>
                    <input
                        type="number"
                        step="0.01"
                        name="salaire"
                        value={formData.salaire}
                        onChange={handleChange}
                        style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                    />
                </div>

                <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                    <button
                        type="button"
                        onClick={onCancel}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                        }}
                    >
                        Annuler
                    </button>
                    <button
                        type="submit"
                        disabled={loading}
                        style={{
                            padding: '10px 20px',
                            backgroundColor: loading ? '#ccc' : '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: loading ? 'not-allowed' : 'pointer'
                        }}
                    >
                        {loading ? '⏳ En cours...' : (enseignant ? '✏️ Modifier' : '➕ Ajouter')}
                    </button>
                </div>
            </form>

            {/* Debug info */}
            <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '12px' }}>
                <strong>Debug Info:</strong>
                <br />Mode: {enseignant ? 'Modification' : 'Création'}
                <br />Utilisateurs disponibles: {utilisateurs.length}
                <br />Utilisateur sélectionné: {formData.utilisateur_id}
                {enseignant && (
                    <>
                        <br />Enseignant ID: {enseignant.id}
                        <br />Utilisateur actuel: {enseignant.utilisateur_id}
                    </>
                )}
            </div>
        </div>
    );
};

export default EnseignantFormFixed;
