<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🔒 AJOUT DES TRIGGERS DE SÉCURITÉ - TABLE PARENTS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .sql-code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Objectif</h2>";
    echo "<p>Créer des triggers de base de données pour garantir que :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>AUCUN</strong> utilisateur sans le rôle 'parent' ne peut être inséré</li>";
    echo "<li>✅ <strong>AUCUN</strong> utilisateur sans le rôle 'parent' ne peut être modifié</li>";
    echo "<li>✅ <strong>VALIDATION</strong> automatique au niveau base de données</li>";
    echo "<li>✅ <strong>SÉCURITÉ</strong> même en cas d'accès direct SQL</li>";
    echo "</ul>";
    echo "</div>";
    
    // Étape 1 : Supprimer les anciens triggers s'ils existent
    echo "<div class='step'>";
    echo "<h3>1. 🗑️ Suppression des Anciens Triggers</h3>";
    
    $oldTriggers = [
        'check_parent_role_insert',
        'check_parent_role_update',
        'check_parent_role_before_insert',
        'check_parent_role_before_update',
        'security_check_parent_role_insert',
        'security_check_parent_role_update'
    ];
    
    foreach ($oldTriggers as $trigger) {
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS `$trigger`");
            echo "<p class='success'>✅ Trigger '$trigger' supprimé (s'il existait)</p>";
        } catch (PDOException $e) {
            echo "<p class='warning'>⚠️ Trigger '$trigger' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Étape 2 : Créer le trigger INSERT
    echo "<div class='step'>";
    echo "<h3>2. ⚡ Création du Trigger INSERT</h3>";
    echo "<p class='info'>Ce trigger empêche l'insertion d'utilisateurs sans le rôle 'parent'</p>";
    
    echo "<div class='sql-code'>";
    echo "CREATE TRIGGER security_check_parent_role_insert<br>";
    echo "BEFORE INSERT ON parents<br>";
    echo "FOR EACH ROW<br>";
    echo "BEGIN<br>";
    echo "&nbsp;&nbsp;DECLARE user_role VARCHAR(50);<br>";
    echo "&nbsp;&nbsp;DECLARE user_name VARCHAR(200);<br>";
    echo "<br>";
    echo "&nbsp;&nbsp;SELECT r.nom, CONCAT(u.nom, ' ', u.prenom)<br>";
    echo "&nbsp;&nbsp;INTO user_role, user_name<br>";
    echo "&nbsp;&nbsp;FROM utilisateurs u<br>";
    echo "&nbsp;&nbsp;JOIN roles r ON u.role_id = r.id<br>";
    echo "&nbsp;&nbsp;WHERE u.id = NEW.utilisateur_id;<br>";
    echo "<br>";
    echo "&nbsp;&nbsp;IF user_role IS NULL THEN<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;SIGNAL SQLSTATE '45000'<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;SET MESSAGE_TEXT = 'SÉCURITÉ: Utilisateur inexistant';<br>";
    echo "&nbsp;&nbsp;ELSEIF user_role != 'parent' THEN<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;SIGNAL SQLSTATE '45000'<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;SET MESSAGE_TEXT = CONCAT('SÉCURITÉ: Seuls les utilisateurs avec le rôle \"parent\" peuvent être insérés. Rôle actuel: \"', user_role, '\" pour ', user_name);<br>";
    echo "&nbsp;&nbsp;END IF;<br>";
    echo "END";
    echo "</div>";
    
    try {
        $triggerInsert = "
            CREATE TRIGGER `security_check_parent_role_insert` 
            BEFORE INSERT ON `parents`
            FOR EACH ROW
            BEGIN
                DECLARE user_role VARCHAR(50);
                DECLARE user_name VARCHAR(200);
                
                SELECT r.nom, CONCAT(u.nom, ' ', u.prenom)
                INTO user_role, user_name
                FROM utilisateurs u
                JOIN roles r ON u.role_id = r.id
                WHERE u.id = NEW.utilisateur_id;
                
                IF user_role IS NULL THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'SÉCURITÉ: L\\'utilisateur spécifié n\\'existe pas';
                ELSEIF user_role != 'parent' THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = CONCAT('SÉCURITÉ: Seuls les utilisateurs avec le rôle \"parent\" peuvent être insérés. Rôle actuel: \"', user_role, '\" pour ', user_name);
                END IF;
            END
        ";
        
        $pdo->exec($triggerInsert);
        echo "<p class='success'>✅ Trigger INSERT créé avec succès</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur création trigger INSERT : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 3 : Créer le trigger UPDATE
    echo "<div class='step'>";
    echo "<h3>3. ⚡ Création du Trigger UPDATE</h3>";
    echo "<p class='info'>Ce trigger empêche la modification si l'utilisateur n'a plus le rôle 'parent'</p>";
    
    try {
        $triggerUpdate = "
            CREATE TRIGGER `security_check_parent_role_update` 
            BEFORE UPDATE ON `parents`
            FOR EACH ROW
            BEGIN
                DECLARE user_role VARCHAR(50);
                DECLARE user_name VARCHAR(200);
                
                SELECT r.nom, CONCAT(u.nom, ' ', u.prenom)
                INTO user_role, user_name
                FROM utilisateurs u
                JOIN roles r ON u.role_id = r.id
                WHERE u.id = NEW.utilisateur_id;
                
                IF user_role IS NULL THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'SÉCURITÉ: L\\'utilisateur spécifié n\\'existe pas';
                ELSEIF user_role != 'parent' THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = CONCAT('SÉCURITÉ: Seuls les utilisateurs avec le rôle \"parent\" peuvent être modifiés. Rôle actuel: \"', user_role, '\" pour ', user_name);
                END IF;
            END
        ";
        
        $pdo->exec($triggerUpdate);
        echo "<p class='success'>✅ Trigger UPDATE créé avec succès</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur création trigger UPDATE : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 4 : Vérifier les triggers créés
    echo "<div class='step'>";
    echo "<h3>4. 🔍 Vérification des Triggers</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TRIGGERS LIKE 'parents'");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($triggers) > 0) {
            echo "<p class='success'>✅ " . count($triggers) . " trigger(s) trouvé(s) pour la table parents</p>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>Trigger</th><th>Event</th><th>Table</th><th>Timing</th><th>Statement</th></tr>";
            foreach ($triggers as $trigger) {
                echo "<tr>";
                echo "<td><strong>{$trigger['Trigger']}</strong></td>";
                echo "<td>{$trigger['Event']}</td>";
                echo "<td>{$trigger['Table']}</td>";
                echo "<td>{$trigger['Timing']}</td>";
                echo "<td>" . substr($trigger['Statement'], 0, 50) . "...</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ Aucun trigger trouvé pour la table parents</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification triggers : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 5 : Test des triggers
    echo "<div class='step'>";
    echo "<h3>5. 🧪 Test des Triggers</h3>";
    echo "<p class='info'>Test automatique pour vérifier que les triggers fonctionnent</p>";
    
    // Créer un utilisateur de test avec un rôle non-parent
    try {
        // Vérifier qu'il y a un rôle admin
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE nom = 'admin' LIMIT 1");
        $stmt->execute();
        $adminRole = $stmt->fetch();
        
        if ($adminRole) {
            // Créer un utilisateur admin de test
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES ('Test', 'Admin', '<EMAIL>', 'test123', :role_id)
            ");
            $stmt->execute(['role_id' => $adminRole['id']]);
            
            // Récupérer l'ID de l'utilisateur
            $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = '<EMAIL>'");
            $stmt->execute();
            $testUser = $stmt->fetch();
            
            if ($testUser) {
                echo "<p class='info'>👤 Utilisateur de test créé : Test Admin (ID: {$testUser['id']}, Rôle: admin)</p>";
                
                // Tenter d'insérer cet utilisateur admin dans la table parents (doit échouer)
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO parents (utilisateur_id, telephone, adresse) 
                        VALUES (:utilisateur_id, '0123456789', 'Test Address')
                    ");
                    $stmt->execute(['utilisateur_id' => $testUser['id']]);
                    
                    echo "<p class='error'>❌ ÉCHEC DU TEST : L'utilisateur admin a été inséré (ne devrait pas arriver !)</p>";
                    
                    // Nettoyer l'insertion incorrecte
                    $deleteStmt = $pdo->prepare("DELETE FROM parents WHERE utilisateur_id = :user_id");
                    $deleteStmt->execute(['user_id' => $testUser['id']]);
                    
                } catch (PDOException $e) {
                    echo "<p class='success'>✅ SUCCÈS DU TEST : Trigger a correctement bloqué l'insertion</p>";
                    echo "<p style='color: #666; margin-left: 20px;'>Message : " . $e->getMessage() . "</p>";
                }
                
                // Nettoyer l'utilisateur de test
                $deleteStmt = $pdo->prepare("DELETE FROM utilisateurs WHERE id = :user_id");
                $deleteStmt->execute(['user_id' => $testUser['id']]);
                echo "<p class='info'>🧹 Utilisateur de test supprimé</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Pas de rôle admin trouvé pour le test</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur lors du test : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎯 SÉCURITÉ TRIGGERS IMPLÉMENTÉE</h3>";
    echo "<p class='success' style='font-size: 18px;'>🔒 TRIGGERS DE SÉCURITÉ CRÉÉS AVEC SUCCÈS !</p>";
    
    echo "<h4>✅ Protections ajoutées :</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Trigger INSERT</strong> : Empêche l'ajout d'utilisateurs non-parents</li>";
    echo "<li>✅ <strong>Trigger UPDATE</strong> : Empêche la modification si le rôle change</li>";
    echo "<li>✅ <strong>Messages explicites</strong> : Erreurs claires avec détails</li>";
    echo "<li>✅ <strong>Validation automatique</strong> : Même en cas d'accès SQL direct</li>";
    echo "</ul>";
    
    echo "<h4>🛡️ Garanties de sécurité :</h4>";
    echo "<ul>";
    echo "<li>🔒 <strong>Impossible</strong> d'insérer un utilisateur sans rôle 'parent'</li>";
    echo "<li>🔒 <strong>Impossible</strong> de modifier si l'utilisateur n'a plus le rôle 'parent'</li>";
    echo "<li>🔒 <strong>Protection</strong> même contre les requêtes SQL directes</li>";
    echo "<li>🔒 <strong>Messages d'erreur</strong> explicites pour le debug</li>";
    echo "</ul>";
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><a href='test_security_complete.php'>Tester la sécurité complète</a></li>";
    echo "<li><a href='../pages/parents/test_filtrage_dropdown.php'>Tester le filtrage dropdown</a></li>";
    echo "<li><a href='http://localhost:3000/parents'>Tester l'interface Parents</a></li>";
    echo "</ol>";
    
    echo "<p class='info'><strong>La table Parents est maintenant sécurisée à 100% !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
