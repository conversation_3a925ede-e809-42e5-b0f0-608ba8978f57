// INTÉGRATION QUIZ CRUD DANS APP.JS
// Copiez ce code dans votre App.js existant

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';

// Imports existants
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Absences from './pages/Absences';
// ... autres imports existants

// NOUVEAU : Import QuizCRUD
import QuizCRUD from './pages/QuizCRUD';

function App() {
    return (
        <AuthProvider>
            <Router>
                <div className="App">
                    <Routes>
                        {/* Routes existantes */}
                        <Route path="/" element={<Login />} />
                        <Route path="/login" element={<Login />} />
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/absences" element={<Absences />} />
                        
                        {/* NOUVELLE ROUTE : Quiz CRUD */}
                        <Route path="/quiz-crud" element={<QuizCRUD />} />
                        
                        {/* ... autres routes existantes */}
                    </Routes>
                </div>
            </Router>
        </AuthProvider>
    );
}

export default App;

// NAVIGATION : Ajoutez dans votre composant de navigation
/*
<Link to="/quiz-crud" className="nav-link">
    <img src="/quiz-icon.png" alt="Quiz" />
    Gestion Quiz
</Link>
*/

// PERMISSIONS : Affichage conditionnel selon le rôle
/*
{(user?.role === 'admin' || user?.role === 'enseignant') && (
    <Link to="/quiz-crud">Gestion Quiz</Link>
)}
*/
