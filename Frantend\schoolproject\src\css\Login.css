:root {
  --antiflash-white: #f5f7f9; /* نفس الخلفية الفاتحة */
  --cerulean: #006989;
  --cerulean-2: #007090;
  --moonstone: #01a7c2;
  --text-dark: #274c77; /* نص داكن مشابه للنص في register */
}
    
.login-container{
   min-height: 100vh;
    background: linear-gradient(to right, #006989, #01a7c2); /* cerulean to moonstone */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
/* Container كامل مركزي مع تدرج الألوان */
.container {
 display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
 
}

/* الفورم بخلفية فاتحة، ظل، وتدوير الحواف */
form {
  background-color: var(--antiflash-white);
  padding: 30px 40px;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(1, 167, 194, 0.3);
  width: 100%;
  max-width: 400px;
  color: var(--cerulean);
  text-align: center;
  transition: transform 0.3s ease;
}

form:hover {
  transform: translateY(-5px);
}

/* عنوان الصفحة */
.login-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 25px;
  color: var(--cerulean-2);
  text-align: center;
}

/* الحقول مع تصميم مشابه للحقل في register */
input {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  border: 1px solid var(--cerulean-2);
  border-radius: 8px;
  font-size: 14px;
  color: var(--text-dark);
  background-color: #ffffffdd;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

input::placeholder {
  color: var(--cerulean);
}

input:focus {
  outline: none;
  border-color: var(--moonstone);
  box-shadow: 0 0 8px #01a7c2aa;
  background-color: #f5f7f9;
}

/* زر الدخول بنفس ستايل زر التسجيل */
button {
  width: 100%;
  padding: 12px;
  background-color: var(--moonstone);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

button:hover:not(:disabled) {
  background-color: var(--cerulean-2);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--moonstone);
}

/* رسائل الحالة */
.message {
  margin-top: 20px;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--cerulean);
}

.message.success {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.message.error {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(102, 163, 177, 0.3);
  border-radius: 50%;
  border-top-color: var(--cerulean);
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  form {
    padding: 30px 20px;
  }

  .login-title {
    font-size: 2rem;
  }

  input, button {
    padding: 12px;
    font-size: 14px;
  }
}
