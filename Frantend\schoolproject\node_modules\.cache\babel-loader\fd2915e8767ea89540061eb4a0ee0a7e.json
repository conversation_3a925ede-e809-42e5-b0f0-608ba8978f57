{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\Absences.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Absences.css'; // Réutilisation des styles\n\nconst Absences = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [absences, setAbsences] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingAbsence, setEditingAbsence] = useState(null);\n  const [etudiants, setEtudiants] = useState([]);\n  const [matieres, setMatieres] = useState([]);\n  const [enseignants, setEnseignants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    matiere_id: '',\n    enseignant_id: '',\n    date_absence: '',\n    justification: ''\n  });\n  useEffect(() => {\n    fetchAbsences();\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'enseignant') {\n      fetchEtudiants();\n      fetchMatieres();\n      if ((user === null || user === void 0 ? void 0 : user.role) === 'admin') {\n        fetchEnseignants();\n      }\n    }\n  }, []);\n  const fetchAbsences = async () => {\n    try {\n      console.log('🔄 Chargement des absences...');\n\n      // Utiliser l'API officielle restructurée\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/absences/', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG ABSENCES API Response:', response.data);\n      console.log('🔍 Status:', response.status);\n      console.log('🔍 Headers:', response.headers);\n\n      // Vérifier si la réponse est un tableau\n      if (Array.isArray(response.data)) {\n        setAbsences(response.data);\n        console.log('✅ Absences chargées:', response.data.length);\n      } else if (response.data && response.data.error) {\n        console.error('❌ Erreur API:', response.data.error);\n        Swal.fire('Erreur API', response.data.error, 'error');\n        setAbsences([]);\n      } else {\n        console.warn('⚠️ Format de réponse inattendu:', response.data);\n        setAbsences([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$config, _error$response3, _error$response4;\n      console.error('❌ Erreur lors du chargement des absences:', error);\n      console.error('❌ Détails erreur:', {\n        message: error.message,\n        response: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data,\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status,\n        url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url\n      });\n\n      // Message d'erreur plus détaillé\n      let errorMessage = 'Impossible de charger les absences';\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n        errorMessage = 'API non trouvée. Vérifiez que le serveur est démarré.';\n      } else if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 500) {\n        errorMessage = 'Erreur serveur. Vérifiez les logs PHP.';\n      } else if (error.code === 'NETWORK_ERROR') {\n        errorMessage = 'Erreur réseau. Vérifiez la connexion au serveur.';\n      }\n      Swal.fire('Erreur', errorMessage, 'error');\n      setAbsences([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      // TEMPORAIRE : Utiliser l'API sans authentification pour les tests\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');\n      console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n      if (response.data.success) {\n        setEtudiants(response.data.etudiants);\n        console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n        console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n      } else {\n        console.error('❌ Erreur API étudiants:', response.data.error);\n        setEtudiants([]);\n      }\n    } catch (error) {\n      var _error$response5;\n      console.error('❌ Erreur lors du chargement des étudiants:', error);\n      console.error('❌ Détails erreur:', ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || error.message);\n      setEtudiants([]);\n    }\n  };\n  const fetchMatieres = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setMatieres(response.data);\n    } catch (error) {\n      console.error('Erreur lors du chargement des matières:', error);\n    }\n  };\n  const fetchEnseignants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG ENSEIGNANTS API Response:', response.data);\n      if (response.data.success) {\n        setEnseignants(response.data.enseignants);\n        console.log('✅ Enseignants chargés:', response.data.enseignants.length);\n      } else {\n        console.error('❌ Erreur API enseignants:', response.data.error);\n        setEnseignants([]);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des enseignants:', error);\n      setEnseignants([]);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Debug : Vérifier les données avant envoi\n    console.log('🔍 DEBUG SUBMIT:');\n    console.log('FormData avant envoi:', formData);\n    console.log('Type de etudiant_id:', typeof formData.etudiant_id);\n    console.log('Valeur de etudiant_id:', formData.etudiant_id);\n    console.log('Étudiants disponibles:', etudiants);\n    console.log('Nombre d\\'étudiants:', etudiants.length);\n    console.log('Premier étudiant:', etudiants[0]);\n\n    // Vérifier si les étudiants sont chargés\n    if (etudiants.length === 0) {\n      console.error('❌ Aucun étudiant chargé !');\n      Swal.fire('Erreur', 'Aucun étudiant disponible. Veuillez recharger la page.', 'error');\n      return;\n    }\n    console.log('Nombre d\\'étudiants:', etudiants.length);\n\n    // Validation des données avec messages détaillés\n    if (!formData.etudiant_id || formData.etudiant_id === '') {\n      console.error('❌ Erreur: etudiant_id vide ou undefined');\n      console.log('FormData complet:', formData);\n      Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');\n      return;\n    }\n    if (!formData.date_absence || formData.date_absence === '') {\n      console.error('❌ Erreur: date_absence vide ou undefined');\n      Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');\n      return;\n    }\n\n    // S'assurer que les IDs sont des nombres\n    const cleanData = {\n      etudiant_id: parseInt(formData.etudiant_id),\n      matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,\n      enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,\n      date_absence: formData.date_absence,\n      justification: formData.justification || null\n    };\n    console.log('🔍 Données nettoyées:', cleanData);\n    try {\n      // Utiliser l'API officielle restructurée\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/absences/';\n      const method = editingAbsence ? 'PUT' : 'POST';\n      const data = editingAbsence ? {\n        ...cleanData,\n        id: editingAbsence.id\n      } : cleanData;\n      console.log('🔍 Données finales envoyées:', data);\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('✅ Réponse serveur:', response.data);\n      Swal.fire('Succès', `Absence ${editingAbsence ? 'modifiée' : 'enregistrée'} avec succès`, 'success');\n      setShowModal(false);\n      setEditingAbsence(null);\n      resetForm();\n      fetchAbsences();\n    } catch (error) {\n      var _error$response6, _error$response7, _error$response7$data;\n      console.error('❌ Erreur complète:', error);\n      console.error('❌ Réponse serveur:', (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data);\n      Swal.fire('Erreur', ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = absence => {\n    setEditingAbsence(absence);\n    setFormData({\n      etudiant_id: absence.etudiant_id,\n      matiere_id: absence.matiere_id || '',\n      enseignant_id: absence.enseignant_id || '',\n      date_absence: absence.date_absence,\n      justification: absence.justification || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        // Utiliser l'API officielle restructurée\n        const token = localStorage.getItem('token');\n        await axios.delete('http://localhost/Project_PFE/Backend/pages/absences/', {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          data: {\n            id\n          }\n        });\n        Swal.fire('Supprimé!', 'L\\'absence a été supprimée.', 'success');\n        fetchAbsences();\n      } catch (error) {\n        console.error('Erreur:', error);\n        Swal.fire('Erreur', 'Impossible de supprimer l\\'absence', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      matiere_id: '',\n      enseignant_id: '',\n      date_absence: '',\n      justification: ''\n    });\n  };\n\n  // Fonctions de filtrage et pagination (comme les factures)\n  const filteredAbsences = absences.filter(absence => {\n    var _absence$etudiant_nom, _absence$matiere_nom, _absence$nom_prenom;\n    const matchesSearch = ((_absence$etudiant_nom = absence.etudiant_nom) === null || _absence$etudiant_nom === void 0 ? void 0 : _absence$etudiant_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_absence$matiere_nom = absence.matiere_nom) === null || _absence$matiere_nom === void 0 ? void 0 : _absence$matiere_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_absence$nom_prenom = absence.nom_prenom) === null || _absence$nom_prenom === void 0 ? void 0 : _absence$nom_prenom.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesStatus = statusFilter === 'all' || statusFilter === 'justified' && absence.justification || statusFilter === 'unjustified' && !absence.justification;\n    return matchesSearch && matchesStatus;\n  });\n\n  // Pagination\n  const totalPages = Math.ceil(filteredAbsences.length / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const currentAbsences = filteredAbsences.slice(startIndex, startIndex + itemsPerPage);\n  const getJustificationBadge = justification => {\n    if (justification && justification.trim()) {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: \"badge badge-success\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 20\n        }\n      }, \"Justifi\\xE9e\");\n    }\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge badge-danger\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 16\n      }\n    }, \"Non justifi\\xE9e\");\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  };\n\n  // Vérifier si l'utilisateur est admin ou enseignant\n  const canManageAbsences = (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'enseignant';\n\n  // Debug pour vérifier les données\n  console.log('🔍 DEBUG ABSENCES:');\n  console.log('User:', user);\n  console.log('User role:', user === null || user === void 0 ? void 0 : user.role);\n  console.log('Can manage absences:', canManageAbsences);\n  console.log('Absences data:', absences);\n  console.log('Absences count:', absences.length);\n  if (canManageAbsences) {\n    console.log('📚 Étudiants disponibles:', etudiants.length);\n    console.log('📖 Matières disponibles:', matieres.length);\n    console.log('👨‍🏫 Enseignants disponibles:', enseignants.length);\n  }\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 17\n      }\n    }, \"Chargement des absences...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCCB Gestion des Absences\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 21\n    }\n  }, filteredAbsences.length, \" absence(s) trouv\\xE9e(s)\", totalPages > 1 && ` • Page ${currentPage}/${totalPages}`), canManageAbsences && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary add-button\",\n    onClick: () => setShowModal(true),\n    title: \"Ajouter une nouvelle absence - Enregistrer l'absence d'un \\xE9tudiant avec date, mati\\xE8re et justification\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 29\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 29\n    }\n  }, \"Nouvelle Absence\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"button-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCC5 Date \\u2022 \\uD83D\\uDC64 \\xC9tudiant \\u2022 \\uD83D\\uDCDA Mati\\xE8re \\u2022 \\uD83D\\uDCAC Justification\"))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-filters\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par \\xE9tudiant, mati\\xE8re ou enseignant...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    className: \"search-input\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 25\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: statusFilter,\n    onChange: e => setStatusFilter(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 29\n    }\n  }, \"\\uD83D\\uDCCA Tous les statuts\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"justified\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 29\n    }\n  }, \"\\u2705 Justifi\\xE9es\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"unjustified\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 29\n    }\n  }, \"\\u274C Non justifi\\xE9es\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"results-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 21\n    }\n  }, filteredAbsences.length, \" absence(s) trouv\\xE9e(s)\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 13\n    }\n  }, filteredAbsences.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/attendance.png\",\n    alt: \"Aucune absence\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 25\n    }\n  }, \"Aucune absence trouv\\xE9e\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date d'absence\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDD Statut\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCAC Justification\"), canManageAbsences && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 59\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 29\n    }\n  }, currentAbsences.map(absence => /*#__PURE__*/React.createElement(\"tr\", {\n    key: absence.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"student-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 49\n    }\n  }, absence.etudiant_nom), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 49\n    }\n  }, absence.etudiant_email))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#e8f5e8',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 45\n    }\n  }, absence.matiere_nom || '-')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#dc3545',\n      fontSize: '1em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 45\n    }\n  }, formatDate(absence.date_absence))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 41\n    }\n  }, getJustificationBadge(absence.justification)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 41\n    }\n  }, absence.justification ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"justification-text\",\n    title: absence.justification,\n    style: {\n      display: 'inline-block',\n      maxWidth: '200px',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      padding: '4px 8px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 49\n    }\n  }, absence.justification) : /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#6c757d',\n      fontStyle: 'italic'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 49\n    }\n  }, \"Aucune justification\")), canManageAbsences && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning edit-button\",\n    onClick: () => handleEdit(absence),\n    title: `Modifier l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 57\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"btn-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 57\n    }\n  }, \"Modifier\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"btn-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 57\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 61\n    }\n  }, \"\\u270F\\uFE0F \\xC9diter les d\\xE9tails\"))), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger delete-button\",\n    onClick: () => handleDelete(absence.id),\n    title: `Supprimer définitivement l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 57\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"btn-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 57\n    }\n  }, \"Supprimer\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"btn-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 57\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 61\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Suppression d\\xE9finitive\")))))))))), filteredAbsences.length > itemsPerPage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 25\n    }\n  }, \"Affichage de \", startIndex + 1, \" \\xE0 \", Math.min(startIndex + itemsPerPage, filteredAbsences.length), \" sur \", filteredAbsences.length, \" absences\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"pagination-btn\",\n    onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n    disabled: currentPage === 1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 29\n    }\n  }, \"\\u2190 Pr\\xE9c\\xE9dent\"), [...Array(totalPages)].map((_, index) => {\n    const pageNumber = index + 1;\n    if (pageNumber === 1 || pageNumber === totalPages || pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1) {\n      return /*#__PURE__*/React.createElement(\"button\", {\n        key: pageNumber,\n        className: `pagination-btn ${currentPage === pageNumber ? 'active' : ''}`,\n        onClick: () => setCurrentPage(pageNumber),\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 41\n        }\n      }, pageNumber);\n    } else if (pageNumber === currentPage - 2 || pageNumber === currentPage + 2) {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        key: pageNumber,\n        className: \"pagination-ellipsis\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 44\n        }\n      }, \"...\");\n    }\n    return null;\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"pagination-btn\",\n    onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n    disabled: currentPage === totalPages,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 29\n    }\n  }, \"Suivant \\u2192\")))), showModal && canManageAbsences && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 29\n    }\n  }, editingAbsence ? 'Modifier l\\'absence' : 'Nouvelle absence'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingAbsence(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => {\n      var _e$target$selectedOpt;\n      console.log('🔍 SELECT CHANGE:');\n      console.log('Valeur sélectionnée:', e.target.value);\n      console.log('Type:', typeof e.target.value);\n      console.log('Option sélectionnée:', (_e$target$selectedOpt = e.target.selectedOptions[0]) === null || _e$target$selectedOpt === void 0 ? void 0 : _e$target$selectedOpt.text);\n      setFormData({\n        ...formData,\n        etudiant_id: e.target.value\n      });\n    },\n    required: true,\n    disabled: editingAbsence,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.etudiant_id || etudiant.id,\n    value: etudiant.etudiant_id || etudiant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 41\n    }\n  }, etudiant.nom, \" \", etudiant.prenom ? ` ${etudiant.prenom}` : '', \" - \", etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe')))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 33\n    }\n  }, \"Mati\\xE8re (optionnel)\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => setFormData({\n      ...formData,\n      matiere_id: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner une mati\\xE8re\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 41\n    }\n  }, matiere.nom)))), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 37\n    }\n  }, \"Enseignant (optionnel)\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.enseignant_id,\n    onChange: e => setFormData({\n      ...formData,\n      enseignant_id: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 41\n    }\n  }, \"S\\xE9lectionner un enseignant\"), enseignants.map(enseignant => /*#__PURE__*/React.createElement(\"option\", {\n    key: enseignant.enseignant_id || enseignant.id,\n    value: enseignant.enseignant_id || enseignant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 45\n    }\n  }, enseignant.nom, \" \", enseignant.prenom ? ` ${enseignant.prenom}` : '', \" - \", enseignant.specialite || 'Spécialité non définie')))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 33\n    }\n  }, \"Date d'absence\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_absence,\n    onChange: e => setFormData({\n      ...formData,\n      date_absence: e.target.value\n    }),\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 33\n    }\n  }, \"Justification (optionnel)\"), /*#__PURE__*/React.createElement(\"textarea\", {\n    value: formData.justification,\n    onChange: e => setFormData({\n      ...formData,\n      justification: e.target.value\n    }),\n    placeholder: \"Motif de l'absence...\",\n    rows: \"3\",\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      resize: 'vertical'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 33\n    }\n  }, editingAbsence ? 'Modifier' : 'Enregistrer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingAbsence(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 33\n    }\n  }, \"Annuler\"))))));\n};\nexport default Absences;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "Absences", "user", "absences", "setAbsences", "loading", "setLoading", "showModal", "setShowModal", "editingAbsence", "setEditingAbsence", "etudiants", "setEtudiants", "matieres", "set<PERSON>ati<PERSON>s", "enseignants", "setEnseignants", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "etudiant_id", "matiere_id", "enseignant_id", "date_absence", "justification", "fetchAbsences", "role", "fetchEtudiants", "fetchMatieres", "fetchEnseignants", "console", "log", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "status", "Array", "isArray", "length", "error", "fire", "warn", "_error$response", "_error$response2", "_error$config", "_error$response3", "_error$response4", "message", "url", "config", "errorMessage", "code", "success", "_error$response5", "handleSubmit", "e", "preventDefault", "cleanData", "parseInt", "method", "id", "resetForm", "_error$response6", "_error$response7", "_error$response7$data", "handleEdit", "absence", "handleDelete", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "delete", "filteredAbsences", "filter", "_absence$etudiant_nom", "_absence$matiere_nom", "_absence$nom_prenom", "matchesSearch", "etudiant_nom", "toLowerCase", "includes", "matiere_nom", "nom_prenom", "matchesStatus", "totalPages", "Math", "ceil", "startIndex", "currentAbsences", "slice", "getJustificationBadge", "trim", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "Date", "toLocaleDateString", "canManageAbsences", "onClick", "src", "alt", "type", "placeholder", "value", "onChange", "target", "map", "key", "etudiant_email", "style", "padding", "backgroundColor", "borderRadius", "fontSize", "color", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "fontStyle", "min", "prev", "max", "disabled", "_", "index", "pageNumber", "onSubmit", "_e$target$selectedOpt", "selectedOptions", "required", "etudiant", "nom", "prenom", "classe_nom", "groupe_nom", "matiere", "enseignant", "specialite", "rows", "width", "border", "resize"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/Absences.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Absences.css'; // Réutilisation des styles\n\nconst Absences = () => {\n    const { user } = useContext(AuthContext);\n    const [absences, setAbsences] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingAbsence, setEditingAbsence] = useState(null);\n    const [etudiants, setEtudiants] = useState([]);\n    const [matieres, setMatieres] = useState([]);\n    const [enseignants, setEnseignants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        matiere_id: '',\n        enseignant_id: '',\n        date_absence: '',\n        justification: ''\n    });\n\n    useEffect(() => {\n        fetchAbsences();\n        if (user?.role === 'admin' || user?.role === 'enseignant') {\n            fetchEtudiants();\n            fetchMatieres();\n            if (user?.role === 'admin') {\n                fetchEnseignants();\n            }\n        }\n    }, []);\n\n    const fetchAbsences = async () => {\n        try {\n            console.log('🔄 Chargement des absences...');\n\n            // Utiliser l'API officielle restructurée\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/absences/', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG ABSENCES API Response:', response.data);\n            console.log('🔍 Status:', response.status);\n            console.log('🔍 Headers:', response.headers);\n\n            // Vérifier si la réponse est un tableau\n            if (Array.isArray(response.data)) {\n                setAbsences(response.data);\n                console.log('✅ Absences chargées:', response.data.length);\n            } else if (response.data && response.data.error) {\n                console.error('❌ Erreur API:', response.data.error);\n                Swal.fire('Erreur API', response.data.error, 'error');\n                setAbsences([]);\n            } else {\n                console.warn('⚠️ Format de réponse inattendu:', response.data);\n                setAbsences([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des absences:', error);\n            console.error('❌ Détails erreur:', {\n                message: error.message,\n                response: error.response?.data,\n                status: error.response?.status,\n                url: error.config?.url\n            });\n\n            // Message d'erreur plus détaillé\n            let errorMessage = 'Impossible de charger les absences';\n            if (error.response?.status === 404) {\n                errorMessage = 'API non trouvée. Vérifiez que le serveur est démarré.';\n            } else if (error.response?.status === 500) {\n                errorMessage = 'Erreur serveur. Vérifiez les logs PHP.';\n            } else if (error.code === 'NETWORK_ERROR') {\n                errorMessage = 'Erreur réseau. Vérifiez la connexion au serveur.';\n            }\n\n            Swal.fire('Erreur', errorMessage, 'error');\n            setAbsences([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            // TEMPORAIRE : Utiliser l'API sans authentification pour les tests\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');\n\n            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n\n            if (response.data.success) {\n                setEtudiants(response.data.etudiants);\n                console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n                console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n            } else {\n                console.error('❌ Erreur API étudiants:', response.data.error);\n                setEtudiants([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des étudiants:', error);\n            console.error('❌ Détails erreur:', error.response?.data || error.message);\n            setEtudiants([]);\n        }\n    };\n\n    const fetchMatieres = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setMatieres(response.data);\n        } catch (error) {\n            console.error('Erreur lors du chargement des matières:', error);\n        }\n    };\n\n    const fetchEnseignants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG ENSEIGNANTS API Response:', response.data);\n\n            if (response.data.success) {\n                setEnseignants(response.data.enseignants);\n                console.log('✅ Enseignants chargés:', response.data.enseignants.length);\n            } else {\n                console.error('❌ Erreur API enseignants:', response.data.error);\n                setEnseignants([]);\n            }\n        } catch (error) {\n            console.error('Erreur lors du chargement des enseignants:', error);\n            setEnseignants([]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n\n        // Debug : Vérifier les données avant envoi\n        console.log('🔍 DEBUG SUBMIT:');\n        console.log('FormData avant envoi:', formData);\n        console.log('Type de etudiant_id:', typeof formData.etudiant_id);\n        console.log('Valeur de etudiant_id:', formData.etudiant_id);\n        console.log('Étudiants disponibles:', etudiants);\n        console.log('Nombre d\\'étudiants:', etudiants.length);\n        console.log('Premier étudiant:', etudiants[0]);\n\n        // Vérifier si les étudiants sont chargés\n        if (etudiants.length === 0) {\n            console.error('❌ Aucun étudiant chargé !');\n            Swal.fire('Erreur', 'Aucun étudiant disponible. Veuillez recharger la page.', 'error');\n            return;\n        }\n        console.log('Nombre d\\'étudiants:', etudiants.length);\n\n        // Validation des données avec messages détaillés\n        if (!formData.etudiant_id || formData.etudiant_id === '') {\n            console.error('❌ Erreur: etudiant_id vide ou undefined');\n            console.log('FormData complet:', formData);\n            Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');\n            return;\n        }\n\n        if (!formData.date_absence || formData.date_absence === '') {\n            console.error('❌ Erreur: date_absence vide ou undefined');\n            Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');\n            return;\n        }\n\n        // S'assurer que les IDs sont des nombres\n        const cleanData = {\n            etudiant_id: parseInt(formData.etudiant_id),\n            matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,\n            enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,\n            date_absence: formData.date_absence,\n            justification: formData.justification || null\n        };\n\n        console.log('🔍 Données nettoyées:', cleanData);\n\n        try {\n            // Utiliser l'API officielle restructurée\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/absences/';\n            const method = editingAbsence ? 'PUT' : 'POST';\n            const data = editingAbsence ? { ...cleanData, id: editingAbsence.id } : cleanData;\n\n            console.log('🔍 Données finales envoyées:', data);\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            });\n\n            console.log('✅ Réponse serveur:', response.data);\n\n            Swal.fire('Succès', `Absence ${editingAbsence ? 'modifiée' : 'enregistrée'} avec succès`, 'success');\n            setShowModal(false);\n            setEditingAbsence(null);\n            resetForm();\n            fetchAbsences();\n        } catch (error) {\n            console.error('❌ Erreur complète:', error);\n            console.error('❌ Réponse serveur:', error.response?.data);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (absence) => {\n        setEditingAbsence(absence);\n        setFormData({\n            etudiant_id: absence.etudiant_id,\n            matiere_id: absence.matiere_id || '',\n            enseignant_id: absence.enseignant_id || '',\n            date_absence: absence.date_absence,\n            justification: absence.justification || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                // Utiliser l'API officielle restructurée\n                const token = localStorage.getItem('token');\n                await axios.delete('http://localhost/Project_PFE/Backend/pages/absences/', {\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${token}`\n                    },\n                    data: { id }\n                });\n                Swal.fire('Supprimé!', 'L\\'absence a été supprimée.', 'success');\n                fetchAbsences();\n            } catch (error) {\n                console.error('Erreur:', error);\n                Swal.fire('Erreur', 'Impossible de supprimer l\\'absence', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            matiere_id: '',\n            enseignant_id: '',\n            date_absence: '',\n            justification: ''\n        });\n    };\n\n    // Fonctions de filtrage et pagination (comme les factures)\n    const filteredAbsences = absences.filter(absence => {\n        const matchesSearch = absence.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            absence.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            absence.nom_prenom?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        const matchesStatus = statusFilter === 'all' ||\n                            (statusFilter === 'justified' && absence.justification) ||\n                            (statusFilter === 'unjustified' && !absence.justification);\n\n        return matchesSearch && matchesStatus;\n    });\n\n    // Pagination\n    const totalPages = Math.ceil(filteredAbsences.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const currentAbsences = filteredAbsences.slice(startIndex, startIndex + itemsPerPage);\n\n    const getJustificationBadge = (justification) => {\n        if (justification && justification.trim()) {\n            return <span className=\"badge badge-success\">Justifiée</span>;\n        }\n        return <span className=\"badge badge-danger\">Non justifiée</span>;\n    };\n\n    const formatDate = (dateString) => {\n        if (!dateString) return '-';\n        return new Date(dateString).toLocaleDateString('fr-FR');\n    };\n\n    // Vérifier si l'utilisateur est admin ou enseignant\n    const canManageAbsences = user?.role === 'admin' || user?.role === 'enseignant';\n\n    // Debug pour vérifier les données\n    console.log('🔍 DEBUG ABSENCES:');\n    console.log('User:', user);\n    console.log('User role:', user?.role);\n    console.log('Can manage absences:', canManageAbsences);\n    console.log('Absences data:', absences);\n    console.log('Absences count:', absences.length);\n    if (canManageAbsences) {\n        console.log('📚 Étudiants disponibles:', etudiants.length);\n        console.log('📖 Matières disponibles:', matieres.length);\n        console.log('👨‍🏫 Enseignants disponibles:', enseignants.length);\n    }\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des absences...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>📋 Gestion des Absences</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredAbsences.length} absence(s) trouvée(s)\n                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}\n                    </span>\n                    \n\n\n                    {/* Bouton d'ajout visible seulement pour Admin et Enseignants */}\n                    {canManageAbsences && (\n                        <button\n                            className=\"btn btn-primary add-button\"\n                            onClick={() => setShowModal(true)}\n                            title=\"Ajouter une nouvelle absence - Enregistrer l'absence d'un étudiant avec date, matière et justification\"\n                        >\n                            <img src=\"/plus.png\" alt=\"Ajouter\" />\n                            <span>Nouvelle Absence</span>\n                            <div className=\"button-info\">\n                                <small>📅 Date • 👤 Étudiant • 📚 Matière • 💬 Justification</small>\n                            </div>\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {/* Filtres et recherche (comme les factures) */}\n            <div className=\"filters-section\">\n                <div className=\"search-filters\">\n                    <div className=\"search-box\">\n                        <input\n                            type=\"text\"\n                            placeholder=\"🔍 Rechercher par étudiant, matière ou enseignant...\"\n                            value={searchTerm}\n                            onChange={(e) => setSearchTerm(e.target.value)}\n                            className=\"search-input\"\n                        />\n                    </div>\n                    <div className=\"filter-group\">\n                        <select\n                            value={statusFilter}\n                            onChange={(e) => setStatusFilter(e.target.value)}\n                            className=\"filter-select\"\n                        >\n                            <option value=\"all\">📊 Tous les statuts</option>\n                            <option value=\"justified\">✅ Justifiées</option>\n                            <option value=\"unjustified\">❌ Non justifiées</option>\n                        </select>\n                    </div>\n                </div>\n                <div className=\"results-info\">\n                    <span className=\"total-count\">\n                        {filteredAbsences.length} absence(s) trouvée(s)\n                    </span>\n                </div>\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredAbsences.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/attendance.png\" alt=\"Aucune absence\" />\n                        <p>Aucune absence trouvée</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>👤 Étudiant</th>\n                                    <th>📚 Matière</th>\n                                    \n                                    <th>📅 Date d'absence</th>\n                                    <th>📝 Statut</th>\n                                    <th>💬 Justification</th>\n                                    {canManageAbsences && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentAbsences.map((absence) => (\n                                    <tr key={absence.id}>\n                                        <td>\n                                            <div className=\"student-info\">\n                                                <strong>{absence.etudiant_nom}</strong>\n                                                <small>{absence.etudiant_email}</small>\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#e8f5e8',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {absence.matiere_nom || '-'}\n                                            </span>\n                                        </td>\n                                       \n                                        <td>\n                                            <strong style={{ color: '#dc3545', fontSize: '1em' }}>\n                                                {formatDate(absence.date_absence)}\n                                            </strong>\n                                        </td>\n                                        <td>{getJustificationBadge(absence.justification)}</td>\n                                        <td>\n                                            {absence.justification ? (\n                                                <span\n                                                    className=\"justification-text\"\n                                                    title={absence.justification}\n                                                    style={{\n                                                        display: 'inline-block',\n                                                        maxWidth: '200px',\n                                                        overflow: 'hidden',\n                                                        textOverflow: 'ellipsis',\n                                                        whiteSpace: 'nowrap',\n                                                        padding: '4px 8px',\n                                                        backgroundColor: '#f8f9fa',\n                                                        borderRadius: '4px',\n                                                        fontSize: '0.9em'\n                                                    }}\n                                                >\n                                                    {absence.justification}\n                                                </span>\n                                            ) : (\n                                                <span style={{ color: '#6c757d', fontStyle: 'italic' }}>\n                                                    Aucune justification\n                                                </span>\n                                            )}\n                                        </td>\n                                        {canManageAbsences && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning edit-button\"\n                                                        onClick={() => handleEdit(absence)}\n                                                        title={`Modifier l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`}\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                        <span className=\"btn-text\">Modifier</span>\n                                                        <div className=\"btn-info\">\n                                                            <small>✏️ Éditer les détails</small>\n                                                        </div>\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger delete-button\"\n                                                        onClick={() => handleDelete(absence.id)}\n                                                        title={`Supprimer définitivement l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`}\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                        <span className=\"btn-text\">Supprimer</span>\n                                                        <div className=\"btn-info\">\n                                                            <small>🗑️ Suppression définitive</small>\n                                                        </div>\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n\n                {/* Pagination (comme les factures) */}\n                {filteredAbsences.length > itemsPerPage && (\n                    <div className=\"pagination-container\">\n                        <div className=\"pagination-info\">\n                            Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredAbsences.length)} sur {filteredAbsences.length} absences\n                        </div>\n                        <div className=\"pagination\">\n                            <button\n                                className=\"pagination-btn\"\n                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                                disabled={currentPage === 1}\n                            >\n                                ← Précédent\n                            </button>\n\n                            {[...Array(totalPages)].map((_, index) => {\n                                const pageNumber = index + 1;\n                                if (\n                                    pageNumber === 1 ||\n                                    pageNumber === totalPages ||\n                                    (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)\n                                ) {\n                                    return (\n                                        <button\n                                            key={pageNumber}\n                                            className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}\n                                            onClick={() => setCurrentPage(pageNumber)}\n                                        >\n                                            {pageNumber}\n                                        </button>\n                                    );\n                                } else if (\n                                    pageNumber === currentPage - 2 ||\n                                    pageNumber === currentPage + 2\n                                ) {\n                                    return <span key={pageNumber} className=\"pagination-ellipsis\">...</span>;\n                                }\n                                return null;\n                            })}\n\n                            <button\n                                className=\"pagination-btn\"\n                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                                disabled={currentPage === totalPages}\n                            >\n                                Suivant →\n                            </button>\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/* Modal pour ajouter/modifier une absence */}\n            {showModal && canManageAbsences && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingAbsence ? 'Modifier l\\'absence' : 'Nouvelle absence'}</h3>\n                            <button \n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingAbsence(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => {\n                                        console.log('🔍 SELECT CHANGE:');\n                                        console.log('Valeur sélectionnée:', e.target.value);\n                                        console.log('Type:', typeof e.target.value);\n                                        console.log('Option sélectionnée:', e.target.selectedOptions[0]?.text);\n                                        setFormData({...formData, etudiant_id: e.target.value});\n                                    }}\n                                    required\n                                    disabled={editingAbsence}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant</option>\n                                    {etudiants.map((etudiant) => (\n                                        <option key={etudiant.etudiant_id || etudiant.id} value={etudiant.etudiant_id || etudiant.id}>\n                                            {etudiant.nom} {etudiant.prenom ? ` ${etudiant.prenom}` : ''} - {etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe'}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                            <div className=\"form-group\">\n                                <label>Matière (optionnel)</label>\n                                <select\n                                    value={formData.matiere_id}\n                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}\n                                >\n                                    <option value=\"\">Sélectionner une matière</option>\n                                    {matieres.map((matiere) => (\n                                        <option key={matiere.id} value={matiere.id}>\n                                            {matiere.nom}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                            {user?.role === 'admin' && (\n                                <div className=\"form-group\">\n                                    <label>Enseignant (optionnel)</label>\n                                    <select\n                                        value={formData.enseignant_id}\n                                        onChange={(e) => setFormData({...formData, enseignant_id: e.target.value})}\n                                    >\n                                        <option value=\"\">Sélectionner un enseignant</option>\n                                        {enseignants.map((enseignant) => (\n                                            <option key={enseignant.enseignant_id || enseignant.id} value={enseignant.enseignant_id || enseignant.id}>\n                                                {enseignant.nom} {enseignant.prenom ? ` ${enseignant.prenom}` : ''} - {enseignant.specialite || 'Spécialité non définie'}\n                                            </option>\n                                        ))}\n                                    </select>\n                                </div>\n                            )}\n                            <div className=\"form-group\">\n                                <label>Date d'absence</label>\n                                <input\n                                    type=\"date\"\n                                    value={formData.date_absence}\n                                    onChange={(e) => setFormData({...formData, date_absence: e.target.value})}\n                                    required\n                                />\n                            </div>\n                            <div className=\"form-group\">\n                                <label>Justification (optionnel)</label>\n                                <textarea\n                                    value={formData.justification}\n                                    onChange={(e) => setFormData({...formData, justification: e.target.value})}\n                                    placeholder=\"Motif de l'absence...\"\n                                    rows=\"3\"\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px',\n                                        resize: 'vertical'\n                                    }}\n                                />\n                            </div>\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingAbsence ? 'Modifier' : 'Enregistrer'}\n                                </button>\n                                <button \n                                    type=\"button\" \n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingAbsence(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Absences;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB,CAAC,CAAC;;AAE9B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACnB,CAAC,CAAC;EAEFlC,SAAS,CAAC,MAAM;IACZmC,aAAa,CAAC,CAAC;IACf,IAAI,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,OAAO,IAAI,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,YAAY,EAAE;MACvDC,cAAc,CAAC,CAAC;MAChBC,aAAa,CAAC,CAAC;MACf,IAAI,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,OAAO,EAAE;QACxBG,gBAAgB,CAAC,CAAC;MACtB;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,sDAAsD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAC7DT,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEI,QAAQ,CAACK,MAAM,CAAC;MAC1CV,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,QAAQ,CAACE,OAAO,CAAC;;MAE5C;MACA,IAAII,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC9BzC,WAAW,CAACqC,QAAQ,CAACI,IAAI,CAAC;QAC1BT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,QAAQ,CAACI,IAAI,CAACI,MAAM,CAAC;MAC7D,CAAC,MAAM,IAAIR,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACK,KAAK,EAAE;QAC7Cd,OAAO,CAACc,KAAK,CAAC,eAAe,EAAET,QAAQ,CAACI,IAAI,CAACK,KAAK,CAAC;QACnDlD,IAAI,CAACmD,IAAI,CAAC,YAAY,EAAEV,QAAQ,CAACI,IAAI,CAACK,KAAK,EAAE,OAAO,CAAC;QACrD9C,WAAW,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACHgC,OAAO,CAACgB,IAAI,CAAC,iCAAiC,EAAEX,QAAQ,CAACI,IAAI,CAAC;QAC9DzC,WAAW,CAAC,EAAE,CAAC;MACnB;IACJ,CAAC,CAAC,OAAO8C,KAAK,EAAE;MAAA,IAAAG,eAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACZrB,OAAO,CAACc,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAE;QAC/BQ,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBjB,QAAQ,GAAAY,eAAA,GAAEH,KAAK,CAACT,QAAQ,cAAAY,eAAA,uBAAdA,eAAA,CAAgBR,IAAI;QAC9BC,MAAM,GAAAQ,gBAAA,GAAEJ,KAAK,CAACT,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBR,MAAM;QAC9Ba,GAAG,GAAAJ,aAAA,GAAEL,KAAK,CAACU,MAAM,cAAAL,aAAA,uBAAZA,aAAA,CAAcI;MACvB,CAAC,CAAC;;MAEF;MACA,IAAIE,YAAY,GAAG,oCAAoC;MACvD,IAAI,EAAAL,gBAAA,GAAAN,KAAK,CAACT,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBV,MAAM,MAAK,GAAG,EAAE;QAChCe,YAAY,GAAG,uDAAuD;MAC1E,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAAP,KAAK,CAACT,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBX,MAAM,MAAK,GAAG,EAAE;QACvCe,YAAY,GAAG,wCAAwC;MAC3D,CAAC,MAAM,IAAIX,KAAK,CAACY,IAAI,KAAK,eAAe,EAAE;QACvCD,YAAY,GAAG,kDAAkD;MACrE;MAEA7D,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAEU,YAAY,EAAE,OAAO,CAAC;MAC1CzD,WAAW,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM2B,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA;MACA,MAAMQ,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,mEAAmE,CAAC;MAErGN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAE9D,IAAIJ,QAAQ,CAACI,IAAI,CAACkB,OAAO,EAAE;QACvBnD,YAAY,CAAC6B,QAAQ,CAACI,IAAI,CAAClC,SAAS,CAAC;QACrCyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,QAAQ,CAACI,IAAI,CAAClC,SAAS,CAACsC,MAAM,CAAC;QACnEb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,QAAQ,CAACI,IAAI,CAAClC,SAAS,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC,MAAM;QACHyB,OAAO,CAACc,KAAK,CAAC,yBAAyB,EAAET,QAAQ,CAACI,IAAI,CAACK,KAAK,CAAC;QAC7DtC,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,OAAOsC,KAAK,EAAE;MAAA,IAAAc,gBAAA;MACZ5B,OAAO,CAACc,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAE,EAAAc,gBAAA,GAAAd,KAAK,CAACT,QAAQ,cAAAuB,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,KAAIK,KAAK,CAACQ,OAAO,CAAC;MACzE9C,YAAY,CAAC,EAAE,CAAC;IACpB;EACJ,CAAC;EAED,MAAMsB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,iEAAiE,EAAE;QAChGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MACFxB,WAAW,CAAC2B,QAAQ,CAACI,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZd,OAAO,CAACc,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACnE;EACJ,CAAC;EAED,MAAMf,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,uEAAuE,EAAE;QACtGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAEhE,IAAIJ,QAAQ,CAACI,IAAI,CAACkB,OAAO,EAAE;QACvB/C,cAAc,CAACyB,QAAQ,CAACI,IAAI,CAAC9B,WAAW,CAAC;QACzCqB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,QAAQ,CAACI,IAAI,CAAC9B,WAAW,CAACkC,MAAM,CAAC;MAC3E,CAAC,MAAM;QACHb,OAAO,CAACc,KAAK,CAAC,2BAA2B,EAAET,QAAQ,CAACI,IAAI,CAACK,KAAK,CAAC;QAC/DlC,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACZd,OAAO,CAACc,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClElC,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMiD,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA/B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/BD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEb,QAAQ,CAAC;IAC9CY,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,OAAOb,QAAQ,CAACE,WAAW,CAAC;IAChEU,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEb,QAAQ,CAACE,WAAW,CAAC;IAC3DU,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE1B,SAAS,CAAC;IAChDyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE1B,SAAS,CAACsC,MAAM,CAAC;IACrDb,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE1B,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAIA,SAAS,CAACsC,MAAM,KAAK,CAAC,EAAE;MACxBb,OAAO,CAACc,KAAK,CAAC,2BAA2B,CAAC;MAC1ClD,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,wDAAwD,EAAE,OAAO,CAAC;MACtF;IACJ;IACAf,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE1B,SAAS,CAACsC,MAAM,CAAC;;IAErD;IACA,IAAI,CAACzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACE,WAAW,KAAK,EAAE,EAAE;MACtDU,OAAO,CAACc,KAAK,CAAC,yCAAyC,CAAC;MACxDd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEb,QAAQ,CAAC;MAC1CxB,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,uDAAuD,GAAGxC,SAAS,CAACsC,MAAM,GAAG,cAAc,EAAE,OAAO,CAAC;MACzH;IACJ;IAEA,IAAI,CAACzB,QAAQ,CAACK,YAAY,IAAIL,QAAQ,CAACK,YAAY,KAAK,EAAE,EAAE;MACxDO,OAAO,CAACc,KAAK,CAAC,0CAA0C,CAAC;MACzDlD,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,gCAAgC,EAAE,OAAO,CAAC;MAC9D;IACJ;;IAEA;IACA,MAAMiB,SAAS,GAAG;MACd1C,WAAW,EAAE2C,QAAQ,CAAC7C,QAAQ,CAACE,WAAW,CAAC;MAC3CC,UAAU,EAAEH,QAAQ,CAACG,UAAU,GAAG0C,QAAQ,CAAC7C,QAAQ,CAACG,UAAU,CAAC,GAAG,IAAI;MACtEC,aAAa,EAAEJ,QAAQ,CAACI,aAAa,GAAGyC,QAAQ,CAAC7C,QAAQ,CAACI,aAAa,CAAC,GAAG,IAAI;MAC/EC,YAAY,EAAEL,QAAQ,CAACK,YAAY;MACnCC,aAAa,EAAEN,QAAQ,CAACM,aAAa,IAAI;IAC7C,CAAC;IAEDM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE+B,SAAS,CAAC;IAE/C,IAAI;MACA;MACA,MAAM9B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMmB,GAAG,GAAG,sDAAsD;MAClE,MAAMW,MAAM,GAAG7D,cAAc,GAAG,KAAK,GAAG,MAAM;MAC9C,MAAMoC,IAAI,GAAGpC,cAAc,GAAG;QAAE,GAAG2D,SAAS;QAAEG,EAAE,EAAE9D,cAAc,CAAC8D;MAAG,CAAC,GAAGH,SAAS;MAEjFhC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEQ,IAAI,CAAC;MAEjD,MAAMJ,QAAQ,GAAG,MAAM1C,KAAK,CAAC;QACzBuE,MAAM;QACNX,GAAG;QACHd,IAAI;QACJF,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAEhD7C,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,WAAW1C,cAAc,GAAG,UAAU,GAAG,aAAa,cAAc,EAAE,SAAS,CAAC;MACpGD,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8D,SAAS,CAAC,CAAC;MACXzC,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACZvC,OAAO,CAACc,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1Cd,OAAO,CAACc,KAAK,CAAC,oBAAoB,GAAAuB,gBAAA,GAAEvB,KAAK,CAACT,QAAQ,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgB5B,IAAI,CAAC;MACzD7C,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,EAAAuB,gBAAA,GAAAxB,KAAK,CAACT,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBzB,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAM0B,UAAU,GAAIC,OAAO,IAAK;IAC5BnE,iBAAiB,CAACmE,OAAO,CAAC;IAC1BpD,WAAW,CAAC;MACRC,WAAW,EAAEmD,OAAO,CAACnD,WAAW;MAChCC,UAAU,EAAEkD,OAAO,CAAClD,UAAU,IAAI,EAAE;MACpCC,aAAa,EAAEiD,OAAO,CAACjD,aAAa,IAAI,EAAE;MAC1CC,YAAY,EAAEgD,OAAO,CAAChD,YAAY;MAClCC,aAAa,EAAE+C,OAAO,CAAC/C,aAAa,IAAI;IAC5C,CAAC,CAAC;IACFtB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsE,YAAY,GAAG,MAAOP,EAAE,IAAK;IAC/B,MAAMQ,MAAM,GAAG,MAAM/E,IAAI,CAACmD,IAAI,CAAC;MAC3B6B,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA;QACA,MAAMlD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAMzC,KAAK,CAAC0F,MAAM,CAAC,sDAAsD,EAAE;UACvE9C,OAAO,EAAE;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUL,KAAK;UACpC,CAAC;UACDO,IAAI,EAAE;YAAE0B;UAAG;QACf,CAAC,CAAC;QACFvE,IAAI,CAACmD,IAAI,CAAC,WAAW,EAAE,6BAA6B,EAAE,SAAS,CAAC;QAChEpB,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACZd,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BlD,IAAI,CAACmD,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;MACtE;IACJ;EACJ,CAAC;EAED,MAAMqB,SAAS,GAAGA,CAAA,KAAM;IACpB/C,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;IACnB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM4D,gBAAgB,GAAGvF,QAAQ,CAACwF,MAAM,CAACd,OAAO,IAAI;IAAA,IAAAe,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA;IAChD,MAAMC,aAAa,GAAG,EAAAH,qBAAA,GAAAf,OAAO,CAACmB,YAAY,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsBK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,OAAAJ,oBAAA,GACxEhB,OAAO,CAACsB,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,OAAAH,mBAAA,GACrEjB,OAAO,CAACuB,UAAU,cAAAN,mBAAA,uBAAlBA,mBAAA,CAAoBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC;IAExF,MAAMI,aAAa,GAAGlF,YAAY,KAAK,KAAK,IACvBA,YAAY,KAAK,WAAW,IAAI0D,OAAO,CAAC/C,aAAc,IACtDX,YAAY,KAAK,aAAa,IAAI,CAAC0D,OAAO,CAAC/C,aAAc;IAE9E,OAAOiE,aAAa,IAAIM,aAAa;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACd,gBAAgB,CAACzC,MAAM,GAAG1B,YAAY,CAAC;EACpE,MAAMkF,UAAU,GAAG,CAACpF,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMmF,eAAe,GAAGhB,gBAAgB,CAACiB,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAGlF,YAAY,CAAC;EAErF,MAAMqF,qBAAqB,GAAI9E,aAAa,IAAK;IAC7C,IAAIA,aAAa,IAAIA,aAAa,CAAC+E,IAAI,CAAC,CAAC,EAAE;MACvC,oBAAOnH,KAAA,CAAAoH,aAAA;QAAMC,SAAS,EAAC,qBAAqB;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,cAAe,CAAC;IACjE;IACA,oBAAO3H,KAAA,CAAAoH,aAAA;MAAMC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,kBAAmB,CAAC;EACpE,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,CAAAxH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,OAAO,IAAI,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,YAAY;;EAE/E;EACAI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;EACjCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEnC,IAAI,CAAC;EAC1BkC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,CAAC;EACrCI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqF,iBAAiB,CAAC;EACtDtF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElC,QAAQ,CAAC;EACvCiC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElC,QAAQ,CAAC8C,MAAM,CAAC;EAC/C,IAAIyE,iBAAiB,EAAE;IACnBtF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE1B,SAAS,CAACsC,MAAM,CAAC;IAC1Db,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAExB,QAAQ,CAACoC,MAAM,CAAC;IACxDb,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEtB,WAAW,CAACkC,MAAM,CAAC;EACrE;EAEA,IAAI5C,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAoH,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9B3H,KAAA,CAAAoH,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/B3H,KAAA,CAAAoH,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4BAA6B,CAC/B,CAAC;EAEd;EAEA,oBACI3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mCAA2B,CAAC,eAChC3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3H,KAAA,CAAAoH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB3B,gBAAgB,CAACzC,MAAM,EAAC,2BACzB,EAACqD,UAAU,GAAG,CAAC,IAAI,WAAWjF,WAAW,IAAIiF,UAAU,EACrD,CAAC,EAKNoB,iBAAiB,iBACdhI,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,4BAA4B;IACtCY,OAAO,EAAEA,CAAA,KAAMnH,YAAY,CAAC,IAAI,CAAE;IAClCwE,KAAK,EAAC,8GAAwG;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE9G3H,KAAA,CAAAoH,aAAA;IAAKc,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACrC3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,kBAAsB,CAAC,eAC7B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oHAA4D,CAClE,CACD,CAEX,CACJ,CAAC,eAGN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IACIgB,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,sEAAsD;IAClEC,KAAK,EAAE/G,UAAW;IAClBgH,QAAQ,EAAG/D,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAE;IAC/CjB,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3B,CACA,CAAC,eACN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3H,KAAA,CAAAoH,aAAA;IACIkB,KAAK,EAAE7G,YAAa;IACpB8G,QAAQ,EAAG/D,CAAC,IAAK9C,eAAe,CAAC8C,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAE;IACjDjB,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,KAAK;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAA2B,CAAC,eAChD3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,WAAW;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAoB,CAAC,eAC/C3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,aAAa;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAwB,CAChD,CACP,CACJ,CAAC,eACN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3H,KAAA,CAAAoH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB3B,gBAAgB,CAACzC,MAAM,EAAC,2BACvB,CACL,CACJ,CAAC,eAENvD,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB3B,gBAAgB,CAACzC,MAAM,KAAK,CAAC,gBAC1BvD,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3H,KAAA,CAAAoH,aAAA;IAAKc,GAAG,EAAC,iBAAiB;IAACC,GAAG,EAAC,gBAAgB;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClD3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,2BAAyB,CAC3B,CAAC,gBAEN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B3H,KAAA,CAAAoH,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eAEnB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,6BAAqB,CAAC,eAC1B3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4BAAoB,CAAC,EACxBK,iBAAiB,iBAAIhI,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CACxC,CACD,CAAC,eACR3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKX,eAAe,CAACyB,GAAG,CAAEtD,OAAO,iBACzBnF,KAAA,CAAAoH,aAAA;IAAIsB,GAAG,EAAEvD,OAAO,CAACN,EAAG;IAAAyC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASxC,OAAO,CAACmB,YAAqB,CAAC,eACvCtG,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQxC,OAAO,CAACwD,cAAsB,CACrC,CACL,CAAC,eACL3I,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAMwB,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGxC,OAAO,CAACsB,WAAW,IAAI,GACtB,CACN,CAAC,eAELzG,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAQwB,KAAK,EAAE;MAAEK,KAAK,EAAE,SAAS;MAAED,QAAQ,EAAE;IAAM,CAAE;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChDC,UAAU,CAACzC,OAAO,CAAChD,YAAY,CAC5B,CACR,CAAC,eACLnC,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,qBAAqB,CAAC/B,OAAO,CAAC/C,aAAa,CAAM,CAAC,eACvDpC,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKxC,OAAO,CAAC/C,aAAa,gBAClBpC,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,oBAAoB;IAC9B/B,KAAK,EAAEH,OAAO,CAAC/C,aAAc;IAC7BwG,KAAK,EAAE;MACHM,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBT,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDxC,OAAO,CAAC/C,aACP,CAAC,gBAEPpC,KAAA,CAAAoH,aAAA;IAAMwB,KAAK,EAAE;MAAEK,KAAK,EAAE,SAAS;MAAEM,SAAS,EAAE;IAAS,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAElD,CAEV,CAAC,EACJK,iBAAiB,iBACdhI,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B3H,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,oCAAoC;IAC9CY,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAACC,OAAO,CAAE;IACnCG,KAAK,EAAE,yBAAyBH,OAAO,CAACmB,YAAY,OAAOsB,UAAU,CAACzC,OAAO,CAAChD,YAAY,CAAC,GAAGgD,OAAO,CAACsB,WAAW,GAAG,OAAOtB,OAAO,CAACsB,WAAW,EAAE,GAAG,EAAE,EAAG;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExJ3H,KAAA,CAAAoH,aAAA;IAAKc,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtC3H,KAAA,CAAAoH,aAAA;IAAMC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAAC,eAC1C3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,uCAA4B,CAClC,CACD,CAAC,eACT3H,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,qCAAqC;IAC/CY,OAAO,EAAEA,CAAA,KAAM7C,YAAY,CAACD,OAAO,CAACN,EAAE,CAAE;IACxCS,KAAK,EAAE,yCAAyCH,OAAO,CAACmB,YAAY,OAAOsB,UAAU,CAACzC,OAAO,CAAChD,YAAY,CAAC,GAAGgD,OAAO,CAACsB,WAAW,GAAG,OAAOtB,OAAO,CAACsB,WAAW,EAAE,GAAG,EAAE,EAAG;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExK3H,KAAA,CAAAoH,aAAA;IAAKc,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACzC3H,KAAA,CAAAoH,aAAA;IAAMC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,WAAe,CAAC,eAC3C3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,8CAAiC,CACvC,CACD,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CACR,EAGA3B,gBAAgB,CAACzC,MAAM,GAAG1B,YAAY,iBACnC7B,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAChB,EAACZ,UAAU,GAAG,CAAC,EAAC,QAAG,EAACF,IAAI,CAAC2C,GAAG,CAACzC,UAAU,GAAGlF,YAAY,EAAEmE,gBAAgB,CAACzC,MAAM,CAAC,EAAC,OAAK,EAACyC,gBAAgB,CAACzC,MAAM,EAAC,WAC3H,CAAC,eACNvD,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,gBAAgB;IAC1BY,OAAO,EAAEA,CAAA,KAAMrG,cAAc,CAAC6H,IAAI,IAAI5C,IAAI,CAAC6C,GAAG,CAACD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;IAC7DE,QAAQ,EAAEhI,WAAW,KAAK,CAAE;IAAA2F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B,wBAEO,CAAC,EAER,CAAC,GAAGtE,KAAK,CAACuD,UAAU,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAACmB,CAAC,EAAEC,KAAK,KAAK;IACtC,MAAMC,UAAU,GAAGD,KAAK,GAAG,CAAC;IAC5B,IACIC,UAAU,KAAK,CAAC,IAChBA,UAAU,KAAKlD,UAAU,IACxBkD,UAAU,IAAInI,WAAW,GAAG,CAAC,IAAImI,UAAU,IAAInI,WAAW,GAAG,CAAE,EAClE;MACE,oBACI3B,KAAA,CAAAoH,aAAA;QACIsB,GAAG,EAAEoB,UAAW;QAChBzC,SAAS,EAAE,kBAAkB1F,WAAW,KAAKmI,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC1E7B,OAAO,EAAEA,CAAA,KAAMrG,cAAc,CAACkI,UAAU,CAAE;QAAAxC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEzCmC,UACG,CAAC;IAEjB,CAAC,MAAM,IACHA,UAAU,KAAKnI,WAAW,GAAG,CAAC,IAC9BmI,UAAU,KAAKnI,WAAW,GAAG,CAAC,EAChC;MACE,oBAAO3B,KAAA,CAAAoH,aAAA;QAAMsB,GAAG,EAAEoB,UAAW;QAACzC,SAAS,EAAC,qBAAqB;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,KAAS,CAAC;IAC5E;IACA,OAAO,IAAI;EACf,CAAC,CAAC,eAEF3H,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,gBAAgB;IAC1BY,OAAO,EAAEA,CAAA,KAAMrG,cAAc,CAAC6H,IAAI,IAAI5C,IAAI,CAAC2C,GAAG,CAACC,IAAI,GAAG,CAAC,EAAE7C,UAAU,CAAC,CAAE;IACtE+C,QAAQ,EAAEhI,WAAW,KAAKiF,UAAW;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,gBAEO,CACP,CACJ,CAER,CAAC,EAGL9G,SAAS,IAAImH,iBAAiB,iBAC3BhI,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK5G,cAAc,GAAG,qBAAqB,GAAG,kBAAuB,CAAC,eACtEf,KAAA,CAAAoH,aAAA;IACIC,SAAS,EAAC,WAAW;IACrBY,OAAO,EAAEA,CAAA,KAAM;MACXnH,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8D,SAAS,CAAC,CAAC;IACf,CAAE;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3H,KAAA,CAAAoH,aAAA;IAAKc,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACN3H,KAAA,CAAAoH,aAAA;IAAM2C,QAAQ,EAAExF,YAAa;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,aAAe,CAAC,eACvB3H,KAAA,CAAAoH,aAAA;IACIkB,KAAK,EAAExG,QAAQ,CAACE,WAAY;IAC5BuG,QAAQ,EAAG/D,CAAC,IAAK;MAAA,IAAAwF,qBAAA;MACbtH,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChCD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6B,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAC;MACnD5F,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAO6B,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAC;MAC3C5F,OAAO,CAACC,GAAG,CAAC,sBAAsB,GAAAqH,qBAAA,GAAExF,CAAC,CAACgE,MAAM,CAACyB,eAAe,CAAC,CAAC,CAAC,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6BzE,IAAI,CAAC;MACtExD,WAAW,CAAC;QAAC,GAAGD,QAAQ;QAAEE,WAAW,EAAEwC,CAAC,CAACgE,MAAM,CAACF;MAAK,CAAC,CAAC;IAC3D,CAAE;IACF4B,QAAQ;IACRP,QAAQ,EAAE5I,cAAe;IAAAuG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,EAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjD1G,SAAS,CAACwH,GAAG,CAAE0B,QAAQ,iBACpBnK,KAAA,CAAAoH,aAAA;IAAQsB,GAAG,EAAEyB,QAAQ,CAACnI,WAAW,IAAImI,QAAQ,CAACtF,EAAG;IAACyD,KAAK,EAAE6B,QAAQ,CAACnI,WAAW,IAAImI,QAAQ,CAACtF,EAAG;IAAAyC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxFwC,QAAQ,CAACC,GAAG,EAAC,GAAC,EAACD,QAAQ,CAACE,MAAM,GAAG,IAAIF,QAAQ,CAACE,MAAM,EAAE,GAAG,EAAE,EAAC,KAAG,EAACF,QAAQ,CAACG,UAAU,IAAIH,QAAQ,CAACI,UAAU,IAAI,aAC3G,CACX,CACG,CACP,CAAC,eACNvK,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA0B,CAAC,eAClC3H,KAAA,CAAAoH,aAAA;IACIkB,KAAK,EAAExG,QAAQ,CAACG,UAAW;IAC3BsG,QAAQ,EAAG/D,CAAC,IAAKzC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,UAAU,EAAEuC,CAAC,CAACgE,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExE3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,EAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDxG,QAAQ,CAACsH,GAAG,CAAE+B,OAAO,iBAClBxK,KAAA,CAAAoH,aAAA;IAAQsB,GAAG,EAAE8B,OAAO,CAAC3F,EAAG;IAACyD,KAAK,EAAEkC,OAAO,CAAC3F,EAAG;IAAAyC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC6C,OAAO,CAACJ,GACL,CACX,CACG,CACP,CAAC,EACL,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,OAAO,iBACnBtC,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CAAC,eACrC3H,KAAA,CAAAoH,aAAA;IACIkB,KAAK,EAAExG,QAAQ,CAACI,aAAc;IAC9BqG,QAAQ,EAAG/D,CAAC,IAAKzC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,aAAa,EAAEsC,CAAC,CAACgE,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3E3H,KAAA,CAAAoH,aAAA;IAAQkB,KAAK,EAAC,EAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAAkC,CAAC,EACnDtG,WAAW,CAACoH,GAAG,CAAEgC,UAAU,iBACxBzK,KAAA,CAAAoH,aAAA;IAAQsB,GAAG,EAAE+B,UAAU,CAACvI,aAAa,IAAIuI,UAAU,CAAC5F,EAAG;IAACyD,KAAK,EAAEmC,UAAU,CAACvI,aAAa,IAAIuI,UAAU,CAAC5F,EAAG;IAAAyC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpG8C,UAAU,CAACL,GAAG,EAAC,GAAC,EAACK,UAAU,CAACJ,MAAM,GAAG,IAAII,UAAU,CAACJ,MAAM,EAAE,GAAG,EAAE,EAAC,KAAG,EAACI,UAAU,CAACC,UAAU,IAAI,wBAC5F,CACX,CACG,CACP,CACR,eACD1K,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,gBAAqB,CAAC,eAC7B3H,KAAA,CAAAoH,aAAA;IACIgB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAExG,QAAQ,CAACK,YAAa;IAC7BoG,QAAQ,EAAG/D,CAAC,IAAKzC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,YAAY,EAAEqC,CAAC,CAACgE,MAAM,CAACF;IAAK,CAAC,CAAE;IAC1E4B,QAAQ;IAAA5C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CACA,CAAC,eACN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAoH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,2BAAgC,CAAC,eACxC3H,KAAA,CAAAoH,aAAA;IACIkB,KAAK,EAAExG,QAAQ,CAACM,aAAc;IAC9BmG,QAAQ,EAAG/D,CAAC,IAAKzC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEM,aAAa,EAAEoC,CAAC,CAACgE,MAAM,CAACF;IAAK,CAAC,CAAE;IAC3ED,WAAW,EAAC,uBAAuB;IACnCsC,IAAI,EAAC,GAAG;IACR/B,KAAK,EAAE;MACHgC,KAAK,EAAE,MAAM;MACb/B,OAAO,EAAE,MAAM;MACfgC,MAAM,EAAE,mBAAmB;MAC3B9B,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChB8B,MAAM,EAAE;IACZ,CAAE;IAAAxD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACN3H,KAAA,CAAAoH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3H,KAAA,CAAAoH,aAAA;IAAQgB,IAAI,EAAC,QAAQ;IAACf,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C5G,cAAc,GAAG,UAAU,GAAG,aAC3B,CAAC,eACTf,KAAA,CAAAoH,aAAA;IACIgB,IAAI,EAAC,QAAQ;IACbf,SAAS,EAAC,mBAAmB;IAC7BY,OAAO,EAAEA,CAAA,KAAM;MACXnH,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8D,SAAS,CAAC,CAAC;IACf,CAAE;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,SAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAepH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}