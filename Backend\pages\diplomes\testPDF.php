<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

try {
    // Test 1: Vérifier la structure de la table Etudiants
    $stmt = $pdo->prepare("DESCRIBE Etudiants");
    $stmt->execute();
    $etudiants_structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Test 2: Vérifier la structure de la table Diplomes
    $stmt = $pdo->prepare("DESCRIBE Diplomes");
    $stmt->execute();
    $diplomes_structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Test 3: Tester la requête corrigée
    $stmt = $pdo->prepare("
        SELECT 
            d.*,
            u.nom as etudiant_nom,
            u.email as etudiant_email,
            u.id as numero_etudiant,
            g.nom as groupe_nom,
            c.nom as classe_nom,
            f.nom as filiere_nom,
            n.nom as niveau_nom,
            DATE_FORMAT(d.date_obtention, '%d/%m/%Y') as date_obtention_fr
        FROM Diplomes d
        JOIN Etudiants e ON d.etudiant_id = e.id
        JOIN Utilisateurs u ON e.utilisateur_id = u.id
        LEFT JOIN Groupes g ON e.groupe_id = g.id
        LEFT JOIN Classes c ON g.classe_id = c.id
        LEFT JOIN Filieres f ON c.filiere_id = f.id
        LEFT JOIN Niveaux n ON c.niveau_id = n.id
        LIMIT 1
    ");
    $stmt->execute();
    $test_diplome = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Test 4: Compter les diplômes
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM Diplomes");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Tests réussis - Problème corrigé',
        'data' => [
            'etudiants_structure' => $etudiants_structure,
            'diplomes_structure' => $diplomes_structure,
            'total_diplomes' => $count['total'],
            'test_diplome' => $test_diplome,
            'correction' => 'Utilisation de u.id comme numero_etudiant au lieu de e.numero_etudiant'
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => 'Erreur SQL: ' . $e->getMessage(),
        'solution' => 'La colonne numero_etudiant n\'existe pas dans la table Etudiants'
    ]);
}
?>
