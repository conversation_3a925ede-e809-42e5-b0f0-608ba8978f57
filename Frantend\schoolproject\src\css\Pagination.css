/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 10px;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 40px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: bold;
}

.pagination-btn.disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.page-number {
    min-width: 36px;
}

.pagination-summary {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .pagination-controls {
        order: 2;
    }
    
    .pagination-info,
    .pagination-summary {
        order: 1;
    }
    
    .pagination-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .pagination-btn {
        margin: 2px;
    }
}
