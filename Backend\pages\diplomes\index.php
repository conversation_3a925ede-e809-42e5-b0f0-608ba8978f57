<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Simulation utilisateur pour test
$user_info = [
    'id' => 1,
    'role' => 'Admin'
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($pdo, $user_info) {
    $role = $user_info['role'];
    $user_id = $user_info['id'];
    
    if ($role === 'Admin') {
        // Admin peut voir tous les diplômes
        $stmt = $pdo->prepare("
            SELECT d.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Diplomes d 
            JOIN Etudiants e ON d.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            ORDER BY d.date_obtention DESC
        ");
        $stmt->execute();
    } elseif ($role === 'Parent') {
        // Parent peut voir les diplômes de ses enfants
        $stmt = $pdo->prepare("
            SELECT d.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Diplomes d 
            JOIN Etudiants e ON d.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            JOIN Parent_Etudiant pe ON e.id = pe.etudiant_id 
            JOIN Parents p ON pe.parent_id = p.id 
            WHERE p.utilisateur_id = ? 
            ORDER BY d.date_obtention DESC
        ");
        $stmt->execute([$user_id]);
    } elseif ($role === 'Etudiant') {
        // Étudiant peut voir ses propres diplômes
        $stmt = $pdo->prepare("
            SELECT d.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Diplomes d 
            JOIN Etudiants e ON d.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            WHERE e.utilisateur_id = ? 
            ORDER BY d.date_obtention DESC
        ");
        $stmt->execute([$user_id]);
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
        return;
    }
    
    $diplomes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($diplomes);
}

function handlePost($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut créer des diplômes']);
        return;
    }
    
    if (!isset($input['etudiant_id'], $input['titre'], $input['date_obtention'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes']);
        return;
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO Diplomes (etudiant_id, titre, date_obtention) 
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([
        $input['etudiant_id'],
        $input['titre'],
        $input['date_obtention']
    ]);
    
    echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
}

function handlePut($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut modifier des diplômes']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $fields = [];
    $values = [];
    
    if (isset($input['titre'])) {
        $fields[] = 'titre = ?';
        $values[] = $input['titre'];
    }
    if (isset($input['date_obtention'])) {
        $fields[] = 'date_obtention = ?';
        $values[] = $input['date_obtention'];
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à modifier']);
        return;
    }
    
    $values[] = $input['id'];
    $sql = "UPDATE Diplomes SET " . implode(', ', $fields) . " WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($values);
    
    echo json_encode(['success' => true]);
}

function handleDelete($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut supprimer des diplômes']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM Diplomes WHERE id = ?");
    $stmt->execute([$input['id']]);
    
    echo json_encode(['success' => true]);
}

?>
