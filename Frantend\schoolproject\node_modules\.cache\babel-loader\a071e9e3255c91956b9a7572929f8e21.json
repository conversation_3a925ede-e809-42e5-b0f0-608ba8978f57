{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\Auth\\\\Login.js\";\nimport React, { useState, useContext } from \"react\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport { AuthContext } from \"../context/AuthContext\";\nimport { FaGraduationCap, FaUser, FaLock, FaEye, FaEyeSlash } from \"react-icons/fa\";\nimport \"../css/Login.css\";\nfunction Login() {\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [message, setMessage] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const navigate = useNavigate();\n  const {\n    login\n  } = useContext(AuthContext);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const response = await fetch(\"http://localhost/Project_PFE/Backend/Auth/login.php\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setMessage(\"✅ Connexion réussie !\");\n\n        // Stocker les informations utilisateur complètes\n        const userInfo = {\n          ...data.user,\n          // Inclut id, nom, prenom, email, role\n          token: data.token\n        };\n        console.log('🔍 Informations utilisateur stockées:', userInfo);\n\n        // Utiliser le contexte d'authentification\n        login(userInfo);\n\n        // Redirection selon le rôle\n        setTimeout(() => {\n          switch (data.role.toLowerCase()) {\n            case 'enseignant':\n              navigate('/dashboard/enseignant');\n              break;\n            case 'etudiant':\n            case 'élève':\n              navigate('/dashboard/etudiant');\n              break;\n            case 'parent':\n              navigate('/dashboard/parent');\n              break;\n            case 'responsable':\n            case 'admin':\n              navigate('/dashboard/responsable');\n              break;\n            default:\n              navigate('/dashboard');\n          }\n        }, 1500);\n      } else {\n        setMessage(\"❌ Email ou mot de passe incorrect\");\n      }\n    } catch (error) {\n      setMessage(\"⚠️ Problème de connexion au serveur\");\n      console.error(\"Erreur :\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"login-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }\n  }, \"Connexion\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"email\",\n    placeholder: \"Email\",\n    value: email,\n    onChange: e => setEmail(e.target.value),\n    required: true,\n    disabled: isLoading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"password\",\n    placeholder: \"Mot de passe\",\n    value: password,\n    onChange: e => setPassword(e.target.value),\n    required: true,\n    disabled: isLoading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    disabled: isLoading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 9\n    }\n  }, isLoading ? \"Connexion en cours...\" : \"Se connecter\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }\n  }, message))));\n}\nexport default Login;", "map": {"version": 3, "names": ["React", "useState", "useContext", "useNavigate", "Link", "AuthContext", "FaGraduationCap", "FaUser", "FaLock", "FaEye", "FaEyeSlash", "<PERSON><PERSON>", "email", "setEmail", "password", "setPassword", "message", "setMessage", "isLoading", "setIsLoading", "navigate", "login", "handleSubmit", "e", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "success", "userInfo", "user", "token", "console", "log", "setTimeout", "role", "toLowerCase", "error", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "disabled"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/Auth/Login.js"], "sourcesContent": ["import React, { useState, useContext } from \"react\";\r\nimport { useNavigate, Link } from \"react-router-dom\";\r\nimport { AuthContext } from \"../context/AuthContext\";\r\nimport { FaGraduationCap, FaUser, FaLock, <PERSON>a<PERSON>ye, FaEyeSlash } from \"react-icons/fa\";\r\nimport \"../css/Login.css\"; \r\n\r\nfunction Login() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [message, setMessage] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const navigate = useNavigate();\r\n  const { login } = useContext(AuthContext);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await fetch(\"http://localhost/Project_PFE/Backend/Auth/login.php\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ email, password }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setMessage(\"✅ Connexion réussie !\");\r\n\r\n        // Stocker les informations utilisateur complètes\r\n        const userInfo = {\r\n          ...data.user,  // Inclut id, nom, prenom, email, role\r\n          token: data.token\r\n        };\r\n\r\n        console.log('🔍 Informations utilisateur stockées:', userInfo);\r\n\r\n        // Utiliser le contexte d'authentification\r\n        login(userInfo);\r\n\r\n        // Redirection selon le rôle\r\n        setTimeout(() => {\r\n          switch(data.role.toLowerCase()) {\r\n            case 'enseignant':\r\n              navigate('/dashboard/enseignant');\r\n              break;\r\n            case 'etudiant':\r\n            case 'élève':\r\n              navigate('/dashboard/etudiant');\r\n              break;\r\n            case 'parent':\r\n              navigate('/dashboard/parent');\r\n              break;\r\n            case 'responsable':\r\n            case 'admin':\r\n              navigate('/dashboard/responsable');\r\n              break;\r\n            default:\r\n              navigate('/dashboard');\r\n          }\r\n        }, 1500);\r\n\r\n      } else {\r\n        setMessage(\"❌ Email ou mot de passe incorrect\");\r\n      }\r\n    } catch (error) {\r\n      setMessage(\"⚠️ Problème de connexion au serveur\");\r\n      console.error(\"Erreur :\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      \r\n    <div className=\"container\">\r\n      <form onSubmit={handleSubmit}>\r\n        <h2>Connexion</h2>\r\n        <input\r\n          type=\"email\"\r\n          placeholder=\"Email\"\r\n          value={email}\r\n          onChange={(e) => setEmail(e.target.value)}\r\n          required\r\n          disabled={isLoading}\r\n        />\r\n        <input\r\n          type=\"password\"\r\n          placeholder=\"Mot de passe\"\r\n          value={password}\r\n          onChange={(e) => setPassword(e.target.value)}\r\n          required\r\n          disabled={isLoading}\r\n        />\r\n        <button type=\"submit\" disabled={isLoading}>\r\n          {isLoading ? \"Connexion en cours...\" : \"Se connecter\"}\r\n        </button>\r\n        <p className=\"message\">{message}</p>\r\n      </form>\r\n    </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AACnF,OAAO,kBAAkB;AAEzB,SAASC,KAAKA,CAAA,EAAG;EACf,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAM,CAAC,GAAGnB,UAAU,CAACG,WAAW,CAAC;EAEzC,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBL,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,qDAAqD,EAAE;QAClFC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnB,KAAK;UAAEE;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBjB,UAAU,CAAC,uBAAuB,CAAC;;QAEnC;QACA,MAAMkB,QAAQ,GAAG;UACf,GAAGH,IAAI,CAACI,IAAI;UAAG;UACfC,KAAK,EAAEL,IAAI,CAACK;QACd,CAAC;QAEDC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEJ,QAAQ,CAAC;;QAE9D;QACAd,KAAK,CAACc,QAAQ,CAAC;;QAEf;QACAK,UAAU,CAAC,MAAM;UACf,QAAOR,IAAI,CAACS,IAAI,CAACC,WAAW,CAAC,CAAC;YAC5B,KAAK,YAAY;cACftB,QAAQ,CAAC,uBAAuB,CAAC;cACjC;YACF,KAAK,UAAU;YACf,KAAK,OAAO;cACVA,QAAQ,CAAC,qBAAqB,CAAC;cAC/B;YACF,KAAK,QAAQ;cACXA,QAAQ,CAAC,mBAAmB,CAAC;cAC7B;YACF,KAAK,aAAa;YAClB,KAAK,OAAO;cACVA,QAAQ,CAAC,wBAAwB,CAAC;cAClC;YACF;cACEA,QAAQ,CAAC,YAAY,CAAC;UAC1B;QACF,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,MAAM;QACLH,UAAU,CAAC,mCAAmC,CAAC;MACjD;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACd1B,UAAU,CAAC,qCAAqC,CAAC;MACjDqB,OAAO,CAACK,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnB,KAAA,CAAA4C,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhCnD,KAAA,CAAA4C,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBnD,KAAA,CAAA4C,aAAA;IAAMQ,QAAQ,EAAE9B,YAAa;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BnD,KAAA,CAAA4C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,WAAa,CAAC,eAClBnD,KAAA,CAAA4C,aAAA;IACES,IAAI,EAAC,OAAO;IACZC,WAAW,EAAC,OAAO;IACnBC,KAAK,EAAE3C,KAAM;IACb4C,QAAQ,EAAGjC,CAAC,IAAKV,QAAQ,CAACU,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;IAC1CG,QAAQ;IACRC,QAAQ,EAAEzC,SAAU;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrB,CAAC,eACFnD,KAAA,CAAA4C,aAAA;IACES,IAAI,EAAC,UAAU;IACfC,WAAW,EAAC,cAAc;IAC1BC,KAAK,EAAEzC,QAAS;IAChB0C,QAAQ,EAAGjC,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;IAC7CG,QAAQ;IACRC,QAAQ,EAAEzC,SAAU;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrB,CAAC,eACFnD,KAAA,CAAA4C,aAAA;IAAQS,IAAI,EAAC,QAAQ;IAACM,QAAQ,EAAEzC,SAAU;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvCjC,SAAS,GAAG,uBAAuB,GAAG,cACjC,CAAC,eACTlB,KAAA,CAAA4C,aAAA;IAAGC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEnC,OAAW,CAC/B,CACH,CACA,CAAC;AAEV;AAEA,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}