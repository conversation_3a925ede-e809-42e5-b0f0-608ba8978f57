import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { FaGraduationCap, FaUsers, FaBookOpen, FaChalkboardTeacher, FaMoon, FaSun } from 'react-icons/fa';

const Homepage = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleTheme = () => setIsDarkMode(!isDarkMode);

  const styles = {
    container: {
      minHeight: '100vh',
      backgroundColor: isDarkMode ? '#1b1b2f' : '#eaebed',
      color: isDarkMode ? '#f0f0f0' : '#333',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      transition: 'background-color 0.5s ease',
    },
    header: {
      textAlign: 'center',
      color: isDarkMode ? '#01a7c2' : '#006989',
      marginBottom: '50px',
    },
    title: {
      fontSize: '3.5rem',
      fontWeight: 'bold',
      marginBottom: '20px',
      color: isDarkMode ? '#caf0f8' : '#007090',
      textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
    },
    subtitle: {
      fontSize: '1.5rem',
      marginBottom: '10px',
      color: '#01a7c2',
      opacity: 0.95,
    },
    description: {
      fontSize: '1.1rem',
      maxWidth: '800px',
      lineHeight: '1.6',
      opacity: 0.9,
    },
    featuresSection: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '30px',
      maxWidth: '1200px',
      width: '100%',
      marginBottom: '50px',
    },
    featureCard: {
      backgroundColor: isDarkMode ? '#2e2e3a' : '#f5f7f9',
      borderRadius: '15px',
      padding: '30px',
      textAlign: 'center',
      color: '#006989',
      border: '1px solid #d6dee3',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      cursor: 'pointer',
      boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
    },
    featureIcon: {
      fontSize: '3rem',
      marginBottom: '20px',
      color: '#01a7c2',
    },
    featureTitle: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      marginBottom: '15px',
      color: '#007090',
    },
    featureDescription: {
      fontSize: '1rem',
      lineHeight: '1.5',
      opacity: 0.95,
      color: isDarkMode ? '#f0f0f0' : '#333',
    },
    ctaSection: {
      textAlign: 'center',
      color: '#006989',
    },
    ctaTitle: {
      fontSize: '2rem',
      marginBottom: '30px',
      fontWeight: 'bold',
    },
    buttonContainer: {
      display: 'flex',
      gap: '20px',
      justifyContent: 'center',
      flexWrap: 'wrap',
    },
    button: {
      padding: '15px 30px',
      fontSize: '1.1rem',
      fontWeight: 'bold',
      border: 'none',
      borderRadius: '50px',
      cursor: 'pointer',
      textDecoration: 'none',
      display: 'inline-block',
      transition: 'all 0.3s ease',
      minWidth: '150px',
    
    },
    primaryButton: {
      backgroundColor: '#01a7c2',
      color: 'white',
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      color: '#007090',
      border: '2px solid #007090',
    },
    toggleBtn: {
      position: 'absolute',
      top: 20,
      right: 20,
      width: '11%',
      backgroundColor: isDarkMode ? '#274c77' : '#caf0f8',
      color: isDarkMode ? '#caf0f8' : '#007090',
      border: 'none',
      padding: '10px 15px',
      borderRadius: '30px',
      cursor: 'pointer',
      fontWeight: 'bold',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
  };

  const features = [
    {
      icon: <FaChalkboardTeacher />,
      title: "Espace Enseignant",
      description: "Gérez vos cours, suivez la présence des élèves et créez des rapports détaillés.",
    },
    {
      icon: <FaGraduationCap />,
      title: "Espace Élève",
      description: "Consultez votre emploi du temps, vos devoirs et suivez vos résultats scolaires.",
    },
    {
      icon: <FaUsers />,
      title: "Espace Parent",
      description: "Suivez la progression de vos enfants et communiquez avec les enseignants.",
    },
    {
      icon: <FaBookOpen />,
      title: "Gestion Administrative",
      description: "Interface complète pour la gestion de l'école et de tous ses utilisateurs.",
    },
  ];

  return (
    <div style={styles.container}>
      <button onClick={toggleTheme} style={styles.toggleBtn}>
        {isDarkMode ? <FaSun /> : <FaMoon />}
        {isDarkMode ? 'Mode Jour' : 'Mode Nuit'}
      </button>

      <div style={styles.header}>
        <h1 style={styles.title}>
          <FaGraduationCap style={{ marginRight: '20px' }} />
          École Moderne
        </h1>
        <h2 style={styles.subtitle}>Système de Gestion Scolaire Intelligent</h2>
        <p style={styles.description}>
          Bienvenue dans notre plateforme de gestion scolaire moderne qui connecte enseignants, 
          élèves, parents et administration dans un environnement numérique intuitif et efficace.
        </p>
      </div>

      <div style={styles.featuresSection}>
        {features.map((feature, index) => (
          <div
            key={index}
            style={styles.featureCard}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-10px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.2)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            <div style={styles.featureIcon}>{feature.icon}</div>
            <h3 style={styles.featureTitle}>{feature.title}</h3>
            <p style={styles.featureDescription}>{feature.description}</p>
          </div>
        ))}
      </div>

      <div style={styles.ctaSection}>
        <h2 style={styles.ctaTitle}>Prêt à commencer ?</h2>
        <div style={styles.buttonContainer}>
          <Link
            to="/login"
            style={{ ...styles.button, ...styles.primaryButton }}
          >
            Se Connecter
          </Link>
          <Link
            to="/registers"
            style={{ ...styles.button, ...styles.secondaryButton }}
          >
            S'inscrire
          </Link>
        </div>
      </div>

      {/* Diagnostic API temporaire */}
     
    </div>
  );
};

export default Homepage;
