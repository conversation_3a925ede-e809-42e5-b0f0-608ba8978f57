<?php
/**
 * Configuration de la base de données
 * Classe Database pour la connexion PDO
 */

class Database {
    // Paramètres de connexion à la base de données
    private $host = "localhost";
    private $db_name = "school_management"; // Nom de votre base de données
    private $username = "root";             // Nom d'utilisateur MySQL
    private $password = "";                 // Mot de passe MySQL (vide par défaut avec Laragon)
    private $charset = "utf8mb4";
    
    public $conn;
    
    /**
     * Obtenir la connexion à la base de données
     * @return PDO|null
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            // DSN (Data Source Name) pour MySQL
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            // Options PDO pour une meilleure sécurité et performance
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            // Création de la connexion PDO
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
            // Log de succès (optionnel)
            error_log("✅ Connexion à la base de données réussie");
            
        } catch(PDOException $exception) {
            // Log de l'erreur
            error_log("❌ Erreur de connexion à la base de données: " . $exception->getMessage());
            
            // En développement, afficher l'erreur
            if (defined('DEBUG') && DEBUG === true) {
                echo "Erreur de connexion: " . $exception->getMessage();
            }
        }
        
        return $this->conn;
    }
    
    /**
     * Fermer la connexion
     */
    public function closeConnection() {
        $this->conn = null;
    }
    
    /**
     * Tester la connexion à la base de données
     * @return bool
     */
    public function testConnection() {
        $conn = $this->getConnection();
        if ($conn !== null) {
            try {
                // Test simple avec une requête
                $stmt = $conn->query("SELECT 1");
                return $stmt !== false;
            } catch (PDOException $e) {
                error_log("❌ Test de connexion échoué: " . $e->getMessage());
                return false;
            }
        }
        return false;
    }
    
    /**
     * Obtenir les informations de la base de données
     * @return array
     */
    public function getDatabaseInfo() {
        $conn = $this->getConnection();
        if ($conn !== null) {
            try {
                $info = [];
                
                // Version MySQL
                $stmt = $conn->query("SELECT VERSION() as version");
                $result = $stmt->fetch();
                $info['mysql_version'] = $result['version'];
                
                // Nom de la base de données
                $info['database_name'] = $this->db_name;
                
                // Charset
                $stmt = $conn->query("SELECT @@character_set_database as charset");
                $result = $stmt->fetch();
                $info['charset'] = $result['charset'];
                
                // Liste des tables
                $stmt = $conn->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $info['tables'] = $tables;
                $info['table_count'] = count($tables);
                
                return $info;
            } catch (PDOException $e) {
                error_log("❌ Erreur lors de la récupération des infos DB: " . $e->getMessage());
                return ['error' => $e->getMessage()];
            }
        }
        return ['error' => 'Connexion impossible'];
    }
}

/**
 * Configuration globale pour le debug
 * Définir DEBUG à true en développement
 */
define('DEBUG', true);

/**
 * Fonction utilitaire pour obtenir une connexion rapide
 * @return PDO|null
 */
function getDbConnection() {
    $database = new Database();
    return $database->getConnection();
}

/**
 * Fonction pour exécuter une requête simple
 * @param string $query
 * @param array $params
 * @return array|false
 */
function executeQuery($query, $params = []) {
    try {
        $db = getDbConnection();
        if ($db) {
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        error_log("❌ Erreur requête: " . $e->getMessage());
        if (DEBUG) {
            echo "Erreur SQL: " . $e->getMessage();
        }
    }
    return false;
}

/**
 * Fonction pour exécuter une requête d'insertion/mise à jour
 * @param string $query
 * @param array $params
 * @return bool|int
 */
function executeUpdate($query, $params = []) {
    try {
        $db = getDbConnection();
        if ($db) {
            $stmt = $db->prepare($query);
            $result = $stmt->execute($params);
            
            // Retourner l'ID du dernier insert si c'est un INSERT
            if (stripos($query, 'INSERT') === 0) {
                return $db->lastInsertId();
            }
            
            return $result;
        }
    } catch (PDOException $e) {
        error_log("❌ Erreur update: " . $e->getMessage());
        if (DEBUG) {
            echo "Erreur SQL: " . $e->getMessage();
        }
    }
    return false;
}

// Test automatique de la connexion au chargement du fichier
if (DEBUG) {
    $database = new Database();
    if ($database->testConnection()) {
        error_log("✅ Configuration database.php chargée avec succès");
    } else {
        error_log("❌ Problème de configuration database.php");
    }
}
?>
