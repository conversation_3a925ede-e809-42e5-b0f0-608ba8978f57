<?php
/**
 * Script de test pour l'API Niveaux
 * Utilisation: php test_niveau_api.php
 */

echo "🧪 Test de l'API Niveaux\n";
echo "========================\n\n";

// Configuration
$baseUrl = 'http://localhost/Project_PFE/Backend/pages/niveaux/niveau.php';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer test-token'
    ]);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: GET - Récupérer tous les niveaux
echo "1️⃣ Test GET - Récupérer tous les niveaux\n";
$response = makeRequest($baseUrl, 'GET');
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && is_array($response['data'])) {
    echo "✅ Succès: " . count($response['data']) . " niveaux trouvés\n";
} else {
    echo "❌ Erreur: Réponse invalide\n";
}
echo "\n";

// Test 2: POST - Créer un nouveau niveau
echo "2️⃣ Test POST - Créer un nouveau niveau\n";
$testData = ['nom' => 'Test Niveau ' . date('H:i:s')];
$response = makeRequest($baseUrl, 'POST', $testData);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === true) {
    echo "✅ Succès: Niveau créé avec ID " . $response['data']['id'] . "\n";
    $testId = $response['data']['id'];
} else {
    echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    $testId = null;
}
echo "\n";

// Test 3: PUT - Modifier le niveau créé
if ($testId) {
    echo "3️⃣ Test PUT - Modifier le niveau créé\n";
    $updateData = ['id' => $testId, 'nom' => 'Test Niveau Modifié ' . date('H:i:s')];
    $response = makeRequest($baseUrl, 'PUT', $updateData);
    echo "Code HTTP: " . $response['code'] . "\n";
    echo "Réponse: " . $response['body'] . "\n";
    if ($response['data'] && $response['data']['success'] === true) {
        echo "✅ Succès: Niveau modifié\n";
    } else {
        echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    }
    echo "\n";
}

// Test 4: DELETE - Supprimer le niveau créé
if ($testId) {
    echo "4️⃣ Test DELETE - Supprimer le niveau créé\n";
    $deleteData = ['id' => $testId];
    $response = makeRequest($baseUrl, 'DELETE', $deleteData);
    echo "Code HTTP: " . $response['code'] . "\n";
    echo "Réponse: " . $response['body'] . "\n";
    if ($response['data'] && $response['data']['success'] === true) {
        echo "✅ Succès: Niveau supprimé\n";
    } else {
        echo "❌ Erreur: " . ($response['data']['error'] ?? 'Erreur inconnue') . "\n";
    }
    echo "\n";
}

// Test 5: Validation des erreurs
echo "5️⃣ Test de validation - POST sans nom\n";
$response = makeRequest($baseUrl, 'POST', ['nom' => '']);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['data'] && $response['data']['success'] === false) {
    echo "✅ Succès: Validation fonctionne - " . $response['data']['error'] . "\n";
} else {
    echo "❌ Erreur: Validation ne fonctionne pas\n";
}
echo "\n";

// Test 6: Test de méthode non autorisée
echo "6️⃣ Test PATCH - Méthode non autorisée\n";
$response = makeRequest($baseUrl, 'PATCH', ['test' => 'data']);
echo "Code HTTP: " . $response['code'] . "\n";
echo "Réponse: " . $response['body'] . "\n";
if ($response['code'] == 405) {
    echo "✅ Succès: Méthode non autorisée correctement gérée\n";
} else {
    echo "❌ Erreur: Méthode non autorisée mal gérée\n";
}
echo "\n";

echo "🏁 Tests terminés!\n";
echo "==================\n";
echo "Vérifiez les logs d'erreur PHP pour plus de détails.\n";
?>
