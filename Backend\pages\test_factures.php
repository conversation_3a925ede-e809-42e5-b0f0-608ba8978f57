<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Test 1: Compter les étudiants
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_etudiants FROM Etudiants");
        $stmt->execute();
        $etudiants_count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Test 2: Compter les factures
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_factures FROM Factures");
        $stmt->execute();
        $factures_count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Test 3: Récupérer quelques étudiants
        $stmt = $pdo->prepare("
            SELECT e.id, u.nom, u.email 
            FROM Etudiants e 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LIMIT 5
        ");
        $stmt->execute();
        $sample_etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Test 4: Récupérer quelques factures
        $stmt = $pdo->prepare("
            SELECT f.*, u.nom as etudiant_nom 
            FROM Factures f 
            LEFT JOIN Etudiants e ON f.etudiant_id = e.id 
            LEFT JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LIMIT 5
        ");
        $stmt->execute();
        $sample_factures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Tests de la base de données réussis',
            'data' => [
                'total_etudiants' => $etudiants_count['total_etudiants'],
                'total_factures' => $factures_count['total_factures'],
                'sample_etudiants' => $sample_etudiants,
                'sample_factures' => $sample_factures
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Créer des données de test
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['create_test_data']) && $input['create_test_data'] === true) {
            // Vérifier s'il y a déjà des étudiants
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM Etudiants");
            $stmt->execute();
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($count['count'] > 0) {
                // Créer quelques factures de test
                $stmt = $pdo->prepare("SELECT id FROM Etudiants LIMIT 3");
                $stmt->execute();
                $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $factures_created = 0;
                foreach ($etudiants as $etudiant) {
                    // Facture payée
                    $stmt = $pdo->prepare("
                        INSERT INTO Factures (etudiant_id, mois, montant, statut, date_paiement) 
                        VALUES (?, ?, ?, 'Payé', ?)
                    ");
                    $stmt->execute([
                        $etudiant['id'],
                        date('Y-m', strtotime('-1 month')),
                        1500.00,
                        date('Y-m-d', strtotime('-15 days'))
                    ]);
                    $factures_created++;
                    
                    // Facture non payée
                    $stmt = $pdo->prepare("
                        INSERT INTO Factures (etudiant_id, mois, montant, statut) 
                        VALUES (?, ?, ?, 'Non payé')
                    ");
                    $stmt->execute([
                        $etudiant['id'],
                        date('Y-m'),
                        1500.00
                    ]);
                    $factures_created++;
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "Données de test créées : $factures_created factures"
                ]);
            } else {
                echo json_encode([
                    'error' => 'Aucun étudiant trouvé. Créez d\'abord des étudiants.'
                ]);
            }
        } else {
            echo json_encode(['error' => 'Paramètre create_test_data manquant']);
        }
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la création des données : ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
