import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import Pagination from '../components/Pagination';
import usePagination from '../hooks/usePagination';
import useSearch from '../hooks/useSearch';
import '../css/UnifiedPages.css';
import '../css/Pagination.css';

const FilieresUnified = () => {
    const [filieres, setFilieres] = useState([]);
    const [loading, setLoading] = useState(true);

    // Hooks personnalisés
    const { 
        searchTerm, 
        setSearchTerm, 
        filterValue, 
        setFilterValue, 
        filteredData, 
        getUniqueFilterValues, 
        clearFilters, 
        hasActiveFilters 
    } = useSearch(
        filieres,
        ['nom', 'code', 'description'], // Champs de recherche
        'domaine' // Champ de filtrage
    );

    const { 
        paginatedData, 
        currentPage, 
        totalPages, 
        goToPage, 
        resetPagination, 
        paginationInfo 
    } = usePagination(filteredData, 10);

    useEffect(() => {
        fetchFilieres();
    }, []);

    useEffect(() => {
        resetPagination();
    }, [filteredData, resetPagination]);

    const fetchFilieres = async () => {
        try {
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/filieres/getFilieres.php');
            setFilieres(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des filières:', error);
            Swal.fire('Erreur', 'Impossible de charger les filières', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleClearFilters = () => {
        clearFilters();
    };

    if (loading) {
        return (
            <div className="unified-container">
                <div className="unified-loading">
                    <div className="unified-spinner"></div>
                    <p>Chargement des filières...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="unified-container">
            {/* En-tête */}
            <div className="unified-header">
                <h1 className="unified-title">
                    <span className="unified-title-icon">🎓</span>
                    Gestion des Filières
                </h1>
                <div className="unified-header-actions">
                    <span className="unified-count">
                        {filteredData.length} filière(s)
                    </span>
                    <button className="unified-btn unified-btn-primary">
                        ➕ Nouvelle Filière
                    </button>
                </div>
            </div>

            {/* Filtres */}
            <div className="unified-filters">
                <div className="unified-filters-grid">
                    <div className="unified-search-box">
                        <input
                            type="text"
                            className="unified-search-input"
                            placeholder="🔍 Rechercher par nom, code ou description..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <span className="unified-search-icon">🔍</span>
                    </div>
                    
                    <select
                        className="unified-filter-select"
                        value={filterValue}
                        onChange={(e) => setFilterValue(e.target.value)}
                    >
                        <option value="all">Tous les domaines</option>
                        {getUniqueFilterValues().map(value => (
                            <option key={value} value={value}>{value}</option>
                        ))}
                    </select>
                    
                    {hasActiveFilters && (
                        <button 
                            className="unified-clear-btn"
                            onClick={handleClearFilters}
                        >
                            ✖️ Effacer
                        </button>
                    )}
                </div>
            </div>

            {/* Contenu principal */}
            <div className="unified-content">
                {filteredData.length === 0 ? (
                    <div className="unified-empty">
                        <div className="unified-empty-icon">🎓</div>
                        <h3 className="unified-empty-title">Aucune filière trouvée</h3>
                        <p className="unified-empty-text">
                            {hasActiveFilters 
                                ? 'Aucune filière ne correspond à vos critères de recherche.'
                                : 'Aucune filière n\'est disponible pour le moment.'
                            }
                        </p>
                        {hasActiveFilters && (
                            <button 
                                className="unified-btn unified-btn-primary"
                                onClick={handleClearFilters}
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <>
                        <table className="unified-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Code</th>
                                    <th>Nom de la Filière</th>
                                    <th>Description</th>
                                    <th>Domaine</th>
                                    <th>Durée</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedData.map((filiere) => (
                                    <tr key={filiere.id}>
                                        <td>
                                            <span className="unified-badge unified-badge-primary">
                                                #{filiere.id}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-info">
                                                {filiere.code || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50' }}>
                                                {filiere.nom}
                                            </strong>
                                        </td>
                                        <td>
                                            <span style={{ color: '#6c757d' }}>
                                                {filiere.description || 'Aucune description'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-warning">
                                                {filiere.domaine || 'Non spécifié'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ color: '#495057' }}>
                                                {filiere.duree ? `${filiere.duree} ans` : 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-success">
                                                Actif
                                            </span>
                                        </td>
                                        <td>
                                            <div className="unified-actions">
                                                <button 
                                                    className="unified-btn unified-btn-info"
                                                    title="Voir détails"
                                                >
                                                    👁️ Voir
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-warning"
                                                    title="Modifier"
                                                >
                                                    ✏️ Modifier
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-danger"
                                                    title="Supprimer"
                                                >
                                                    🗑️ Supprimer
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        {/* Pagination */}
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={goToPage}
                            itemsPerPage={10}
                            totalItems={paginationInfo.totalItems}
                        />
                    </>
                )}
            </div>
        </div>
    );
};

export default FilieresUnified;
