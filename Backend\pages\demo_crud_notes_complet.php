<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>📊 INTERFACE CRUD NOTES - CALCUL AUTOMATIQUE COMPLET</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .demo-section { background: white; border: 2px solid #28a745; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .test-button { color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; font-weight: bold; }
        .test-button:hover { color: white; text-decoration: none; transform: translateY(-2px); }
        .test-button.notes { background: #28a745; }
        .test-button.notes:hover { background: #218838; }
        .test-button.success { background: #20c997; }
        .test-button.success:hover { background: #1ea085; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0; }
        .feature-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; }
        .logic-chain { background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 15px 0; border: 2px solid #28a745; }
        .chain-step { display: flex; align-items: center; margin: 10px 0; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chain-icon { font-size: 24px; margin-right: 15px; }
        .chain-text { flex: 1; }
        .chain-arrow { font-size: 20px; color: #28a745; margin: 0 10px; }
        .grade-example { background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; border: 1px solid #c3e6cb; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>📊 Interface CRUD Notes - Calcul Automatique Intelligent</h2>";
    echo "<p>Interface complète de gestion des notes avec calcul automatique basé sur les réponses aux quiz :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Calcul automatique</strong> : Notes calculées depuis les réponses aux quiz</li>";
    echo "<li>✅ <strong>Chaîne logique complète</strong> : Devoir → Quiz → ReponsesQuiz → Notes</li>";
    echo "<li>✅ <strong>CRUD complet</strong> : Créer, Lire, Modifier, Supprimer avec permissions</li>";
    echo "<li>✅ <strong>Recalcul intelligent</strong> : Mise à jour automatique des notes</li>";
    echo "<li>✅ <strong>Statistiques détaillées</strong> : Affichage des performances quiz</li>";
    echo "</ul>";
    echo "</div>";
    
    // Chaîne logique
    echo "<div class='step'>";
    echo "<h3>🔁 Chaîne Logique Complète</h3>";
    
    echo "<div class='logic-chain'>";
    echo "<h4>📋 Flux de Données : Devoir → Quiz → ReponsesQuiz → Notes</h4>";
    
    echo "<div class='chain-step'>";
    echo "<span class='chain-icon'>📝</span>";
    echo "<div class='chain-text'>";
    echo "<strong>1. Devoir</strong><br>";
    echo "Création d'un devoir avec matière et classe associées";
    echo "</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<span class='chain-arrow'>⬇️</span>";
    echo "</div>";
    
    echo "<div class='chain-step'>";
    echo "<span class='chain-icon'>❓</span>";
    echo "<div class='chain-text'>";
    echo "<strong>2. Quiz</strong><br>";
    echo "Questions créées pour le devoir avec réponses correctes";
    echo "</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<span class='chain-arrow'>⬇️</span>";
    echo "</div>";
    
    echo "<div class='chain-step'>";
    echo "<span class='chain-icon'>💬</span>";
    echo "<div class='chain-text'>";
    echo "<strong>3. ReponsesQuiz</strong><br>";
    echo "Étudiants répondent aux questions (est_correct calculé)";
    echo "</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<span class='chain-arrow'>⬇️</span>";
    echo "</div>";
    
    echo "<div class='chain-step'>";
    echo "<span class='chain-icon'>📊</span>";
    echo "<div class='chain-text'>";
    echo "<strong>4. Notes</strong><br>";
    echo "Calcul automatique : (bonnes réponses / total questions) × 20";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<h4>🧮 Formule de Calcul</h4>";
    echo "<div class='grade-example'>";
    echo "<h5>📐 Calcul Automatique de la Note</h5>";
    echo "<pre>";
    echo "note = (bonnes_reponses / total_questions) × 20\n\n";
    echo "Exemple :\n";
    echo "- Total questions : 10\n";
    echo "- Bonnes réponses : 8\n";
    echo "- Note = (8 / 10) × 20 = 16/20\n";
    echo "- Mention : Très Bien";
    echo "</pre>";
    echo "</div>";
    echo "</div>";
    
    // Structure de la base de données
    echo "<div class='step'>";
    echo "<h3>🗄️ Structure de la Base de Données</h3>";
    
    echo "<h4>📋 Table Notes (Principale)</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace;'>";
    echo "<pre>";
    echo "CREATE TABLE Notes (\n";
    echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
    echo "    etudiant_id INT,                    -- FK vers Etudiants\n";
    echo "    devoir_id INT,                      -- FK vers Devoirs\n";
    echo "    matiere_id INT,                     -- FK vers Matieres (auto-récupéré)\n";
    echo "    note DECIMAL(5,2),                  -- Note calculée sur 20\n";
    echo "    date_enregistrement DATE,           -- Date de création/modification\n";
    echo "    FOREIGN KEY (etudiant_id) REFERENCES Etudiants(id),\n";
    echo "    FOREIGN KEY (devoir_id) REFERENCES Devoirs(id),\n";
    echo "    FOREIGN KEY (matiere_id) REFERENCES Matieres(id)\n";
    echo ");";
    echo "</pre>";
    echo "</div>";
    
    echo "<h4>🔗 Relations Automatiques</h4>";
    echo "<ul>";
    echo "<li><strong>matiere_id :</strong> Récupéré automatiquement depuis Devoirs.matiere_id</li>";
    echo "<li><strong>Calcul note :</strong> Basé sur ReponsesQuiz.est_correct pour le devoir</li>";
    echo "<li><strong>Prévention doublons :</strong> Un seul enregistrement par étudiant/devoir</li>";
    echo "<li><strong>Mise à jour :</strong> Recalcul possible à tout moment</li>";
    echo "</ul>";
    echo "</div>";
    
    // Fonctionnalités CRUD
    echo "<div class='step'>";
    echo "<h3>⚙️ Fonctionnalités CRUD Intelligentes</h3>";
    
    echo "<div class='feature-grid'>";
    
    echo "<div class='feature-card'>";
    echo "<h5>➕ Création (CREATE)</h5>";
    echo "<ul>";
    echo "<li>Sélection étudiant et devoir</li>";
    echo "<li>Calcul automatique par défaut</li>";
    echo "<li>Saisie manuelle optionnelle</li>";
    echo "<li>Prévisualisation du calcul</li>";
    echo "<li>Récupération auto matiere_id</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>👁️ Lecture (READ)</h5>";
    echo "<ul>";
    echo "<li>Affichage avec statistiques quiz</li>";
    echo "<li>Filtres par matière</li>";
    echo "<li>Recherche multi-critères</li>";
    echo "<li>Couleurs selon performance</li>";
    echo "<li>Permissions par rôle</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>✏️ Modification (UPDATE)</h5>";
    echo "<ul>";
    echo "<li>Modification manuelle note</li>";
    echo "<li>Recalcul automatique</li>";
    echo "<li>Mise à jour date</li>";
    echo "<li>Validation 0-20</li>";
    echo "<li>Confirmation utilisateur</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>🗑️ Suppression (DELETE)</h5>";
    echo "<ul>";
    echo "<li>Confirmation SweetAlert</li>";
    echo "<li>Suppression définitive</li>";
    echo "<li>Permissions vérifiées</li>";
    echo "<li>Feedback utilisateur</li>";
    echo "<li>Mise à jour automatique</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Gestion des permissions
    echo "<div class='step'>";
    echo "<h3>🔒 Gestion des Permissions par Rôle</h3>";
    
    echo "<h4>👑 Admin (Accès Complet)</h4>";
    echo "<ul>";
    echo "<li><strong>Lecture :</strong> Toutes les notes de tous les étudiants</li>";
    echo "<li><strong>Création :</strong> Ajouter des notes pour n'importe quel étudiant</li>";
    echo "<li><strong>Modification :</strong> Éditer toutes les notes et recalculer</li>";
    echo "<li><strong>Suppression :</strong> Supprimer n'importe quelle note</li>";
    echo "</ul>";
    
    echo "<h4>👨‍🏫 Enseignant (Accès Limité)</h4>";
    echo "<ul>";
    echo "<li><strong>Lecture :</strong> Notes des matières/classes qu'il enseigne</li>";
    echo "<li><strong>Création :</strong> Ajouter des notes pour ses étudiants</li>";
    echo "<li><strong>Modification :</strong> Éditer les notes de ses classes</li>";
    echo "<li><strong>Suppression :</strong> Supprimer les notes de ses matières</li>";
    echo "</ul>";
    
    echo "<h4>👨‍🎓 Étudiant (Lecture Seule)</h4>";
    echo "<ul>";
    echo "<li><strong>Lecture :</strong> Ses propres notes uniquement</li>";
    echo "<li><strong>Création :</strong> Non autorisée</li>";
    echo "<li><strong>Modification :</strong> Non autorisée</li>";
    echo "<li><strong>Suppression :</strong> Non autorisée</li>";
    echo "</ul>";
    
    echo "<h4>👨‍👩‍👧‍👦 Parent (Lecture Limitée)</h4>";
    echo "<ul>";
    echo "<li><strong>Lecture :</strong> Notes de ses enfants uniquement</li>";
    echo "<li><strong>Création :</strong> Non autorisée</li>";
    echo "<li><strong>Modification :</strong> Non autorisée</li>";
    echo "<li><strong>Suppression :</strong> Non autorisée</li>";
    echo "</ul>";
    echo "</div>";
    
    // Interface utilisateur
    echo "<div class='demo-section'>";
    echo "<h3>🎨 Interface Utilisateur Intelligente</h3>";
    
    echo "<h4>✨ Caractéristiques Avancées</h4>";
    echo "<ul>";
    echo "<li><strong>Calcul en temps réel :</strong> Prévisualisation avant sauvegarde</li>";
    echo "<li><strong>Statistiques visuelles :</strong> Bonnes réponses / Total questions</li>";
    echo "<li><strong>Codes couleur :</strong> Performance selon la note obtenue</li>";
    echo "<li><strong>Bouton recalcul :</strong> Mise à jour automatique des notes</li>";
    echo "<li><strong>Filtres intelligents :</strong> Par matière et recherche</li>";
    echo "<li><strong>Design cohérent :</strong> Style uniforme avec autres modules</li>";
    echo "</ul>";
    
    echo "<h4>🎯 Éléments Spécifiques Notes</h4>";
    echo "<ul>";
    echo "<li><strong>Affichage note :</strong> X/20 avec mention (Très Bien, Bien, etc.)</li>";
    echo "<li><strong>Statistiques quiz :</strong> Bonnes réponses / Total avec pourcentage</li>";
    echo "<li><strong>Indicateur incomplet :</strong> Si toutes les questions ne sont pas répondues</li>";
    echo "<li><strong>Bouton recalcul :</strong> Icône refresh pour mise à jour automatique</li>";
    echo "<li><strong>Date d'enregistrement :</strong> Suivi des modifications</li>";
    echo "</ul>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/notes' target='_blank' class='test-button notes'>📊 Tester l'Interface Notes</a>";
    echo "</div>";
    echo "</div>";
    
    // API Backend
    echo "<div class='step'>";
    echo "<h3>🔧 API Backend Intelligente</h3>";
    
    echo "<h4>📡 Endpoints Disponibles</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace;'>";
    echo "<pre>";
    echo "GET    /notes/                      # Lister les notes (avec filtres par rôle)\n";
    echo "POST   /notes/                      # Créer une nouvelle note\n";
    echo "PUT    /notes/                      # Modifier une note existante\n";
    echo "DELETE /notes/                      # Supprimer une note\n\n";
    echo "# Endpoints utilitaires\n";
    echo "GET    /notes/?action=etudiants     # Liste des étudiants\n";
    echo "GET    /notes/?action=devoirs       # Liste des devoirs avec nb questions\n";
    echo "GET    /notes/?action=calcul        # Calcul automatique note\n";
    echo "       &etudiant_id=X&devoir_id=Y   # Paramètres pour calcul";
    echo "</pre>";
    echo "</div>";
    
    echo "<h4>🧮 Fonctionnalités Calcul</h4>";
    echo "<ul>";
    echo "<li><strong>Calcul automatique :</strong> Fonction calculateNoteFromQuiz()</li>";
    echo "<li><strong>Validation données :</strong> Vérification relations et contraintes</li>";
    echo "<li><strong>Prévention doublons :</strong> Un seul enregistrement par étudiant/devoir</li>";
    echo "<li><strong>Récupération auto :</strong> matiere_id depuis le devoir</li>";
    echo "<li><strong>Recalcul intelligent :</strong> Mise à jour avec nouvelles réponses</li>";
    echo "</ul>";
    echo "</div>";
    
    // Exemples d'utilisation
    echo "<div class='step'>";
    echo "<h3>💡 Exemples d'Utilisation</h3>";
    
    echo "<h4>🎯 Scénario 1 : Création Automatique</h4>";
    echo "<ol>";
    echo "<li><strong>Sélection :</strong> Étudiant 'Marie Dupont' + Devoir 'Mathématiques Ch.1'</li>";
    echo "<li><strong>Calcul :</strong> 8 bonnes réponses sur 10 questions</li>";
    echo "<li><strong>Résultat :</strong> Note = (8/10) × 20 = 16/20 (Très Bien)</li>";
    echo "<li><strong>Sauvegarde :</strong> Note enregistrée avec matiere_id auto-récupéré</li>";
    echo "</ol>";
    
    echo "<h4>🔄 Scénario 2 : Recalcul Automatique</h4>";
    echo "<ol>";
    echo "<li><strong>Situation :</strong> Étudiant répond à une question supplémentaire</li>";
    echo "<li><strong>Action :</strong> Clic sur bouton 'Recalculer' dans l'interface</li>";
    echo "<li><strong>Calcul :</strong> 9 bonnes réponses sur 10 questions maintenant</li>";
    echo "<li><strong>Mise à jour :</strong> Note = (9/10) × 20 = 18/20 (Très Bien)</li>";
    echo "</ol>";
    
    echo "<h4>✏️ Scénario 3 : Modification Manuelle</h4>";
    echo "<ol>";
    echo "<li><strong>Contexte :</strong> Enseignant veut ajuster une note</li>";
    echo "<li><strong>Action :</strong> Modification manuelle de 16/20 à 17/20</li>";
    echo "<li><strong>Validation :</strong> Note entre 0 et 20 vérifiée</li>";
    echo "<li><strong>Sauvegarde :</strong> Date d'enregistrement mise à jour</li>";
    echo "</ol>";
    echo "</div>";
    
    // Résultat final
    echo "<div class='demo-section'>";
    echo "<h3>🎉 INTERFACE NOTES COMPLÈTE</h3>";
    echo "<p class='success' style='font-size: 18px;'>✅ Interface de gestion des notes avec calcul automatique opérationnelle !</p>";
    
    echo "<h4>🚀 Fonctionnalités Implémentées</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Calcul automatique intelligent</strong> : Notes basées sur les quiz</li>";
    echo "<li>✅ <strong>Chaîne logique complète</strong> : Devoir → Quiz → ReponsesQuiz → Notes</li>";
    echo "<li>✅ <strong>CRUD complet</strong> avec gestion des permissions</li>";
    echo "<li>✅ <strong>Interface moderne</strong> avec statistiques visuelles</li>";
    echo "<li>✅ <strong>API robuste</strong> avec validation et sécurité</li>";
    echo "<li>✅ <strong>Recalcul intelligent</strong> : Mise à jour automatique</li>";
    echo "</ul>";
    
    echo "<h4>🎯 Avantages Système</h4>";
    echo "<p>L'interface Notes apporte une valeur ajoutée significative :</p>";
    echo "<ul>";
    echo "<li>Automatisation complète du calcul des notes</li>";
    echo "<li>Cohérence avec les réponses aux quiz</li>";
    echo "<li>Traçabilité et historique des modifications</li>";
    echo "<li>Interface intuitive pour tous les rôles</li>";
    echo "<li>Intégration parfaite avec l'écosystème existant</li>";
    echo "</ul>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/notes' target='_blank' class='test-button success'>🎉 Utiliser l'Interface Notes</a>";
    echo "</div>";
    
    echo "<p class='info'><strong>📊 L'interface Notes avec calcul automatique est opérationnelle !</strong></p>";
    
    echo "<h4>🔗 Liens Utiles</h4>";
    echo "<ul>";
    echo "<li><a href='http://localhost:3000/notes' target='_blank'>📊 Interface Notes</a></li>";
    echo "<li><a href='http://localhost:3000/reponses-quiz' target='_blank'>💬 Interface ReponsesQuiz</a></li>";
    echo "<li><a href='http://localhost:3000/devoirs' target='_blank'>📝 Gestion des Devoirs</a></li>";
    echo "<li><a href='http://localhost:3000/quiz' target='_blank'>❓ Gestion des Quiz</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
