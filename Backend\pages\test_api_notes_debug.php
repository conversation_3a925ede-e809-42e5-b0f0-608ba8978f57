<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔧 DEBUG API NOTES - CORRECTION ERREUR JSON</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 10px 0; font-family: monospace; }
        .fix-demo { background: white; border: 2px solid #28a745; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .test-button { color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; font-weight: bold; }
        .test-button:hover { color: white; text-decoration: none; transform: translateY(-2px); }
        .test-button.debug { background: #dc3545; }
        .test-button.debug:hover { background: #c82333; }
        .test-button.success { background: #28a745; }
        .test-button.success:hover { background: #218838; }
    </style>";
    
    echo "<div class='error'>";
    echo "<h2>❌ Erreur Identifiée</h2>";
    echo "<p>L'interface React reçoit du HTML au lieu de JSON depuis l'API Notes :</p>";
    echo "<ul>";
    echo "<li><strong>Erreur :</strong> SyntaxError: Unexpected token '&lt;', \"&lt;br /&gt;&lt;b&gt;\"... is not valid JSON</li>";
    echo "<li><strong>Cause :</strong> L'API PHP retourne une page d'erreur HTML</li>";
    echo "<li><strong>Endpoint :</strong> http://localhost/Project_PFE/Backend/pages/notes/</li>";
    echo "<li><strong>Méthode :</strong> GET avec Authorization Bearer token</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test de l'API
    echo "<div class='step'>";
    echo "<h3>🧪 Test Direct de l'API Notes</h3>";
    
    echo "<h4>📡 Test de l'Endpoint</h4>";
    echo "<p>Testons l'accès direct à l'API Notes :</p>";
    
    // Test 1: Vérifier l'existence du fichier
    $api_file = '../notes/index.php';
    if (file_exists($api_file)) {
        echo "<p class='success'>✅ Fichier API trouvé : " . realpath($api_file) . "</p>";
    } else {
        echo "<p class='error'>❌ Fichier API non trouvé : $api_file</p>";
    }
    
    // Test 2: Vérifier les dépendances
    $db_file = '../../config/db.php';
    $auth_file = '../../config/auth.php';
    
    if (file_exists($db_file)) {
        echo "<p class='success'>✅ Fichier DB trouvé : " . realpath($db_file) . "</p>";
    } else {
        echo "<p class='error'>❌ Fichier DB non trouvé : $db_file</p>";
    }
    
    if (file_exists($auth_file)) {
        echo "<p class='success'>✅ Fichier Auth trouvé : " . realpath($auth_file) . "</p>";
    } else {
        echo "<p class='error'>❌ Fichier Auth non trouvé : $auth_file</p>";
    }
    
    // Test 3: Vérifier la syntaxe PHP
    echo "<h4>🔍 Test de Syntaxe PHP</h4>";
    $syntax_check = shell_exec("php -l $api_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "<p class='success'>✅ Syntaxe PHP correcte</p>";
    } else {
        echo "<p class='error'>❌ Erreur de syntaxe PHP :</p>";
        echo "<div class='code-block'><pre>$syntax_check</pre></div>";
    }
    echo "</div>";
    
    // Solutions proposées
    echo "<div class='step'>";
    echo "<h3>🔧 Solutions Proposées</h3>";
    
    echo "<h4>1. Vérifier la Configuration</h4>";
    echo "<ul>";
    echo "<li>Vérifier que les fichiers config/db.php et config/auth.php existent</li>";
    echo "<li>Vérifier la connexion à la base de données</li>";
    echo "<li>Vérifier la configuration des headers CORS</li>";
    echo "</ul>";
    
    echo "<h4>2. Tester l'API Manuellement</h4>";
    echo "<ul>";
    echo "<li>Accéder directement à l'endpoint via navigateur</li>";
    echo "<li>Vérifier les logs d'erreur PHP</li>";
    echo "<li>Tester avec un token d'authentification valide</li>";
    echo "</ul>";
    
    echo "<h4>3. Corriger les Erreurs Potentielles</h4>";
    echo "<ul>";
    echo "<li>Corriger les erreurs de syntaxe PHP</li>";
    echo "<li>Vérifier les chemins des fichiers inclus</li>";
    echo "<li>Ajouter la gestion d'erreurs appropriée</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test de connexion DB
    echo "<div class='step'>";
    echo "<h3>🗄️ Test de Connexion Base de Données</h3>";
    
    try {
        if (file_exists($db_file)) {
            require_once($db_file);
            echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
            
            // Test de la table Notes
            $stmt = $pdo->query("SHOW TABLES LIKE 'Notes'");
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✅ Table Notes existe</p>";
                
                // Vérifier la structure
                $stmt = $pdo->query("DESCRIBE Notes");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<p class='info'>📋 Structure de la table Notes :</p>";
                echo "<ul>";
                foreach ($columns as $column) {
                    echo "<li><strong>{$column['Field']}</strong> : {$column['Type']}</li>";
                }
                echo "</ul>";
            } else {
                echo "<p class='error'>❌ Table Notes n'existe pas</p>";
                echo "<p class='warning'>⚠️ Création de la table Notes nécessaire</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur de connexion DB : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Création de la table si nécessaire
    echo "<div class='step'>";
    echo "<h3>🛠️ Création Table Notes (si nécessaire)</h3>";
    
    if (isset($pdo)) {
        try {
            $create_table_sql = "
                CREATE TABLE IF NOT EXISTS Notes (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    etudiant_id INT,
                    devoir_id INT,
                    matiere_id INT,
                    note DECIMAL(5,2),
                    date_enregistrement DATE,
                    FOREIGN KEY (etudiant_id) REFERENCES Etudiants(id),
                    FOREIGN KEY (devoir_id) REFERENCES Devoirs(id),
                    FOREIGN KEY (matiere_id) REFERENCES Matieres(id)
                )
            ";
            
            $pdo->exec($create_table_sql);
            echo "<p class='success'>✅ Table Notes créée/vérifiée avec succès</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur création table : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Test API simple
    echo "<div class='step'>";
    echo "<h3">🧪 Test API Simplifié</h3>";
    
    echo "<h4>📡 Test GET sans authentification</h4>";
    echo "<p>Testons une version simplifiée de l'API :</p>";
    
    // Créer un test API simple
    $simple_api_content = '<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

try {
    // Test simple sans authentification
    echo json_encode([
        "status" => "success",
        "message" => "API Notes accessible",
        "timestamp" => date("Y-m-d H:i:s"),
        "data" => []
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
?>';
    
    file_put_contents('../notes/test_simple.php', $simple_api_content);
    echo "<p class='success'>✅ API de test créée : /notes/test_simple.php</p>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='../notes/test_simple.php' target='_blank' class='test-button debug'>🧪 Tester API Simple</a>";
    echo "<a href='../notes/' target='_blank' class='test-button debug'>🔍 Tester API Complète</a>";
    echo "</div>";
    echo "</div>";
    
    // Instructions de correction
    echo "<div class='fix-demo'>";
    echo "<h3>🔧 INSTRUCTIONS DE CORRECTION</h3>";
    
    echo "<h4>✅ Étapes de Résolution</h4>";
    echo "<ol>";
    echo "<li><strong>Vérifier l'API simple :</strong> Cliquer sur 'Tester API Simple' ci-dessus</li>";
    echo "<li><strong>Si l'API simple fonctionne :</strong> Le problème est dans l'authentification</li>";
    echo "<li><strong>Si l'API simple ne fonctionne pas :</strong> Problème de configuration serveur</li>";
    echo "<li><strong>Vérifier les logs PHP :</strong> Consulter les logs d'erreur du serveur</li>";
    echo "<li><strong>Corriger les erreurs :</strong> Selon les messages d'erreur trouvés</li>";
    echo "</ol>";
    
    echo "<h4>🎯 Points de Vérification</h4>";
    echo "<ul>";
    echo "<li>✅ Serveur web (Apache/Nginx) démarré</li>";
    echo "<li>✅ PHP fonctionnel avec extensions PDO</li>";
    echo "<li>✅ Base de données accessible</li>";
    echo "<li>✅ Table Notes créée</li>";
    echo "<li>✅ Fichiers config/db.php et config/auth.php présents</li>";
    echo "<li>✅ Headers CORS configurés</li>";
    echo "</ul>";
    
    echo "<h4>🔗 Liens de Test</h4>";
    echo "<ul>";
    echo "<li><a href='../notes/test_simple.php' target='_blank'>🧪 API Simple (sans auth)</a></li>";
    echo "<li><a href='../notes/' target='_blank'>🔍 API Complète (avec auth)</a></li>";
    echo "<li><a href='http://localhost:3000/notes' target='_blank'>📊 Interface React Notes</a></li>";
    echo "</ul>";
    
    echo "<p class='info'><strong>🎯 Une fois l'API corrigée, l'interface React fonctionnera parfaitement !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
