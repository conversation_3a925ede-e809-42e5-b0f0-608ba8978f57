<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Cours API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Cours API - Headers: " . json_encode(getallheaders()));

$method = $_SERVER['REQUEST_METHOD'];

// Gérer le header X-HTTP-Method-Override pour les requêtes PUT via POST
if ($method === 'POST' && isset($_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'])) {
    $method = $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'];
    error_log("Method override detected via header: " . $method);
}

// Gérer le champ _method pour les requêtes PUT via POST
if ($method === 'POST' && isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
    $method = 'PUT';
    error_log("Method override detected via _method field: " . $method);
}

// Dossier pour stocker les fichiers PDF
$uploadDir = '../../uploads/cours/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

if ($method === 'POST') {
    error_log("POST Data: " . json_encode($_POST));
    error_log("POST Files: " . json_encode($_FILES));

    // Vérifier si c'est une requête de modification (PUT simulé)
    if (isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
        error_log("POST with _method=PUT detected - redirecting to PUT logic");
        $method = 'PUT';
        goto handle_put;
    }

    if (!isset($_POST['titre']) || empty(trim($_POST['titre']))) {
        echo json_encode(['success' => false, 'error' => 'Titre du cours requis']);
        exit;
    }
    if (!isset($_POST['matiere_id']) || !is_numeric($_POST['matiere_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la matière requis']);
        exit;
    }
    if (!isset($_POST['classe_id']) || !is_numeric($_POST['classe_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis']);
        exit;
    }
    if (!isset($_POST['date_publication']) || empty($_POST['date_publication'])) {
        echo json_encode(['success' => false, 'error' => 'Date de publication requise']);
        exit;
    }
    $titre = trim($_POST['titre']);
    $description = trim($_POST['description'] ?? '');
    $matiere_id = intval($_POST['matiere_id']);
    $classe_id = intval($_POST['classe_id']);
    $date_publication = $_POST['date_publication'];

    // Vérifier si la matière existe
    try {
        $checkMatiereStmt = $pdo->prepare("SELECT id FROM Matieres WHERE id = :matiere_id");
        $checkMatiereStmt->execute(['matiere_id' => $matiere_id]);
        if (!$checkMatiereStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Matière non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check matiere error: " . $e->getMessage());
    }

    // Vérifier si la classe existe
    try {
        $checkClasseStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :classe_id");
        $checkClasseStmt->execute(['classe_id' => $classe_id]);
        if (!$checkClasseStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe error: " . $e->getMessage());
    }

    // Gestion de l'upload du fichier PDF
    $fichier_pdf = null;
    $taille_fichier = null;

    if (isset($_FILES['fichier_pdf']) && $_FILES['fichier_pdf']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['fichier_pdf'];

        // Vérifier le type de fichier
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mimeType !== 'application/pdf') {
            echo json_encode(['success' => false, 'error' => 'Seuls les fichiers PDF sont autorisés']);
            exit;
        }

        // Vérifier la taille (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode(['success' => false, 'error' => 'Le fichier ne doit pas dépasser 10MB']);
            exit;
        }

        // Générer un nom unique pour le fichier
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('cours_') . '.' . $extension;
        $targetPath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $fichier_pdf = $filename;
            $taille_fichier = round($file['size'] / (1024 * 1024), 2) . ' MB';
            error_log("File uploaded successfully: " . $filename . " (Size: " . $taille_fichier . ")");
        } else {
            echo json_encode(['success' => false, 'error' => 'Erreur lors de l\'upload du fichier']);
            exit;
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Fichier PDF requis']);
        exit;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Cours (titre, description, fichier_pdf, taille_fichier, date_publication, matiere_id, classe_id) VALUES (:titre, :description, :fichier_pdf, :taille_fichier, :date_publication, :matiere_id, :classe_id)");
        $stmt->execute([
            'titre' => $titre,
            'description' => $description,
            'fichier_pdf' => $fichier_pdf,
            'taille_fichier' => $taille_fichier,
            'date_publication' => $date_publication,
            'matiere_id' => $matiere_id,
            'classe_id' => $classe_id
        ]);
        $newId = $pdo->lastInsertId();
        error_log("Cours created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Cours ajouté avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("
            SELECT Cours.id, Cours.titre, Cours.description, Cours.fichier_pdf, Cours.taille_fichier, Cours.date_publication,
                   Cours.matiere_id, Matieres.nom AS matiere_nom,
                   Cours.classe_id, Classes.nom AS classe_nom
            FROM Cours
            LEFT JOIN Matieres ON Cours.matiere_id = Matieres.id
            LEFT JOIN Classes ON Cours.classe_id = Classes.id
            ORDER BY Cours.date_publication DESC
        ");
        $cours = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Vérifier si les fichiers existent physiquement
        foreach ($cours as &$c) {
            if ($c['fichier_pdf']) {
                $filePath = $uploadDir . $c['fichier_pdf'];
                if (!file_exists($filePath)) {
                    error_log("Warning: File not found: " . $filePath);
                }
            }
        }

        error_log("GET Success: " . count($cours) . " cours found");
        echo json_encode($cours);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    handle_put:
    error_log("PUT Data: " . json_encode($_POST));
    error_log("PUT Files: " . json_encode($_FILES));

    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        error_log("PUT Error - ID validation failed. POST data: " . json_encode($_POST));
        error_log("PUT Error - _POST keys: " . implode(', ', array_keys($_POST)));
        echo json_encode(['success' => false, 'error' => 'ID du cours requis et doit être numérique. Données reçues: ' . json_encode(array_keys($_POST))]);
        exit;
    }

    if (!isset($_POST['titre']) || empty(trim($_POST['titre']))) {
        echo json_encode(['success' => false, 'error' => 'Titre du cours requis']);
        exit;
    }

    if (!isset($_POST['matiere_id']) || !is_numeric($_POST['matiere_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la matière requis']);
        exit;
    }

    if (!isset($_POST['classe_id']) || !is_numeric($_POST['classe_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis']);
        exit;
    }

    if (!isset($_POST['date_publication']) || empty($_POST['date_publication'])) {
        echo json_encode(['success' => false, 'error' => 'Date de publication requise']);
        exit;
    }

    $id = intval($_POST['id']);
    $titre = trim($_POST['titre']);
    $description = trim($_POST['description'] ?? '');
    $matiere_id = intval($_POST['matiere_id']);
    $classe_id = intval($_POST['classe_id']);
    $date_publication = $_POST['date_publication'];

    // Vérifier si le cours existe
    try {
        $checkStmt = $pdo->prepare("SELECT fichier_pdf, taille_fichier FROM Cours WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        $existingCours = $checkStmt->fetch();
        if (!$existingCours) {
            echo json_encode(['success' => false, 'error' => 'Cours non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check cours exists error: " . $e->getMessage());
    }

    // Gestion de l'upload du nouveau fichier PDF (optionnel en modification)
    $fichier_pdf = $existingCours['fichier_pdf']; // Garder l'ancien par défaut
    $taille_fichier = $existingCours['taille_fichier'];
    $newFileUploaded = false;

    error_log("PUT - Cours existant trouvé: ID=" . $id . ", fichier_actuel=" . $existingCours['fichier_pdf']);
    
    if (isset($_FILES['fichier_pdf']) && $_FILES['fichier_pdf']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['fichier_pdf'];
        error_log("PUT - Nouveau fichier PDF détecté: " . $file['name'] . " (" . round($file['size'] / (1024 * 1024), 2) . " MB)");

        // Vérifier le type de fichier
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mimeType !== 'application/pdf') {
            echo json_encode(['success' => false, 'error' => 'Seuls les fichiers PDF sont autorisés']);
            exit;
        }

        // Vérifier la taille (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode(['success' => false, 'error' => 'Le fichier ne doit pas dépasser 10MB']);
            exit;
        }

        // Supprimer l'ancien fichier
        if ($existingCours['fichier_pdf'] && file_exists($uploadDir . $existingCours['fichier_pdf'])) {
            unlink($uploadDir . $existingCours['fichier_pdf']);
        }

        // Générer un nom unique pour le nouveau fichier
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('cours_') . '.' . $extension;
        $targetPath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $fichier_pdf = $filename;
            $taille_fichier = round($file['size'] / (1024 * 1024), 2) . ' MB';
            $newFileUploaded = true;
            error_log("New file uploaded successfully: " . $filename . " (Size: " . $taille_fichier . ")");
        } else {
            echo json_encode(['success' => false, 'error' => 'Erreur lors de l\'upload du nouveau fichier']);
            exit;
        }
    } else {
        error_log("PUT - Aucun nouveau fichier PDF uploadé, conservation du fichier existant: " . $existingCours['fichier_pdf']);
    }

    try {
        if ($newFileUploaded) {
            // Mise à jour avec nouveau fichier
            $stmt = $pdo->prepare("UPDATE Cours SET titre = :titre, description = :description, fichier_pdf = :fichier_pdf, taille_fichier = :taille_fichier, date_publication = :date_publication, matiere_id = :matiere_id, classe_id = :classe_id WHERE id = :id");
            $result = $stmt->execute([
                'titre' => $titre,
                'description' => $description,
                'fichier_pdf' => $fichier_pdf,
                'taille_fichier' => $taille_fichier,
                'date_publication' => $date_publication,
                'matiere_id' => $matiere_id,
                'classe_id' => $classe_id,
                'id' => $id
            ]);
        } else {
            // Mise à jour sans nouveau fichier
            $stmt = $pdo->prepare("UPDATE Cours SET titre = :titre, description = :description, date_publication = :date_publication, matiere_id = :matiere_id, classe_id = :classe_id WHERE id = :id");
            $result = $stmt->execute([
                'titre' => $titre,
                'description' => $description,
                'date_publication' => $date_publication,
                'matiere_id' => $matiere_id,
                'classe_id' => $classe_id,
                'id' => $id
            ]);
        }

        if ($result) {
            error_log("Cours updated successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Cours mis à jour avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Échec de la mise à jour']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("DELETE Data: " . json_encode($data));

    if (!isset($data['id'])) {
        echo json_encode(['success' => false, 'error' => 'ID du cours requis']);
        exit;
    }

    $id = intval($data['id']);

    // Récupérer les infos du cours pour supprimer le fichier
    try {
        $checkStmt = $pdo->prepare("SELECT fichier_pdf FROM cours WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        $cours = $checkStmt->fetch();
        if (!$cours) {
            echo json_encode(['success' => false, 'error' => 'Cours non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check cours exists error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM cours WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);

        if ($result && $stmt->rowCount() > 0) {
            // Supprimer le fichier PDF
            if ($cours['fichier_pdf'] && file_exists($uploadDir . $cours['fichier_pdf'])) {
                unlink($uploadDir . $cours['fichier_pdf']);
                error_log("File deleted: " . $cours['fichier_pdf']);
            }

            error_log("Cours deleted successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Cours supprimé avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Aucun cours supprimé']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
