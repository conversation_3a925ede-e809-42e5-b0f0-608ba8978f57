import React from 'react';
import '../css/Pagination.css';

const Pagination = ({ 
    currentPage, 
    totalPages, 
    onPageChange, 
    itemsPerPage = 10, 
    totalItems = 0 
}) => {
    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;
        
        if (totalPages <= maxVisiblePages) {
            // Si moins de 5 pages, afficher toutes
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Logique pour afficher 5 pages autour de la page actuelle
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);
            
            // Ajuster si on est près du début ou de la fin
            if (currentPage <= 3) {
                endPage = 5;
            }
            if (currentPage >= totalPages - 2) {
                startPage = totalPages - 4;
            }
            
            for (let i = startPage; i <= endPage; i++) {
                pages.push(i);
            }
        }
        
        return pages;
    };

    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);

    if (totalPages <= 1) return null;

    return (
        <div className="pagination-container">
            <div className="pagination-info">
                <span>
                    Affichage de {startItem} à {endItem} sur {totalItems} éléments
                </span>
            </div>
            
            <div className="pagination-controls">
                {/* Bouton Première page */}
                <button
                    className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
                    onClick={() => {
                        console.log('⏮️ Navigation: Première page');
                        onPageChange(1);
                    }}
                    disabled={currentPage === 1}
                    title="Première page"
                >
                    ⏮️
                </button>

                {/* Bouton Page précédente */}
                <button
                    className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
                    onClick={() => {
                        console.log(`⬅️ Navigation: Page ${currentPage} → Page ${currentPage - 1}`);
                        onPageChange(currentPage - 1);
                    }}
                    disabled={currentPage === 1}
                    title="Page précédente"
                >
                    ⬅️
                </button>
                
                {/* Numéros de pages */}
                {getPageNumbers().map(pageNumber => (
                    <button
                        key={pageNumber}
                        className={`pagination-btn page-number ${currentPage === pageNumber ? 'active' : ''}`}
                        onClick={() => {
                            console.log(`📄 Navigation: Page ${currentPage} → Page ${pageNumber}`);
                            onPageChange(pageNumber);
                        }}
                    >
                        {pageNumber}
                    </button>
                ))}
                
                {/* Bouton Page suivante */}
                <button
                    className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
                    onClick={() => {
                        console.log(`➡️ Navigation: Page ${currentPage} → Page ${currentPage + 1}`);
                        onPageChange(currentPage + 1);
                    }}
                    disabled={currentPage === totalPages}
                    title="Page suivante"
                >
                    ➡️
                </button>

                {/* Bouton Dernière page */}
                <button
                    className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
                    onClick={() => {
                        console.log(`⏭️ Navigation: Dernière page (${totalPages})`);
                        onPageChange(totalPages);
                    }}
                    disabled={currentPage === totalPages}
                    title="Dernière page"
                >
                    ⏭️
                </button>
            </div>
            
            <div className="pagination-summary">
                <span>
                    Page {currentPage} sur {totalPages}
                </span>
            </div>
        </div>
    );
};

export default Pagination;
