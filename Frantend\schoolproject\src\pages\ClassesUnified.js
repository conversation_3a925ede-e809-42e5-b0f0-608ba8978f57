import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import Pagination from '../components/Pagination';
import usePagination from '../hooks/usePagination';
import useSearch from '../hooks/useSearch';
import '../css/UnifiedPages.css';
import '../css/Pagination.css';

const ClassesUnified = () => {
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);

    // Hooks personnalisés
    const { 
        searchTerm, 
        setSearchTerm, 
        filterValue, 
        setFilterValue, 
        filteredData, 
        getUniqueFilterValues, 
        clearFilters, 
        hasActiveFilters 
    } = useSearch(
        classes,
        ['nom', 'filiere_nom', 'niveau_nom'], // Champs de recherche
        'filiere_nom' // Champ de filtrage
    );

    const { 
        paginatedData, 
        currentPage, 
        totalPages, 
        goToPage, 
        resetPagination, 
        paginationInfo 
    } = usePagination(filteredData, 10);

    useEffect(() => {
        fetchClasses();
    }, []);

    useEffect(() => {
        resetPagination();
    }, [filteredData, resetPagination]);

    const fetchClasses = async () => {
        try {
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/getClasses.php');
            setClasses(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des classes:', error);
            Swal.fire('Erreur', 'Impossible de charger les classes', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleClearFilters = () => {
        clearFilters();
    };

    if (loading) {
        return (
            <div className="unified-container">
                <div className="unified-loading">
                    <div className="unified-spinner"></div>
                    <p>Chargement des classes...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="unified-container">
            {/* En-tête */}
            <div className="unified-header">
                <h1 className="unified-title">
                    <span className="unified-title-icon">🏫</span>
                    Gestion des Classes
                </h1>
                <div className="unified-header-actions">
                    <span className="unified-count">
                        {filteredData.length} classe(s)
                    </span>
                    <button className="unified-btn unified-btn-primary">
                        ➕ Nouvelle Classe
                    </button>
                </div>
            </div>

            {/* Filtres */}
            <div className="unified-filters">
                <div className="unified-filters-grid">
                    <div className="unified-search-box">
                        <input
                            type="text"
                            className="unified-search-input"
                            placeholder="🔍 Rechercher par nom, filière ou niveau..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <span className="unified-search-icon">🔍</span>
                    </div>
                    
                    <select
                        className="unified-filter-select"
                        value={filterValue}
                        onChange={(e) => setFilterValue(e.target.value)}
                    >
                        <option value="all">Toutes les filières</option>
                        {getUniqueFilterValues().map(value => (
                            <option key={value} value={value}>{value}</option>
                        ))}
                    </select>
                    
                    {hasActiveFilters && (
                        <button 
                            className="unified-clear-btn"
                            onClick={handleClearFilters}
                        >
                            ✖️ Effacer
                        </button>
                    )}
                </div>
            </div>

            {/* Contenu principal */}
            <div className="unified-content">
                {filteredData.length === 0 ? (
                    <div className="unified-empty">
                        <div className="unified-empty-icon">🏫</div>
                        <h3 className="unified-empty-title">Aucune classe trouvée</h3>
                        <p className="unified-empty-text">
                            {hasActiveFilters 
                                ? 'Aucune classe ne correspond à vos critères de recherche.'
                                : 'Aucune classe n\'est disponible pour le moment.'
                            }
                        </p>
                        {hasActiveFilters && (
                            <button 
                                className="unified-btn unified-btn-primary"
                                onClick={handleClearFilters}
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <>
                        <table className="unified-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom de la Classe</th>
                                    <th>Filière</th>
                                    <th>Niveau</th>
                                    <th>Capacité</th>
                                    <th>Étudiants</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedData.map((classe) => (
                                    <tr key={classe.id}>
                                        <td>
                                            <span className="unified-badge unified-badge-primary">
                                                #{classe.id}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50' }}>
                                                {classe.nom}
                                            </strong>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-info">
                                                {classe.filiere_nom || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-warning">
                                                {classe.niveau_nom || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ color: '#495057' }}>
                                                {classe.capacite || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-success">
                                                {classe.nb_etudiants || 0}
                                            </span>
                                        </td>
                                        <td>
                                            <span className={`unified-badge ${
                                                classe.statut === 'active' ? 'unified-badge-success' : 'unified-badge-danger'
                                            }`}>
                                                {classe.statut === 'active' ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="unified-actions">
                                                <button 
                                                    className="unified-btn unified-btn-info"
                                                    title="Voir détails"
                                                >
                                                    👁️ Voir
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-warning"
                                                    title="Modifier"
                                                >
                                                    ✏️ Modifier
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-danger"
                                                    title="Supprimer"
                                                >
                                                    🗑️ Supprimer
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        {/* Pagination */}
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={goToPage}
                            itemsPerPage={10}
                            totalItems={paginationInfo.totalItems}
                        />
                    </>
                )}
            </div>
        </div>
    );
};

export default ClassesUnified;
