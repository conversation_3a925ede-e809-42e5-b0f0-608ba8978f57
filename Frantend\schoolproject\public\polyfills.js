// Polyfills pour résoudre les erreurs de compatibilité

// Polyfill pour process
if (typeof global === 'undefined') {
    window.global = window;
}

if (typeof process === 'undefined') {
    window.process = {
        env: {
            NODE_ENV: 'development'
        },
        version: '',
        versions: {},
        platform: 'browser',
        browser: true
    };
}

// Polyfill pour Buffer si nécessaire
if (typeof Buffer === 'undefined') {
    window.Buffer = {
        isBuffer: function() { return false; },
        from: function(data) { return data; },
        alloc: function(size) { return new Array(size).fill(0); }
    };
}

console.log('Polyfills chargés avec succès');
