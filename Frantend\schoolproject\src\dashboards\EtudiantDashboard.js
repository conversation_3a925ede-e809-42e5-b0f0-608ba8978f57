import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { FaGraduationCap, FaCalendarAlt, FaClipboardList, FaChartLine, FaBookOpen, FaClock } from 'react-icons/fa';

const EtudiantDashboard = () => {
  const { user } = useContext(AuthContext);
  const [emploiDuTemps, setEmploiDuTemps] = useState([]);
  const [devoirs, setDevoirs] = useState([]);
  const [notes, setNotes] = useState([]);
  const [stats, setStats] = useState({
    moyenneGenerale: 0,
    devoirsEnCours: 0,
    coursAujourdhui: 0,
    absences: 0
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simuler des données pour le moment
      setStats({
        moyenneGenerale: 14.5,
        devoirsEnCours: 5,
        coursAujourdhui: 4,
        absences: 2
      });

      setEmploiDuTemps([
        { id: 1, matiere: 'Mathématiques', heure: '08:00-09:30', salle: 'A101', enseignant: 'M. Dupont' },
        { id: 2, matiere: 'Français', heure: '10:00-11:30', salle: 'B205', enseignant: 'Mme Martin' },
        { id: 3, matiere: 'Histoire', heure: '14:00-15:30', salle: 'C301', enseignant: 'M. Bernard' },
        { id: 4, matiere: 'Anglais', heure: '15:45-17:15', salle: 'D102', enseignant: 'Ms Johnson' }
      ]);

      setDevoirs([
        { id: 1, matiere: 'Mathématiques', titre: 'Exercices sur les fonctions', dateRendu: '2024-01-15', statut: 'en_cours' },
        { id: 2, matiere: 'Français', titre: 'Dissertation sur Molière', dateRendu: '2024-01-18', statut: 'en_cours' },
        { id: 3, matiere: 'Physique', titre: 'TP sur la mécanique', dateRendu: '2024-01-20', statut: 'a_faire' }
      ]);

      setNotes([
        { id: 1, matiere: 'Mathématiques', note: 16, coefficient: 2, date: '2024-01-10' },
        { id: 2, matiere: 'Français', note: 13, coefficient: 3, date: '2024-01-08' },
        { id: 3, matiere: 'Histoire', note: 15, coefficient: 2, date: '2024-01-05' }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  };

  const getStatutColor = (statut) => {
    switch(statut) {
      case 'en_cours': return '#ffc107';
      case 'termine': return '#28a745';
      case 'a_faire': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatutText = (statut) => {
    switch(statut) {
      case 'en_cours': return 'En cours';
      case 'termine': return 'Terminé';
      case 'a_faire': return 'À faire';
      default: return 'Inconnu';
    }
  };

  const styles = {
    container: {
      padding: '20px',
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
    },
    header: {
      marginBottom: '30px',
    },
    welcomeText: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    subtitle: {
      color: '#666',
      fontSize: '1.1rem',
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    statCard: {
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      textAlign: 'center',
      transition: 'transform 0.3s ease',
    },
    statIcon: {
      fontSize: '2rem',
      marginBottom: '10px',
      color: '#007bff',
    },
    statNumber: {
      fontSize: '1.8rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '5px',
    },
    statLabel: {
      color: '#666',
      fontSize: '0.9rem',
    },
    contentGrid: {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gap: '30px',
      marginBottom: '30px',
    },
    card: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    },
    cardTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
    },
    cardIcon: {
      marginRight: '10px',
      color: '#007bff',
    },
    scheduleItem: {
      padding: '15px',
      borderLeft: '4px solid #007bff',
      backgroundColor: '#f8f9ff',
      marginBottom: '15px',
      borderRadius: '5px',
    },
    scheduleHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '8px',
    },
    scheduleTitle: {
      fontWeight: 'bold',
      color: '#333',
    },
    scheduleTime: {
      color: '#007bff',
      fontSize: '0.9rem',
    },
    scheduleDetails: {
      color: '#666',
      fontSize: '0.9rem',
    },
    homeworkItem: {
      padding: '15px',
      border: '1px solid #eee',
      borderRadius: '8px',
      marginBottom: '15px',
    },
    homeworkHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '10px',
    },
    homeworkTitle: {
      fontWeight: 'bold',
      color: '#333',
    },
    homeworkStatus: {
      padding: '4px 12px',
      borderRadius: '20px',
      fontSize: '0.8rem',
      fontWeight: 'bold',
      color: 'white',
    },
    homeworkDetails: {
      color: '#666',
      fontSize: '0.9rem',
    },
    gradeItem: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '12px',
      borderBottom: '1px solid #eee',
    },
    gradeSubject: {
      fontWeight: 'bold',
      color: '#333',
    },
    gradeValue: {
      fontSize: '1.2rem',
      fontWeight: 'bold',
      color: '#007bff',
    },
    fullWidthCard: {
      gridColumn: '1 / -1',
    },
  };

  const statCards = [
    { icon: FaChartLine, number: stats.moyenneGenerale + '/20', label: 'Moyenne Générale', color: '#28a745' },
    { icon: FaClipboardList, number: stats.devoirsEnCours, label: 'Devoirs en Cours', color: '#ffc107' },
    { icon: FaCalendarAlt, number: stats.coursAujourdhui, label: "Cours Aujourd'hui", color: '#007bff' },
    { icon: FaClock, number: stats.absences, label: 'Absences', color: '#dc3545' },
  ];

  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <h1 style={styles.welcomeText}>
          <FaGraduationCap style={{ marginRight: '15px', color: '#007bff' }} />
          Bienvenue, {user?.email || 'Étudiant'}
        </h1>
        <p style={styles.subtitle}>Tableau de bord étudiant - Suivez vos cours et vos résultats</p>
      </div>

      {/* Statistiques */}
      <div style={styles.statsGrid}>
        {statCards.map((stat, index) => (
          <div 
            key={index} 
            style={styles.statCard}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={styles.statIcon}>
              <stat.icon />
            </div>
            <div style={styles.statNumber}>{stat.number}</div>
            <div style={styles.statLabel}>{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Contenu principal */}
      <div style={styles.contentGrid}>
        {/* Emploi du temps */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaCalendarAlt style={styles.cardIcon} />
            Emploi du Temps - Aujourd'hui
          </h2>
          {emploiDuTemps.map(cours => (
            <div key={cours.id} style={styles.scheduleItem}>
              <div style={styles.scheduleHeader}>
                <span style={styles.scheduleTitle}>{cours.matiere}</span>
                <span style={styles.scheduleTime}>{cours.heure}</span>
              </div>
              <div style={styles.scheduleDetails}>
                Salle: {cours.salle} | Enseignant: {cours.enseignant}
              </div>
            </div>
          ))}
        </div>

        {/* Devoirs */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaClipboardList style={styles.cardIcon} />
            Devoirs à Rendre
          </h2>
          {devoirs.map(devoir => (
            <div key={devoir.id} style={styles.homeworkItem}>
              <div style={styles.homeworkHeader}>
                <span style={styles.homeworkTitle}>{devoir.titre}</span>
                <span 
                  style={{
                    ...styles.homeworkStatus, 
                    backgroundColor: getStatutColor(devoir.statut)
                  }}
                >
                  {getStatutText(devoir.statut)}
                </span>
              </div>
              <div style={styles.homeworkDetails}>
                {devoir.matiere} | À rendre le: {devoir.dateRendu}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Notes récentes */}
      <div style={{...styles.card, ...styles.fullWidthCard}}>
        <h2 style={styles.cardTitle}>
          <FaBookOpen style={styles.cardIcon} />
          Notes Récentes
        </h2>
        {notes.map(note => (
          <div key={note.id} style={styles.gradeItem}>
            <div>
              <span style={styles.gradeSubject}>{note.matiere}</span>
              <div style={{ color: '#666', fontSize: '0.9rem' }}>
                Coefficient: {note.coefficient} | Date: {note.date}
              </div>
            </div>
            <span style={styles.gradeValue}>{note.note}/20</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EtudiantDashboard;
