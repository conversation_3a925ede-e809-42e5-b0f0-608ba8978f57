<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Parent-Étudiant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.info {
            color: #007bff;
        }
        .relations-list {
            margin-top: 20px;
        }
        .relation-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Parent-Étudiant</h1>
        <p>Interface de test pour vérifier les fonctionnalités CRUD de l'API parent_etudiant</p>
    </div>

    <div class="container">
        <h2>📋 Tests de base</h2>
        <div class="test-section">
            <h3>1. Test de lecture (GET)</h3>
            <button class="btn" onclick="testRead()">🔍 Lire toutes les relations</button>
        </div>

        <div class="test-section">
            <h3>2. Test de création (POST)</h3>
            <div class="form-group">
                <label>Parent ID:</label>
                <input type="number" id="create_parent_id" placeholder="ID du parent" value="1">
            </div>
            <div class="form-group">
                <label>Étudiant ID:</label>
                <input type="number" id="create_etudiant_id" placeholder="ID de l'étudiant" value="1">
            </div>
            <div class="form-group">
                <label>Lien de parenté:</label>
                <select id="create_lien_parente">
                    <option value="Père">Père</option>
                    <option value="Mère">Mère</option>
                    <option value="Tuteur">Tuteur</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            <button class="btn" onclick="testCreate()">➕ Créer une relation</button>
        </div>

        <div class="test-section">
            <h3>3. Test de modification (PUT)</h3>
            <div class="form-group">
                <label>ID de la relation à modifier:</label>
                <input type="number" id="update_id" placeholder="ID de la relation">
            </div>
            <div class="form-group">
                <label>Nouveau lien de parenté:</label>
                <select id="update_lien_parente">
                    <option value="Père">Père</option>
                    <option value="Mère">Mère</option>
                    <option value="Tuteur">Tuteur</option>
                    <option value="Autre">Autre</option>
                </select>
            </div>
            <button class="btn btn-warning" onclick="testUpdate()">✏️ Modifier la relation</button>
        </div>

        <div class="test-section">
            <h3>4. Test de suppression (DELETE)</h3>
            <div class="form-group">
                <label>ID de la relation à supprimer:</label>
                <input type="number" id="delete_id" placeholder="ID de la relation">
            </div>
            <button class="btn btn-danger" onclick="testDelete()">🗑️ Supprimer la relation</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Résultats des tests</h2>
        <div id="log" class="log"></div>
    </div>

    <div class="container">
        <h2>📋 Relations actuelles</h2>
        <div id="relations-list" class="relations-list"></div>
    </div>

    <script>
        const API_URL = 'http://localhost/Project_PFE/Backend/pages/parent_etudiant/';

        function addLog(message, type = 'info', data = null) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            let logMessage = `[${timestamp}] ${message}`;
            if (data) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            
            entry.textContent = logMessage;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        async function testRead() {
            try {
                addLog('🔄 Test GET - Lecture des relations...', 'info');
                
                const response = await fetch(API_URL, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addLog('✅ GET réussi', 'success', data);
                    displayRelations(data);
                } else {
                    addLog('❌ Erreur GET', 'error', data);
                }
            } catch (error) {
                addLog('❌ Erreur réseau GET', 'error', error.message);
            }
        }

        async function testCreate() {
            try {
                const parent_id = document.getElementById('create_parent_id').value;
                const etudiant_id = document.getElementById('create_etudiant_id').value;
                const lien_parente = document.getElementById('create_lien_parente').value;
                
                if (!parent_id || !etudiant_id) {
                    addLog('❌ Veuillez remplir tous les champs', 'error');
                    return;
                }
                
                const data = {
                    parent_id: parseInt(parent_id),
                    etudiant_id: parseInt(etudiant_id),
                    lien_parente: lien_parente
                };
                
                addLog('🔄 Test POST - Création d\'une relation...', 'info', data);
                
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addLog('✅ POST réussi', 'success', result);
                    testRead(); // Rafraîchir la liste
                } else {
                    addLog('❌ Erreur POST', 'error', result);
                }
            } catch (error) {
                addLog('❌ Erreur réseau POST', 'error', error.message);
            }
        }

        async function testUpdate() {
            try {
                const id = document.getElementById('update_id').value;
                const lien_parente = document.getElementById('update_lien_parente').value;
                
                if (!id) {
                    addLog('❌ Veuillez spécifier l\'ID de la relation', 'error');
                    return;
                }
                
                const data = {
                    id: parseInt(id),
                    lien_parente: lien_parente
                };
                
                addLog('🔄 Test PUT - Modification d\'une relation...', 'info', data);
                
                const response = await fetch(API_URL, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addLog('✅ PUT réussi', 'success', result);
                    testRead(); // Rafraîchir la liste
                } else {
                    addLog('❌ Erreur PUT', 'error', result);
                }
            } catch (error) {
                addLog('❌ Erreur réseau PUT', 'error', error.message);
            }
        }

        async function testDelete() {
            try {
                const id = document.getElementById('delete_id').value;
                
                if (!id) {
                    addLog('❌ Veuillez spécifier l\'ID de la relation', 'error');
                    return;
                }
                
                const data = { id: parseInt(id) };
                
                addLog('🔄 Test DELETE - Suppression d\'une relation...', 'info', data);
                
                const response = await fetch(API_URL, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addLog('✅ DELETE réussi', 'success', result);
                    testRead(); // Rafraîchir la liste
                } else {
                    addLog('❌ Erreur DELETE', 'error', result);
                }
            } catch (error) {
                addLog('❌ Erreur réseau DELETE', 'error', error.message);
            }
        }

        function displayRelations(relations) {
            const container = document.getElementById('relations-list');
            container.innerHTML = '';
            
            if (!relations || relations.length === 0) {
                container.innerHTML = '<p>Aucune relation trouvée</p>';
                return;
            }
            
            relations.forEach(relation => {
                const item = document.createElement('div');
                item.className = 'relation-item';
                item.innerHTML = `
                    <strong>ID:</strong> ${relation.id} | 
                    <strong>Parent:</strong> ${relation.parent_nom} (${relation.parent_email}) | 
                    <strong>Étudiant:</strong> ${relation.etudiant_nom} (${relation.etudiant_email}) | 
                    <strong>Lien:</strong> ${relation.lien_parente}
                `;
                container.appendChild(item);
            });
        }

        // Charger les relations au démarrage
        window.onload = function() {
            addLog('🚀 Interface de test chargée', 'info');
            testRead();
        };
    </script>
</body>
</html>
