-- ============================================================================
-- MISE À JOUR DE LA TABLE NOTIFICATIONS POUR L'INTÉGRATION MESSAGERIE
-- ============================================================================

-- Supprimer la table existante si elle existe
DROP TABLE IF EXISTS `notifications`;

-- Créer la nouvelle table notifications avec structure améliorée
CREATE TABLE `notifications` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NOT NULL,                          -- Destinataire de la notification
    
    -- Type et contenu de la notification
    `type_notification` ENUM('message', 'system', 'reminder', 'absence', 'retard', 'cours', 'devoir') DEFAULT 'message',
    `titre` VARCHAR(255) NOT NULL,                          -- Titre court de la notification
    `message` TEXT NOT NULL,                                -- Contenu détaillé de la notification
    
    -- Dates et statut
    `date_envoi` DATETIME DEFAULT CURRENT_TIMESTAMP,        -- Date de création
    `lu` BOOLEAN DEFAULT FALSE,                             -- Statut de lecture
    `date_lecture` DATETIME NULL DEFAULT NULL,              -- Date de lecture
    
    -- Références pour les notifications de messages
    `message_id` INT NULL DEFAULT NULL,                     -- Référence au message source (si applicable)
    `expediteur_id` INT NULL DEFAULT NULL,                  -- Expéditeur du message (si applicable)
    
    -- Métadonnées
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Clés étrangères
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`expediteur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Index pour optimiser les performances
    INDEX `idx_utilisateur_id` (`utilisateur_id`),
    INDEX `idx_type_notification` (`type_notification`),
    INDEX `idx_date_envoi` (`date_envoi`),
    INDEX `idx_lu` (`lu`),
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_expediteur_id` (`expediteur_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- DONNÉES DE TEST POUR LES NOTIFICATIONS
-- ============================================================================

-- Insérer quelques notifications de test (optionnel)
INSERT INTO `notifications` (`utilisateur_id`, `type_notification`, `titre`, `message`, `date_envoi`, `lu`) VALUES
(2, 'message', 'Nouveau message', 'Vous avez reçu un nouveau message de Admin', NOW() - INTERVAL 1 HOUR, FALSE),
(3, 'system', 'Mise à jour système', 'Le système de messagerie a été mis à jour avec de nouvelles fonctionnalités', NOW() - INTERVAL 2 HOUR, TRUE),
(1, 'reminder', 'Rappel', 'N\'oubliez pas de consulter vos messages', NOW() - INTERVAL 3 HOUR, FALSE);

-- ============================================================================
-- VÉRIFICATION DE LA STRUCTURE
-- ============================================================================

-- Afficher la structure de la table
DESCRIBE `notifications`;

-- Compter les notifications
SELECT COUNT(*) as total_notifications FROM `notifications`;

-- Afficher les contraintes de clés étrangères
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'notifications' 
AND CONSTRAINT_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;
