import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const DevoirsCRUD = () => {
    const { user } = useContext(AuthContext);
    const [devoirs, setDevoirs] = useState([]);
    const [matieres, setMatieres] = useState([]);
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingDevoir, setEditingDevoir] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [matiereFilter, setMatiereFilter] = useState('all');
    const [classeFilter, setClasseFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        titre: '',
        description: '',
        fichier_pdf: null,
        date_remise: '',
        matiere_id: '',
        classe_id: ''
    });

    // Vérifier si l'utilisateur est Admin ou Enseignant
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';
    const isTeacher = user?.role === 'Enseignant' || user?.role === 'enseignant' || user?.role === 'teacher';
    const canManage = isAdmin || isTeacher;

    useEffect(() => {
        fetchDevoirs();
        fetchMatieres();
        fetchClasses();
    }, []);

    const fetchMatieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setMatieres(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des matières:', error);
            setMatieres([
                { id: 1, nom: 'Mathématiques' },
                { id: 2, nom: 'Physique' },
                { id: 3, nom: 'Français' },
                { id: 4, nom: 'Histoire' }
            ]);
        }
    };

    const fetchClasses = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setClasses(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des classes:', error);
            setClasses([
                { id: 1, nom: 'Classe A' },
                { id: 2, nom: 'Classe B' },
                { id: 3, nom: 'Classe C' },
                { id: 4, nom: 'Classe D' }
            ]);
        }
    };

    const fetchDevoirs = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des devoirs...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/devoirs/devoir.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let devoirsData = response.data;
            if (!Array.isArray(devoirsData)) {
                devoirsData = [];
            }

            setDevoirs(devoirsData);
            console.log('✅ Devoirs chargés:', devoirsData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des devoirs:', error);

            // Données de test avec fichiers PDF (compatible avec structure BDD réelle)
            const testDevoirs = [
                {
                    id: 1,
                    titre: 'Exercices de Mathématiques',
                    description: 'Résoudre les équations du chapitre 3',
                    fichier_pdf: 'math_exercices.pdf',
                    date_remise: '2024-02-15',
                    matiere_id: 1,
                    matiere_nom: 'Mathématiques',
                    classe_id: 1,
                    classe_nom: 'Classe A',
                    taille_fichier: '1.5 MB'
                },
                {
                    id: 2,
                    titre: 'Dissertation de Français',
                    description: 'Analyse littéraire sur "Le Petit Prince"',
                    fichier_pdf: 'francais_dissertation.pdf',
                    date_remise: '2024-02-20',
                    matiere_id: 3,
                    matiere_nom: 'Français',
                    classe_id: 2,
                    classe_nom: 'Classe B',
                    taille_fichier: '2.1 MB'
                },
                {
                    id: 3,
                    titre: 'Expérience de Physique',
                    description: 'Rapport sur l\'électricité statique',
                    fichier_pdf: 'physique_experience.pdf',
                    date_remise: '2024-02-25',
                    matiere_id: 2,
                    matiere_nom: 'Physique',
                    classe_id: 1,
                    classe_nom: 'Classe A',
                    taille_fichier: '3.2 MB'
                },
                {
                    id: 4,
                    titre: 'Recherche Historique',
                    description: 'Les événements de la Seconde Guerre mondiale',
                    fichier_pdf: 'histoire_recherche.pdf',
                    date_remise: '2024-03-01',
                    matiere_id: 4,
                    matiere_nom: 'Histoire',
                    classe_id: 3,
                    classe_nom: 'Classe C',
                    taille_fichier: '2.8 MB'
                },
                {
                    id: 5,
                    titre: 'Problèmes d\'Algèbre',
                    description: 'Résolution de systèmes d\'équations',
                    fichier_pdf: 'math_algebre.pdf',
                    date_remise: '2024-03-05',
                    matiere_id: 1,
                    matiere_nom: 'Mathématiques',
                    classe_id: 2,
                    classe_nom: 'Classe B',
                    taille_fichier: '1.9 MB'
                },
                {
                    id: 6,
                    titre: 'Analyse de Texte',
                    description: 'Commentaire composé sur un poème de Baudelaire',
                    fichier_pdf: 'francais_analyse.pdf',
                    date_remise: '2024-03-10',
                    matiere_id: 3,
                    matiere_nom: 'Français',
                    classe_id: 3,
                    classe_nom: 'Classe C',
                    taille_fichier: '1.7 MB'
                }
            ];

            setDevoirs(testDevoirs);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testDevoirs.length} devoirs de test avec fichiers PDF.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent créer/modifier des devoirs', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/devoirs/devoir.php';

            // Validation côté frontend
            if (!formData.titre.trim()) {
                Swal.fire('Erreur', 'Le titre du devoir est requis', 'error');
                return;
            }

            if (!formData.matiere_id) {
                Swal.fire('Erreur', 'Veuillez sélectionner une matière', 'error');
                return;
            }

            if (!formData.classe_id) {
                Swal.fire('Erreur', 'Veuillez sélectionner une classe', 'error');
                return;
            }

            // Créer FormData pour gérer l'upload de fichier
            const formDataToSend = new FormData();
            formDataToSend.append('titre', formData.titre.trim());
            formDataToSend.append('description', formData.description.trim());
            formDataToSend.append('matiere_id', formData.matiere_id);
            formDataToSend.append('classe_id', formData.classe_id);
            formDataToSend.append('date_remise', formData.date_remise);

            if (editingDevoir) {
                formDataToSend.append('id', editingDevoir.id);
                formDataToSend.append('_method', 'PUT'); // Champ caché pour identifier PUT
            }

            if (formData.fichier_pdf) {
                formDataToSend.append('fichier_pdf', formData.fichier_pdf);
            }

            // Pour les modifications, utiliser POST avec _method=PUT car FormData ne fonctionne pas bien avec PUT
            const response = await axios({
                method: 'POST',
                url,
                data: formDataToSend,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 30000 // 30 secondes pour l'upload
            });

            if (response.data && response.data.success === true) {
                Swal.fire('Succès', `Devoir ${editingDevoir ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingDevoir(null);
                resetForm();
                fetchDevoirs();
            } else {
                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('❌ Erreur complète:', error);

            let errorMessage = 'Une erreur est survenue';

            if (error.response?.data?.error) {
                errorMessage = error.response.data.error;
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.status === 413) {
                errorMessage = 'Le fichier est trop volumineux (max 10MB)';
            } else if (error.response?.status === 415) {
                errorMessage = 'Type de fichier non supporté (PDF uniquement)';
            } else if (error.message) {
                errorMessage = error.message;
            }

            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (devoir) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent modifier des devoirs', 'error');
            return;
        }

        if (!devoir || !devoir.id) {
            Swal.fire('Erreur', 'Devoir invalide', 'error');
            return;
        }

        setEditingDevoir(devoir);
        setFormData({
            titre: devoir.titre || '',
            description: devoir.description || '',
            fichier_pdf: null, // Ne pas pré-remplir le fichier
            date_remise: devoir.date_remise || '',
            matiere_id: devoir.matiere_id || '',
            classe_id: devoir.classe_id || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent supprimer des devoirs', 'error');
            return;
        }

        if (!id || isNaN(id)) {
            Swal.fire('Erreur', 'ID du devoir invalide', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action supprimera également le fichier PDF associé!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/devoirs/devoir.php';

                const response = await axios({
                    method: 'DELETE',
                    url: url,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify({ id: parseInt(id) }),
                    timeout: 10000
                });

                if (response.data && response.data.success === true) {
                    Swal.fire('Supprimé!', 'Le devoir et son fichier PDF ont été supprimés.', 'success');
                    fetchDevoirs();
                } else {
                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                let errorMessage = 'Impossible de supprimer le devoir';
                if (error.response?.data?.error) {
                    errorMessage = error.response.data.error;
                }
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    // Fonction pour télécharger le PDF
    const handleDownloadPDF = async (devoir) => {
        try {
            const token = localStorage.getItem('token');

            // Vérifier que le devoir a un fichier PDF
            if (!devoir.fichier_pdf) {
                Swal.fire('Erreur', 'Aucun fichier PDF associé à ce devoir', 'error');
                return;
            }

            const url = `http://localhost/Project_PFE/Backend/pages/devoirs/download.php?file=${encodeURIComponent(devoir.fichier_pdf)}&devoir_id=${devoir.id}`;

            const response = await axios({
                method: 'GET',
                url,
                headers: {
                    Authorization: `Bearer ${token}`
                },
                responseType: 'blob',
                timeout: 30000 // 30 secondes de timeout
            });

            // Vérifier que la réponse est bien un PDF
            if (response.data.type !== 'application/pdf' && !response.headers['content-type']?.includes('application/pdf')) {
                console.error('❌ Type de fichier incorrect:', response.data.type);
                Swal.fire('Erreur', 'Le fichier téléchargé n\'est pas un PDF valide', 'error');
                return;
            }

            // Créer un lien de téléchargement
            const blob = new Blob([response.data], { type: 'application/pdf' });
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${devoir.titre.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(downloadUrl);

            Swal.fire({
                title: 'Téléchargement réussi!',
                text: `Le fichier "${devoir.titre}.pdf" a été téléchargé.`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

        } catch (error) {
            console.error('❌ Erreur téléchargement:', error);
            let errorMessage = 'Impossible de télécharger le fichier PDF';
            if (error.response?.status === 404) {
                errorMessage = 'Fichier PDF non trouvé sur le serveur';
            } else if (error.response?.status === 403) {
                errorMessage = 'Accès non autorisé au fichier PDF';
            }
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const resetForm = () => {
        setFormData({
            titre: '',
            description: '',
            fichier_pdf: null,
            date_remise: '',
            matiere_id: '',
            classe_id: ''
        });
    };

    // Fonction de pagination
    const paginate = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    // Filtrage des données
    const filteredDevoirs = devoirs.filter(d => {
        const matchesSearch = d.titre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             d.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             d.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             d.classe_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesMatiere = matiereFilter === 'all' || d.matiere_id?.toString() === matiereFilter;
        const matchesClasse = classeFilter === 'all' || d.classe_id?.toString() === classeFilter;

        return matchesSearch && matchesMatiere && matchesClasse;
    });

    // Pagination
    const totalPages = Math.ceil(filteredDevoirs.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentDevoirs = filteredDevoirs.slice(startIndex, startIndex + itemsPerPage);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, matiereFilter, classeFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-content">
                    <div className="loading-icon">📚</div>
                    <h2>Chargement des devoirs...</h2>
                </div>
            </div>
        );
    }

    return (
        <div className="factures-container">
    {/* Header */}
    <div className="page-header">
        <h1>📝 Gestion des Devoirs</h1>
        <div className="header-info">
            <span className="total-count">
                {filteredDevoirs.length} devoir(s) trouvé(s)
                {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
            </span>
            <div style={{ display: 'flex', gap: '10px' }}>
                {canManage && (
                    <button
                        className="btn btn-primary"
                        onClick={() => setShowModal(true)}
                    >
                        <img src="/plus.png" alt="Ajouter" /> Nouveau Devoir
                    </button>
                )}
            </div>
        </div>
    </div>


                      
            {/* Message d'information pour les non-gestionnaires */}
            {!canManage && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les devoirs en mode lecture seule.
                        Seuls les administrateurs et enseignants peuvent créer, modifier ou supprimer des devoirs.
                        Cliquez sur les liens PDF pour télécharger les devoirs.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher un devoir..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="matiere-filter">
                    <select
                        value={matiereFilter}
                        onChange={(e) => {
                            setMatiereFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Toutes les matières</option>
                        {matieres.map(matiere => (
                            <option key={matiere.id} value={matiere.id}>
                                {matiere.nom}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="classe-filter">
                    <select
                        value={classeFilter}
                        onChange={(e) => {
                            setClasseFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Toutes les classes</option>
                        {classes.map(classe => (
                            <option key={classe.id} value={classe.id}>
                                {classe.nom}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredDevoirs.length === 0 ? (
                    <div className="no-data">
                        <img src="/pdf-icon.png" alt="Aucun devoir" />
                        <p>Aucun devoir trouvé</p>
                        {(searchTerm || matiereFilter !== 'all' || classeFilter !== 'all') && (
                            <button
                                onClick={() => {
                                    setSearchTerm('');
                                    setMatiereFilter('all');
                                    setClasseFilter('all');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📝 Titre du Devoir</th>
                                    <th>📖 Matière</th>
                                    <th>🏫 Classe</th>
                                    <th>📅 Date Remise</th>
                                    <th>📄 Fichier PDF</th>
                                    <th>📊 Taille</th>
                                    {canManage && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentDevoirs.map((d) => (
                                    <tr key={d.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{d.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{d.titre}</strong>
                                                {d.description && (
                                                    <small style={{
                                                        display: 'block',
                                                        color: '#6c757d',
                                                        marginTop: '4px'
                                                    }}>
                                                        {d.description.length > 50
                                                            ? d.description.substring(0, 50) + '...'
                                                            : d.description}
                                                    </small>
                                                )}
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#856404'
                                            }}>
                                                {d.matiere_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#d4edda',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#155724'
                                            }}>
                                                {d.classe_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {d.date_remise ? new Date(d.date_remise).toLocaleDateString('fr-FR') : 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            {d.fichier_pdf ? (
                                                <button
                                                    className="btn btn-sm btn-success"
                                                    onClick={() => handleDownloadPDF(d)}
                                                    title="Télécharger le PDF"
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '5px',
                                                        fontSize: '0.8em'
                                                    }}
                                                >
                                                    📥 PDF
                                                </button>
                                            ) : (
                                                <span style={{ fontSize: '0.8em', color: '#6c757d' }}>
                                                    Aucun fichier
                                                </span>
                                            )}
                                        </td>
                                        <td>
                                            <span style={{
                                                fontSize: '0.8em',
                                                color: '#6c757d'
                                            }}>
                                                {d.taille_fichier || 'N/A'}
                                            </span>
                                        </td>
                                        {canManage && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(d)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(d.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredDevoirs.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredDevoirs.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des devoirs</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {matieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Matières disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {classes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Classes disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {currentDevoirs.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un devoir */}
            {showModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content" style={{ maxWidth: '600px' }}>
                        <div className="modal-header">
                            <h3>{editingDevoir ? 'Modifier le devoir' : 'Nouveau devoir'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingDevoir(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            {/* Affichage des informations du devoir en modification */}
                            {editingDevoir && (
                                <div style={{
                                    padding: '15px',
                                    backgroundColor: '#e3f2fd',
                                    borderRadius: '8px',
                                    marginBottom: '20px',
                                    border: '1px solid #bbdefb'
                                }}>
                                    <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>
                                        📝 Modification du devoir #{editingDevoir.id}
                                    </h4>
                                    <p style={{ margin: '5px 0', fontSize: '14px', color: '#1976d2' }}>
                                        📄 Fichier actuel: <strong>{editingDevoir.fichier_pdf || 'Aucun fichier'}</strong>
                                        {editingDevoir.taille_fichier && ` (${editingDevoir.taille_fichier})`}
                                    </p>
                                    <p style={{ margin: '5px 0 0 0', fontSize: '12px', color: '#666' }}>
                                        💡 Laissez le champ fichier vide pour conserver le fichier actuel
                                    </p>
                                </div>
                            )}

                            <div className="form-group">
                                <label>Titre du devoir *</label>
                                <input
                                    type="text"
                                    value={formData.titre}
                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}
                                    placeholder="Ex: Exercices de Mathématiques..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                    placeholder="Description du devoir..."
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>
                                    Fichier PDF {editingDevoir ? '(Optionnel - Laisser vide pour conserver le fichier actuel)' : '(Optionnel)'}
                                </label>
                                <input
                                    type="file"
                                    accept=".pdf"
                                    onChange={(e) => {
                                        const file = e.target.files[0];
                                        if (file) {
                                            // Validation côté client
                                            if (file.type !== 'application/pdf') {
                                                Swal.fire('Erreur', 'Seuls les fichiers PDF sont acceptés', 'error');
                                                e.target.value = '';
                                                return;
                                            }
                                            if (file.size > 10 * 1024 * 1024) {
                                                Swal.fire('Erreur', 'Le fichier ne doit pas dépasser 10MB', 'error');
                                                e.target.value = '';
                                                return;
                                            }
                                        }
                                        setFormData({...formData, fichier_pdf: file});
                                    }}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Formats acceptés: PDF uniquement. Taille max: 10MB
                                    {editingDevoir && !formData.fichier_pdf && (
                                        <span style={{ color: '#17a2b8', display: 'block', marginTop: '5px' }}>
                                            📄 Fichier actuel: {editingDevoir.fichier_pdf || 'Aucun fichier'}
                                            {editingDevoir.taille_fichier && ` (${editingDevoir.taille_fichier})`}
                                        </span>
                                    )}
                                    {formData.fichier_pdf && (
                                        <span style={{ color: '#28a745', display: 'block', marginTop: '5px' }}>
                                            ✅ Nouveau fichier sélectionné: {formData.fichier_pdf.name}
                                            ({(formData.fichier_pdf.size / (1024 * 1024)).toFixed(2)} MB)
                                        </span>
                                    )}
                                </small>
                            </div>

                            <div style={{ display: 'flex', gap: '15px' }}>
                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>Matière *</label>
                                    <select
                                        value={formData.matiere_id}
                                        onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '10px',
                                            border: '1px solid #ced4da',
                                            borderRadius: '4px',
                                            fontSize: '14px'
                                        }}
                                    >
                                        <option value="">Sélectionner une matière</option>
                                        {matieres.map((matiere) => (
                                            <option key={matiere.id} value={matiere.id}>
                                                {matiere.nom}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>Classe *</label>
                                    <select
                                        value={formData.classe_id}
                                        onChange={(e) => setFormData({...formData, classe_id: e.target.value})}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '10px',
                                            border: '1px solid #ced4da',
                                            borderRadius: '4px',
                                            fontSize: '14px'
                                        }}
                                    >
                                        <option value="">Sélectionner une classe</option>
                                        {classes.map((classe) => (
                                            <option key={classe.id} value={classe.id}>
                                                {classe.nom}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            <div className="form-group">
                                <label>Date de remise (Optionnelle)</label>
                                <input
                                    type="date"
                                    value={formData.date_remise}
                                    onChange={(e) => setFormData({...formData, date_remise: e.target.value})}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingDevoir(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingDevoir ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DevoirsCRUD;
