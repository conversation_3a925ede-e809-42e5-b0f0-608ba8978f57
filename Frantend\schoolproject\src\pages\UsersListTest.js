import React, { useState, useEffect } from 'react';

const UsersListTest = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      console.log('🔄 Début de la récupération des utilisateurs...');
      setLoading(true);
      setError(null);
      
      const url = 'http://localhost/Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true';
      console.log('📡 URL appelée:', url);
      
      const response = await fetch(url);
      console.log('📥 Réponse reçue:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('📊 Données reçues:', data);
      
      if (data.success) {
        setUsers(data.users);
        console.log('✅ Utilisateurs chargés:', data.users.length);
      } else {
        setError(data.error || 'Erreur lors du chargement des utilisateurs');
        console.error('❌ Erreur API:', data.error);
      }
    } catch (err) {
      console.error('💥 Erreur de connexion:', err);
      setError('Erreur de connexion au serveur: ' + err.message);
    } finally {
      setLoading(false);
      console.log('🏁 Fin de la récupération');
    }
  };

  const styles = {
    container: {
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      fontFamily: 'Arial, sans-serif'
    },
    header: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      textAlign: 'center',
      marginBottom: '20px'
    },
    status: {
      padding: '15px',
      borderRadius: '8px',
      marginBottom: '20px',
      fontSize: '16px'
    },
    loading: {
      background: '#fff3cd',
      border: '1px solid #ffeaa7',
      color: '#856404'
    },
    error: {
      background: '#f8d7da',
      border: '1px solid #f5c6cb',
      color: '#721c24'
    },
    success: {
      background: '#d4edda',
      border: '1px solid #c3e6cb',
      color: '#155724'
    },
    userCard: {
      background: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '15px',
      margin: '10px 0',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    },
    button: {
      background: '#007bff',
      color: 'white',
      border: 'none',
      padding: '10px 20px',
      borderRadius: '5px',
      cursor: 'pointer',
      margin: '5px'
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h1>🧪 Test Page Utilisateurs</h1>
        <p>Version simplifiée pour diagnostic</p>
      </div>

      {loading && (
        <div style={{...styles.status, ...styles.loading}}>
          🔄 Chargement des utilisateurs en cours...
        </div>
      )}

      {error && (
        <div style={{...styles.status, ...styles.error}}>
          ❌ Erreur: {error}
          <br />
          <button style={styles.button} onClick={fetchUsers}>
            🔄 Réessayer
          </button>
        </div>
      )}

      {!loading && !error && users.length === 0 && (
        <div style={{...styles.status, ...styles.error}}>
          📭 Aucun utilisateur trouvé
          <br />
          <button style={styles.button} onClick={fetchUsers}>
            🔄 Recharger
          </button>
        </div>
      )}

      {!loading && !error && users.length > 0 && (
        <div>
          <div style={{...styles.status, ...styles.success}}>
            ✅ {users.length} utilisateur(s) chargé(s) avec succès
          </div>
          
          <div>
            {users.slice(0, 5).map((user, index) => (
              <div key={user.id || index} style={styles.userCard}>
                <h3>👤 {user.nom || 'Nom non défini'}</h3>
                <p>📧 Email: {user.email || 'Email non défini'}</p>
                <p>🎭 Rôle: {user.role_nom || 'Rôle non défini'}</p>
                <p>🆔 ID: {user.id || 'ID non défini'}</p>
                {user.parent_telephone && (
                  <p>📞 Téléphone: {user.parent_telephone}</p>
                )}
              </div>
            ))}
            
            {users.length > 5 && (
              <div style={{...styles.status, ...styles.success}}>
                ... et {users.length - 5} autre(s) utilisateur(s)
              </div>
            )}
          </div>
        </div>
      )}

      <div style={{marginTop: '30px', padding: '20px', background: '#f8f9fa', borderRadius: '8px'}}>
        <h3>🔧 Informations de débogage</h3>
        <p><strong>URL API:</strong> http://localhost/Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true</p>
        <p><strong>État de chargement:</strong> {loading ? 'En cours' : 'Terminé'}</p>
        <p><strong>Erreur:</strong> {error || 'Aucune'}</p>
        <p><strong>Nombre d'utilisateurs:</strong> {users.length}</p>
        <p><strong>Heure du test:</strong> {new Date().toLocaleString()}</p>
        
        <button style={styles.button} onClick={() => window.location.reload()}>
          🔄 Recharger la page
        </button>
        
        <button 
          style={styles.button} 
          onClick={() => window.open('http://localhost/Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true', '_blank')}
        >
          🌐 Tester l'API directement
        </button>
      </div>
    </div>
  );
};

export default UsersListTest;
