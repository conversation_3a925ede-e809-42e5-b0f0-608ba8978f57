<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Matiere API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Matiere API - Headers: " . json_encode(getallheaders()));
error_log("Matiere API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("POST Data: " . json_encode($data));

    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("POST Error: Nom de la matière requis");
        echo json_encode(['success' => false, 'error' => 'Nom de la matière requis']);
        exit;
    }
    if (!isset($data['filiere_id']) || !is_numeric($data['filiere_id'])) {
        error_log("POST Error: ID de la filière requis");
        echo json_encode(['success' => false, 'error' => 'ID de la filière requis et doit être un nombre']);
        exit;
    }

    $nom = trim($data['nom']);
    $filiere_id = intval($data['filiere_id']);

    // Vérifier si la filière existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Filieres WHERE id = :filiere_id");
        $checkStmt->execute(['filiere_id' => $filiere_id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Filière non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check filiere error: " . $e->getMessage());
    }

    // Vérifier si la matière existe déjà dans cette filière
    try {
        $checkMatiereStmt = $pdo->prepare("SELECT id FROM Matieres WHERE nom = :nom AND filiere_id = :filiere_id");
        $checkMatiereStmt->execute(['nom' => $nom, 'filiere_id' => $filiere_id]);
        if ($checkMatiereStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Cette matière existe déjà dans cette filière']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check matiere error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Matieres (nom, filiere_id) VALUES (:nom, :filiere_id)");
        $stmt->execute(['nom' => $nom, 'filiere_id' => $filiere_id]);
        $newId = $pdo->lastInsertId();
        error_log("Matiere created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Matière ajoutée avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        // On récupère la liste des matières avec le nom de leur filière (jointure)
        $stmt = $pdo->query("
            SELECT Matieres.id, Matieres.nom, Matieres.filiere_id, Filieres.nom AS filiere_nom
            FROM Matieres
            LEFT JOIN Filieres ON Matieres.filiere_id = Filieres.id
            ORDER BY Matieres.id DESC
        ");
        $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($matieres);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);

    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom'])) || !isset($data['filiere_id']) || !is_numeric($data['filiere_id'])) {
        echo json_encode(['error' => 'ID, nom de la matière et ID de la filière requis']);
        exit;
    }

    $id = intval($data['id']);
    $nom = trim($data['nom']);
    $filiere_id = intval($data['filiere_id']);

    try {
        $stmt = $pdo->prepare("UPDATE Matieres SET nom = :nom, filiere_id = :filiere_id WHERE id = :id");
        $stmt->execute(['nom' => $nom, 'filiere_id' => $filiere_id, 'id' => $id]);
        echo json_encode(['success' => true, 'message' => 'Matière mise à jour avec succès']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);

    if (!isset($data['id'])) {
        echo json_encode(['error' => 'ID de la matière requis']);
        exit;
    }

    $id = intval($data['id']);

    try {
        $stmt = $pdo->prepare("DELETE FROM Matieres WHERE id = :id");
        $stmt->execute(['id' => $id]);
        echo json_encode(['success' => true, 'message' => 'Matière supprimée avec succès']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
