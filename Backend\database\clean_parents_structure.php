<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🗑️ NETTOYAGE COMPLET - STRUCTURE PARENTS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    // Vérification de confirmation
    $confirm = $_GET['confirm'] ?? '';
    
    if ($confirm !== 'yes') {
        echo "<div class='warning'>";
        echo "<h2>⚠️ ATTENTION - NETTOYAGE COMPLET</h2>";
        echo "<p><strong>Cette opération va :</strong></p>";
        echo "<ul>";
        echo "<li>🗑️ <strong>SUPPRIMER</strong> la table Parents existante</li>";
        echo "<li>🗑️ <strong>SUPPRIMER</strong> tous les triggers liés aux parents</li>";
        echo "<li>🗑️ <strong>SUPPRIMER</strong> toutes les vues liées aux parents</li>";
        echo "<li>🗑️ <strong>EFFACER</strong> toutes les données parents actuelles</li>";
        echo "<li>🧹 <strong>NETTOYER</strong> les fichiers backend parents</li>";
        echo "</ul>";
        echo "<p class='error'><strong>⚠️ CETTE ACTION EST IRRÉVERSIBLE !</strong></p>";
        echo "<p><strong>Assurez-vous d'avoir :</strong></p>";
        echo "<ul>";
        echo "<li>✅ Sauvegardé les données importantes</li>";
        echo "<li>✅ Confirmé que vous voulez repartir sur une base propre</li>";
        echo "<li>✅ Compris que toute la structure parents sera recréée</li>";
        echo "</ul>";
        echo "<p class='info'><strong>Après le nettoyage, nous créerons :</strong></p>";
        echo "<ul>";
        echo "<li>✅ Nouvelle table Parents selon votre structure</li>";
        echo "<li>✅ Backend API propre et cohérent</li>";
        echo "<li>✅ Interface similaire à celle des rôles</li>";
        echo "<li>✅ Triggers de sécurité pour garantir la cohérence</li>";
        echo "</ul>";
        echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🗑️ CONFIRMER LE NETTOYAGE</a></p>";
        echo "<p><a href='../pages/parents/debug_filtrage.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 Voir l'état actuel</a></p>";
        echo "</div>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🧹 DÉBUT DU NETTOYAGE</h2>";
    echo "<p>Suppression de toute la structure parents existante...</p>";
    echo "</div>";
    
    // Désactiver les vérifications de clés étrangères temporairement
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // Étape 1 : Supprimer les triggers liés aux parents
    echo "<div class='step'>";
    echo "<h3>1. 🗑️ Suppression des Triggers</h3>";
    
    $triggers = [
        'check_parent_role_before_insert',
        'check_parent_role_before_update',
        'check_parent_role_insert',
        'check_parent_role_update'
    ];
    
    foreach ($triggers as $trigger) {
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS `$trigger`");
            echo "<p class='success'>✅ Trigger '$trigger' supprimé</p>";
        } catch (PDOException $e) {
            echo "<p class='warning'>⚠️ Trigger '$trigger' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Étape 2 : Supprimer les vues liées aux parents
    echo "<div class='step'>";
    echo "<h3>2. 👁️ Suppression des Vues</h3>";
    
    $views = [
        'v_parents_complets',
        'parents_securise',
        'v_parents_securise'
    ];
    
    foreach ($views as $view) {
        try {
            $pdo->exec("DROP VIEW IF EXISTS `$view`");
            echo "<p class='success'>✅ Vue '$view' supprimée</p>";
        } catch (PDOException $e) {
            echo "<p class='warning'>⚠️ Vue '$view' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Étape 3 : Sauvegarder les données parents existantes (optionnel)
    echo "<div class='step'>";
    echo "<h3>3. 💾 Sauvegarde des Données Existantes</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'parents'");
        if ($stmt->rowCount() > 0) {
            // Vérifier s'il y a des données
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM parents");
            $count = $countStmt->fetch()['count'];
            
            if ($count > 0) {
                echo "<p class='info'>📊 $count enregistrement(s) trouvé(s) dans la table parents</p>";
                
                // Afficher un aperçu des données
                $stmt = $pdo->query("SELECT * FROM parents LIMIT 5");
                $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($data) > 0) {
                    echo "<h4>📋 Aperçu des données qui seront supprimées :</h4>";
                    echo "<table>";
                    echo "<tr>";
                    foreach (array_keys($data[0]) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    foreach ($data as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                    if ($count > 5) {
                        echo "<p style='color: #666;'>... et " . ($count - 5) . " autre(s)</p>";
                    }
                }
            } else {
                echo "<p class='info'>📊 Table parents vide</p>";
            }
        } else {
            echo "<p class='info'>📊 Table parents n'existe pas</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur lors de la vérification : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 4 : Supprimer la table parents
    echo "<div class='step'>";
    echo "<h3>4. 🗑️ Suppression de la Table Parents</h3>";
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS `parents`");
        echo "<p class='success'>✅ Table 'parents' supprimée</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur suppression table parents : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 5 : Nettoyer les fichiers backend
    echo "<div class='step'>";
    echo "<h3>5. 🧹 Nettoyage des Fichiers Backend</h3>";
    
    $filesToClean = [
        '../pages/parents/parent.php',
        '../pages/parents/get_users_parents.php',
        '../pages/parents/setup_table.php',
        '../pages/parents/insert_test_data.php',
        '../pages/parents/create_test_users_parents.php',
        '../pages/parents/add_role_constraint.php',
        '../pages/parents/test_role_constraint.php',
        '../pages/parents/test_filtrage_utilisateurs.php',
        '../pages/parents/debug_filtrage.php'
    ];
    
    $cleanedFiles = 0;
    foreach ($filesToClean as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                echo "<p class='success'>✅ Fichier supprimé : " . basename($file) . "</p>";
                $cleanedFiles++;
            } else {
                echo "<p class='error'>❌ Impossible de supprimer : " . basename($file) . "</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ Fichier inexistant : " . basename($file) . "</p>";
        }
    }
    
    echo "<p class='info'>📊 $cleanedFiles fichier(s) nettoyé(s)</p>";
    echo "</div>";
    
    // Étape 6 : Vérifier que tout est nettoyé
    echo "<div class='step'>";
    echo "<h3>6. ✅ Vérification du Nettoyage</h3>";
    
    // Vérifier les tables
    $stmt = $pdo->query("SHOW TABLES LIKE 'parents'");
    if ($stmt->rowCount() === 0) {
        echo "<p class='success'>✅ Table 'parents' complètement supprimée</p>";
    } else {
        echo "<p class='error'>❌ Table 'parents' existe encore</p>";
    }
    
    // Vérifier les triggers
    $stmt = $pdo->query("SHOW TRIGGERS LIKE 'parents'");
    $remainingTriggers = $stmt->fetchAll();
    if (count($remainingTriggers) === 0) {
        echo "<p class='success'>✅ Aucun trigger lié aux parents</p>";
    } else {
        echo "<p class='warning'>⚠️ " . count($remainingTriggers) . " trigger(s) restant(s)</p>";
    }
    
    // Vérifier les vues
    $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_" . $pdo->query("SELECT DATABASE()")->fetchColumn() . " LIKE '%parent%'");
    $remainingViews = $stmt->fetchAll();
    if (count($remainingViews) === 0) {
        echo "<p class='success'>✅ Aucune vue liée aux parents</p>";
    } else {
        echo "<p class='warning'>⚠️ " . count($remainingViews) . " vue(s) restante(s)</p>";
    }
    echo "</div>";
    
    // Réactiver les vérifications de clés étrangères
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎯 NETTOYAGE TERMINÉ</h3>";
    echo "<p class='success' style='font-size: 18px;'>🧹 NETTOYAGE COMPLET RÉUSSI !</p>";
    echo "<p>Toute la structure parents existante a été supprimée.</p>";
    
    echo "<h4>✅ Éléments supprimés :</h4>";
    echo "<ul>";
    echo "<li>✅ Table 'parents' et toutes ses données</li>";
    echo "<li>✅ Triggers de validation des rôles</li>";
    echo "<li>✅ Vues liées aux parents</li>";
    echo "<li>✅ Fichiers backend obsolètes</li>";
    echo "</ul>";
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><a href='create_new_parents_structure.php'>Créer la nouvelle structure Parents</a></li>";
    echo "<li><a href='create_parents_backend.php'>Créer le nouveau backend</a></li>";
    echo "<li><a href='create_parents_frontend.php'>Créer la nouvelle interface</a></li>";
    echo "</ol>";
    
    echo "<p class='info'><strong>La base est maintenant propre pour recréer une structure cohérente !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
