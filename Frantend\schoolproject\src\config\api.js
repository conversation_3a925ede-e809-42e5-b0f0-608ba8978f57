// Configuration des URLs d'API
const API_CONFIG = {
    // URL de base - Modifiez selon votre configuration
    BASE_URL: 'http://localhost/Project_PFE/Backend',
    
    // URLs spécifiques pour chaque endpoint
    ENDPOINTS: {
        FACTURES: '/pages/factures/',
        DIPLOMES: '/pages/diplomes/',
        ABSENCES: '/pages/absences/',
        RETARDS: '/pages/retards/',
        QUIZ: '/pages/quiz/',
        ETUDIANTS: '/pages/etudiants/',
        ENSEIGNANTS: '/pages/enseignants/',
        MATIERES: '/pages/matieres/',
        DEVOIRS: '/pages/devoirs/',
        UTILISATEURS: '/pages/utilisateurs/',
        MESSAGES: '/pages/messages/',
        NOTIFICATIONS: '/pages/notifications/',
        PARENT_ETUDIANT: '/pages/parent_etudiant/',
        AUTH: '/Auth/'
    }
};

// Fonction helper pour construire les URLs complètes
export const getApiUrl = (endpoint) => {
    return `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS[endpoint]}`;
};

// URLs alternatives à tester selon votre configuration
export const ALTERNATIVE_URLS = {
    // Pour Laragon avec port par défaut
    LARAGON_DEFAULT: 'http://localhost/Project_PFE/Backend',
    
    // Pour Laragon avec port spécifique
    LARAGON_PORT: 'http://localhost:80/Project_PFE/Backend',
    
    // Pour XAMPP
    XAMPP: 'http://localhost/Project_PFE/Backend',
    
    // Pour WAMP
    WAMP: 'http://localhost/Project_PFE/Backend',
    
    // Avec port spécifique
    WITH_PORT: 'http://localhost:8080/Project_PFE/Backend'
};

// Fonction pour tester la connectivité
export const testApiConnection = async () => {
    const testUrls = Object.values(ALTERNATIVE_URLS);
    
    for (const baseUrl of testUrls) {
        try {
            const response = await fetch(`${baseUrl}/pages/factures/`, {
                method: 'OPTIONS'
            });
            
            if (response.ok || response.status === 200) {
                console.log(`✅ API accessible à: ${baseUrl}`);
                return baseUrl;
            }
        } catch (error) {
            console.log(`❌ Échec de connexion à: ${baseUrl}`);
        }
    }
    
    console.error('❌ Aucune URL d\'API accessible trouvée');
    return null;
};

export default API_CONFIG;
