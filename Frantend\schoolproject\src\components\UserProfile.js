import React, { useState, useEffect, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import {
  FaUser,
  FaEnvelope,
  FaUserTag,
  FaCalendarAlt,
  FaPhone,
  FaMapMarkerAlt,
  FaGraduationCap,
  FaChalkboardTeacher,
  FaUsers,
  FaBookOpen,
  FaTasks,
  FaEdit,
  FaSave,
  FaTimes,
  FaSpinner,
  FaArrowLeft
} from 'react-icons/fa';

const UserProfile = () => {
  const { user } = useContext(AuthContext);
  const { id } = useParams(); // Pour afficher le profil d'un autre utilisateur
  const [userDetails, setUserDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
const [modalMessage, setModalMessage] = useState('');
const [modalType, setModalType] = useState('success'); // 'success' أو 'error'
const [isModalVisible, setIsModalVisible] = useState(false);

  useEffect(() => {
    fetchUserDetails();
  }, [user, id]);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      let url;

      if (id) {
        // Affichage du profil d'un autre utilisateur (pour les admins)
        url = `http://localhost/Project_PFE/Backend/pages/utilisateurs/getUserDetails.php?id=${id}`;
      } else if (user?.email) {
        // Affichage du profil de l'utilisateur connecté
        url = `http://localhost/Project_PFE/Backend/pages/utilisateurs/getUserDetails.php?email=${user.email}`;
      } else {
        setError('Aucun utilisateur spécifié');
        setLoading(false);
        return;
      }

      const response = await fetch(url);
      const data = await response.json();

      if (data.success) {
        setUserDetails(data.user);
        setEditForm({
          nom: data.user.nom,
          email: data.user.email,
          telephone: data.user.parent_telephone || '',
          adresse: data.user.parent_adresse || ''
        });
      } else {
        setError(data.error || 'Erreur lors du chargement du profil');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({
      nom: userDetails.nom,
      email: userDetails.email,
      telephone: userDetails.parent_telephone || '',
      adresse: userDetails.parent_adresse || ''
    });
  };

  const handleSave = async () => {
    try {
      const response = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/updateUser.php', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: userDetails.id,
          nom: editForm.nom,
          email: editForm.email,
          telephone: editForm.telephone,
          adresse: editForm.adresse
        })
      });

      const data = await response.json();

      if (data.success) {
        // Mettre à jour les données locales
        setUserDetails({
          ...userDetails,
          nom: editForm.nom,
          email: editForm.email,
          parent_telephone: editForm.telephone,
          parent_adresse: editForm.adresse
        });
       
setIsEditing(false);
setModalMessage('Informations mises à jour avec succès !');
setModalType('success');
setIsModalVisible(true);
    } else {
setModalMessage('Erreur lors de la mise à jour : ' + (data.error || 'Erreur inconnue'));
setModalType('error');
setIsModalVisible(true);
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur de connexion au serveur');
    }
  };

  const styles = {
    container: {
      minHeight: '100vh',
      backgroundColor: '#f5f7f9',
      padding: '20px',
      marginLeft: '70px'
    },
    profileCard: {
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: 'white',
      borderRadius: '16px',
      overflow: 'hidden',
      boxShadow: '0 8px 32px rgba(0, 105, 137, 0.1)',
      border: '1px solid rgba(0, 105, 137, 0.1)'
    },
    header: {
      background: 'linear-gradient(135deg, #006989 0%, #01a7c2 100%)',
      padding: '40px 30px',
      textAlign: 'center',
      color: 'white'
    },
    avatar: {
      width: '120px',
      height: '120px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #007090 0%, #01a7c2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 auto 20px',
      fontSize: '3rem',
      color: 'white',
      border: '4px solid rgba(255, 255, 255, 0.3)',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.2)'
    },
    userName: {
      fontSize: '2rem',
      fontWeight: 'bold',
      marginBottom: '10px',
      color: 'white'
    },
    userRole: {
      fontSize: '1.2rem',
      opacity: 0.9,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '10px',
      color: 'rgba(255, 255, 255, 0.9)'
    },
    content: {
      padding: '30px'
    },
    section: {
      marginBottom: '30px'
    },
    sectionTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#274c77',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      paddingBottom: '10px',
      borderBottom: '2px solid #01a7c2'
    },
    infoGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
      gap: '20px'
    },
    infoItem: {
      backgroundColor: '#f5f7f9',
      padding: '20px',
      borderRadius: '12px',
      border: '1px solid rgba(0, 105, 137, 0.1)',
      transition: 'all 0.3s ease',
      '&:hover': {
        boxShadow: '0 4px 16px rgba(0, 105, 137, 0.1)'
      }
    },
    infoLabel: {
      fontSize: '0.9rem',
      color: '#006989',
      marginBottom: '8px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontWeight: '600'
    },
    infoValue: {
      fontSize: '1rem',
      color: '#274c77',
      fontWeight: '500'
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '20px'
    },
    statCard: {
      backgroundColor: '#f5f7f9',
      border: '2px solid #01a7c2',
      padding: '25px',
      borderRadius: '16px',
      textAlign: 'center',
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: '0 8px 24px rgba(1, 167, 194, 0.2)'
      }
    },
    statNumber: {
      fontSize: '2.5rem',
      fontWeight: 'bold',
      color: '#006989',
      marginBottom: '8px'
    },
    statLabel: {
      fontSize: '0.95rem',
      color: '#274c77',
      fontWeight: '500'
    },
    editButton: {
      position: 'absolute',
      top: '20px',
      right: '20px',
      width:'15%',
      backgroundColor: '#01a7c2',
      border: 'none',
      borderRadius: '10px',
      padding: '12px 16px',
      color: 'white',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: '0.9rem',
      fontWeight: '500',
      transition: 'all 0.3s ease',
      boxShadow: '0 4px 12px rgba(1, 167, 194, 0.3)',
      '&:hover': {
        backgroundColor: '#007090',
        transform: 'translateY(-1px)'
      }
    },
    input: {
      width: '100%',
      padding: '12px',
      borderRadius: '8px',
      border: '2px solid #01a7c2',
      backgroundColor: 'white',
      color: '#274c77',
      fontSize: '1rem',
      transition: 'all 0.3s ease',
      '&:focus': {
        outline: 'none',
        borderColor: '#006989',
        boxShadow: '0 0 0 3px rgba(1, 167, 194, 0.1)'
      }
    },
    buttonGroup: {
      display: 'flex',
      gap: '12px',
      justifyContent: 'flex-end',
      marginTop: '20px'
    },
    button: {
      padding: '12px 20px',
      borderRadius: '8px',
      border: 'none',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: '0.9rem',
      fontWeight: '500',
      transition: 'all 0.3s ease'
    },
    saveButton: {
      backgroundColor: '#006989',
      color: 'white',
      '&:hover': {
        backgroundColor: '#007090',
        transform: 'translateY(-1px)'
      }
    },
    cancelButton: {
      backgroundColor: '#ef233c',
      color: 'white',
      '&:hover': {
        backgroundColor: '#d32f2f',
        transform: 'translateY(-1px)'
      }
    },
    backButton: {
      position: 'absolute',
      top: '20px',
      left: '20px',
      width:'15%',
      backgroundColor: 'rgb(0 101 130)',
      border: 'none',
      borderRadius: '10px',
      padding: '12px 16px',
      color: 'white',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: '0.9rem',
      fontWeight: '500',
      transition: 'all 0.3s ease',
      boxShadow: '0 4px 12px rgba(0, 112, 144, 0.3)',
      '&:hover': {
        backgroundColor: '#006989',
        transform: 'translateY(-1px)'
      }
    },
    loadingContainer: {
      textAlign: 'center',
      padding: '60px 20px',
      backgroundColor: 'white',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 105, 137, 0.1)'
    },
    spinner: {
      fontSize: '3rem',
      color: '#01a7c2',
      animation: 'spin 1s linear infinite',
      marginBottom: '20px'
    },
    loadingText: {
      color: '#274c77',
      fontSize: '1.1rem',
      fontWeight: '500'
    },
    errorText: {
      color: '#f44336',
      fontSize: '1.2rem',
      fontWeight: '500'
    }
  };

  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingContainer}>
          <FaSpinner style={styles.spinner} />
          <p style={styles.loadingText}>Chargement du profil...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingContainer}>
          <p style={styles.errorText}>{error}</p>
        </div>
      </div>
    );
  }

  const getRoleIcon = (role) => {
    switch (role?.toLowerCase()) {
      case 'enseignant': return <FaChalkboardTeacher />;
      case 'etudiant': return <FaGraduationCap />;
      case 'parent': return <FaUsers />;
      default: return <FaUser />;
    }
  };

  return (
    <div style={styles.container}>
      <div style={{ ...styles.profileCard, position: 'relative' }}>
        {/* Bouton de retour pour les admins */}
        {id && (
          <button
            style={styles.backButton}
            onClick={() => window.history.back()}
          >
            <FaArrowLeft /> Retour
          </button>
        )}

        {/* Bouton d'édition */}
        {!isEditing && (!id || user?.role === 'admin' || user?.role === 'responsable') ? (
          <button style={styles.editButton} onClick={handleEdit}>
            <FaEdit /> Modifier
          </button>
        ) : isEditing ? (
          <div style={styles.buttonGroup}>
            <button
              style={{ ...styles.button, ...styles.saveButton }}
              onClick={handleSave}
            >
              <FaSave /> Sauvegarder
            </button>
            <button
              style={{ ...styles.button, ...styles.cancelButton }}
              onClick={handleCancel}
            >
              <FaTimes /> Annuler
            </button>
          </div>
        ) : null}

        {/* En-tête du profil */}
        <div style={styles.header}>
          <div style={styles.avatar}>
            {getRoleIcon(userDetails?.role_nom)}
          </div>
          <h1 style={styles.userName}>
            {isEditing ? (
              <input 
                style={styles.input}
                value={editForm.nom}
                onChange={(e) => setEditForm({...editForm, nom: e.target.value})}
              />
            ) : userDetails?.nom}
          </h1>
          <div style={styles.userRole}>
            {getRoleIcon(userDetails?.role_nom)}
            <span>{userDetails?.role_nom}</span>
          </div>
        </div>

        {/* Contenu du profil */}
        <div style={styles.content}>
          {/* Informations personnelles */}
          <div style={styles.section}>
            <h2 style={styles.sectionTitle}>
              <FaUser /> Informations Personnelles
            </h2>
            <div style={styles.infoGrid}>
              <div style={styles.infoItem}>
                <div style={styles.infoLabel}>
                  <FaEnvelope /> Email
                </div>
                <div style={styles.infoValue}>
                  {isEditing ? (
                    <input 
                      style={styles.input}
                      value={editForm.email}
                      onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                    />
                  ) : userDetails?.email}
                </div>
              </div>
              
              <div style={styles.infoItem}>
                <div style={styles.infoLabel}>
                  <FaUserTag /> Rôle
                </div>
                <div style={styles.infoValue}>{userDetails?.role_nom}</div>
              </div>
              
              <div style={styles.infoItem}>
                <div style={styles.infoLabel}>
                  <FaCalendarAlt /> Membre depuis
                </div>
                <div style={styles.infoValue}>{userDetails?.created_at_formatted}</div>
              </div>

              {/* Informations spécifiques aux parents */}
              {userDetails?.is_parent > 0 && (
                <>
                  <div style={styles.infoItem}>
                    <div style={styles.infoLabel}>
                      <FaPhone /> Téléphone
                    </div>
                    <div style={styles.infoValue}>
                      {isEditing ? (
                        <input 
                          style={styles.input}
                          value={editForm.telephone}
                          onChange={(e) => setEditForm({...editForm, telephone: e.target.value})}
                        />
                      ) : userDetails?.parent_telephone || 'Non renseigné'}
                    </div>
                  </div>
                  
                  <div style={styles.infoItem}>
                    <div style={styles.infoLabel}>
                      <FaMapMarkerAlt /> Adresse
                    </div>
                    <div style={styles.infoValue}>
                      {isEditing ? (
                        <input 
                          style={styles.input}
                          value={editForm.adresse}
                          onChange={(e) => setEditForm({...editForm, adresse: e.target.value})}
                        />
                      ) : userDetails?.parent_adresse || 'Non renseignée'}
                    </div>
                  </div>
                </>
              )}

              {/* Informations spécifiques aux étudiants */}
              {userDetails?.is_etudiant > 0 && (
                <>
                  {userDetails?.groupe_nom && (
                    <div style={styles.infoItem}>
                      <div style={styles.infoLabel}>
                        <FaUsers /> Groupe
                      </div>
                      <div style={styles.infoValue}>{userDetails.groupe_nom}</div>
                    </div>
                  )}

                  {userDetails?.classe_nom && (
                    <div style={styles.infoItem}>
                      <div style={styles.infoLabel}>
                        <FaGraduationCap /> Classe
                      </div>
                      <div style={styles.infoValue}>{userDetails.classe_nom}</div>
                    </div>
                  )}

                  {userDetails?.filiere_nom && (
                    <div style={styles.infoItem}>
                      <div style={styles.infoLabel}>
                        <FaBookOpen /> Filière
                      </div>
                      <div style={styles.infoValue}>{userDetails.filiere_nom}</div>
                    </div>
                  )}

                  {userDetails?.niveau_nom && (
                    <div style={styles.infoItem}>
                      <div style={styles.infoLabel}>
                        <FaTasks /> Niveau
                      </div>
                      <div style={styles.infoValue}>{userDetails.niveau_nom}</div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Statistiques */}
          {userDetails?.stats && (
            <div style={styles.section}>
              <h2 style={styles.sectionTitle}>
                <FaBookOpen /> Statistiques
              </h2>
              <div style={styles.statsGrid}>
                {userDetails.role_nom === 'Enseignant' && (
                  <>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_cours || 0}</div>
                      <div style={styles.statLabel}>Cours enseignés</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_devoirs || 0}</div>
                      <div style={styles.statLabel}>Devoirs créés</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_matieres || 0}</div>
                      <div style={styles.statLabel}>Matières enseignées</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_enseignements || 0}</div>
                      <div style={styles.statLabel}>Affectations</div>
                    </div>
                  </>
                )}
                
                {userDetails.role_nom === 'Etudiant' && (
                  <>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_devoirs_disponibles || 0}</div>
                      <div style={styles.statLabel}>Devoirs disponibles</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_notes || 0}</div>
                      <div style={styles.statLabel}>Notes enregistrées</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>
                        {userDetails.stats?.moyenne_generale ?
                          parseFloat(userDetails.stats.moyenne_generale).toFixed(2) : 'N/A'}
                      </div>
                      <div style={styles.statLabel}>Moyenne générale</div>
                    </div>
                    <div style={styles.statCard}>
                      <div style={styles.statNumber}>{userDetails.stats?.total_absences || 0}</div>
                      <div style={styles.statLabel}>Absences</div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
{isModalVisible && (
  <div style={{
    position: 'fixed',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999
  }}>
    <div style={{
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '12px',
      minWidth: '300px',
      textAlign: 'center',
      border: modalType === 'success' ? '2px solid #4CAF50' : '2px solid #e53935'
    }}>
      <h3 style={{ color: modalType === 'success' ? '#4CAF50' : '#e53935' }}>
        {modalType === 'success' ? 'Succès' : 'Erreur'}
      </h3>
      <p>{modalMessage}</p>
      <button
        onClick={() => setIsModalVisible(false)}
        style={{
          marginTop: '15px',
          padding: '10px 20px',
          backgroundColor: modalType === 'success' ? '#4CAF50' : '#e53935',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
      >
        OK
      </button>
    </div>
  </div>
)}

          {/* Enfants pour les parents */}
          {userDetails?.enfants && userDetails.enfants.length > 0 && (
            <div style={styles.section}>
              <h2 style={styles.sectionTitle}>
                <FaUsers /> Mes Enfants
              </h2>
              <div style={styles.infoGrid}>
                {userDetails.enfants.map((enfant, index) => (
                  <div key={index} style={styles.infoItem}>
                    <div style={styles.infoLabel}>
                      <FaGraduationCap /> {enfant.enfant_nom}
                    </div>
                    <div style={styles.infoValue}>
                      <div><strong>Lien:</strong> {enfant.lien_parente}</div>
                      {enfant.classe_nom && <div><strong>Classe:</strong> {enfant.classe_nom}</div>}
                      {enfant.groupe_nom && <div><strong>Groupe:</strong> {enfant.groupe_nom}</div>}
                      {enfant.filiere_nom && <div><strong>Filière:</strong> {enfant.filiere_nom}</div>}
                      {enfant.niveau_nom && <div><strong>Niveau:</strong> {enfant.niveau_nom}</div>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    
  );
};

export default UserProfile;