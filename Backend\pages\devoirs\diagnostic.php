<?php
/**
 * Script de diagnostic pour l'API Devoirs
 * Utilisation: Accéder via navigateur ou curl
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

echo json_encode([
    'status' => 'API Devoirs Diagnostic',
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => []
]);

$tests = [];

// Test 1: Vérifier la connexion à la base de données
try {
    require_once('../../config/db.php');
    $tests['database'] = [
        'status' => 'OK',
        'message' => 'Connexion à la base de données réussie'
    ];
} catch (Exception $e) {
    $tests['database'] = [
        'status' => 'ERROR',
        'message' => 'Erreur de connexion BDD: ' . $e->getMessage()
    ];
}

// Test 2: Vérifier la structure de la table Devoirs
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("DESCRIBE Devoirs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $tests['table_structure'] = [
            'status' => 'OK',
            'message' => 'Table Devoirs trouvée',
            'columns' => array_column($columns, 'Field')
        ];
    } catch (PDOException $e) {
        $tests['table_structure'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Devoirs: ' . $e->getMessage()
        ];
    }
}

// Test 3: Vérifier les matières
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Matieres");
        $count = $stmt->fetch()['count'];
        $tests['matieres'] = [
            'status' => 'OK',
            'message' => "Table Matieres: $count matières trouvées"
        ];
    } catch (PDOException $e) {
        $tests['matieres'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Matieres: ' . $e->getMessage()
        ];
    }
}

// Test 4: Vérifier les classes
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Classes");
        $count = $stmt->fetch()['count'];
        $tests['classes'] = [
            'status' => 'OK',
            'message' => "Table Classes: $count classes trouvées"
        ];
    } catch (PDOException $e) {
        $tests['classes'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Classes: ' . $e->getMessage()
        ];
    }
}

// Test 5: Vérifier le dossier uploads
$uploadDir = '../../uploads/devoirs/';
if (!file_exists($uploadDir)) {
    $tests['uploads'] = [
        'status' => 'WARNING',
        'message' => 'Dossier uploads n\'existe pas',
        'path' => realpath($uploadDir)
    ];
} else {
    $writable = is_writable($uploadDir);
    $tests['uploads'] = [
        'status' => $writable ? 'OK' : 'ERROR',
        'message' => $writable ? 'Dossier uploads accessible' : 'Dossier uploads non accessible en écriture',
        'path' => realpath($uploadDir),
        'permissions' => substr(sprintf('%o', fileperms($uploadDir)), -4)
    ];
}

// Test 6: Vérifier les devoirs existants
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Devoirs");
        $count = $stmt->fetch()['count'];
        $tests['devoirs_count'] = [
            'status' => 'OK',
            'message' => "Table Devoirs: $count devoirs trouvés"
        ];
        
        // Récupérer quelques devoirs pour test
        $stmt = $pdo->query("SELECT id, titre, fichier_pdf FROM Devoirs LIMIT 3");
        $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $tests['devoirs_sample'] = [
            'status' => 'OK',
            'message' => 'Échantillon de devoirs',
            'data' => $devoirs
        ];
    } catch (PDOException $e) {
        $tests['devoirs_count'] = [
            'status' => 'ERROR',
            'message' => 'Erreur lecture devoirs: ' . $e->getMessage()
        ];
    }
}

// Test 7: Vérifier la configuration PHP
$tests['php_config'] = [
    'status' => 'INFO',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit')
];

// Test 8: Vérifier les headers
$tests['headers'] = [
    'status' => 'INFO',
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'non défini',
    'authorization' => isset($_SERVER['HTTP_AUTHORIZATION']) ? 'présent' : 'absent',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'non défini'
];

// Test 9: Test de requête GET
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("
            SELECT Devoirs.id, Devoirs.titre, Devoirs.description, Devoirs.fichier_pdf, Devoirs.taille_fichier, Devoirs.date_remise,
                   Devoirs.matiere_id, Matieres.nom AS matiere_nom,
                   Devoirs.classe_id, Classes.nom AS classe_nom
            FROM Devoirs
            LEFT JOIN Matieres ON Devoirs.matiere_id = Matieres.id
            LEFT JOIN Classes ON Devoirs.classe_id = Classes.id
            ORDER BY Devoirs.date_remise DESC
            LIMIT 5
        ");
        $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $tests['api_get_test'] = [
            'status' => 'OK',
            'message' => 'Test requête GET réussi',
            'count' => count($devoirs),
            'sample' => array_slice($devoirs, 0, 2) // Premiers 2 devoirs
        ];
    } catch (PDOException $e) {
        $tests['api_get_test'] = [
            'status' => 'ERROR',
            'message' => 'Erreur test GET: ' . $e->getMessage()
        ];
    }
}

// Test 10: Vérifier les fichiers PDF existants
if (isset($pdo) && file_exists($uploadDir)) {
    try {
        $stmt = $pdo->query("SELECT fichier_pdf FROM Devoirs WHERE fichier_pdf IS NOT NULL");
        $files = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $existingFiles = 0;
        $missingFiles = 0;
        
        foreach ($files as $file) {
            if (file_exists($uploadDir . $file)) {
                $existingFiles++;
            } else {
                $missingFiles++;
            }
        }
        
        $tests['pdf_files'] = [
            'status' => $missingFiles > 0 ? 'WARNING' : 'OK',
            'message' => 'Vérification des fichiers PDF',
            'total_files' => count($files),
            'existing_files' => $existingFiles,
            'missing_files' => $missingFiles
        ];
    } catch (PDOException $e) {
        $tests['pdf_files'] = [
            'status' => 'ERROR',
            'message' => 'Erreur vérification fichiers: ' . $e->getMessage()
        ];
    }
}

// Résultat final
$allOk = true;
foreach ($tests as $test) {
    if (isset($test['status']) && $test['status'] === 'ERROR') {
        $allOk = false;
        break;
    }
}

$result = [
    'status' => $allOk ? 'OK' : 'ERROR',
    'message' => $allOk ? 'Tous les tests sont passés' : 'Certains tests ont échoué',
    'timestamp' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => count($tests),
        'passed' => count(array_filter($tests, function($test) { return isset($test['status']) && $test['status'] === 'OK'; })),
        'warnings' => count(array_filter($tests, function($test) { return isset($test['status']) && $test['status'] === 'WARNING'; })),
        'errors' => count(array_filter($tests, function($test) { return isset($test['status']) && $test['status'] === 'ERROR'; }))
    ],
    'tests' => $tests
];

echo json_encode($result, JSON_PRETTY_PRINT);
?>
