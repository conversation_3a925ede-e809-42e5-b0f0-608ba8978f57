<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Devoirs API - Method: " . $_SERVER['REQUEST_METHOD']);

$method = $_SERVER['REQUEST_METHOD'];

// Gérer le header X-HTTP-Method-Override pour les requêtes PUT via POST
if ($method === 'POST' && isset($_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'])) {
    $method = $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'];
    error_log("Method override detected via header: " . $method);
}

// Gérer le champ _method pour les requêtes PUT via POST
if ($method === 'POST' && isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
    $method = 'PUT';
    error_log("Method override detected via _method field: " . $method);
}

// Dossier pour stocker les fichiers PDF
$uploadDir = '../../uploads/devoirs/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

if ($method === 'POST') {
    error_log("POST Data: " . json_encode($_POST));
    error_log("POST Files: " . json_encode($_FILES));

    // Vérifier si c'est une requête de modification (PUT simulé)
    if (isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
        error_log("POST with _method=PUT detected - redirecting to PUT logic");
        $method = 'PUT';
        goto handle_put;
    }

    // Validation des champs requis selon la structure de la base de données
    if (!isset($_POST['titre']) || empty(trim($_POST['titre']))) {
        echo json_encode(['success' => false, 'error' => 'Titre du devoir requis']);
        exit;
    }

    if (!isset($_POST['matiere_id']) || !is_numeric($_POST['matiere_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la matière requis']);
        exit;
    }

    if (!isset($_POST['classe_id']) || !is_numeric($_POST['classe_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis']);
        exit;
    }

    $titre = trim($_POST['titre']);
    $description = isset($_POST['description']) ? trim($_POST['description']) : null;
    $matiere_id = intval($_POST['matiere_id']);
    $classe_id = intval($_POST['classe_id']);
    $date_remise = isset($_POST['date_remise']) && !empty($_POST['date_remise']) ? $_POST['date_remise'] : null;

    // Vérifier si la matière existe
    try {
        $checkMatiereStmt = $pdo->prepare("SELECT id FROM Matieres WHERE id = :matiere_id");
        $checkMatiereStmt->execute(['matiere_id' => $matiere_id]);
        if (!$checkMatiereStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Matière non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check matiere error: " . $e->getMessage());
    }

    // Vérifier si la classe existe
    try {
        $checkClasseStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :classe_id");
        $checkClasseStmt->execute(['classe_id' => $classe_id]);
        if (!$checkClasseStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe error: " . $e->getMessage());
    }

    $fichier_pdf = null;

    // Gestion de l'upload du fichier PDF (optionnel selon votre structure)
    if (isset($_FILES['fichier_pdf']) && $_FILES['fichier_pdf']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['fichier_pdf'];

        // Vérifier le type de fichier
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mimeType !== 'application/pdf') {
            echo json_encode(['success' => false, 'error' => 'Seuls les fichiers PDF sont autorisés']);
            exit;
        }

        // Vérifier la taille (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode(['success' => false, 'error' => 'Le fichier ne doit pas dépasser 10MB']);
            exit;
        }

        // Générer un nom unique pour le fichier
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('devoir_') . '.' . $extension;
        $targetPath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $fichier_pdf = $filename;
            error_log("File uploaded successfully: " . $filename);
        } else {
            echo json_encode(['success' => false, 'error' => 'Erreur lors de l\'upload du fichier']);
            exit;
        }
    }

    try {
        // Insertion selon la structure exacte de votre base de données
        $stmt = $pdo->prepare("INSERT INTO Devoirs (matiere_id, classe_id, titre, description, date_remise, fichier_pdf) VALUES (:matiere_id, :classe_id, :titre, :description, :date_remise, :fichier_pdf)");
        $stmt->execute([
            'matiere_id' => $matiere_id,
            'classe_id' => $classe_id,
            'titre' => $titre,
            'description' => $description,
            'date_remise' => $date_remise,
            'fichier_pdf' => $fichier_pdf
        ]);
        $newId = $pdo->lastInsertId();
        error_log("Devoir created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Devoir ajouté avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("
            SELECT Devoirs.id, Devoirs.matiere_id, Devoirs.classe_id, Devoirs.titre,
                   Devoirs.description, Devoirs.date_remise, Devoirs.fichier_pdf,
                   Matieres.nom AS matiere_nom,
                   Classes.nom AS classe_nom
            FROM Devoirs
            LEFT JOIN Matieres ON Devoirs.matiere_id = Matieres.id
            LEFT JOIN Classes ON Devoirs.classe_id = Classes.id
            ORDER BY Devoirs.id DESC
        ");
        $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculer la taille des fichiers et vérifier leur existence
        foreach ($devoirs as &$d) {
            if ($d['fichier_pdf']) {
                $filePath = $uploadDir . $d['fichier_pdf'];
                if (file_exists($filePath)) {
                    $fileSize = filesize($filePath);
                    $d['taille_fichier'] = round($fileSize / (1024 * 1024), 2) . ' MB';
                } else {
                    error_log("Warning: File not found: " . $filePath);
                    $d['taille_fichier'] = 'Fichier manquant';
                }
            } else {
                $d['taille_fichier'] = null;
            }
        }

        error_log("GET Success: " . count($devoirs) . " devoirs found");
        echo json_encode($devoirs);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    handle_put:
    error_log("PUT Data: " . json_encode($_POST));
    error_log("PUT Files: " . json_encode($_FILES));

    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        error_log("PUT Error - ID validation failed. POST data: " . json_encode($_POST));
        echo json_encode(['success' => false, 'error' => 'ID du devoir requis et doit être numérique']);
        exit;
    }

    if (!isset($_POST['titre']) || empty(trim($_POST['titre']))) {
        echo json_encode(['success' => false, 'error' => 'Titre du devoir requis']);
        exit;
    }

    if (!isset($_POST['matiere_id']) || !is_numeric($_POST['matiere_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la matière requis']);
        exit;
    }

    if (!isset($_POST['classe_id']) || !is_numeric($_POST['classe_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis']);
        exit;
    }

    $id = intval($_POST['id']);
    $titre = trim($_POST['titre']);
    $description = isset($_POST['description']) ? trim($_POST['description']) : null;
    $matiere_id = intval($_POST['matiere_id']);
    $classe_id = intval($_POST['classe_id']);
    $date_remise = isset($_POST['date_remise']) && !empty($_POST['date_remise']) ? $_POST['date_remise'] : null;

    // Vérifier si le devoir existe
    try {
        $checkStmt = $pdo->prepare("SELECT fichier_pdf FROM Devoirs WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        $existingDevoir = $checkStmt->fetch();
        if (!$existingDevoir) {
            echo json_encode(['success' => false, 'error' => 'Devoir non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check devoir exists error: " . $e->getMessage());
    }

    // Gestion de l'upload du nouveau fichier PDF (optionnel en modification)
    $fichier_pdf = $existingDevoir['fichier_pdf']; // Garder l'ancien par défaut
    $newFileUploaded = false;

    error_log("PUT - Devoir existant trouvé: ID=" . $id . ", fichier_actuel=" . $existingDevoir['fichier_pdf']);

    if (isset($_FILES['fichier_pdf']) && $_FILES['fichier_pdf']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['fichier_pdf'];
        error_log("PUT - Nouveau fichier PDF détecté: " . $file['name']);

        // Vérifier le type de fichier
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mimeType !== 'application/pdf') {
            echo json_encode(['success' => false, 'error' => 'Seuls les fichiers PDF sont autorisés']);
            exit;
        }

        // Vérifier la taille (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode(['success' => false, 'error' => 'Le fichier ne doit pas dépasser 10MB']);
            exit;
        }

        // Supprimer l'ancien fichier
        if ($existingDevoir['fichier_pdf'] && file_exists($uploadDir . $existingDevoir['fichier_pdf'])) {
            unlink($uploadDir . $existingDevoir['fichier_pdf']);
        }

        // Générer un nom unique pour le nouveau fichier
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('devoir_') . '.' . $extension;
        $targetPath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $fichier_pdf = $filename;
            $newFileUploaded = true;
            error_log("New file uploaded successfully: " . $filename);
        } else {
            echo json_encode(['success' => false, 'error' => 'Erreur lors de l\'upload du nouveau fichier']);
            exit;
        }
    } else {
        error_log("PUT - Aucun nouveau fichier PDF uploadé, conservation du fichier existant");
    }

    try {
        // Mise à jour selon la structure exacte de votre base de données
        $stmt = $pdo->prepare("UPDATE Devoirs SET matiere_id = :matiere_id, classe_id = :classe_id, titre = :titre, description = :description, date_remise = :date_remise, fichier_pdf = :fichier_pdf WHERE id = :id");
        $result = $stmt->execute([
            'matiere_id' => $matiere_id,
            'classe_id' => $classe_id,
            'titre' => $titre,
            'description' => $description,
            'date_remise' => $date_remise,
            'fichier_pdf' => $fichier_pdf,
            'id' => $id
        ]);

        if ($result) {
            error_log("Devoir updated successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Devoir mis à jour avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Échec de la mise à jour']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['id']) || !is_numeric($data['id'])) {
        echo json_encode(['success' => false, 'error' => 'ID du devoir requis']);
        exit;
    }

    $id = intval($data['id']);

    // Récupérer les infos du devoir pour supprimer le fichier
    try {
        $checkStmt = $pdo->prepare("SELECT fichier_pdf FROM Devoirs WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        $devoir = $checkStmt->fetch();
        if (!$devoir) {
            echo json_encode(['success' => false, 'error' => 'Devoir non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check devoir exists error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM Devoirs WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);

        if ($result && $stmt->rowCount() > 0) {
            // Supprimer le fichier PDF
            if ($devoir['fichier_pdf'] && file_exists($uploadDir . $devoir['fichier_pdf'])) {
                unlink($uploadDir . $devoir['fichier_pdf']);
                error_log("File deleted: " . $devoir['fichier_pdf']);
            }

            error_log("Devoir deleted successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Devoir supprimé avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Aucun devoir supprimé']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
