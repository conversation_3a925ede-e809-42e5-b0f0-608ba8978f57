# 🔧 Guide de Résolution - Problèmes CRUD Cours

## 🎯 **Problèmes Identifiés et Solutions**

### **1. <PERSON><PERSON>ur "Cours non trouvé" lors de la suppression**

#### **Cause Probable**
- ID du cours non transmis correctement
- Cours inexistant en base de données
- Problème de type de données (string vs int)

#### **Solutions Appliquées**
```javascript
// Validation de l'ID avant suppression
if (!id || isNaN(id)) {
    Swal.fire('Erreur', 'ID du cours invalide', 'error');
    return;
}

// Envoi avec conversion explicite en entier
data: JSON.stringify({ id: parseInt(id) })
```

### **2. Erreur "ID et titre du cours requis" lors de la modification**

#### **Cause Probable**
- FormData mal formaté pour les requêtes PUT
- ID manquant ou invalide
- Titre vide ou non transmis

#### **Solutions Appliquées**
```javascript
// Validation de l'ID pour modification
if (editingCours && (!editingCours.id || isNaN(editingCours.id))) {
    Swal.fire('Erreur', 'ID du cours invalide pour la modification', 'error');
    return;
}

// Ajout de l'ID dans FormData
if (editingCours) {
    formDataToSend.append('id', editingCours.id);
}
```

### **3. Erreur "Connexion API échouée" lors de l'ajout**

#### **Causes Probables**
- Serveur PHP non démarré
- URL incorrecte
- Problème de CORS
- Timeout de connexion

#### **Solutions Appliquées**
```javascript
// URL vérifiée et timeout ajouté
const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';
timeout: 30000 // 30 secondes

// Gestion d'erreurs spécifique
if (error.code === 'ECONNREFUSED') {
    errorMessage = 'Serveur non accessible - Vérifiez que le serveur PHP fonctionne';
} else if (error.code === 'ECONNABORTED') {
    errorMessage = 'Timeout - Le serveur met trop de temps à répondre';
}
```

### **4. Erreur "Impossible de télécharger le fichier PDF"**

#### **Causes Probables**
- Fichier PDF inexistant sur le serveur
- Problème de permissions
- URL de téléchargement incorrecte
- Type MIME incorrect

#### **Solutions Appliquées**
```javascript
// Vérification du fichier avant téléchargement
if (!cours.fichier_pdf) {
    Swal.fire('Erreur', 'Aucun fichier PDF associé à ce cours', 'error');
    return;
}

// URL encodée et timeout
const url = `http://localhost/Project_PFE/Backend/pages/cours/download.php?file=${encodeURIComponent(cours.fichier_pdf)}&cours_id=${cours.id}`;
timeout: 30000

// Vérification du type MIME
if (response.data.type !== 'application/pdf' && !response.headers['content-type']?.includes('application/pdf')) {
    Swal.fire('Erreur', 'Le fichier téléchargé n\'est pas un PDF valide', 'error');
    return;
}
```

## 🧪 **Outils de Diagnostic Créés**

### **1. Bouton Test API Amélioré**
- **Fonction** : Teste la connexion avec logs détaillés
- **Localisation** : Bouton "🧪 Test API" dans l'interface
- **Informations** : Status, nombre de cours, erreurs spécifiques

### **2. Script de Diagnostic Backend**
- **Fichier** : `Backend/pages/cours/diagnostic.php`
- **URL** : `http://localhost/Project_PFE/Backend/pages/cours/diagnostic.php`
- **Tests** :
  - Connexion base de données
  - Structure table Cours
  - Tables Matieres et Classes
  - Dossier uploads et permissions
  - Configuration PHP
  - Headers de requête

### **3. Logs de Debug Renforcés**
```javascript
// Logs détaillés pour chaque opération
console.log('🔄 Envoi requête cours:', {
    method: editingCours ? 'PUT' : 'POST',
    url, titre, matiere_id, classe_id, date_publication,
    editingId: editingCours?.id,
    hasFile: !!formData.fichier_pdf,
    token: token ? 'présent' : 'absent'
});
```

## 🔍 **Procédure de Diagnostic**

### **Étape 1 : Vérifier le Serveur**
```bash
# Vérifier que le serveur PHP fonctionne
curl http://localhost/Project_PFE/Backend/pages/cours/diagnostic.php
```

### **Étape 2 : Tester l'API depuis l'Interface**
1. Ouvrir la page Cours
2. Ouvrir la console (F12)
3. Cliquer "🧪 Test API"
4. Analyser les logs

### **Étape 3 : Tester les Opérations CRUD**

#### **Test Ajout**
1. Cliquer "Nouveau Cours"
2. Remplir tous les champs obligatoires
3. Sélectionner un fichier PDF < 10MB
4. Cliquer "Créer"
5. Vérifier les logs console

#### **Test Modification**
1. Cliquer "Modifier" sur un cours existant
2. Modifier le titre
3. Cliquer "Modifier"
4. Vérifier les logs console

#### **Test Suppression**
1. Cliquer "Supprimer" sur un cours
2. Confirmer la suppression
3. Vérifier les logs console

#### **Test Téléchargement**
1. Cliquer sur le bouton "📥 PDF"
2. Vérifier le téléchargement
3. Vérifier les logs console

## 🛠️ **Solutions par Type d'Erreur**

### **Erreur de Connexion**
```bash
# Vérifier le serveur Apache/Nginx
sudo systemctl status apache2
# ou
sudo systemctl status nginx

# Vérifier PHP
php -v

# Tester l'URL directement
curl -I http://localhost/Project_PFE/Backend/pages/cours/cours.php
```

### **Erreur de Base de Données**
```sql
-- Vérifier la structure
DESCRIBE Cours;
DESCRIBE Matieres;
DESCRIBE Classes;

-- Vérifier les données
SELECT COUNT(*) FROM Cours;
SELECT COUNT(*) FROM Matieres;
SELECT COUNT(*) FROM Classes;
```

### **Erreur de Permissions**
```bash
# Vérifier les permissions du dossier uploads
ls -la Backend/uploads/cours/

# Corriger les permissions si nécessaire
chmod 755 Backend/uploads/cours/
```

### **Erreur de Configuration PHP**
```bash
# Vérifier la configuration PHP
php -i | grep upload_max_filesize
php -i | grep post_max_size
php -i | grep max_execution_time
```

## 📋 **Checklist de Validation**

### **Backend**
- [ ] Serveur PHP démarré
- [ ] Base de données accessible
- [ ] Tables Cours, Matieres, Classes existent
- [ ] Dossier uploads/cours/ existe avec permissions 755
- [ ] Configuration PHP appropriée (upload_max_filesize, post_max_size)

### **Frontend**
- [ ] URL API correcte
- [ ] Token d'authentification présent
- [ ] Validation des champs obligatoires
- [ ] Gestion d'erreurs appropriée
- [ ] Logs de debug activés

### **Réseau**
- [ ] CORS configuré correctement
- [ ] Headers Authorization envoyés
- [ ] Content-Type approprié (multipart/form-data pour uploads)
- [ ] Timeout suffisant (30 secondes)

## 🎯 **Résultats Attendus Après Correction**

### **Ajout de Cours**
- ✅ Validation côté client réussie
- ✅ Upload PDF fonctionnel
- ✅ Sauvegarde en base de données
- ✅ Message de succès affiché
- ✅ Liste rafraîchie automatiquement

### **Modification de Cours**
- ✅ Chargement des données existantes
- ✅ Validation de l'ID
- ✅ Mise à jour en base de données
- ✅ Gestion optionnelle du nouveau fichier PDF

### **Suppression de Cours**
- ✅ Confirmation utilisateur
- ✅ Suppression en base de données
- ✅ Suppression du fichier PDF
- ✅ Mise à jour de l'interface

### **Téléchargement PDF**
- ✅ Vérification de l'existence du fichier
- ✅ Téléchargement automatique
- ✅ Nom de fichier personnalisé
- ✅ Validation du type MIME

**🎉 Avec ces corrections, tous les problèmes CRUD devraient être résolus !**

### **Prochaines Étapes**
1. Tester le diagnostic backend
2. Utiliser le bouton Test API
3. Tester chaque opération CRUD
4. Vérifier les logs pour identifier les problèmes restants
