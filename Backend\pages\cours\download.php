<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Download PDF - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Download PDF - GET params: " . json_encode($_GET));

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
    exit;
}

if (!isset($_GET['file']) || !isset($_GET['cours_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Paramètres manquants']);
    exit;
}

$filename = $_GET['file'];
$cours_id = intval($_GET['cours_id']);

// Vérifier que le cours existe et récupérer ses informations
try {
    $stmt = $pdo->prepare("
        SELECT c.id, c.titre, c.fichier_pdf, c.matiere_id,
               m.nom as matiere_nom
        FROM cours c
        LEFT JOIN matieres m ON c.matiere_id = m.id
        WHERE c.id = :cours_id AND c.fichier_pdf = :filename
    ");
    $stmt->execute(['cours_id' => $cours_id, 'filename' => $filename]);
    $cours = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cours) {
        http_response_code(404);
        echo json_encode(['error' => 'Cours non trouvé']);
        exit;
    }
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de base de données']);
    exit;
}

// Chemin vers le fichier
$uploadDir = '../../uploads/cours/';
$filePath = $uploadDir . $filename;

// Vérifier que le fichier existe
if (!file_exists($filePath)) {
    error_log("File not found: " . $filePath);
    http_response_code(404);
    echo json_encode(['error' => 'Fichier non trouvé']);
    exit;
}

// Vérifier que c'est bien un PDF
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $filePath);
finfo_close($finfo);

if ($mimeType !== 'application/pdf') {
    error_log("Invalid file type: " . $mimeType);
    http_response_code(400);
    echo json_encode(['error' => 'Type de fichier invalide']);
    exit;
}

// Sécurité : vérifier que le nom de fichier ne contient pas de caractères dangereux
if (preg_match('/[^a-zA-Z0-9._-]/', $filename)) {
    error_log("Invalid filename: " . $filename);
    http_response_code(400);
    echo json_encode(['error' => 'Nom de fichier invalide']);
    exit;
}

// Log de l'accès au fichier
error_log("PDF Download - Cours: " . $cours['titre'] . " - File: " . $filename);

// Envoyer le fichier
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $cours['titre'] . '.pdf"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Lire et envoyer le fichier par chunks pour éviter les problèmes de mémoire
$handle = fopen($filePath, 'rb');
if ($handle) {
    while (!feof($handle)) {
        echo fread($handle, 8192);
        flush();
    }
    fclose($handle);
} else {
    error_log("Cannot open file: " . $filePath);
    http_response_code(500);
    echo json_encode(['error' => 'Impossible d\'ouvrir le fichier']);
}

exit;
?>
