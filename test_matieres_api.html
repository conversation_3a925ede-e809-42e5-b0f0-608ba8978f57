<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Matières</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🧪 Test API Matières pour Enseignants</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Ce test va appeler l'API des matières et afficher la réponse pour diagnostiquer le problème.</p>
        <button onclick="testMatiereAPI()">🔍 Tester API matiere.php</button>
        <button onclick="testMatieresNoAuth()">🔍 Tester API getMatieres_no_auth.php</button>
        <button onclick="testGetMatieres()">🔍 Tester API getMatieres.php</button>
    </div>

    <div id="results"></div>

    <script>
        async function testMatiereAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Test en cours...</h3></div>';
            
            try {
                console.log('🔄 Test de l\'API matiere.php...');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer test-token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('📊 Réponse API:', data);
                
                resultsDiv.innerHTML = `
                    <div class="test-section ${response.ok ? 'success' : 'error'}">
                        <h3>📊 Résultat API matiere.php</h3>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Type de données:</strong> ${Array.isArray(data) ? 'Array' : typeof data}</p>
                        <p><strong>Nombre d'éléments:</strong> ${Array.isArray(data) ? data.length : 'N/A'}</p>
                        <p><strong>Propriété success:</strong> ${data.hasOwnProperty('success') ? data.success : 'Non présente'}</p>
                        <h4>Données brutes:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Erreur:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Erreur</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testMatieresNoAuth() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Test en cours...</h3></div>';
            
            try {
                console.log('🔄 Test de l\'API getMatieres_no_auth.php...');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/getMatieres_no_auth.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('📊 Réponse API:', data);
                
                resultsDiv.innerHTML = `
                    <div class="test-section ${response.ok ? 'success' : 'error'}">
                        <h3>📊 Résultat API getMatieres_no_auth.php</h3>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Type de données:</strong> ${Array.isArray(data) ? 'Array' : typeof data}</p>
                        <p><strong>Propriété success:</strong> ${data.hasOwnProperty('success') ? data.success : 'Non présente'}</p>
                        <p><strong>Propriété matieres:</strong> ${data.hasOwnProperty('matieres') ? 'Présente (' + data.matieres.length + ' éléments)' : 'Non présente'}</p>
                        <h4>Données brutes:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Erreur:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Erreur</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testGetMatieres() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Test en cours...</h3></div>';
            
            try {
                console.log('🔄 Test de l\'API getMatieres.php...');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/getMatieres.php', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer enseignant-token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('📊 Réponse API:', data);
                
                resultsDiv.innerHTML = `
                    <div class="test-section ${response.ok ? 'success' : 'error'}">
                        <h3>📊 Résultat API getMatieres.php</h3>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Type de données:</strong> ${Array.isArray(data) ? 'Array' : typeof data}</p>
                        <p><strong>Propriété success:</strong> ${data.hasOwnProperty('success') ? data.success : 'Non présente'}</p>
                        <h4>Données brutes:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Erreur:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Erreur</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
