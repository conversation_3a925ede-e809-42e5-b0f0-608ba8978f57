<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notes - Matières</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        
        .note-item {
            background: #e9ecef;
            padding: 8px;
            margin: 3px 0;
            border-radius: 3px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Notes - Problème Matières</h1>
    
    <div class="container">
        <h2>🧪 Tests API Notes</h2>
        <button onclick="testNotesAPI('enseignant-token')">Test Enseignant</button>
        <button onclick="testNotesAPI('admin-token')">Test Admin</button>
        <button onclick="testNotesAPI('etudiant-token')">Test Étudiant</button>
        <button onclick="testMatieresAPI()">Test API Matières</button>
        <button onclick="clearResults()">Effacer</button>
    </div>

    <div id="test-results"></div>

    <script>
        const API_BASE = 'http://localhost/Project_PFE/Backend/pages/';
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultContainer = document.createElement('div');
            resultContainer.className = 'container';
            resultContainer.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${type}">${content}</div>
            `;
            resultsDiv.appendChild(resultContainer);
        }

        function addDetailedResult(title, success, data, error = null) {
            const resultsDiv = document.getElementById('test-results');
            const resultContainer = document.createElement('div');
            resultContainer.className = 'container';
            
            let statusClass = success ? 'success' : 'error';
            let statusText = success ? '✅ Succès' : '❌ Échec';
            
            let content = `
                <h3>${title}</h3>
                <div class="status ${statusClass}">${statusText}</div>
            `;
            
            if (success && data) {
                if (data.success && data.data) {
                    content += `
                        <div class="status info">
                            📊 Total notes: ${data.count || data.data.length}<br>
                            👤 Rôle: ${data.role}<br>
                            🔧 Permissions: ${JSON.stringify(data.permissions)}
                        </div>
                        <h4>📋 Structure des Notes (3 premiers):</h4>
                    `;
                    
                    data.data.slice(0, 3).forEach((note, index) => {
                        content += `
                            <div class="note-item">
                                <strong>Note ${index + 1}:</strong><br>
                                📖 Matière: <strong>${note.matiere_nom || 'MANQUANT'}</strong><br>
                                👤 Étudiant: ${note.etudiant_nom || 'MANQUANT'}<br>
                                📚 Devoir: ${note.devoir_titre || 'MANQUANT'}<br>
                                📊 Note: ${note.note || 'MANQUANT'}<br>
                                📅 Date: ${note.date_formatted || note.date_enregistrement || 'MANQUANT'}
                            </div>
                        `;
                    });
                    
                    if (data.data.length > 3) {
                        content += `<div class="note-item">... et ${data.data.length - 3} autres notes</div>`;
                    }
                    
                    // Analyser les champs manquants
                    const champsManquants = [];
                    if (data.data.length > 0) {
                        const premierNote = data.data[0];
                        if (!premierNote.matiere_nom) champsManquants.push('matiere_nom');
                        if (!premierNote.etudiant_nom) champsManquants.push('etudiant_nom');
                        if (!premierNote.devoir_titre) champsManquants.push('devoir_titre');
                        
                        if (champsManquants.length > 0) {
                            content += `
                                <div class="status warning">
                                    ⚠️ Champs manquants détectés: ${champsManquants.join(', ')}
                                </div>
                            `;
                        }
                    }
                    
                    content += `<h4>🔍 Données brutes (première note):</h4>`;
                    content += `<pre>${JSON.stringify(data.data[0] || {}, null, 2)}</pre>`;
                } else {
                    content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            }
            
            if (error) {
                content += `
                    <div class="status error">
                        ❌ Erreur: ${error}
                    </div>
                `;
            }
            
            resultContainer.innerHTML = content;
            resultsDiv.appendChild(resultContainer);
        }

        async function testNotesAPI(token) {
            try {
                addResult(`🔄 Test API Notes avec ${token}`, 'Chargement en cours...', 'info');
                
                const response = await fetch(API_BASE + 'notes/api.php', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log(`🔍 Réponse Notes API (${token}):`, data);
                
                if (response.ok) {
                    addDetailedResult(`Notes API - ${token}`, true, data);
                } else {
                    addDetailedResult(`Notes API - ${token}`, false, data, `HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error(`❌ Erreur Notes API (${token}):`, error);
                addDetailedResult(`Notes API - ${token}`, false, null, error.message);
            }
        }

        async function testMatieresAPI() {
            try {
                addResult('🔄 Test API Matières', 'Chargement en cours...', 'info');
                
                const response = await fetch(API_BASE + 'matieres/getMatieres_simple.php');
                const data = await response.json();
                
                console.log('🔍 Réponse Matières API:', data);
                
                if (response.ok && data.success) {
                    const content = `
                        <div class="status success">✅ API Matières fonctionne</div>
                        <div class="status info">
                            📊 Total matières: ${data.total}<br>
                            🔧 API Version: ${data.api_version}
                        </div>
                        <h4>📚 Matières disponibles:</h4>
                        ${data.matieres.slice(0, 5).map(m => `
                            <div class="note-item">📖 ${m.nom} (ID: ${m.id})</div>
                        `).join('')}
                        <pre>${JSON.stringify(data.matieres[0] || {}, null, 2)}</pre>
                    `;
                    
                    const resultsDiv = document.getElementById('test-results');
                    const resultContainer = document.createElement('div');
                    resultContainer.className = 'container';
                    resultContainer.innerHTML = `<h3>API Matières</h3>${content}`;
                    resultsDiv.appendChild(resultContainer);
                } else {
                    addDetailedResult('API Matières', false, data, `HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error('❌ Erreur API Matières:', error);
                addDetailedResult('API Matières', false, null, error.message);
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Test automatique au chargement
        window.onload = function() {
            addResult('🚀 Page de diagnostic chargée', 'Prêt pour les tests de debug Notes-Matières', 'info');
        };
    </script>
</body>
</html>
