<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Classe API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Classe API - Headers: " . json_encode(getallheaders()));
error_log("Classe API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("POST Data: " . json_encode($data));

    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("POST Error: Nom de la classe requis");
        echo json_encode(['success' => false, 'error' => 'Nom de la classe requis']);
        exit;
    }
    if (!isset($data['filiere_id']) || !is_numeric($data['filiere_id'])) {
        error_log("POST Error: ID de la filière requis");
        echo json_encode(['success' => false, 'error' => 'ID de la filière requis et doit être un nombre']);
        exit;
    }
    if (!isset($data['niveau_id']) || !is_numeric($data['niveau_id'])) {
        error_log("POST Error: ID du niveau requis");
        echo json_encode(['success' => false, 'error' => 'ID du niveau requis et doit être un nombre']);
        exit;
    }

    $nom = trim($data['nom']);
    $filiere_id = intval($data['filiere_id']);
    $niveau_id = intval($data['niveau_id']);

    // Vérifier si la filière existe
    try {
        $checkFiliereStmt = $pdo->prepare("SELECT id FROM Filieres WHERE id = :filiere_id");
        $checkFiliereStmt->execute(['filiere_id' => $filiere_id]);
        if (!$checkFiliereStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Filière non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check filiere error: " . $e->getMessage());
    }

    // Vérifier si le niveau existe
    try {
        $checkNiveauStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE id = :niveau_id");
        $checkNiveauStmt->execute(['niveau_id' => $niveau_id]);
        if (!$checkNiveauStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Niveau non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau error: " . $e->getMessage());
    }

    // Vérifier si la classe existe déjà avec le même nom dans la même filière et niveau
    try {
        $checkClasseStmt = $pdo->prepare("SELECT id FROM Classes WHERE nom = :nom AND filiere_id = :filiere_id AND niveau_id = :niveau_id");
        $checkClasseStmt->execute(['nom' => $nom, 'filiere_id' => $filiere_id, 'niveau_id' => $niveau_id]);
        if ($checkClasseStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Cette classe existe déjà dans cette filière et ce niveau']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Classes (nom, filiere_id, niveau_id) VALUES (:nom, :filiere_id, :niveau_id)");
        $stmt->execute([
            'nom' => $nom,
            'filiere_id' => $filiere_id,
            'niveau_id' => $niveau_id
        ]);
        $newId = $pdo->lastInsertId();
        error_log("Classe created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Classe ajoutée avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("
            SELECT Classes.id, Classes.nom,
                   Classes.filiere_id, Filieres.nom AS filiere_nom,
                   Classes.niveau_id, Niveaux.nom AS niveau_nom
            FROM Classes
            LEFT JOIN Filieres ON Classes.filiere_id = Filieres.id
            LEFT JOIN Niveaux ON Classes.niveau_id = Niveaux.id
            ORDER BY Classes.id DESC
        ");
        $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("GET Success: " . count($classes) . " classes found");
        echo json_encode($classes);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("PUT Data: " . json_encode($data));

    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom'])) ||
        !isset($data['filiere_id']) || !is_numeric($data['filiere_id']) ||
        !isset($data['niveau_id']) || !is_numeric($data['niveau_id'])) {
        error_log("PUT Error: ID, nom, ID filière et ID niveau requis");
        echo json_encode(['success' => false, 'error' => 'ID, nom, ID filière et ID niveau requis']);
        exit;
    }

    $id = intval($data['id']);
    $nom = trim($data['nom']);
    $filiere_id = intval($data['filiere_id']);
    $niveau_id = intval($data['niveau_id']);

    // Vérifier si la classe existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe exists error: " . $e->getMessage());
    }

    // Vérifier si la filière existe
    try {
        $checkFiliereStmt = $pdo->prepare("SELECT id FROM Filieres WHERE id = :filiere_id");
        $checkFiliereStmt->execute(['filiere_id' => $filiere_id]);
        if (!$checkFiliereStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Filière non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check filiere error: " . $e->getMessage());
    }

    // Vérifier si le niveau existe
    try {
        $checkNiveauStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE id = :niveau_id");
        $checkNiveauStmt->execute(['niveau_id' => $niveau_id]);
        if (!$checkNiveauStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Niveau non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau error: " . $e->getMessage());
    }

    // Vérifier si le nom n'est pas déjà utilisé par une autre classe dans la même filière et niveau
    try {
        $checkNameStmt = $pdo->prepare("SELECT id FROM Classes WHERE nom = :nom AND filiere_id = :filiere_id AND niveau_id = :niveau_id AND id != :id");
        $checkNameStmt->execute(['nom' => $nom, 'filiere_id' => $filiere_id, 'niveau_id' => $niveau_id, 'id' => $id]);
        if ($checkNameStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Ce nom de classe est déjà utilisé dans cette filière et ce niveau']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe name error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("UPDATE Classes SET nom = :nom, filiere_id = :filiere_id, niveau_id = :niveau_id WHERE id = :id");
        $result = $stmt->execute([
            'nom' => $nom,
            'filiere_id' => $filiere_id,
            'niveau_id' => $niveau_id,
            'id' => $id
        ]);
        if ($result) {
            error_log("Classe updated successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Classe mise à jour avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Échec de la mise à jour']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("DELETE Data: " . json_encode($data));

    if (!isset($data['id'])) {
        error_log("DELETE Error: ID de la classe requis");
        echo json_encode(['success' => false, 'error' => 'ID de la classe requis']);
        exit;
    }

    $id = intval($data['id']);

    // Vérifier si la classe existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Classes WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Classe non trouvée']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check classe exists error: " . $e->getMessage());
    }

    // Vérifier les dépendances (si la classe est utilisée dans d'autres tables)
    try {
        // Exemple : vérifier si la classe est utilisée dans la table Groupes
        $checkDependencyStmt = $pdo->prepare("SELECT COUNT(*) as count FROM Groupes WHERE classe_id = :id");
        $checkDependencyStmt->execute(['id' => $id]);
        $dependency = $checkDependencyStmt->fetch();
        if ($dependency['count'] > 0) {
            echo json_encode(['success' => false, 'error' => 'Impossible de supprimer cette classe car elle est utilisée par des groupes']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check dependencies error: " . $e->getMessage());
        // Continue même si la vérification des dépendances échoue
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM Classes WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);
        if ($result && $stmt->rowCount() > 0) {
            error_log("Classe deleted successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Classe supprimée avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Aucune classe supprimée']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
