/* Parent-Etudiant Styles */
.parent-etudiant-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-text h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.header-text p {
    color: #6c757d;
    margin: 0;
    font-size: 1rem;
}

.add-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.add-btn:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.add-btn img {
    width: 16px;
    height: 16px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn img {
    width: 16px;
    height: 16px;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

/* Loading */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #6c757d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Access Denied */
.access-denied {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.access-denied img {
    width: 100px;
    height: 100px;
    opacity: 0.5;
    margin-bottom: 20px;
}

.access-denied h2 {
    color: #dc3545;
    margin-bottom: 10px;
}

/* Table */
.table-responsive {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.parent-info strong,
.student-info strong {
    display: block;
    color: #2c3e50;
    font-weight: 600;
}

.parent-info small,
.student-info small {
    color: #6c757d;
    font-size: 0.85em;
}

/* Badges */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 600;
    text-transform: uppercase;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 8px;
}

/* No data */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-data img {
    width: 80px;
    height: 80px;
    opacity: 0.5;
    margin-bottom: 20px;
}

.no-data p {
    font-size: 1.1em;
    margin: 0;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.25rem;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: #f8f9fa;
}

.close-btn img {
    width: 20px;
    height: 20px;
}

/* Form */
.modal-content form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #495057;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-group input:disabled,
.form-group select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Responsive */
@media (max-width: 768px) {
    .parent-etudiant-container {
        padding: 15px;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .header-text {
        text-align: center;
    }

    .header-text h1 {
        font-size: 1.5rem;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .table {
        min-width: 700px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 10px 8px;
        font-size: 14px;
    }

    .parent-info strong,
    .student-info strong {
        font-size: 14px;
    }

    .parent-info small,
    .student-info small {
        font-size: 12px;
    }
}
