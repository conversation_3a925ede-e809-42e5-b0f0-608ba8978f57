import React, { useState, useEffect } from 'react';
import { FaTimes, FaSave, FaUser, FaEnvelope, FaUserTag, FaLock } from 'react-icons/fa';

const UserEditModal = ({ user, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    email: '',
    role_id: '',
    mot_de_passe: ''
  });
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (user && isOpen) {
      setFormData({
        nom: user.nom || '',
        prenom: user.prenom || '', // Sera vide si la colonne n'existe pas
        email: user.email || '',
        role_id: user.role_id || '',
        mot_de_passe: '' // Ne pas pré-remplir le mot de passe
      });
      setErrors({});
    }
  }, [user, isOpen]);

  useEffect(() => {
    if (isOpen) {
      fetchRoles();
    }
  }, [isOpen]);

  const fetchRoles = async () => {
    try {
      console.log('🔄 Chargement des rôles...');

      // Essayer d'abord l'API principale
      let response = await fetch('http://localhost/Project_PFE/Backend/pages/roles/role.php');
      let data = await response.json();

      console.log('📊 Réponse API rôles (role.php):', data);

      // L'API retourne directement un tableau de rôles
      if (Array.isArray(data) && data.length > 0) {
        setRoles(data);
        console.log('✅ Rôles chargés depuis role.php:', data.length);
        return;
      }

      // Si échec, essayer l'API alternative
      console.log('🔄 Tentative avec API alternative...');
      response = await fetch('http://localhost/Project_PFE/Backend/pages/roles/index.php');
      data = await response.json();

      console.log('📊 Réponse API rôles (index.php):', data);

      if (data.success && Array.isArray(data.roles)) {
        setRoles(data.roles);
        console.log('✅ Rôles chargés depuis index.php:', data.roles.length);
      } else if (Array.isArray(data)) {
        setRoles(data);
        console.log('✅ Rôles chargés (format tableau):', data.length);
      } else {
        console.error('❌ Format de réponse inattendu:', data);
        setRoles([]);

        // Afficher une alerte à l'utilisateur
        if (data.error) {
          console.error('Erreur API:', data.error);
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des rôles:', error);
      setRoles([]);

      // En cas d'erreur totale, utiliser des rôles par défaut
      console.log('🔄 Utilisation des rôles par défaut...');
      setRoles([
        { id: 1, nom: 'Admin' },
        { id: 2, nom: 'Enseignant' },
        { id: 3, nom: 'Etudiant' },
        { id: 4, nom: 'Parent' }
      ]);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.nom.trim()) {
      newErrors.nom = 'Le nom est requis';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.role_id) {
      newErrors.role_id = 'Le rôle est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const dataToSend = {
        id: user.id,
        nom: formData.nom,
        prenom: formData.prenom,
        email: formData.email,
        role_id: parseInt(formData.role_id)
      };

      // Ajouter le prénom seulement s'il est fourni et si le champ existe
      if (formData.prenom.trim()) {
        dataToSend.prenom = formData.prenom;
      }

      // Ajouter le mot de passe seulement s'il est fourni
      if (formData.mot_de_passe.trim()) {
        dataToSend.mot_de_passe = formData.mot_de_passe;
      }

      const response = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSend)
      });

      const result = await response.json();

      if (result.success) {
        onSave(result);
        onClose();
      } else {
        setErrors({ general: result.error || 'Erreur lors de la modification' });
      }
    } catch (error) {
      setErrors({ general: 'Erreur de connexion au serveur' });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (!isOpen) return null;

  const styles = {
    overlay: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    },
    modal: {
      backgroundColor: 'white',
      borderRadius: '15px',
      padding: '30px',
      width: '90%',
      maxWidth: '500px',
      maxHeight: '90vh',
      overflow: 'auto',
      boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '25px',
      paddingBottom: '15px',
      borderBottom: '2px solid #f0f0f0'
    },
    title: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: 'var(--cerulean)',
      display: 'flex',
      alignItems: 'center',
      gap: '10px'
    },
    closeButton: {
      background: 'none',
      border: 'none',
      fontSize: '1.5rem',
      color: '#999',
      cursor: 'pointer',
      padding: '5px'
    },
    form: {
      display: 'flex',
      flexDirection: 'column',
      gap: '20px'
    },
    formGroup: {
      display: 'flex',
      flexDirection: 'column',
      gap: '8px'
    },
    label: {
      fontSize: '14px',
      fontWeight: 'bold',
      color: '#333',
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    },
    input: {
      padding: '12px 15px',
      border: '2px solid #e0e0e0',
      borderRadius: '8px',
      fontSize: '14px',
      transition: 'border-color 0.3s ease'
    },
    inputError: {
      borderColor: '#e53935'
    },
    select: {
      padding: '12px 15px',
      border: '2px solid #e0e0e0',
      borderRadius: '8px',
      fontSize: '14px',
      backgroundColor: 'white',
      cursor: 'pointer'
    },
    error: {
      color: '#e53935',
      fontSize: '12px',
      marginTop: '5px'
    },
    generalError: {
      backgroundColor: '#ffebee',
      color: '#c62828',
      padding: '10px',
      borderRadius: '8px',
      marginBottom: '15px',
      fontSize: '14px'
    },
    buttonGroup: {
      display: 'flex',
      gap: '15px',
      justifyContent: 'flex-end',
      marginTop: '25px',
      paddingTop: '20px',
      borderTop: '2px solid #f0f0f0'
    },
    button: {
      padding: '12px 25px',
      borderRadius: '8px',
      border: 'none',
      fontSize: '14px',
      fontWeight: 'bold',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      transition: 'all 0.3s ease'
    },
    saveButton: {
      backgroundColor: 'var(--cerulean)',
      color: 'white'
    },
    cancelButton: {
      backgroundColor: '#f5f5f5',
      color: '#666'
    },
    loadingButton: {
      backgroundColor: '#ccc',
      cursor: 'not-allowed'
    }
  };

  return (
    <div style={styles.overlay} onClick={onClose}>
      <div style={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div style={styles.header}>
          <h2 style={styles.title}>
            <FaUser /> Modifier l'utilisateur
          </h2>
          <button style={styles.closeButton} onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        {errors.general && (
          <div style={styles.generalError}>
            {errors.general}
          </div>
        )}

        <form style={styles.form} onSubmit={handleSubmit}>
          <div style={styles.formGroup}>
            <label style={styles.label}>
              <FaUser /> Nom *
            </label>
            <input
              type="text"
              name="nom"
              value={formData.nom}
              onChange={handleChange}
              style={{
                ...styles.input,
                ...(errors.nom ? styles.inputError : {})
              }}
              placeholder="Nom de l'utilisateur"
              disabled={loading}
            />
            {errors.nom && <div style={styles.error}>{errors.nom}</div>}
          </div>

          

          <div style={styles.formGroup}>
            <label style={styles.label}>
              <FaEnvelope /> Email *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              style={{
                ...styles.input,
                ...(errors.email ? styles.inputError : {})
              }}
              placeholder="<EMAIL>"
              disabled={loading}
            />
            {errors.email && <div style={styles.error}>{errors.email}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              <FaUserTag /> Rôle *
            </label>
            <select
              name="role_id"
              value={formData.role_id}
              onChange={handleChange}
              style={{
                ...styles.select,
                ...(errors.role_id ? styles.inputError : {})
              }}
              disabled={loading}
            >
              <option value="">
                {roles.length === 0 ? 'Chargement des rôles...' : 'Sélectionner un rôle'}
              </option>
              {roles.map(role => (
                <option key={role.id} value={role.id}>
                  {role.nom}
                </option>
              ))}
            </select>
            {roles.length === 0 && (
              <small style={{ color: '#666', fontSize: '12px', marginTop: '5px' }}>
                Si les rôles ne se chargent pas, vérifiez la console du navigateur
              </small>
            )}
            {errors.role_id && <div style={styles.error}>{errors.role_id}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              <FaLock /> Nouveau mot de passe (optionnel)
            </label>
            <input
              type="password"
              name="mot_de_passe"
              value={formData.mot_de_passe}
              onChange={handleChange}
              style={styles.input}
              placeholder="Laisser vide pour conserver l'actuel"
              disabled={loading}
            />
            <small style={{ color: '#666', fontSize: '12px' }}>
              Laissez vide si vous ne voulez pas changer le mot de passe
            </small>
          </div>

          <div style={styles.buttonGroup}>
            <button
              type="button"
              onClick={onClose}
              style={{ ...styles.button, ...styles.cancelButton }}
              disabled={loading}
            >
              Annuler
            </button>
            <button
              type="submit"
              style={{
                ...styles.button,
                ...(loading ? styles.loadingButton : styles.saveButton)
              }}
              disabled={loading}
            >
              <FaSave /> {loading ? 'Enregistrement...' : 'Enregistrer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserEditModal;
