<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Dynamique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #666;
        }
        .refresh-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
        }
        .refresh-btn:hover {
            background-color: #0056b3;
        }
        .auto-refresh {
            background-color: #28a745;
        }
        .auto-refresh.disabled {
            background-color: #6c757d;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Dashboard Dynamique - Données Réelles</h1>
        <p>Ce test vérifie que toutes les statistiques sont chargées dynamiquement depuis la base de données sans données de fallback.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="refreshBtn" class="refresh-btn">🔄 Actualiser</button>
            <button id="autoRefreshBtn" class="refresh-btn auto-refresh">🔄 Auto ON</button>
            <button id="testErrorBtn" class="refresh-btn" style="background-color: #dc3545;">❌ Tester Erreur</button>
        </div>

        <div id="status"></div>
        <div id="statsContainer"></div>
        <div id="timestamp" class="timestamp"></div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;

        const API_URL = 'http://localhost/Project_PFE/Backend/pages/dashboard/stats.php';
        
        const statLabels = {
            cours: 'Cours',
            matieres: 'Matières',
            groupes: 'Groupes',
            etudiants: 'Étudiants',
            parents: 'Parents',
            filieres: 'Filières',
            utilisateurs: 'Utilisateurs',
            enseignants: 'Enseignants',
            classes: 'Classes',
            niveaux: 'Niveaux',
            absences_aujourdhui: 'Absences Aujourd\'hui',
            retards_aujourdhui: 'Retards Aujourd\'hui',
            devoirs_en_cours: 'Devoirs en Cours',
            quiz_actifs: 'Quiz Actifs',
            factures_impayees: 'Factures Impayées',
            diplomes_annee: 'Diplômes Cette Année'
        };

        async function fetchStats(testError = false) {
            const statusDiv = document.getElementById('status');
            const statsContainer = document.getElementById('statsContainer');
            const timestampDiv = document.getElementById('timestamp');
            
            statusDiv.innerHTML = '<div class="loading">⏳ Chargement des données dynamiques...</div>';
            statsContainer.innerHTML = '';
            
            try {
                const url = testError ? 'http://invalid-url/stats.php' : API_URL;
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.stats) {
                    // Succès - afficher les données dynamiques
                    statusDiv.innerHTML = `
                        <div class="success">
                            ✅ Données chargées dynamiquement depuis la base de données
                            <br>📊 ${Object.keys(data.stats).length} statistiques récupérées
                            <br>🕒 Dernière mise à jour: ${data.timestamp}
                        </div>
                    `;
                    
                    // Créer les cartes de statistiques
                    let statsHTML = '<div class="stats-grid">';
                    for (const [key, value] of Object.entries(data.stats)) {
                        if (statLabels[key] && typeof value === 'number') {
                            statsHTML += `
                                <div class="stat-card">
                                    <div class="stat-number">${value}</div>
                                    <div class="stat-label">${statLabels[key]}</div>
                                </div>
                            `;
                        }
                    }
                    statsHTML += '</div>';
                    
                    statsContainer.innerHTML = statsHTML;
                    timestampDiv.innerHTML = `Dernière actualisation: ${new Date().toLocaleTimeString('fr-FR')}`;
                    
                } else {
                    throw new Error(data.error || 'Aucune donnée reçue du serveur');
                }
                
            } catch (error) {
                // Erreur - PAS de données de fallback
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ Impossible de charger les statistiques depuis la base de données
                        <br><strong>Erreur:</strong> ${error.message}
                        <br><br>
                        ⚠️ <strong>Aucune donnée de fallback</strong> - L'interface reste vide jusqu'à résolution du problème
                        <br>🔧 Vérifiez la connexion au serveur et actualisez
                    </div>
                `;
                
                statsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; border: 2px dashed #dc3545; border-radius: 10px; background-color: #f8f9fa;">
                        <h3 style="color: #dc3545;">📊 Aucune statistique disponible</h3>
                        <p style="color: #666;">Les données ne peuvent pas être chargées depuis la base de données.</p>
                        <button onclick="fetchStats()" class="refresh-btn">🔄 Réessayer</button>
                    </div>
                `;
                
                timestampDiv.innerHTML = `Dernière tentative: ${new Date().toLocaleTimeString('fr-FR')} (ÉCHEC)`;
            }
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('autoRefreshBtn');
            
            if (autoRefresh) {
                btn.textContent = '🔄 Auto ON';
                btn.className = 'refresh-btn auto-refresh';
                startAutoRefresh();
            } else {
                btn.textContent = '⏸️ Auto OFF';
                btn.className = 'refresh-btn auto-refresh disabled';
                stopAutoRefresh();
            }
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(() => {
                console.log('🔄 Rafraîchissement automatique...');
                fetchStats();
            }, 30000); // 30 secondes pour le test
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // Event listeners
        document.getElementById('refreshBtn').addEventListener('click', () => fetchStats());
        document.getElementById('autoRefreshBtn').addEventListener('click', toggleAutoRefresh);
        document.getElementById('testErrorBtn').addEventListener('click', () => fetchStats(true));

        // Démarrage initial
        fetchStats();
        startAutoRefresh();
    </script>
</body>
</html>
