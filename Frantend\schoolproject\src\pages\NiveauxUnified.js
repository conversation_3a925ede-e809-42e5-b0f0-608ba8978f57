import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import Pagination from '../components/Pagination';
import usePagination from '../hooks/usePagination';
import useSearch from '../hooks/useSearch';
import '../css/UnifiedPages.css';
import '../css/Pagination.css';

const NiveauxUnified = () => {
    const [niveaux, setNiveaux] = useState([]);
    const [loading, setLoading] = useState(true);

    // Hooks personnalisés
    const { 
        searchTerm, 
        setSearchTerm, 
        filterValue, 
        setFilterValue, 
        filteredData, 
        getUniqueFilterValues, 
        clearFilters, 
        hasActiveFilters 
    } = useSearch(
        niveaux,
        ['nom', 'description'], // Champs de recherche
        'type' // Champ de filtrage
    );

    const { 
        paginatedData, 
        currentPage, 
        totalPages, 
        goToPage, 
        resetPagination, 
        paginationInfo 
    } = usePagination(filteredData, 10);

    useEffect(() => {
        fetchNiveaux();
    }, []);

    useEffect(() => {
        resetPagination();
    }, [filteredData, resetPagination]);

    const fetchNiveaux = async () => {
        try {
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/niveaux/getNiveaux.php');
            setNiveaux(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des niveaux:', error);
            Swal.fire('Erreur', 'Impossible de charger les niveaux', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleClearFilters = () => {
        clearFilters();
    };

    if (loading) {
        return (
            <div className="unified-container">
                <div className="unified-loading">
                    <div className="unified-spinner"></div>
                    <p>Chargement des niveaux...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="unified-container">
            {/* En-tête */}
            <div className="unified-header">
                <h1 className="unified-title">
                    <span className="unified-title-icon">📊</span>
                    Gestion des Niveaux
                </h1>
                <div className="unified-header-actions">
                    <span className="unified-count">
                        {filteredData.length} niveau(x)
                    </span>
                    <button className="unified-btn unified-btn-primary">
                        ➕ Nouveau Niveau
                    </button>
                </div>
            </div>

            {/* Filtres */}
            <div className="unified-filters">
                <div className="unified-filters-grid">
                    <div className="unified-search-box">
                        <input
                            type="text"
                            className="unified-search-input"
                            placeholder="🔍 Rechercher par nom ou description..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <span className="unified-search-icon">🔍</span>
                    </div>
                    
                    <select
                        className="unified-filter-select"
                        value={filterValue}
                        onChange={(e) => setFilterValue(e.target.value)}
                    >
                        <option value="all">Tous les types</option>
                        {getUniqueFilterValues().map(value => (
                            <option key={value} value={value}>{value}</option>
                        ))}
                    </select>
                    
                    {hasActiveFilters && (
                        <button 
                            className="unified-clear-btn"
                            onClick={handleClearFilters}
                        >
                            ✖️ Effacer
                        </button>
                    )}
                </div>
            </div>

            {/* Contenu principal */}
            <div className="unified-content">
                {filteredData.length === 0 ? (
                    <div className="unified-empty">
                        <div className="unified-empty-icon">📊</div>
                        <h3 className="unified-empty-title">Aucun niveau trouvé</h3>
                        <p className="unified-empty-text">
                            {hasActiveFilters 
                                ? 'Aucun niveau ne correspond à vos critères de recherche.'
                                : 'Aucun niveau n\'est disponible pour le moment.'
                            }
                        </p>
                        {hasActiveFilters && (
                            <button 
                                className="unified-btn unified-btn-primary"
                                onClick={handleClearFilters}
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <>
                        <table className="unified-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom du Niveau</th>
                                    <th>Description</th>
                                    <th>Ordre</th>
                                    <th>Type</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedData.map((niveau) => (
                                    <tr key={niveau.id}>
                                        <td>
                                            <span className="unified-badge unified-badge-primary">
                                                #{niveau.id}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50' }}>
                                                {niveau.nom}
                                            </strong>
                                        </td>
                                        <td>
                                            <span style={{ color: '#6c757d' }}>
                                                {niveau.description || 'Aucune description'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-info">
                                                {niveau.ordre || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-warning">
                                                {niveau.type || 'Standard'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-success">
                                                Actif
                                            </span>
                                        </td>
                                        <td>
                                            <div className="unified-actions">
                                                <button 
                                                    className="unified-btn unified-btn-info"
                                                    title="Voir détails"
                                                >
                                                    👁️ Voir
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-warning"
                                                    title="Modifier"
                                                >
                                                    ✏️ Modifier
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-danger"
                                                    title="Supprimer"
                                                >
                                                    🗑️ Supprimer
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        {/* Pagination */}
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={goToPage}
                            itemsPerPage={10}
                            totalItems={paginationInfo.totalItems}
                        />
                    </>
                )}
            </div>
        </div>
    );
};

export default NiveauxUnified;
