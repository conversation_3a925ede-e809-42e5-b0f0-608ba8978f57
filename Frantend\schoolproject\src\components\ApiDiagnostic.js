import React, { useState, useEffect } from 'react';
import { ALTERNATIVE_URLS } from '../config/api';

const ApiDiagnostic = () => {
    const [testResults, setTestResults] = useState({});
    const [testing, setTesting] = useState(false);
    const [workingUrl, setWorkingUrl] = useState(null);

    const testUrl = async (name, url) => {
        try {
            console.log(`🔍 Test de: ${url}`);

            // Test 1: API de test simple
            const testResponse = await fetch(`${url}/pages/test_api.php`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (testResponse.ok) {
                const data = await testResponse.json();
                if (data.success) {
                    return { status: 'success', message: `✅ API OK (${data.total_users} utilisateurs)`, url };
                }
            }

            // Test 2: Factures API
            const facturesResponse = await fetch(`${url}/pages/factures/`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (facturesResponse.ok) {
                return { status: 'success', message: 'Factures API OK', url };
            }

            // Test 3: getUsers.php existant
            const usersResponse = await fetch(`${url}/pages/utilisateurs/getUsers.php`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (usersResponse.ok) {
                return { status: 'success', message: 'Users API OK', url };
            }

            return { status: 'error', message: `HTTP ${facturesResponse.status}`, url };

        } catch (error) {
            console.error(`❌ Erreur pour ${url}:`, error);
            return {
                status: 'error',
                message: error.message.includes('fetch') ? 'Connexion refusée' : error.message,
                url
            };
        }
    };

    const runDiagnostic = async () => {
        setTesting(true);
        setTestResults({});
        
        const results = {};
        
        for (const [name, url] of Object.entries(ALTERNATIVE_URLS)) {
            console.log(`🧪 Test de ${name}: ${url}`);
            const result = await testUrl(name, url);
            results[name] = result;
            
            if (result.status === 'success' && !workingUrl) {
                setWorkingUrl(url);
            }
            
            setTestResults(prev => ({ ...prev, [name]: result }));
            
            // Petit délai pour éviter de surcharger
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        setTesting(false);
    };

    useEffect(() => {
        runDiagnostic();
    }, []);

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
        alert('URL copiée dans le presse-papiers !');
    };

    return (
        <div style={{
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            margin: '20px',
            fontFamily: 'Arial, sans-serif'
        }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>
                🔧 Diagnostic API - Résolution du problème
            </h2>
            
            {testing && (
                <div style={{ 
                    padding: '15px', 
                    backgroundColor: '#e3f2fd', 
                    borderRadius: '6px',
                    marginBottom: '20px'
                }}>
                    <p>🔍 Test des URLs en cours...</p>
                </div>
            )}

            {workingUrl && (
                <div style={{ 
                    padding: '15px', 
                    backgroundColor: '#d4edda', 
                    borderRadius: '6px',
                    marginBottom: '20px',
                    border: '1px solid #c3e6cb'
                }}>
                    <h3 style={{ color: '#155724', margin: '0 0 10px 0' }}>
                        ✅ URL fonctionnelle trouvée !
                    </h3>
                    <p style={{ margin: '5px 0' }}>
                        <strong>URL à utiliser :</strong> 
                        <code style={{ 
                            backgroundColor: '#f8f9fa', 
                            padding: '2px 6px', 
                            borderRadius: '3px',
                            marginLeft: '10px'
                        }}>
                            {workingUrl}
                        </code>
                        <button 
                            onClick={() => copyToClipboard(workingUrl)}
                            style={{
                                marginLeft: '10px',
                                padding: '5px 10px',
                                backgroundColor: '#28a745',
                                color: 'white',
                                border: 'none',
                                borderRadius: '3px',
                                cursor: 'pointer'
                            }}
                        >
                            Copier
                        </button>
                    </p>
                </div>
            )}

            <div style={{ marginBottom: '20px' }}>
                <h3 style={{ color: '#495057' }}>Résultats des tests :</h3>
                
                {Object.entries(testResults).map(([name, result]) => (
                    <div 
                        key={name}
                        style={{
                            padding: '10px',
                            margin: '10px 0',
                            borderRadius: '6px',
                            backgroundColor: result.status === 'success' ? '#d4edda' : '#f8d7da',
                            border: `1px solid ${result.status === 'success' ? '#c3e6cb' : '#f5c6cb'}`
                        }}
                    >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: 'bold' }}>
                                {result.status === 'success' ? '✅' : '❌'} {name}
                            </span>
                            <span style={{ fontSize: '0.9em', color: '#6c757d' }}>
                                {result.message}
                            </span>
                        </div>
                        <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px' }}>
                            {result.url}
                        </div>
                    </div>
                ))}
            </div>

            <div style={{ 
                padding: '15px', 
                backgroundColor: '#fff3cd', 
                borderRadius: '6px',
                border: '1px solid #ffeaa7'
            }}>
                <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>
                    📋 Instructions de correction :
                </h4>
                <ol style={{ margin: '0', paddingLeft: '20px' }}>
                    <li>Identifiez l'URL qui fonctionne ci-dessus</li>
                    <li>Remplacez toutes les URLs dans vos fichiers React</li>
                    <li>Vérifiez que votre serveur PHP (Laragon/XAMPP) est démarré</li>
                    <li>Assurez-vous que le dossier Project_PFE est accessible</li>
                </ol>
            </div>

            <button 
                onClick={runDiagnostic}
                disabled={testing}
                style={{
                    marginTop: '15px',
                    padding: '10px 20px',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: testing ? 'not-allowed' : 'pointer',
                    opacity: testing ? 0.6 : 1
                }}
            >
                {testing ? '🔄 Test en cours...' : '🔄 Relancer le diagnostic'}
            </button>
        </div>
    );
};

export default ApiDiagnostic;
