<?php
/**
 * 🧪 TEST D'ACCÈS AUX DIPLÔMES
 * 
 * Ce script teste les permissions d'accès aux diplômes selon le rôle utilisateur
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Inclure le système d'authentification
require_once 'config/auth.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Test d'authentification
$user = authenticateRequest($pdo);

if (!$user) {
    echo json_encode([
        'success' => false,
        'error' => 'Authentification échouée'
    ]);
    exit();
}

// Déterminer les permissions selon le rôle
$role = $user['role'];
$permissions = [
    'can_view_diplomes' => true, // Tous peuvent voir
    'can_create_diplome' => false,
    'can_edit_diplome' => false,
    'can_delete_diplome' => false,
    'can_generate_pdf' => false
];

// Permissions selon le rôle
switch ($role) {
    case 'Admin':
    case 'admin':
    case 'responsable':
        $permissions['can_create_diplome'] = true;
        $permissions['can_edit_diplome'] = true;
        $permissions['can_delete_diplome'] = true;
        $permissions['can_generate_pdf'] = true;
        break;
        
    case 'Enseignant':
    case 'enseignant':
    case 'Teacher':
        $permissions['can_generate_pdf'] = true;
        break;
        
    case 'Parent':
    case 'parent':
        $permissions['can_generate_pdf'] = false; // Parents ne peuvent pas générer de PDF
        break;
        
    case 'Etudiant':
    case 'etudiant':
    case 'élève':
        $permissions['can_generate_pdf'] = false; // ÉTUDIANTS NE PEUVENT PAS GÉNÉRER DE PDF
        break;
}

echo json_encode([
    'success' => true,
    'user_info' => [
        'id' => $user['id'],
        'nom' => $user['nom'],
        'email' => $user['email'],
        'role' => $role
    ],
    'permissions' => $permissions,
    'security_rules' => [
        'pdf_generation_blocked_for_students' => $role === 'Etudiant',
        'consultation_only_mode' => in_array($role, ['Etudiant', 'Parent']),
        'admin_full_access' => in_array($role, ['Admin', 'admin', 'responsable'])
    ],
    'message' => $role === 'Etudiant' 
        ? '🔒 Mode consultation étudiant : Vous pouvez voir vos diplômes mais pas les télécharger en PDF'
        : ($permissions['can_generate_pdf'] 
            ? '✅ Vous avez accès à la génération de PDF' 
            : '⚠️ Génération PDF non autorisée pour votre rôle')
]);
?>
