<?php
// ============================================================================
// SCRIPT D'EXÉCUTION DES MISES À JOUR DE BASE DE DONNÉES
// ============================================================================

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Mise à jour Base de Données - Messagerie</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🔄 Mise à jour Base de Données - Système de Messagerie</h1>";

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
} catch (PDOException $e) {
    echo "<div class='error'>❌ Erreur de connexion à la base de données: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit();
}

// Fonction pour exécuter un fichier SQL
function executeSQLFile($pdo, $filePath, $fileName) {
    echo "<h2>📄 Exécution de $fileName</h2>";
    
    if (!file_exists($filePath)) {
        echo "<div class='error'>❌ Fichier $fileName non trouvé</div>";
        return false;
    }
    
    $sql = file_get_contents($filePath);
    
    if (empty($sql)) {
        echo "<div class='error'>❌ Fichier $fileName vide</div>";
        return false;
    }
    
    try {
        // Diviser le SQL en requêtes individuelles
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($queries as $query) {
            if (empty($query) || strpos($query, '--') === 0) {
                continue; // Ignorer les commentaires et lignes vides
            }
            
            try {
                $pdo->exec($query);
                $successCount++;
            } catch (PDOException $e) {
                $errorCount++;
                echo "<div class='error'>❌ Erreur dans la requête: " . $e->getMessage() . "</div>";
                echo "<div class='code'>Requête: " . htmlspecialchars($query) . "</div>";
            }
        }
        
        if ($errorCount === 0) {
            echo "<div class='success'>✅ $fileName exécuté avec succès ($successCount requêtes)</div>";
            return true;
        } else {
            echo "<div class='error'>⚠️ $fileName exécuté avec $errorCount erreur(s) et $successCount succès</div>";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur lors de l'exécution de $fileName: " . $e->getMessage() . "</div>";
        return false;
    }
}

// Liste des fichiers SQL à exécuter
$sqlFiles = [
    'update_messages_table.sql' => 'Mise à jour table Messages',
    'update_notifications_table.sql' => 'Mise à jour table Notifications'
];

$allSuccess = true;

foreach ($sqlFiles as $file => $description) {
    $filePath = __DIR__ . '/' . $file;
    $success = executeSQLFile($pdo, $filePath, $description);
    $allSuccess = $allSuccess && $success;
}

// Vérification finale
echo "<h2>🔍 Vérification Finale</h2>";

try {
    // Vérifier la table messages
    $stmt = $pdo->query("DESCRIBE messages");
    $messageColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $messageColumnNames = array_column($messageColumns, 'Field');
    
    $requiredMessageColumns = ['modifie', 'date_modification', 'supprime_par_expediteur', 'supprime_par_destinataire'];
    $missingMessageColumns = array_diff($requiredMessageColumns, $messageColumnNames);
    
    if (empty($missingMessageColumns)) {
        echo "<div class='success'>✅ Table messages : toutes les colonnes requises sont présentes</div>";
    } else {
        echo "<div class='error'>❌ Table messages : colonnes manquantes : " . implode(', ', $missingMessageColumns) . "</div>";
        $allSuccess = false;
    }
    
    // Vérifier la table notifications
    $stmt = $pdo->query("DESCRIBE notifications");
    $notifColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $notifColumnNames = array_column($notifColumns, 'Field');
    
    $requiredNotifColumns = ['type_notification', 'titre', 'message_id', 'expediteur_id'];
    $missingNotifColumns = array_diff($requiredNotifColumns, $notifColumnNames);
    
    if (empty($missingNotifColumns)) {
        echo "<div class='success'>✅ Table notifications : toutes les colonnes requises sont présentes</div>";
    } else {
        echo "<div class='error'>❌ Table notifications : colonnes manquantes : " . implode(', ', $missingNotifColumns) . "</div>";
        $allSuccess = false;
    }
    
    // Compter les enregistrements
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM messages");
    $messageCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM notifications");
    $notifCount = $stmt->fetch()['count'];
    
    echo "<div class='info'>";
    echo "<h4>📊 Statistiques :</h4>";
    echo "<ul>";
    echo "<li>Messages : $messageCount enregistrement(s)</li>";
    echo "<li>Notifications : $notifCount enregistrement(s)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur lors de la vérification : " . $e->getMessage() . "</div>";
    $allSuccess = false;
}

// Résultat final
if ($allSuccess) {
    echo "<div class='success'>";
    echo "<h3>🎉 Mise à jour terminée avec succès !</h3>";
    echo "<p>Le système de messagerie avancé est maintenant prêt à être utilisé.</p>";
    echo "<ul>";
    echo "<li>✅ Table messages mise à jour avec les nouvelles fonctionnalités</li>";
    echo "<li>✅ Table notifications configurée pour l'intégration</li>";
    echo "<li>✅ Toutes les colonnes requises sont présentes</li>";
    echo "</ul>";
    echo "<p><strong>Prochaines étapes :</strong></p>";
    echo "<ul>";
    echo "<li>🧪 <a href='../pages/messages/setup-complet.php'>Tester le système complet</a></li>";
    echo "<li>💬 <a href='http://localhost:3000/messages'>Accéder à l'interface React</a></li>";
    echo "<li>🔧 <a href='../pages/messages/test-api.php'>Tester les APIs</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ Mise à jour terminée avec des erreurs</h3>";
    echo "<p>Certaines étapes ont échoué. Veuillez vérifier les erreurs ci-dessus et réessayer.</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
