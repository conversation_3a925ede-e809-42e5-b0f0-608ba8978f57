import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import { AuthContext } from '../context/AuthContext';
import '../css/Factures.css';

const ReponsesQuizUnified = () => {
    const { user } = useContext(AuthContext);
    const [reponses, setReponses] = useState([]);
    const [quizDisponibles, setQuizDisponibles] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingReponse, setEditingReponse] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterCorrect, setFilterCorrect] = useState('all');
    const [filterMatiere, setFilterMatiere] = useState('all');
    const [filterEtudiant, setFilterEtudiant] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [statistics, setStatistics] = useState({});
    const [matieres, setMatieres] = useState([]);
    const [etudiants, setEtudiants] = useState([]);
    const [formData, setFormData] = useState({
        quiz_id: '',
        reponse: ''
    });

    // Déterminer le rôle et les permissions
    const isEtudiant = user?.role === 'etudiant' || user?.role === 'Etudiant';
    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';
    
    // Permissions selon les spécifications
    const canManage = isEtudiant; // Seuls les étudiants peuvent CRUD leurs réponses
    const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)

    useEffect(() => {
        if (canView) {
            fetchReponses();
            if (isEtudiant) {
                fetchQuizDisponibles();
            }
        }
    }, [canView, isEtudiant]);

    const fetchReponses = async () => {
        try {
            console.log('🔄 Chargement des réponses...');

            // Déterminer le token selon le rôle
            let authToken = 'default-token';
            if (isEtudiant) authToken = 'etudiant-token';
            else if (isEnseignant) authToken = 'enseignant-token';
            else if (isAdmin) authToken = 'admin-token';

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php', {
                headers: { Authorization: `Bearer ${authToken}` }
            });

            console.log('✅ Réponse API réponses:', response.data);
            if (response.data.success) {
                const reponsesData = response.data.data || [];
                setReponses(reponsesData);
                
                // Calculer les statistiques
                const stats = calculateStatistics(reponsesData);
                setStatistics(stats);
                
                // Extraire les matières et étudiants uniques pour les filtres
                if (!isEtudiant) {
                    const matieresUniques = [...new Set(reponsesData.map(r => r.matiere_nom).filter(Boolean))];
                    setMatieres(matieresUniques);
                    
                    if (isAdmin) {
                        const etudiantsUniques = [...new Set(reponsesData.map(r => r.etudiant_nom).filter(Boolean))];
                        setEtudiants(etudiantsUniques);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des réponses:', error);
            if (error.response?.status === 403) {
                Swal.fire('Accès refusé', 'Vous n\'avez pas les permissions pour consulter les réponses', 'error');
            } else {
                Swal.fire('Erreur', 'Impossible de charger les réponses', 'error');
            }
            setReponses([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchQuizDisponibles = async () => {
        try {
            console.log('🔄 Chargement des quiz disponibles...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/reponsesquiz/quiz-disponibles.php', {
                headers: { Authorization: `Bearer etudiant-token` }
            });

            console.log('✅ Réponse API quiz disponibles:', response.data);
            if (response.data.success) {
                setQuizDisponibles(response.data.data || []);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des quiz:', error);
            setQuizDisponibles([]);
        }
    };

    const calculateStatistics = (reponsesData) => {
        const total = reponsesData.length;
        const correctes = reponsesData.filter(r => r.est_correct === 1).length;
        const incorrectes = reponsesData.filter(r => r.est_correct === 0).length;
        const nonEvaluees = reponsesData.filter(r => r.est_correct === null).length;
        
        const etudiants = [...new Set(reponsesData.map(r => r.etudiant_id).filter(Boolean))];
        const quiz = [...new Set(reponsesData.map(r => r.quiz_id).filter(Boolean))];
        
        return {
            total_reponses: total,
            reponses_correctes: correctes,
            reponses_incorrectes: incorrectes,
            reponses_non_evaluees: nonEvaluees,
            pourcentage_reussite: total > 0 ? Math.round((correctes / total) * 100) : 0,
            nombre_etudiants: etudiants.length,
            nombre_quiz: quiz.length
        };
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les étudiants peuvent créer/modifier des réponses', 'error');
            return;
        }

        if (!formData.quiz_id || !formData.reponse.trim()) {
            Swal.fire('Erreur', 'Veuillez sélectionner un quiz et saisir votre réponse', 'error');
            return;
        }

        try {
            const url = 'http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php';
            const method = editingReponse ? 'PUT' : 'POST';
            const data = editingReponse ? { ...formData, id: editingReponse.id } : formData;

            console.log('🔄 Envoi réponse:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer etudiant-token`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse envoyée:', response.data);

            if (response.data.success) {
                Swal.fire({
                    title: 'Succès !',
                    text: response.data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
                setShowModal(false);
                setEditingReponse(null);
                resetForm();
                fetchReponses();
                if (isEtudiant) fetchQuizDisponibles();
            } else {
                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (reponse) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les étudiants peuvent modifier leurs réponses', 'error');
            return;
        }

        setEditingReponse(reponse);
        setFormData({
            quiz_id: reponse.quiz_id || '',
            reponse: reponse.reponse || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les étudiants peuvent supprimer leurs réponses', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Supprimer cette réponse ?',
            text: 'Cette action est irréversible !',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                console.log('🗑️ Suppression réponse ID:', id);
                
                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php', {
                    headers: { 
                        Authorization: `Bearer etudiant-token`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                console.log('✅ Réponse supprimée:', response.data);

                if (response.data.success) {
                    Swal.fire({
                        title: 'Supprimé !',
                        text: response.data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    fetchReponses();
                    if (isEtudiant) fetchQuizDisponibles();
                } else {
                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la réponse', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la réponse', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            quiz_id: '',
            reponse: ''
        });
    };

    // Filtrage des réponses selon le rôle
    const filteredReponses = reponses.filter(reponse => {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = (
            (reponse.reponse || '').toLowerCase().includes(searchLower) ||
            (reponse.question || '').toLowerCase().includes(searchLower) ||
            (reponse.devoir_titre || '').toLowerCase().includes(searchLower) ||
            (reponse.matiere_nom || '').toLowerCase().includes(searchLower) ||
            (reponse.etudiant_nom || '').toLowerCase().includes(searchLower)
        );

        const matchesCorrect = filterCorrect === 'all' || 
            (filterCorrect === 'correct' && reponse.est_correct === 1) ||
            (filterCorrect === 'incorrect' && reponse.est_correct === 0) ||
            (filterCorrect === 'non_evalue' && reponse.est_correct === null);

        const matchesMatiere = filterMatiere === 'all' || reponse.matiere_nom === filterMatiere;
        const matchesEtudiant = filterEtudiant === 'all' || reponse.etudiant_nom === filterEtudiant;

        return matchesSearch && matchesCorrect && matchesMatiere && matchesEtudiant;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentReponses = filteredReponses.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredReponses.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, filterCorrect, filterMatiere, filterEtudiant]);

    const getCorrectBadge = (est_correct) => {
        const baseStyle = {
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.8em',
            fontWeight: 'bold'
        };

        if (est_correct === 1) {
            return <span style={{...baseStyle, backgroundColor: '#28a745', color: 'white'}}>✅ Correct</span>;
        } else if (est_correct === 0) {
            return <span style={{...baseStyle, backgroundColor: '#dc3545', color: 'white'}}>❌ Incorrect</span>;
        } else {
            return <span style={{...baseStyle, backgroundColor: '#6c757d', color: 'white'}}>⏳ Non évalué</span>;
        }
    };

    const getHeaderTitle = () => {
        if (isEtudiant) return '📝 Mes Réponses aux Quiz';
        if (isEnseignant) return '👨‍🏫 Suivi des Réponses aux Quiz';
        if (isAdmin) return '🛡️ Administration - Réponses aux Quiz';
        return '📝 Réponses aux Quiz';
    };

    const getInfoMessage = () => {
        if (isEtudiant) {
            return {
                style: { backgroundColor: '#fff3cd', border: '1px solid #ffc107', color: '#856404' },
                text: 'ℹ️ Vous pouvez ajouter, modifier ou supprimer vos réponses. Les corrections ne sont pas affichées - concentrez-vous sur vos meilleures réponses !'
            };
        } else if (isEnseignant) {
            return {
                style: { backgroundColor: '#d1ecf1', border: '1px solid #bee5eb', color: '#0c5460' },
                text: '👨‍🏫 Mode Enseignant : Vous consultez toutes les réponses des étudiants avec leur état de correction. Cette interface est en lecture seule.'
            };
        } else if (isAdmin) {
            return {
                style: { backgroundColor: '#e2e3e5', border: '1px solid #d6d8db', color: '#383d41' },
                text: '🛡️ Mode Administrateur : Vous consultez toutes les données en lecture seule. Aucune modification n\'est possible depuis cette interface.'
            };
        }
        return null;
    };

    const styles = {
        accessDenied: {
            textAlign: 'center',
            padding: '50px 20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            margin: '20px 0'
        },
        idBadge: {
            backgroundColor: isAdmin ? '#6f42c1' : '#007bff',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.8em',
            fontWeight: 'bold'
        },
        reponseText: {
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            backgroundColor: '#f8f9fa',
            padding: '6px 10px',
            borderRadius: '4px',
            border: '1px solid #dee2e6'
        },
        correctReponse: {
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            backgroundColor: '#d4edda',
            padding: '6px 10px',
            borderRadius: '4px',
            border: '1px solid #c3e6cb',
            color: '#155724'
        }
    };

    // Vérification d'accès
    if (!canView) {
        return (
            <div className="factures-container">
                <div style={styles.accessDenied}>
                    <h2>🚫 Accès Refusé</h2>
                    <p>Vous n'avez pas les permissions pour accéder aux réponses des quiz.</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des réponses...</p>
            </div>
        );
    }

    const infoMessage = getInfoMessage();

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>{getHeaderTitle()}</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredReponses.length} réponse(s) trouvée(s)
                    </span>
                    {canManage && (
                        <button 
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvelle Réponse
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information selon le rôle */}
            {infoMessage && (
                <div style={{
                    ...infoMessage.style,
                    borderRadius: '8px',
                    padding: '15px',
                    margin: '20px 0'
                }}>
                    <p style={{ margin: '0' }}>{infoMessage.text}</p>
                </div>
            )}

            {/* Statistiques selon le rôle */}
            {statistics.total_reponses > 0 && (
                <div style={{
                    display: 'grid',
                    gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',
                    gap: '15px',
                    backgroundColor: isAdmin ? '#f8f9fa' : '#e3f2fd',
                    border: `1px solid ${isAdmin ? '#dee2e6' : '#2196f3'}`,
                    borderRadius: '8px',
                    padding: '20px',
                    margin: '20px 0'
                }}>
                    <div style={{
                        textAlign: 'center',
                        padding: '15px',
                        backgroundColor: 'white',
                        borderRadius: '6px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: isAdmin ? '#6f42c1' : '#007bff' }}>
                            {statistics.total_reponses}
                        </div>
                        <div style={{ fontSize: '12px', color: '#6c757d' }}>
                            {isEtudiant ? 'Mes Réponses' : 'Total Réponses'}
                        </div>
                    </div>

                    {!isEtudiant && (
                        <>
                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                                    {statistics.reponses_correctes}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Correctes</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>
                                    {statistics.reponses_incorrectes}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Incorrectes</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff' }}>
                                    {statistics.pourcentage_reussite}%
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Taux Réussite</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#17a2b8' }}>
                                    {statistics.nombre_etudiants}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Étudiants</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fd7e14' }}>
                                    {statistics.nombre_quiz}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Quiz</div>
                            </div>
                        </>
                    )}
                </div>
            )}

            {/* Filtres et recherche */}
            <div className="search-section">
                <div className="search-bar">
                    <img src="/search.png" alt="Rechercher" />
                    <input
                        type="text"
                        placeholder={isEtudiant ? "Rechercher dans vos réponses..." : "Rechercher par réponse, question, étudiant, devoir..."}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {!isEtudiant && (
                    <div className="filter-section" style={{
                        display: 'grid',
                        gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',
                        gap: '10px',
                        marginTop: '15px'
                    }}>
                        <select
                            value={filterCorrect}
                            onChange={(e) => setFilterCorrect(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">📊 Toutes les réponses</option>
                            <option value="correct">✅ Correctes uniquement</option>
                            <option value="incorrect">❌ Incorrectes uniquement</option>
                            <option value="non_evalue">⏳ Non évaluées</option>
                        </select>

                        <select
                            value={filterMatiere}
                            onChange={(e) => setFilterMatiere(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">📖 Toutes les matières</option>
                            {matieres.map(matiere => (
                                <option key={matiere} value={matiere}>{matiere}</option>
                            ))}
                        </select>

                        {isAdmin && (
                            <select
                                value={filterEtudiant}
                                onChange={(e) => setFilterEtudiant(e.target.value)}
                                className="filter-select"
                            >
                                <option value="all">👤 Tous les étudiants</option>
                                {etudiants.map(etudiant => (
                                    <option key={etudiant} value={etudiant}>{etudiant}</option>
                                ))}
                            </select>
                        )}
                    </div>
                )}
            </div>

            {/* Tableau des réponses */}
            <div className="table-container">
                {filteredReponses.length === 0 ? (
                    <div className="no-data">
                        <img src="/empty.png" alt="Aucune donnée" />
                        <p>Aucune réponse trouvée</p>
                        <p>{isEtudiant ? "Commencez par répondre à un quiz !" : "Modifiez vos critères de recherche ou filtres"}</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    {!isEtudiant && <th>👤 Étudiant</th>}
                                    <th>❓ Question</th>
                                    <th>📝 {isEtudiant ? 'Ma Réponse' : 'Réponse'}</th>
                                    {!isEtudiant && <th>🎯 Attendue</th>}
                                    {!isEtudiant && <th>✅ État</th>}
                                    <th>📚 Devoir</th>
                                    <th>📖 Matière</th>
                                    {isAdmin && <th>🏫 Classe</th>}
                                    {canManage && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentReponses.map((reponse) => (
                                    <tr key={reponse.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{reponse.id}
                                            </span>
                                        </td>
                                        {!isEtudiant && (
                                            <td>
                                                <div className="user-info">
                                                    <strong style={{ fontSize: '0.9em' }}>
                                                        {reponse.etudiant_nom || 'Nom non disponible'}
                                                    </strong>
                                                    <br />
                                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>
                                                        {reponse.etudiant_email || 'Email non disponible'}
                                                    </small>
                                                </div>
                                            </td>
                                        )}
                                        <td>
                                            <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                                                <strong style={{ fontSize: '0.9em' }}>
                                                    {reponse.question || 'Question non définie'}
                                                </strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div style={styles.reponseText} title={reponse.reponse}>
                                                {reponse.reponse || 'Réponse vide'}
                                            </div>
                                        </td>
                                        {!isEtudiant && (
                                            <td>
                                                <div style={styles.correctReponse} title={reponse.reponse_correcte}>
                                                    {reponse.reponse_correcte || 'Non définie'}
                                                </div>
                                            </td>
                                        )}
                                        {!isEtudiant && (
                                            <td>
                                                {getCorrectBadge(reponse.est_correct)}
                                            </td>
                                        )}
                                        <td>
                                            <span style={{
                                                padding: '3px 6px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.8em'
                                            }}>
                                                {reponse.devoir_titre || 'Non spécifié'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '3px 6px',
                                                backgroundColor: '#fff3e0',
                                                borderRadius: '4px',
                                                fontSize: '0.8em'
                                            }}>
                                                {reponse.matiere_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <span style={{
                                                    padding: '3px 6px',
                                                    backgroundColor: '#f3e5f5',
                                                    borderRadius: '4px',
                                                    fontSize: '0.8em'
                                                }}>
                                                    {reponse.classe_nom || 'Non spécifiée'}
                                                </span>
                                            </td>
                                        )}
                                        {canManage && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(reponse)}
                                                        title="Modifier ma réponse"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(reponse.id)}
                                                        title="Supprimer ma réponse"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="btn btn-outline-primary"
                    >
                        Précédent
                    </button>

                    <div className="page-info">
                        Page {currentPage} sur {totalPages}
                    </div>

                    <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="btn btn-outline-primary"
                    >
                        Suivant
                    </button>
                </div>
            )}

            {/* Modal pour ajouter/modifier une réponse (étudiants uniquement) */}
            {showModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingReponse ? 'Modifier ma réponse' : 'Nouvelle réponse'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingReponse(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Quiz / Question *</label>
                                <select
                                    value={formData.quiz_id}
                                    onChange={(e) => setFormData({...formData, quiz_id: e.target.value})}
                                    required
                                    disabled={editingReponse} // Empêcher la modification du quiz lors de l'édition
                                >
                                    <option value="">Sélectionner un quiz...</option>
                                    {quizDisponibles.map(q => (
                                        <option
                                            key={q.id}
                                            value={q.id}
                                            disabled={q.deja_repondu && !editingReponse}
                                        >
                                            {q.quiz_display}
                                            {q.deja_repondu && !editingReponse ? ' (Déjà répondu)' : ''}
                                        </option>
                                    ))}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    {editingReponse
                                        ? 'Vous ne pouvez pas changer le quiz lors de la modification'
                                        : 'Sélectionnez le quiz auquel vous voulez répondre'
                                    }
                                </small>
                            </div>

                            <div className="form-group">
                                <label>Votre Réponse *</label>
                                <textarea
                                    value={formData.reponse}
                                    onChange={(e) => setFormData({...formData, reponse: e.target.value})}
                                    placeholder="Saisissez votre réponse..."
                                    required
                                    rows="4"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Donnez votre meilleure réponse. Vous pourrez la modifier plus tard si nécessaire.
                                </small>
                            </div>

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingReponse ? '💾 Modifier' : '➕ Répondre'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingReponse(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ReponsesQuizUnified;
