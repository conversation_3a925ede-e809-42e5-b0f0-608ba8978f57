# 🔧 Guide de Correction des URLs d'API

## 🚨 Problème Identifié

Le message "Impossible de charger les factures" apparaît car l'URL de l'API n'est pas accessible. Voici comment résoudre le problème :

## 📋 Étapes de Diagnostic

### 1. **Accédez à la page d'accueil**
- Allez sur `http://localhost:3000/` (votre app React)
- Vous verrez maintenant un **diagnostic automatique** en bas de page
- Il testera automatiquement différentes URLs

### 2. **Identifiez l'URL qui fonctionne**
Le diagnostic testera ces URLs :
- `http://localhost/Project_PFE/Backend`
- `http://localhost:80/Project_PFE/Backend`
- `http://localhost:8080/Project_PFE/Backend`

### 3. **Vérifications préalables**

#### ✅ **Serveur PHP démarré ?**
- **Laragon** : Vérifiez que Apache et MySQL sont démarrés
- **XAMPP** : Démarrez Apache et MySQL
- **WAMP** : Vérifiez que les services sont actifs

#### ✅ **Fichiers accessibles ?**
Testez manuellement dans votre navigateur :
```
http://localhost/Project_PFE/Backend/pages/factures/
```

Vous devriez voir une erreur JSON comme :
```json
{"error": "Token manquant"}
```
C'est **NORMAL** ! Cela signifie que l'API fonctionne.

#### ✅ **Structure des dossiers correcte ?**
```
c:\laragon\www\Project_PFE\
├── Backend\
│   ├── pages\
│   │   ├── factures\
│   │   │   └── index.php
│   │   ├── diplomes\
│   │   └── ...
│   └── config\
│       └── db.php
└── Frantend\
    └── schoolproject\
```

## 🛠️ Solutions par Environnement

### **Laragon (Recommandé)**
```
URL à utiliser : http://localhost/Project_PFE/Backend
```

### **XAMPP**
```
URL à utiliser : http://localhost/Project_PFE/Backend
```

### **WAMP**
```
URL à utiliser : http://localhost/Project_PFE/Backend
```

### **Serveur avec port spécifique**
```
URL à utiliser : http://localhost:8080/Project_PFE/Backend
```

## 🔄 Correction Automatique

Une fois que vous avez identifié l'URL qui fonctionne, remplacez dans **TOUS** ces fichiers :

### **Fichiers à modifier :**
1. `Frantend/schoolproject/src/pages/Factures.js`
2. `Frantend/schoolproject/src/pages/Diplomes.js`
3. `Frantend/schoolproject/src/pages/Absences.js`
4. `Frantend/schoolproject/src/pages/Retards.js`
5. `Frantend/schoolproject/src/pages/Quiz.js`

### **Rechercher et remplacer :**
```javascript
// ANCIEN (à remplacer)
'http://localhost/Project_PFE/Backend/pages/factures/'

// NOUVEAU (exemple avec l'URL qui fonctionne)
'http://localhost:80/Project_PFE/Backend/pages/factures/'
```

## 🚀 Solution Rapide

### **Option 1 : Modification manuelle**
1. Ouvrez chaque fichier React
2. Remplacez toutes les occurrences de l'URL
3. Sauvegardez et testez

### **Option 2 : Utilisation du fichier de config**
Modifiez `Frantend/schoolproject/src/config/api.js` :
```javascript
const API_CONFIG = {
    BASE_URL: 'http://localhost:80/Project_PFE/Backend', // Votre URL qui fonctionne
    // ...
};
```

Puis dans chaque fichier, remplacez :
```javascript
// Au lieu de :
'http://localhost/Project_PFE/Backend/pages/factures/'

// Utilisez :
import { getApiUrl } from '../config/api';
// ...
const response = await axios.get(getApiUrl('FACTURES'), {
```

## 🧪 Test Final

Après correction :
1. Redémarrez votre serveur React (`npm start`)
2. Connectez-vous à l'application
3. Accédez à `/factures`
4. Vous devriez voir la liste (vide au début) sans erreur

## ❗ Problèmes Courants

### **Erreur CORS**
Si vous voyez des erreurs CORS, ajoutez dans vos fichiers PHP :
```php
header('Access-Control-Allow-Origin: http://localhost:3000');
```

### **Erreur 404**
- Vérifiez que le dossier `Project_PFE` est dans le bon répertoire
- Vérifiez les permissions des fichiers

### **Erreur de base de données**
- Vérifiez `Backend/config/db.php`
- Assurez-vous que MySQL est démarré
- Vérifiez les credentials de connexion

## 📞 Support

Si le problème persiste :
1. Copiez l'URL qui fonctionne depuis le diagnostic
2. Remplacez dans tous les fichiers React
3. Redémarrez le serveur React
4. Testez à nouveau

**Le diagnostic automatique vous donnera l'URL exacte à utiliser !** 🎯
