// Script d'impression automatique des diplomes - Version de sauvegarde
// Compatible avec tous les navigateurs modernes

(function() {
    'use strict';
    
    // Variables globales
    var printState = {
        dialogOpened: false,
        completed: false,
        startTime: null,
        timeoutId: null,
        focusTimeoutId: null
    };
    
    // Configuration
    var config = {
        printDelay: 1500,        // Delai avant impression (ms)
        closeDelay: 500,         // Delai avant fermeture (ms)
        focusDelay: 1000,        // Delai detection focus (ms)
        maxTimeout: 30000        // Timeout maximum (ms)
    };
    
    // Fonction de log avec timestamp
    function log(message) {
        var timestamp = new Date().toLocaleTimeString();
        console.log('[' + timestamp + '] Diploma Print: ' + message);
    }
    
    // Fonction de fermeture securisee
    function closeWindow() {
        log('Attempting to close window...');
        
        try {
            // Methode 1: Fermeture directe
            window.close();
            
            // Methode 2: Si la fermeture echoue, afficher un message
            setTimeout(function() {
                if (!window.closed) {
                    log('Window could not be closed automatically');
                    showCloseMessage();
                }
            }, 1000);
            
        } catch (error) {
            log('Error closing window: ' + error.message);
            showCloseMessage();
        }
    }
    
    // Afficher un message de fermeture manuelle
    function showCloseMessage() {
        var messageHTML = [
            '<div style="',
                'position: fixed;',
                'top: 0;',
                'left: 0;',
                'width: 100%;',
                'height: 100%;',
                'background: rgba(0,0,0,0.8);',
                'display: flex;',
                'justify-content: center;',
                'align-items: center;',
                'z-index: 9999;',
                'font-family: Arial, sans-serif;',
            '">',
                '<div style="',
                    'background: white;',
                    'padding: 30px;',
                    'border-radius: 10px;',
                    'text-align: center;',
                    'box-shadow: 0 4px 20px rgba(0,0,0,0.3);',
                    'max-width: 400px;',
                '">',
                    '<h2 style="color: #28a745; margin-bottom: 15px;">',
                        'Impression Terminee',
                    '</h2>',
                    '<p style="color: #6c757d; margin-bottom: 20px;">',
                        'Le diplome a ete traite avec succes.<br>',
                        'Vous pouvez fermer cette fenetre.',
                    '</p>',
                    '<button onclick="window.close()" style="',
                        'background: #007bff;',
                        'color: white;',
                        'border: none;',
                        'padding: 10px 20px;',
                        'border-radius: 5px;',
                        'cursor: pointer;',
                        'font-size: 14px;',
                    '">',
                        'Fermer la fenetre',
                    '</button>',
                '</div>',
            '</div>'
        ].join('');
        
        document.body.insertAdjacentHTML('beforeend', messageHTML);
    }
    
    // Fonction principale d'impression
    function startPrint() {
        if (printState.dialogOpened) {
            log('Print dialog already opened, skipping...');
            return;
        }
        
        printState.dialogOpened = true;
        printState.startTime = Date.now();
        
        log('Starting print process...');
        
        // Lancer l'impression
        try {
            window.print();
            log('Print dialog opened successfully');
        } catch (error) {
            log('Error opening print dialog: ' + error.message);
            setTimeout(closeWindow, 2000);
            return;
        }
        
        // Configurer les gestionnaires d'evenements
        setupPrintEventHandlers();
        
        // Timeout de securite
        printState.timeoutId = setTimeout(function() {
            if (!printState.completed) {
                log('Security timeout reached - forcing close');
                markPrintCompleted();
                closeWindow();
            }
        }, config.maxTimeout);
    }
    
    // Configurer les gestionnaires d'evenements d'impression
    function setupPrintEventHandlers() {
        // Evenement apres impression (supporte par la plupart des navigateurs)
        window.addEventListener('afterprint', function() {
            log('Print completed (afterprint event)');
            markPrintCompleted();
            setTimeout(closeWindow, config.closeDelay);
        });
        
        // Evenement avant impression
        window.addEventListener('beforeprint', function() {
            log('Print started (beforeprint event)');
        });
        
        // Detection du retour de focus (pour gerer l'annulation)
        window.addEventListener('focus', function() {
            if (printState.dialogOpened && !printState.completed) {
                log('Window regained focus - user may have cancelled');
                
                // Nettoyer le timeout precedent
                if (printState.focusTimeoutId) {
                    clearTimeout(printState.focusTimeoutId);
                }
                
                // Attendre un peu avant de fermer (au cas ou c'est juste un changement de focus)
                printState.focusTimeoutId = setTimeout(function() {
                    if (!printState.completed) {
                        log('Focus timeout - assuming cancellation');
                        markPrintCompleted();
                        closeWindow();
                    }
                }, config.focusDelay);
            }
        });
        
        // Detection de la perte de focus (quand la boite de dialogue s'ouvre)
        window.addEventListener('blur', function() {
            if (printState.dialogOpened && !printState.completed) {
                log('Window lost focus - print dialog likely opened');
            }
        });
    }
    
    // Marquer l'impression comme terminee
    function markPrintCompleted() {
        if (printState.completed) return;
        
        printState.completed = true;
        
        // Nettoyer les timeouts
        if (printState.timeoutId) {
            clearTimeout(printState.timeoutId);
        }
        if (printState.focusTimeoutId) {
            clearTimeout(printState.focusTimeoutId);
        }
        
        // Calculer la duree
        if (printState.startTime) {
            var duration = Date.now() - printState.startTime;
            log('Print process completed in ' + duration + 'ms');
        }
    }
    
    // Gestionnaire d'erreurs global
    function setupErrorHandlers() {
        window.addEventListener('error', function(event) {
            log('JavaScript error: ' + event.message);
            setTimeout(closeWindow, 2000);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            log('Unhandled promise rejection: ' + event.reason);
        });
    }
    
    // Fonction d'initialisation
    function initialize() {
        log('Initializing automatic print script...');
        
        // Configurer les gestionnaires d'erreurs
        setupErrorHandlers();
        
        // Attendre que la page soit completement chargee
        if (document.readyState === 'complete') {
            scheduleAutoPrint();
        } else {
            window.addEventListener('load', scheduleAutoPrint);
        }
    }
    
    // Programmer l'impression automatique
    function scheduleAutoPrint() {
        log('Page loaded, scheduling auto-print in ' + config.printDelay + 'ms...');
        
        setTimeout(function() {
            log('Starting automatic print...');
            startPrint();
        }, config.printDelay);
    }
    
    // Demarrer l'initialisation
    initialize();
    
    // Exposer quelques fonctions pour le debug
    window.diplomaPrint = {
        start: startPrint,
        close: closeWindow,
        state: printState,
        config: config
    };
    
})();
