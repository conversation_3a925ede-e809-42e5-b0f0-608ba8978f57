<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Fonction d'authentification simplifiée pour les tests
function getAuthenticatedUser() {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    // Pour les tests, on simule différents rôles
    // En production, vous devriez vérifier le token dans votre base de données
    if ($token) {
        return [
            'id' => 1,
            'role' => 'enseignant', // Peut être 'enseignant', 'admin', 'etudiant'
            'email' => '<EMAIL>'
        ];
    }
    
    return null;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $user = getAuthenticatedUser();
    
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user);
            break;
        case 'POST':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePost($pdo, $user, $input);
            break;
        case 'PUT':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePut($pdo, $user, $input);
            break;
        case 'DELETE':
            $input = json_decode(file_get_contents("php://input"), true);
            handleDelete($pdo, $user, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

function handleGet($pdo, $user) {
    try {
        // Vérifier les permissions - Seuls enseignants et admins peuvent voir les quiz
        if (!$user || !in_array($user['role'], ['enseignant', 'admin'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Accès refusé. Seuls les enseignants et admins peuvent consulter les quiz.']);
            return;
        }

        // Récupérer tous les quiz avec les informations des devoirs
        $sql = "
            SELECT 
                q.id,
                q.devoir_id,
                q.question,
                q.reponse_correcte,
                d.titre as devoir_titre,
                d.description as devoir_description,
                d.date_remise as devoir_date_remise,
                m.nom as matiere_nom,
                c.nom as classe_nom
            FROM Quiz q
            LEFT JOIN Devoirs d ON q.devoir_id = d.id
            LEFT JOIN Matieres m ON d.matiere_id = m.id
            LEFT JOIN Classes c ON d.classe_id = c.id
            ORDER BY q.id DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $quiz = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($quiz);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des quiz: ' . $e->getMessage()]);
    }
}

function handlePost($pdo, $user, $input) {
    // Seuls les enseignants peuvent créer des quiz
    if (!$user || $user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent créer des quiz']);
        return;
    }
    
    if (!isset($input['devoir_id']) || !isset($input['question']) || !isset($input['reponse_correcte'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (devoir_id, question, reponse_correcte requis)']);
        return;
    }
    
    try {
        // Vérifier que le devoir existe
        $checkStmt = $pdo->prepare("SELECT id FROM Devoirs WHERE id = ?");
        $checkStmt->execute([$input['devoir_id']]);
        if (!$checkStmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Le devoir sélectionné n\'existe pas']);
            return;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO Quiz (devoir_id, question, reponse_correcte) 
            VALUES (?, ?, ?)
        ");
        
        $stmt->execute([
            $input['devoir_id'],
            $input['question'],
            $input['reponse_correcte']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Quiz créé avec succès',
            'id' => $pdo->lastInsertId()
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

function handlePut($pdo, $user, $input) {
    // Seuls les enseignants peuvent modifier des quiz
    if (!$user || $user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent modifier des quiz']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID du quiz manquant']);
        return;
    }
    
    try {
        // Vérifier que le quiz existe
        $checkStmt = $pdo->prepare("SELECT id FROM Quiz WHERE id = ?");
        $checkStmt->execute([$input['id']]);
        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Quiz non trouvé']);
            return;
        }
        
        // Vérifier que le devoir existe si fourni
        if (isset($input['devoir_id'])) {
            $checkDevoirStmt = $pdo->prepare("SELECT id FROM Devoirs WHERE id = ?");
            $checkDevoirStmt->execute([$input['devoir_id']]);
            if (!$checkDevoirStmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => 'Le devoir sélectionné n\'existe pas']);
                return;
            }
        }
        
        $stmt = $pdo->prepare("
            UPDATE Quiz 
            SET devoir_id = ?, 
                question = ?, 
                reponse_correcte = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            $input['devoir_id'] ?? null,
            $input['question'] ?? '',
            $input['reponse_correcte'] ?? '',
            $input['id']
        ]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Quiz modifié avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'Aucune modification nécessaire'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

function handleDelete($pdo, $user, $input) {
    // Seuls les enseignants peuvent supprimer des quiz
    if (!$user || $user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent supprimer des quiz']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID du quiz manquant']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM Quiz WHERE id = ?");
        $stmt->execute([$input['id']]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Quiz supprimé avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Quiz non trouvé'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
