<?php
// API complète pour la gestion des utilisateurs (CRUD)
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'GET':
        handleGet($pdo);
        break;
    case 'POST':
        handlePost($pdo, $input);
        break;
    case 'PUT':
        handlePut($pdo, $input);
        break;
    case 'DELETE':
        handleDelete($pdo, $input);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Méthode non autorisée']);
        break;
}

// Fonction pour récupérer un utilisateur spécifique
function handleGet($pdo) {
    $userId = $_GET['id'] ?? null;

    if ($userId) {
        // Récupérer un utilisateur spécifique avec toutes ses informations
        try {
            // Vérifier quelles tables et colonnes existent
            $table_structures = getTableStructures($pdo);

            // Construire la requête selon les tables et colonnes disponibles
            $select_fields = ['u.id', 'u.nom', 'u.email', 'u.role_id', 'r.nom as role_nom'];
            $joins = ['LEFT JOIN roles r ON u.role_id = r.id'];

            // Ajouter prenom seulement si la colonne existe
            if (isset($table_structures['utilisateurs']) && in_array('prenom', $table_structures['utilisateurs'])) {
                $select_fields[] = 'u.prenom';
            }

            // Ajouter les jointures selon les tables disponibles
            if (isset($table_structures['enseignants'])) {
                $joins[] = 'LEFT JOIN enseignants e ON u.id = e.utilisateur_id';
                if (in_array('nom_prenom', $table_structures['enseignants'])) {
                    $select_fields[] = 'e.nom_prenom as enseignant_nom_prenom';
                }
                $select_fields[] = 'e.id as enseignant_profile_id';
            }

            if (isset($table_structures['etudiants'])) {
                $joins[] = 'LEFT JOIN etudiants et ON u.id = et.utilisateur_id';
                $select_fields[] = 'et.id as etudiant_profile_id';

                // Ajouter classe_id seulement si la colonne existe
                if (in_array('classe_id', $table_structures['etudiants']) && isset($table_structures['classes'])) {
                    $joins[] = 'LEFT JOIN classes c ON et.classe_id = c.id';
                    $select_fields[] = 'c.nom as classe_nom';

                    // Ajouter filière et niveau si disponibles
                    if (isset($table_structures['filieres'])) {
                        $joins[] = 'LEFT JOIN filieres f ON c.filiere_id = f.id';
                        $select_fields[] = 'f.nom as filiere_nom';
                    }

                    if (isset($table_structures['niveaux'])) {
                        $joins[] = 'LEFT JOIN niveaux n ON c.niveau_id = n.id';
                        $select_fields[] = 'n.nom as niveau_nom';
                    }
                }
            }

            if (isset($table_structures['parents'])) {
                $joins[] = 'LEFT JOIN parents p ON u.id = p.utilisateur_id';
                $select_fields[] = 'p.id as parent_profile_id';
                if (in_array('telephone', $table_structures['parents'])) {
                    $select_fields[] = 'p.telephone as parent_telephone';
                }
                if (in_array('adresse', $table_structures['parents'])) {
                    $select_fields[] = 'p.adresse as parent_adresse';
                }
            }

            $sql = "SELECT " . implode(', ', $select_fields) . "\n";
            $sql .= "FROM utilisateurs u\n";
            $sql .= implode("\n", $joins) . "\n";
            $sql .= "WHERE u.id = ?";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                // Ajouter un champ prenom vide si la colonne n'existe pas
                if (!isset($table_structures['utilisateurs']) || !in_array('prenom', $table_structures['utilisateurs'])) {
                    $user['prenom'] = '';
                }

                echo json_encode(['success' => true, 'user' => $user]);
            } else {
                echo json_encode(['error' => 'Utilisateur non trouvé']);
            }
        } catch (PDOException $e) {
            echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['error' => 'ID utilisateur requis']);
    }
}

// Fonction utilitaire pour récupérer la structure des tables
function getTableStructures($pdo) {
    $structures = [];
    $tables = ['utilisateurs', 'roles', 'enseignants', 'etudiants', 'parents', 'classes', 'filieres', 'niveaux'];

    foreach ($tables as $table) {
        try {
            // Vérifier si la table existe
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // Récupérer les colonnes de la table
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $structures[$table] = $columns;
            }
        } catch (Exception $e) {
            // Table n'existe pas ou erreur, on l'ignore
            continue;
        }
    }

    return $structures;
}

// Fonction pour créer un nouvel utilisateur
function handlePost($pdo, $input) {
    if (!isset($input['nom'], $input['email'], $input['role_id'])) {
        echo json_encode(['error' => 'Données manquantes (nom, email, role_id requis)']);
        return;
    }

    try {
        // Vérifier quelles colonnes existent
        $stmt = $pdo->query("DESCRIBE utilisateurs");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Vérifier si l'email existe déjà
        $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
        $stmt->execute([$input['email']]);
        if ($stmt->fetch()) {
            echo json_encode(['error' => 'Cet email est déjà utilisé']);
            return;
        }

        // Construire la requête d'insertion selon les colonnes disponibles
        $insert_fields = ['nom', 'email', 'mot_de_passe', 'role_id'];
        $insert_values = [
            $input['nom'],
            $input['email'],
            password_hash($input['mot_de_passe'] ?? 'password123', PASSWORD_DEFAULT),
            $input['role_id']
        ];

        // Ajouter prenom seulement si la colonne existe
        if (in_array('prenom', $columns) && isset($input['prenom'])) {
            $insert_fields[] = 'prenom';
            $insert_values[] = $input['prenom'];
        }

        $placeholders = str_repeat('?,', count($insert_fields) - 1) . '?';
        $sql = "INSERT INTO utilisateurs (" . implode(', ', $insert_fields) . ") VALUES ($placeholders)";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($insert_values);

        $userId = $pdo->lastInsertId();

        echo json_encode(['success' => true, 'id' => $userId, 'message' => 'Utilisateur créé avec succès']);

    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la création : ' . $e->getMessage()]);
    }
}

// Fonction pour modifier un utilisateur
function handlePut($pdo, $input) {
    if (!isset($input['id'])) {
        echo json_encode(['error' => 'ID utilisateur requis']);
        return;
    }

    try {
        // Vérifier quelles colonnes existent
        $stmt = $pdo->query("DESCRIBE utilisateurs");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Vérifier si l'utilisateur existe
        $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE id = ?");
        $stmt->execute([$input['id']]);
        if (!$stmt->fetch()) {
            echo json_encode(['error' => 'Utilisateur non trouvé']);
            return;
        }

        // Construire la requête de mise à jour dynamiquement
        $fields = [];
        $values = [];

        if (isset($input['nom'])) {
            $fields[] = "nom = ?";
            $values[] = $input['nom'];
        }

        // Ajouter prenom seulement si la colonne existe
        if (in_array('prenom', $columns) && isset($input['prenom'])) {
            $fields[] = "prenom = ?";
            $values[] = $input['prenom'];
        }

        if (isset($input['email'])) {
            // Vérifier si l'email n'est pas déjà utilisé par un autre utilisateur
            $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ? AND id != ?");
            $stmt->execute([$input['email'], $input['id']]);
            if ($stmt->fetch()) {
                echo json_encode(['error' => 'Cet email est déjà utilisé par un autre utilisateur']);
                return;
            }

            $fields[] = "email = ?";
            $values[] = $input['email'];
        }

        if (isset($input['role_id'])) {
            $fields[] = "role_id = ?";
            $values[] = $input['role_id'];
        }

        if (isset($input['mot_de_passe']) && !empty($input['mot_de_passe'])) {
            $fields[] = "mot_de_passe = ?";
            $values[] = password_hash($input['mot_de_passe'], PASSWORD_DEFAULT);
        }

        if (empty($fields)) {
            echo json_encode(['error' => 'Aucune donnée à mettre à jour']);
            return;
        }

        $values[] = $input['id']; // Pour la clause WHERE

        $sql = "UPDATE utilisateurs SET " . implode(", ", $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        echo json_encode(['success' => true, 'message' => 'Utilisateur modifié avec succès']);

    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la modification : ' . $e->getMessage()]);
    }
}

// Fonction pour supprimer un utilisateur
function handleDelete($pdo, $input) {
    if (!isset($input['id'])) {
        echo json_encode(['error' => 'ID utilisateur requis']);
        return;
    }
    
    try {
        // Commencer une transaction
        $pdo->beginTransaction();
        
        // Vérifier si l'utilisateur existe
        $stmt = $pdo->prepare("SELECT id, role_id FROM utilisateurs WHERE id = ?");
        $stmt->execute([$input['id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            $pdo->rollback();
            echo json_encode(['error' => 'Utilisateur non trouvé']);
            return;
        }
        
        // Supprimer les enregistrements liés dans les tables spécialisées
        $stmt = $pdo->prepare("DELETE FROM parents WHERE utilisateur_id = ?");
        $stmt->execute([$input['id']]);
        
        $stmt = $pdo->prepare("DELETE FROM etudiants WHERE utilisateur_id = ?");
        $stmt->execute([$input['id']]);
        
        $stmt = $pdo->prepare("DELETE FROM enseignants WHERE utilisateur_id = ?");
        $stmt->execute([$input['id']]);
        
        // Supprimer l'utilisateur principal
        $stmt = $pdo->prepare("DELETE FROM utilisateurs WHERE id = ?");
        $stmt->execute([$input['id']]);
        
        // Valider la transaction
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => 'Utilisateur supprimé avec succès']);
        
    } catch (PDOException $e) {
        $pdo->rollback();
        echo json_encode(['error' => 'Erreur lors de la suppression : ' . $e->getMessage()]);
    }
}
