<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST APPLICATION LOGIQUE ABSENCES → RETARDS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; color: white; text-decoration: none; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
        .comparison .absences { border-left: 4px solid #28a745; }
        .comparison .retards { border-left: 4px solid #fd7e14; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🔄 Application de la Logique des Absences aux Retards</h2>";
    echo "<p>Vérification que la même logique qui fonctionne pour les absences est appliquée aux retards</p>";
    echo "</div>";
    
    // 1. Test connexion base de données
    echo "<div class='step'>";
    echo "<h3>🗄️ 1. Test Connexion Base de Données</h3>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
        
        // Vérifier les données
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Absences");
        $absences_count = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Retards");
        $retards_count = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Etudiants");
        $etudiants_count = $stmt->fetch()['count'];
        
        echo "<p class='info'>📋 $absences_count absence(s) en base</p>";
        echo "<p class='info'>⏰ $retards_count retard(s) en base</p>";
        echo "<p class='info'>👨‍🎓 $etudiants_count étudiant(s) en base</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    echo "</div>";
    
    // 2. Comparaison des APIs
    echo "<div class='step'>";
    echo "<h3>🔄 2. Comparaison APIs Absences vs Retards</h3>";
    
    echo "<div class='comparison'>";
    
    // Test API Absences
    echo "<div class='absences'>";
    echo "<h4>📋 API Absences (Référence qui fonctionne)</h4>";
    
    $absences_url = "http://localhost/Project_PFE/Backend/pages/absences/index_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $absences_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$absences_url' target='_blank'>index_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Absences accessible</p>";
            
            $data = json_decode($response, true);
            if (is_array($data)) {
                echo "<p class='success'>✅ Format JSON valide (tableau)</p>";
                echo "<p class='info'>📊 " . count($data) . " absence(s) retournée(s)</p>";
                
                if (!empty($data)) {
                    echo "<h5>📋 Structure première absence :</h5>";
                    $first = $data[0];
                    $fields = ['id', 'etudiant_id', 'etudiant_nom', 'etudiant_email', 'date_absence', 'justification'];
                    echo "<ul>";
                    foreach ($fields as $field) {
                        $value = isset($first[$field]) ? $first[$field] : 'NON DÉFINI';
                        echo "<li><strong>$field :</strong> $value</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test API Absences : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Test API Retards
    echo "<div class='retards'>";
    echo "<h4>⏰ API Retards (Applique la même logique)</h4>";
    
    $retards_url = "http://localhost/Project_PFE/Backend/pages/retards/index_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $retards_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$retards_url' target='_blank'>index_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Retards accessible</p>";
            
            $data = json_decode($response, true);
            if (is_array($data)) {
                echo "<p class='success'>✅ Format JSON valide (tableau) - MÊME LOGIQUE</p>";
                echo "<p class='info'>📊 " . count($data) . " retard(s) retourné(s)</p>";
                
                if (!empty($data)) {
                    echo "<h5>⏰ Structure premier retard :</h5>";
                    $first = $data[0];
                    $fields = ['id', 'etudiant_id', 'etudiant_nom', 'etudiant_email', 'date_retard', 'duree_retard', 'justification'];
                    echo "<ul>";
                    foreach ($fields as $field) {
                        $value = isset($first[$field]) ? $first[$field] : 'NON DÉFINI';
                        echo "<li><strong>$field :</strong> $value</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test API Retards : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "</div>"; // fin comparison
    echo "</div>";
    
    // 3. Test API Étudiants (commune aux deux)
    echo "<div class='step'>";
    echo "<h3>👨‍🎓 3. Test API Étudiants (Commune aux Deux)</h3>";
    
    $etudiants_url = "http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $etudiants_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<h4>🔗 URL : <a href='$etudiants_url' target='_blank'>$etudiants_url</a></h4>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Étudiants accessible</p>";
            
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                echo "<p class='success'>✅ Format JSON valide (success: true)</p>";
                echo "<p class='info'>👨‍🎓 " . count($data['etudiants']) . " étudiant(s) retourné(s)</p>";
                
                if (!empty($data['etudiants'])) {
                    echo "<h5>👨‍🎓 Structure premier étudiant :</h5>";
                    $first = $data['etudiants'][0];
                    $fields = ['id', 'nom', 'prenom', 'email', 'classe_nom', 'numero_etudiant'];
                    echo "<ul>";
                    foreach ($fields as $field) {
                        $value = isset($first[$field]) ? $first[$field] : 'NON DÉFINI';
                        echo "<li><strong>$field :</strong> $value</li>";
                    }
                    echo "</ul>";
                    
                    echo "<h5>🎯 Format pour React (Absences & Retards) :</h5>";
                    echo "<p><strong>Clé :</strong> etudiant.etudiant_id || etudiant.id</p>";
                    echo "<p><strong>Valeur :</strong> etudiant.etudiant_id || etudiant.id</p>";
                    echo "<p><strong>Affichage :</strong> {etudiant.nom} {etudiant.prenom} - {etudiant.classe_nom || 'Sans classe'}</p>";
                }
            } else {
                echo "<p class='error'>❌ Format JSON invalide ou success: false</p>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test API Étudiants : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 4. Vérification de la logique appliquée
    echo "<div class='step'>";
    echo "<h3>✅ 4. Vérification de la Logique Appliquée</h3>";
    
    echo "<h4>🔄 Modifications Appliquées aux Retards :</h4>";
    echo "<ol>";
    echo "<li><strong>fetchEtudiants() :</strong> Utilise la même API que les absences (getEtudiants_no_auth.php)</li>";
    echo "<li><strong>fetchRetards() :</strong> Utilise la même structure de vérification (Array.isArray)</li>";
    echo "<li><strong>handleSubmit() :</strong> Utilise la même API sans auth (index_no_auth.php)</li>";
    echo "<li><strong>handleDelete() :</strong> Utilise la même API sans auth</li>";
    echo "<li><strong>Select étudiants :</strong> Utilise la même logique (etudiant.etudiant_id || etudiant.id)</li>";
    echo "<li><strong>Affichage :</strong> Utilise le même format ({nom} {prenom} - {classe_nom || 'Sans classe'})</li>";
    echo "</ol>";
    
    echo "<h4>🎯 Résultat Attendu :</h4>";
    echo "<p>Les retards devraient maintenant fonctionner <strong>exactement comme les absences</strong> :</p>";
    echo "<ul>";
    echo "<li>✅ Chargement automatique des étudiants</li>";
    echo "<li>✅ Affichage correct dans le select</li>";
    echo "<li>✅ Création de retards fonctionnelle</li>";
    echo "<li>✅ Modification et suppression fonctionnelles</li>";
    echo "<li>✅ Même gestion d'erreurs</li>";
    echo "<li>✅ Même structure de données</li>";
    echo "</ul>";
    echo "</div>";
    
    // 5. Instructions de test
    echo "<div class='step'>";
    echo "<h3>🧪 5. Instructions de Test</h3>";
    
    echo "<h4>🎯 Test Interface React :</h4>";
    echo "<ol>";
    echo "<li><strong>Ouvrez :</strong> <a href='http://localhost:3000/retards' target='_blank'>http://localhost:3000/retards</a></li>";
    echo "<li><strong>Console :</strong> F12 → Console pour voir les logs</li>";
    echo "<li><strong>Vérifiez :</strong> Messages identiques à ceux des absences</li>";
    echo "<li><strong>Testez :</strong> Ajout d'un nouveau retard</li>";
    echo "<li><strong>Vérifiez :</strong> Liste déroulante des étudiants avec format 'Nom Prénom - Classe'</li>";
    echo "</ol>";
    
    echo "<h4>📋 Comparaison avec Absences :</h4>";
    echo "<ol>";
    echo "<li><strong>Ouvrez aussi :</strong> <a href='http://localhost:3000/absences' target='_blank'>http://localhost:3000/absences</a></li>";
    echo "<li><strong>Comparez :</strong> Le comportement doit être identique</li>";
    echo "<li><strong>Logs :</strong> Même structure de messages dans la console</li>";
    echo "<li><strong>Interface :</strong> Même format d'affichage des étudiants</li>";
    echo "</ol>";
    echo "</div>";
    
    // 6. Liens de test
    echo "<div class='step'>";
    echo "<h3>🔗 6. Liens de Test</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$retards_url' target='_blank' class='btn'>⏰ API Retards</a>";
    echo "<a href='$absences_url' target='_blank' class='btn'>📋 API Absences</a>";
    echo "<a href='$etudiants_url' target='_blank' class='btn'>👨‍🎓 API Étudiants</a>";
    echo "<a href='http://localhost:3000/retards' target='_blank' class='btn btn-success'>⚛️ Interface Retards</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='btn btn-warning'>⚛️ Interface Absences</a>";
    echo "</div>";
    
    echo "<p class='success'>🎉 <strong>La logique des absences a été appliquée aux retards !</strong></p>";
    echo "<p class='info'>🔄 <strong>Les retards devraient maintenant fonctionner exactement comme les absences.</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
