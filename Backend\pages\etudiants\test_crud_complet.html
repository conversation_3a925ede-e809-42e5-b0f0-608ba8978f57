<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test CRUD Complet Étudiants - Interface Factures</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .log-entry { margin: 5px 0; padding: 5px; background: white; border-radius: 2px; }
        .log-success { border-left: 4px solid #28a745; }
        .log-error { border-left: 4px solid #dc3545; }
        .log-info { border-left: 4px solid #007bff; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test CRUD Complet Étudiants - Interface Factures</h1>
        
        <div class="section success">
            <h2>🎯 Objectifs du Test</h2>
            <ul>
                <li>✅ Interface similaire aux factures (design et organisation)</li>
                <li>✅ CRUD complet : Créer, Lire, Modifier, Supprimer</li>
                <li>✅ Filtrage utilisateurs : seuls les rôles "etudiant"</li>
                <li>✅ Restrictions d'accès : seuls les admins peuvent modifier</li>
                <li>✅ Connexion dynamique Frontend ↔ Backend via API</li>
            </ul>
        </div>

        <!-- Schéma de base de données -->
        <div class="section info">
            <h2>🗄️ Schéma de Base de Données</h2>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
CREATE TABLE `etudiants` (
    `id` INT(10) NOT NULL AUTO_INCREMENT,
    `utilisateur_id` INT(10) NULL DEFAULT NULL,
    `groupe_id` INT(10) NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `utilisateur_id` (`utilisateur_id`),
    INDEX `groupe_id` (`groupe_id`),
    CONSTRAINT `etudiants_ibfk_1` FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs` (`id`),
    CONSTRAINT `etudiants_ibfk_2` FOREIGN KEY (`groupe_id`) REFERENCES `groupes` (`id`)
);
            </pre>
        </div>

        <!-- Test des utilisateurs étudiants -->
        <div class="section">
            <h2>👥 Test Filtrage Utilisateurs Étudiants</h2>
            <button class="btn btn-primary" onclick="testFiltragUtilisateurs()">🔍 Tester Filtrage</button>
            <div id="filtrageResult"></div>
        </div>

        <!-- Test CRUD complet -->
        <div class="section">
            <h2>🔧 Test CRUD Complet</h2>
            <div class="form-group">
                <label for="test_utilisateur_id">Utilisateur Étudiant:</label>
                <select id="test_utilisateur_id">
                    <option value="">Chargement...</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="test_groupe_id">Groupe (optionnel):</label>
                <select id="test_groupe_id">
                    <option value="">Aucun groupe</option>
                </select>
            </div>
            
            <div style="margin: 20px 0;">
                <button class="btn btn-success" onclick="testCreate()">➕ CREATE</button>
                <button class="btn btn-primary" onclick="testRead()">📋 READ</button>
                <button class="btn btn-warning" onclick="testUpdate()">✏️ UPDATE</button>
                <button class="btn btn-danger" onclick="testDelete()">🗑️ DELETE</button>
            </div>
            
            <button class="btn btn-info" onclick="testWorkflowComplet()">🚀 Test Workflow Complet</button>
        </div>

        <!-- Liste des étudiants -->
        <div class="section">
            <h2>📋 Liste des Étudiants (Interface Factures)</h2>
            <button class="btn btn-primary" onclick="loadEtudiants()">🔄 Recharger</button>
            <div id="etudiantsList"></div>
        </div>

        <!-- Comparaison avec Factures -->
        <div class="section">
            <h2>📊 Comparaison Interface Factures vs Étudiants</h2>
            <div class="comparison">
                <div>
                    <h4>💰 Interface Factures (Modèle)</h4>
                    <ul>
                        <li>Tableau : ID, Étudiant, Mois, Montant, Statut, Date, Actions</li>
                        <li>Modal avec formulaire structuré</li>
                        <li>Restrictions admin pour CRUD</li>
                        <li>Messages d'information pour non-admins</li>
                        <li>Boutons d'action avec icônes</li>
                    </ul>
                </div>
                <div>
                    <h4>🎓 Interface Étudiants (Nouvelle)</h4>
                    <ul>
                        <li>Tableau : ID, Nom, Email, Groupe, Statut, Actions</li>
                        <li>Modal avec formulaire similaire</li>
                        <li>Restrictions admin identiques</li>
                        <li>Messages d'information similaires</li>
                        <li>Boutons d'action avec icônes</li>
                        <li>+ Filtrage automatique utilisateurs</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Log détaillé -->
        <div class="section">
            <h2>🔍 Log Détaillé</h2>
            <button class="btn btn-danger" onclick="clearLog()">🧹 Effacer Log</button>
            <div id="logContainer" class="log">
                <div class="log-entry log-info">
                    <strong>[Prêt]</strong> Interface de test chargée.
                </div>
            </div>
        </div>

        <!-- Instructions React -->
        <div class="section warning">
            <h2>📋 Instructions pour Tester dans React</h2>
            <ol>
                <li><strong>Accédez à la page Étudiants</strong> dans votre interface React</li>
                <li><strong>Vérifiez l'interface</strong> : doit ressembler aux factures</li>
                <li><strong>Testez les restrictions</strong> : seuls les admins voient les boutons CRUD</li>
                <li><strong>Testez le filtrage</strong> : seuls les utilisateurs "etudiant" dans la liste</li>
                <li><strong>Testez le CRUD complet</strong> : Créer, Modifier, Supprimer</li>
                <li><strong>Vérifiez l'exclusion automatique</strong> après ajout</li>
            </ol>
        </div>
    </div>

    <script>
        let testEtudiantId = null;

        function addLog(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            
            let html = `<strong>[${timestamp}]</strong> ${message}`;
            if (data) {
                html += `<br><pre style="margin: 5px 0; font-size: 11px;">${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            entry.innerHTML = html;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info"><strong>[Effacé]</strong> Log effacé.</div>';
        }

        async function testFiltragUtilisateurs() {
            try {
                addLog('🔍 Test filtrage utilisateurs étudiants', 'info');
                
                // Récupérer tous les utilisateurs
                const responseUtilisateurs = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php');
                const utilisateurs = await responseUtilisateurs.json();
                
                // Filtrer les étudiants
                const utilisateursEtudiants = utilisateurs.filter(user => {
                    const roleNom = (user.role_nom || user.role || '').toLowerCase();
                    return roleNom === 'etudiant' || roleNom === 'étudiant';
                });
                
                // Récupérer les étudiants existants
                const responseEtudiants = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');
                const etudiants = await responseEtudiants.json();
                
                const etudiantsExistants = etudiants.map(e => e.utilisateur_id).filter(id => id !== null);
                const utilisateursDisponibles = utilisateursEtudiants.filter(user => 
                    !etudiantsExistants.includes(user.id)
                );
                
                addLog('✅ Résultats filtrage', 'success', {
                    total_utilisateurs: utilisateurs.length,
                    utilisateurs_etudiants: utilisateursEtudiants.length,
                    etudiants_existants: etudiantsExistants.length,
                    utilisateurs_disponibles: utilisateursDisponibles.length
                });
                
                // Remplir les selects
                const selectUtilisateur = document.getElementById('test_utilisateur_id');
                selectUtilisateur.innerHTML = '<option value="">Sélectionner un utilisateur étudiant...</option>';
                
                utilisateursDisponibles.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.nom} - ${user.email} (ID: ${user.id})`;
                    selectUtilisateur.appendChild(option);
                });
                
                // Afficher les résultats
                document.getElementById('filtrageResult').innerHTML = `
                    <div style="margin-top: 15px;">
                        <h4>📊 Résultats du Filtrage :</h4>
                        <table>
                            <tr><th>Catégorie</th><th>Nombre</th><th>Statut</th></tr>
                            <tr><td>Total utilisateurs</td><td>${utilisateurs.length}</td><td>ℹ️</td></tr>
                            <tr><td>Utilisateurs étudiants</td><td>${utilisateursEtudiants.length}</td><td>${utilisateursEtudiants.length > 0 ? '✅' : '❌'}</td></tr>
                            <tr><td>Étudiants existants</td><td>${etudiantsExistants.length}</td><td>ℹ️</td></tr>
                            <tr><td>Utilisateurs disponibles</td><td>${utilisateursDisponibles.length}</td><td>${utilisateursDisponibles.length > 0 ? '✅' : '⚠️'}</td></tr>
                        </table>
                        <p><strong>Conclusion :</strong> ${utilisateursDisponibles.length > 0 ? 
                            '✅ Le filtrage fonctionne parfaitement' : 
                            '⚠️ Aucun utilisateur étudiant disponible'}</p>
                    </div>
                `;
                
            } catch (error) {
                addLog('❌ Erreur test filtrage', 'error', { error: error.message });
            }
        }

        async function loadGroupes() {
            try {
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/groupes/groupe.php');
                const groupes = await response.json();
                
                const selectGroupe = document.getElementById('test_groupe_id');
                selectGroupe.innerHTML = '<option value="">Aucun groupe</option>';
                
                if (Array.isArray(groupes)) {
                    groupes.forEach(groupe => {
                        const option = document.createElement('option');
                        option.value = groupe.id;
                        option.textContent = groupe.nom;
                        selectGroupe.appendChild(option);
                    });
                }
                
                addLog('✅ Groupes chargés', 'success', { count: groupes.length });
                
            } catch (error) {
                addLog('❌ Erreur chargement groupes', 'error', { error: error.message });
            }
        }

        async function testCreate() {
            try {
                const utilisateur_id = document.getElementById('test_utilisateur_id').value;
                const groupe_id = document.getElementById('test_groupe_id').value;
                
                if (!utilisateur_id) {
                    addLog('❌ Veuillez sélectionner un utilisateur', 'error');
                    return;
                }
                
                const data = {
                    utilisateur_id: parseInt(utilisateur_id),
                    groupe_id: groupe_id ? parseInt(groupe_id) : null
                };
                
                addLog('➕ Test CREATE', 'info', data);
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testEtudiantId = result.id;
                    addLog('✅ CREATE réussi', 'success', result);
                    await loadEtudiants();
                    await testFiltragUtilisateurs();
                } else {
                    addLog('❌ CREATE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur CREATE', 'error', { error: error.message });
            }
        }

        async function testRead() {
            try {
                addLog('📋 Test READ', 'info');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');
                const etudiants = await response.json();
                
                addLog('✅ READ réussi', 'success', { count: etudiants.length, data: etudiants });
                
            } catch (error) {
                addLog('❌ Erreur READ', 'error', { error: error.message });
            }
        }

        async function testUpdate() {
            if (!testEtudiantId) {
                addLog('❌ Aucun étudiant de test. Créez d\'abord un étudiant.', 'error');
                return;
            }
            
            try {
                const data = {
                    id: testEtudiantId,
                    utilisateur_id: document.getElementById('test_utilisateur_id').value,
                    groupe_id: document.getElementById('test_groupe_id').value || null
                };
                
                addLog('✏️ Test UPDATE', 'info', data);
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('✅ UPDATE réussi', 'success', result);
                    await loadEtudiants();
                } else {
                    addLog('❌ UPDATE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur UPDATE', 'error', { error: error.message });
            }
        }

        async function testDelete() {
            if (!testEtudiantId) {
                addLog('❌ Aucun étudiant de test. Créez d\'abord un étudiant.', 'error');
                return;
            }
            
            try {
                addLog('🗑️ Test DELETE', 'info', { id: testEtudiantId });
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: testEtudiantId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('✅ DELETE réussi', 'success', result);
                    testEtudiantId = null;
                    await loadEtudiants();
                    await testFiltragUtilisateurs();
                } else {
                    addLog('❌ DELETE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur DELETE', 'error', { error: error.message });
            }
        }

        async function testWorkflowComplet() {
            addLog('🚀 Début test workflow complet', 'info');
            
            await testFiltragUtilisateurs();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCreate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testRead();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testUpdate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDelete();
            
            addLog('🎉 Test workflow complet terminé', 'success');
        }

        async function loadEtudiants() {
            try {
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');
                const etudiants = await response.json();
                
                const etudiantsList = document.getElementById('etudiantsList');
                
                if (!Array.isArray(etudiants) || etudiants.length === 0) {
                    etudiantsList.innerHTML = '<p>Aucun étudiant trouvé.</p>';
                    return;
                }
                
                let html = `<h4>Total: ${etudiants.length} étudiant(s)</h4>`;
                html += '<table><tr><th>ID</th><th>Nom</th><th>Email</th><th>Groupe</th><th>Actions</th></tr>';
                
                etudiants.forEach(etudiant => {
                    html += `<tr>
                        <td>#${etudiant.id}</td>
                        <td>${etudiant.nom || 'N/A'}</td>
                        <td>${etudiant.email || 'N/A'}</td>
                        <td>${etudiant.groupe_nom || 'Aucun groupe'}</td>
                        <td>
                            <button class="btn btn-danger" onclick="deleteEtudiant(${etudiant.id})" style="font-size: 12px; padding: 5px 10px;">
                                🗑️ Supprimer
                            </button>
                        </td>
                    </tr>`;
                });
                
                html += '</table>';
                etudiantsList.innerHTML = html;
                
            } catch (error) {
                addLog('❌ Erreur chargement étudiants', 'error', { error: error.message });
            }
        }

        async function deleteEtudiant(id) {
            try {
                if (!confirm(`Supprimer l'étudiant ID ${id} ?`)) return;
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: id })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ Étudiant ${id} supprimé`, 'success');
                    await loadEtudiants();
                    await testFiltragUtilisateurs();
                } else {
                    addLog(`❌ Échec suppression ${id}`, 'error', result);
                }
                
            } catch (error) {
                addLog(`❌ Erreur suppression ${id}`, 'error', { error: error.message });
            }
        }

        // Initialisation
        window.addEventListener('load', () => {
            addLog('🌐 Page chargée', 'info');
            testFiltragUtilisateurs();
            loadGroupes();
            loadEtudiants();
        });
    </script>
</body>
</html>
