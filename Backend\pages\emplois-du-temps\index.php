<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');
require_once('../../config/auth.php');

// Vérification de l'authentification
$headers = getallheaders();
$token = null;

if (isset($headers['Authorization'])) {
    $token = str_replace('Bearer ', '', $headers['Authorization']);
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['error' => 'Token manquant']);
    exit();
}

$user_info = verifyToken($token);
if (!$user_info) {
    http_response_code(401);
    echo json_encode(['error' => 'Token invalide']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($pdo, $user_info) {
    $role = $user_info['role'];
    $user_id = $user_info['id'];
    
    $query = "
        SELECT edt.*, 
               c.nom as classe_nom, c.niveau,
               m.nom as matiere_nom, m.code as matiere_code,
               u.nom as enseignant_nom, u.prenom as enseignant_prenom,
               u.email as enseignant_email
        FROM EmploisDuTemps edt
        JOIN Classes c ON edt.classe_id = c.id
        JOIN Matieres m ON edt.matiere_id = m.id
        JOIN Enseignants ens ON edt.enseignant_id = ens.id
        JOIN Utilisateurs u ON ens.utilisateur_id = u.id
    ";
    
    // Filtrage selon le rôle
    if ($role === 'Admin') {
        // Admin voit tous les emplois du temps
        $stmt = $pdo->prepare($query . " ORDER BY c.nom, edt.jour, edt.heure_debut");
        $stmt->execute();
    } elseif ($role === 'Enseignant') {
        // Enseignant voit ses propres cours
        $query .= " WHERE ens.utilisateur_id = ? ORDER BY c.nom, edt.jour, edt.heure_debut";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$user_id]);
    } elseif ($role === 'Etudiant') {
        // Étudiant voit l'emploi du temps de sa classe
        $query .= " 
            WHERE c.id = (
                SELECT g.classe_id FROM Etudiants e 
                JOIN Groupes g ON e.groupe_id = g.id 
                WHERE e.utilisateur_id = ?
            )
            ORDER BY edt.jour, edt.heure_debut
        ";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$user_id]);
    } elseif ($role === 'Parent') {
        // Parent voit les emplois du temps des classes de ses enfants
        $query .= " 
            WHERE c.id IN (
                SELECT g.classe_id FROM Parent_Etudiant pe 
                JOIN Etudiants e ON pe.etudiant_id = e.id 
                JOIN Groupes g ON e.groupe_id = g.id 
                WHERE pe.parent_id = (SELECT id FROM Parents WHERE utilisateur_id = ?)
            )
            ORDER BY c.nom, edt.jour, edt.heure_debut
        ";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$user_id]);
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
        return;
    }
    
    $emplois = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($emplois);
}

function handlePost($pdo, $user_info, $input) {
    // Vérifier les permissions
    if (!in_array($user_info['role'], ['Admin'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut créer des emplois du temps']);
        return;
    }
    
    // Validation des données
    if (!isset($input['classe_id'], $input['jour'], $input['heure_debut'], $input['heure_fin'], $input['matiere_id'], $input['enseignant_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (classe_id, jour, heure_debut, heure_fin, matiere_id, enseignant_id requis)']);
        return;
    }
    
    // Vérifier que la classe existe
    $stmt = $pdo->prepare("SELECT id FROM Classes WHERE id = ?");
    $stmt->execute([$input['classe_id']]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Classe non trouvée']);
        return;
    }
    
    // Vérifier que la matière existe
    $stmt = $pdo->prepare("SELECT id FROM Matieres WHERE id = ?");
    $stmt->execute([$input['matiere_id']]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Matière non trouvée']);
        return;
    }
    
    // Vérifier que l'enseignant existe
    $stmt = $pdo->prepare("SELECT id FROM Enseignants WHERE id = ?");
    $stmt->execute([$input['enseignant_id']]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Enseignant non trouvé']);
        return;
    }
    
    // Vérifier les conflits d'horaires pour la classe
    $stmt = $pdo->prepare("
        SELECT id FROM EmploisDuTemps 
        WHERE classe_id = ? AND jour = ? 
        AND ((heure_debut <= ? AND heure_fin > ?) OR (heure_debut < ? AND heure_fin >= ?))
    ");
    $stmt->execute([
        $input['classe_id'], 
        $input['jour'], 
        $input['heure_debut'], $input['heure_debut'],
        $input['heure_fin'], $input['heure_fin']
    ]);
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['error' => 'Conflit d\'horaire pour cette classe']);
        return;
    }
    
    // Vérifier les conflits d'horaires pour l'enseignant
    $stmt = $pdo->prepare("
        SELECT id FROM EmploisDuTemps 
        WHERE enseignant_id = ? AND jour = ? 
        AND ((heure_debut <= ? AND heure_fin > ?) OR (heure_debut < ? AND heure_fin >= ?))
    ");
    $stmt->execute([
        $input['enseignant_id'], 
        $input['jour'], 
        $input['heure_debut'], $input['heure_debut'],
        $input['heure_fin'], $input['heure_fin']
    ]);
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode(['error' => 'Conflit d\'horaire pour cet enseignant']);
        return;
    }
    
    // Insérer l'emploi du temps
    $stmt = $pdo->prepare("
        INSERT INTO EmploisDuTemps (classe_id, jour, heure_debut, heure_fin, matiere_id, enseignant_id) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $input['classe_id'],
        $input['jour'],
        $input['heure_debut'],
        $input['heure_fin'],
        $input['matiere_id'],
        $input['enseignant_id']
    ]);
    
    echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
}

function handlePut($pdo, $user_info, $input) {
    // Vérifier les permissions
    if (!in_array($user_info['role'], ['Admin'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut modifier les emplois du temps']);
        return;
    }
    
    // Validation des données
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de l\'emploi du temps manquant']);
        return;
    }
    
    // Vérifier que l'emploi du temps existe
    $stmt = $pdo->prepare("SELECT * FROM EmploisDuTemps WHERE id = ?");
    $stmt->execute([$input['id']]);
    $emploi = $stmt->fetch();
    
    if (!$emploi) {
        http_response_code(404);
        echo json_encode(['error' => 'Emploi du temps non trouvé']);
        return;
    }
    
    // Préparer les champs à mettre à jour
    $updates = [];
    $params = [];
    
    if (isset($input['classe_id'])) {
        $stmt = $pdo->prepare("SELECT id FROM Classes WHERE id = ?");
        $stmt->execute([$input['classe_id']]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Classe non trouvée']);
            return;
        }
        $updates[] = "classe_id = ?";
        $params[] = $input['classe_id'];
    }
    
    if (isset($input['jour'])) {
        $updates[] = "jour = ?";
        $params[] = $input['jour'];
    }
    
    if (isset($input['heure_debut'])) {
        $updates[] = "heure_debut = ?";
        $params[] = $input['heure_debut'];
    }
    
    if (isset($input['heure_fin'])) {
        $updates[] = "heure_fin = ?";
        $params[] = $input['heure_fin'];
    }
    
    if (isset($input['matiere_id'])) {
        $stmt = $pdo->prepare("SELECT id FROM Matieres WHERE id = ?");
        $stmt->execute([$input['matiere_id']]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Matière non trouvée']);
            return;
        }
        $updates[] = "matiere_id = ?";
        $params[] = $input['matiere_id'];
    }
    
    if (isset($input['enseignant_id'])) {
        $stmt = $pdo->prepare("SELECT id FROM Enseignants WHERE id = ?");
        $stmt->execute([$input['enseignant_id']]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Enseignant non trouvé']);
            return;
        }
        $updates[] = "enseignant_id = ?";
        $params[] = $input['enseignant_id'];
    }
    
    if (empty($updates)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à mettre à jour']);
        return;
    }
    
    $params[] = $input['id'];
    
    $stmt = $pdo->prepare("UPDATE EmploisDuTemps SET " . implode(', ', $updates) . " WHERE id = ?");
    $stmt->execute($params);
    
    echo json_encode(['success' => true]);
}

function handleDelete($pdo, $user_info, $input) {
    // Vérifier les permissions
    if (!in_array($user_info['role'], ['Admin'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut supprimer les emplois du temps']);
        return;
    }
    
    // Validation des données
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de l\'emploi du temps manquant']);
        return;
    }
    
    // Vérifier que l'emploi du temps existe
    $stmt = $pdo->prepare("SELECT id FROM EmploisDuTemps WHERE id = ?");
    $stmt->execute([$input['id']]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Emploi du temps non trouvé']);
        return;
    }
    
    // Supprimer l'emploi du temps
    $stmt = $pdo->prepare("DELETE FROM EmploisDuTemps WHERE id = ?");
    $stmt->execute([$input['id']]);
    
    echo json_encode(['success' => true]);
}

// Fonctions utilitaires pour récupérer les données de référence
function getClassesList($pdo) {
    $stmt = $pdo->prepare("
        SELECT c.id, c.nom, c.niveau, f.nom as filiere_nom, n.nom as niveau_nom
        FROM Classes c
        LEFT JOIN Filieres f ON c.filiere_id = f.id
        LEFT JOIN Niveaux n ON c.niveau_id = n.id
        ORDER BY c.nom
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMatieresList($pdo) {
    $stmt = $pdo->prepare("
        SELECT m.id, m.nom, m.code, m.description
        FROM Matieres m
        ORDER BY m.nom
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getEnseignantsList($pdo) {
    $stmt = $pdo->prepare("
        SELECT e.id, u.nom, u.prenom, u.email
        FROM Enseignants e
        JOIN Utilisateurs u ON e.utilisateur_id = u.id
        ORDER BY u.nom, u.prenom
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Endpoint pour récupérer les données de référence
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'classes':
            echo json_encode(getClassesList($pdo));
            break;
        case 'matieres':
            echo json_encode(getMatieresList($pdo));
            break;
        case 'enseignants':
            echo json_encode(getEnseignantsList($pdo));
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Action non reconnue']);
    }
    exit();
}
?>
