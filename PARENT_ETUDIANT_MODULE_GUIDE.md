# 👨‍👩‍👧‍👦 Module Parent-Étudiant - Guide Complet

## 📋 Résumé du Module

Le module Parent-Étudiant permet de gérer les relations familiales entre parents et étudiants dans le système de gestion scolaire. Il suit exactement le modèle du module factures pour une cohérence parfaite.

## 🏗️ Architecture Implémentée

### Backend (API REST)
- **Fichier principal** : `Backend/pages/parent_etudiant/index.php`
- **Méthodes supportées** : GET, POST, PUT, DELETE
- **Contrôle d'accès** :
  - **Admin** : CRUD complet (Create, Read, Update, Delete)
  - **Enseignant** : Lecture seule (Read only)
  - **Étudiant/Parent** : Aucun accès

### Frontend (React)
- **Composant principal** : `Frantend/schoolproject/src/pages/ParentEtudiant.js`
- **Styles** : `Frantend/schoolproject/src/css/ParentEtudiant.css`
- **Route** : `/parent-etudiant`

### Base de Données
```sql
CREATE TABLE `parent_etudiant` (
    `id` INT(10) NOT NULL AUTO_INCREMENT,
    `parent_id` INT(10) NULL DEFAULT NULL,
    `etudiant_id` INT(10) NULL DEFAULT NULL,
    `lien_parente` ENUM('Père','Mère','Tuteur','Autre') NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`parent_id`) REFERENCES `parents` (`id`),
    FOREIGN KEY (`etudiant_id`) REFERENCES `etudiants` (`id`)
);
```

## 🔧 Fonctionnalités

### Interface Utilisateur
- **Design cohérent** avec le module factures
- **Table responsive** avec pagination
- **Modal de création/modification**
- **Boutons d'action** (Modifier/Supprimer) pour les admins
- **Dropdowns intelligents** pour sélectionner parents et étudiants
- **Badges colorés** pour les types de liens de parenté

### Validation et Sécurité
- **Validation côté serveur** des données
- **Prévention des doublons** (même parent-étudiant)
- **Contrôle d'accès strict** par rôle
- **Validation des liens de parenté** (Père, Mère, Tuteur, Autre)

## 🧪 Tests et Validation

### 1. Test de l'API Backend
```
http://localhost/Project_PFE/Backend/pages/parent_etudiant/test_api.html
```
Interface de test complète pour vérifier toutes les opérations CRUD.

### 2. Configuration et Données de Test
```
http://localhost/Project_PFE/Backend/pages/parent_etudiant/setup_test_data.php
```
Script pour vérifier la structure de la base de données et créer des données de test.

### 3. Interface React
```
http://localhost:3000/parent-etudiant
```
Interface utilisateur complète (nécessite une connexion avec les bons droits).

## 🚀 Instructions de Test

### Étape 1 : Vérification Backend
1. Ouvrir `setup_test_data.php` pour vérifier la structure DB
2. Utiliser `test_api.html` pour tester les opérations CRUD
3. Vérifier que les données sont correctement créées/modifiées/supprimées

### Étape 2 : Test Frontend
1. Démarrer le serveur React : `npm start` (avec `NODE_OPTIONS=--openssl-legacy-provider` si nécessaire)
2. Se connecter avec un compte **Admin** pour accès complet
3. Se connecter avec un compte **Enseignant** pour accès lecture seule
4. Vérifier que les **Étudiants/Parents** n'ont aucun accès

### Étape 3 : Test des Fonctionnalités
- ✅ **Création** : Ajouter une nouvelle relation parent-étudiant
- ✅ **Lecture** : Affichage de toutes les relations avec détails
- ✅ **Modification** : Changer le lien de parenté
- ✅ **Suppression** : Supprimer une relation existante
- ✅ **Validation** : Tenter de créer des doublons (doit échouer)

## 🎨 Design et UX

### Cohérence Visuelle
- **Même palette de couleurs** que le module factures
- **Même structure de layout** (header, table, modal)
- **Même style de boutons** et interactions
- **Même responsive design** pour mobile/tablet

### Badges de Liens de Parenté
- **Père** : Bleu (#007bff)
- **Mère** : Rose (#e91e63)
- **Tuteur** : Orange (#ff9800)
- **Autre** : Gris (#6c757d)

## 📁 Fichiers Créés/Modifiés

### Nouveaux Fichiers
- `Backend/pages/parent_etudiant/index.php` - API principale
- `Backend/pages/parent_etudiant/test_api.html` - Interface de test
- `Backend/pages/parent_etudiant/setup_test_data.php` - Configuration DB
- `Frantend/schoolproject/src/pages/ParentEtudiant.js` - Composant React
- `Frantend/schoolproject/src/css/ParentEtudiant.css` - Styles CSS

### Fichiers Modifiés
- `Frantend/schoolproject/src/App.js` - Ajout de la route
- `Frantend/schoolproject/src/config/api.js` - Ajout de l'endpoint

## 🔐 Contrôle d'Accès

| Rôle | Permissions |
|------|-------------|
| **Admin** | ✅ Créer, ✅ Lire, ✅ Modifier, ✅ Supprimer |
| **Enseignant** | ❌ Créer, ✅ Lire, ❌ Modifier, ❌ Supprimer |
| **Étudiant** | ❌ Aucun accès |
| **Parent** | ❌ Aucun accès |

## 🐛 Résolution de Problèmes

### Erreur Node.js OpenSSL
Si vous rencontrez l'erreur `digital envelope routines::unsupported` :
```bash
$env:NODE_OPTIONS="--openssl-legacy-provider"
npm start
```

### Erreur de Base de Données
Si la table `parent_etudiant` n'existe pas, exécutez `setup_test_data.php` pour la créer automatiquement.

### Erreur d'Accès API
Vérifiez que :
- Le serveur Apache/Laragon est démarré
- L'URL de l'API est correcte dans `config/api.js`
- Les tables `parents` et `etudiants` contiennent des données

## ✅ Validation Complète

Le module Parent-Étudiant est maintenant **entièrement fonctionnel** et suit parfaitement les spécifications :

- ✅ **Architecture propre** suivant le modèle factures
- ✅ **Contrôle d'accès strict** par rôle
- ✅ **Interface cohérente** avec le design existant
- ✅ **API REST complète** avec validation
- ✅ **Tests intégrés** pour validation
- ✅ **Documentation complète**

Le module est prêt pour la production ! 🚀
