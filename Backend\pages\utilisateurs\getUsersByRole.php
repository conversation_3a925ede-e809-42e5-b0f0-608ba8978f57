<?php
// Autoriser les requêtes CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Récupérer le rôle demandé depuis les paramètres GET
    $role = isset($_GET['role']) ? trim($_GET['role']) : null;
    $exclude_assigned = isset($_GET['exclude_assigned']) ? filter_var($_GET['exclude_assigned'], FILTER_VALIDATE_BOOLEAN) : false;
    
    if (!$role) {
        echo json_encode(['error' => 'Paramètre "role" requis (ex: ?role=parent)']);
        exit();
    }

    try {
        // Construire la requête selon le rôle demandé
        $sql = "
            SELECT 
                u.id,
                u.nom,
                u.prenom,
                u.email,
                CONCAT(u.nom, ' ', u.prenom) as nom_complet,
                r.nom as role_nom,
                r.id as role_id
            FROM utilisateurs u
            INNER JOIN roles r ON u.role_id = r.id
            WHERE LOWER(r.nom) = LOWER(:role)
        ";
        
        // Ajouter une condition pour exclure les utilisateurs déjà assignés
        if ($exclude_assigned) {
            switch (strtolower($role)) {
                case 'parent':
                    $sql .= " AND u.id NOT IN (SELECT utilisateur_id FROM parents WHERE utilisateur_id IS NOT NULL)";
                    break;
                case 'etudiant':
                case 'élève':
                    $sql .= " AND u.id NOT IN (SELECT utilisateur_id FROM etudiants WHERE utilisateur_id IS NOT NULL)";
                    break;
                case 'enseignant':
                case 'professeur':
                    $sql .= " AND u.id NOT IN (SELECT utilisateur_id FROM enseignants WHERE utilisateur_id IS NOT NULL)";
                    break;
            }
        }
        
        $sql .= " ORDER BY u.nom, u.prenom";

        $stmt = $pdo->prepare($sql);
        $stmt->execute(['role' => $role]);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Ajouter des informations supplémentaires selon le rôle
        foreach ($users as &$user) {
            // Vérifier si l'utilisateur est déjà assigné dans une table spécialisée
            $user['is_assigned'] = false;
            $user['assignment_info'] = null;
            
            switch (strtolower($role)) {
                case 'parent':
                    $checkStmt = $pdo->prepare("SELECT id, telephone, adresse FROM parents WHERE utilisateur_id = ?");
                    $checkStmt->execute([$user['id']]);
                    $assignment = $checkStmt->fetch(PDO::FETCH_ASSOC);
                    if ($assignment) {
                        $user['is_assigned'] = true;
                        $user['assignment_info'] = $assignment;
                    }
                    break;
                    
                case 'etudiant':
                case 'élève':
                    $checkStmt = $pdo->prepare("
                        SELECT 
                            e.id,
                            c.nom as classe_nom,
                            f.nom as filiere_nom,
                            n.nom as niveau_nom
                        FROM etudiants e
                        LEFT JOIN classes c ON e.classe_id = c.id
                        LEFT JOIN filieres f ON c.filiere_id = f.id
                        LEFT JOIN niveaux n ON c.niveau_id = n.id
                        WHERE e.utilisateur_id = ?
                    ");
                    $checkStmt->execute([$user['id']]);
                    $assignment = $checkStmt->fetch(PDO::FETCH_ASSOC);
                    if ($assignment) {
                        $user['is_assigned'] = true;
                        $user['assignment_info'] = $assignment;
                    }
                    break;
                    
                case 'enseignant':
                case 'professeur':
                    $checkStmt = $pdo->prepare("
                        SELECT 
                            e.id,
                            e.nom_prenom,
                            COUNT(en.id) as nb_enseignements
                        FROM enseignants e
                        LEFT JOIN enseignements en ON e.id = en.enseignant_id
                        WHERE e.utilisateur_id = ?
                        GROUP BY e.id
                    ");
                    $checkStmt->execute([$user['id']]);
                    $assignment = $checkStmt->fetch(PDO::FETCH_ASSOC);
                    if ($assignment) {
                        $user['is_assigned'] = true;
                        $user['assignment_info'] = $assignment;
                    }
                    break;
            }
        }

        // Statistiques
        $total_users = count($users);
        $assigned_users = count(array_filter($users, function($user) { return $user['is_assigned']; }));
        $available_users = $total_users - $assigned_users;

        echo json_encode([
            'success' => true,
            'role' => $role,
            'exclude_assigned' => $exclude_assigned,
            'statistics' => [
                'total_users' => $total_users,
                'assigned_users' => $assigned_users,
                'available_users' => $available_users
            ],
            'users' => $users
        ]);

    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
