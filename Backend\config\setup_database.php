<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>⚙️ CONFIGURATION AUTOMATIQUE DATABASE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>⚙️ CONFIGURATION AUTOMATIQUE</h2>";
    echo "<p>Configurez automatiquement la connexion à votre base de données</p>";
    echo "</div>";
    
    // Traitement du formulaire
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $host = $_POST['host'] ?? 'localhost';
        $db_name = $_POST['db_name'] ?? 'school_management';
        $username = $_POST['username'] ?? 'root';
        $password = $_POST['password'] ?? '';
        $charset = $_POST['charset'] ?? 'utf8mb4';
        
        echo "<div class='info'>";
        echo "<h3>🔧 Génération du fichier database.php</h3>";
        echo "</div>";
        
        // Générer le contenu du fichier database.php
        $database_content = "<?php
/**
 * Configuration de la base de données - Générée automatiquement
 * Date de génération: " . date('Y-m-d H:i:s') . "
 */

class Database {
    // Paramètres de connexion à la base de données
    private \$host = \"$host\";
    private \$db_name = \"$db_name\";
    private \$username = \"$username\";
    private \$password = \"$password\";
    private \$charset = \"$charset\";
    
    public \$conn;
    
    /**
     * Obtenir la connexion à la base de données
     * @return PDO|null
     */
    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            
            \$options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4\"
            ];
            
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
            
        } catch(PDOException \$exception) {
            error_log(\"❌ Erreur de connexion à la base de données: \" . \$exception->getMessage());
        }
        
        return \$this->conn;
    }
    
    /**
     * Tester la connexion à la base de données
     * @return bool
     */
    public function testConnection() {
        \$conn = \$this->getConnection();
        if (\$conn !== null) {
            try {
                \$stmt = \$conn->query(\"SELECT 1\");
                return \$stmt !== false;
            } catch (PDOException \$e) {
                return false;
            }
        }
        return false;
    }
    
    /**
     * Obtenir les informations de la base de données
     * @return array
     */
    public function getDatabaseInfo() {
        \$conn = \$this->getConnection();
        if (\$conn !== null) {
            try {
                \$info = [];
                
                \$stmt = \$conn->query(\"SELECT VERSION() as version\");
                \$result = \$stmt->fetch();
                \$info['mysql_version'] = \$result['version'];
                
                \$info['database_name'] = \$this->db_name;
                
                \$stmt = \$conn->query(\"SELECT @@character_set_database as charset\");
                \$result = \$stmt->fetch();
                \$info['charset'] = \$result['charset'];
                
                \$stmt = \$conn->query(\"SHOW TABLES\");
                \$tables = \$stmt->fetchAll(PDO::FETCH_COLUMN);
                \$info['tables'] = \$tables;
                \$info['table_count'] = count(\$tables);
                
                return \$info;
            } catch (PDOException \$e) {
                return ['error' => \$e->getMessage()];
            }
        }
        return ['error' => 'Connexion impossible'];
    }
}

/**
 * Fonction utilitaire pour obtenir une connexion rapide
 * @return PDO|null
 */
function getDbConnection() {
    \$database = new Database();
    return \$database->getConnection();
}
?>";
        
        // Sauvegarder le fichier
        $database_file = __DIR__ . '/database.php';
        
        if (file_put_contents($database_file, $database_content)) {
            echo "<div class='success'>";
            echo "<h3>✅ Fichier database.php généré avec succès</h3>";
            echo "<p><strong>Emplacement :</strong> $database_file</p>";
            echo "</div>";
            
            // Tester la connexion
            echo "<div class='info'>";
            echo "<h3>🧪 Test de la connexion</h3>";
            echo "</div>";
            
            try {
                require_once $database_file;
                $database = new Database();
                
                if ($database->testConnection()) {
                    echo "<div class='success'>";
                    echo "<h3>🎉 CONNEXION RÉUSSIE !</h3>";
                    echo "<p>La configuration de la base de données fonctionne parfaitement.</p>";
                    
                    $info = $database->getDatabaseInfo();
                    if (!isset($info['error'])) {
                        echo "<p><strong>Base de données :</strong> {$info['database_name']}</p>";
                        echo "<p><strong>Version MySQL :</strong> {$info['mysql_version']}</p>";
                        echo "<p><strong>Nombre de tables :</strong> {$info['table_count']}</p>";
                    }
                    echo "</div>";
                    
                    echo "<div style='text-align: center; margin: 20px 0;'>";
                    echo "<a href='test_database.php' target='_blank' class='btn btn-success'>🔍 Test Complet</a>";
                    echo "<a href='../pages/notes/index_fixed.php' target='_blank' class='btn btn-success'>📊 Tester API Notes</a>";
                    echo "</div>";
                    
                } else {
                    echo "<div class='error'>";
                    echo "<h3>❌ ÉCHEC DE LA CONNEXION</h3>";
                    echo "<p>Le fichier a été créé mais la connexion échoue.</p>";
                    echo "<p>Vérifiez vos paramètres de base de données.</p>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ ERREUR LORS DU TEST</h3>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>";
            echo "<h3>❌ ERREUR DE SAUVEGARDE</h3>";
            echo "<p>Impossible de créer le fichier database.php</p>";
            echo "<p>Vérifiez les permissions du dossier.</p>";
            echo "</div>";
        }
    } else {
        // Afficher le formulaire
        echo "<div class='warning'>";
        echo "<h3>📋 Formulaire de Configuration</h3>";
        echo "<p>Remplissez les informations de votre base de données :</p>";
        echo "</div>";
        
        echo "<form method='POST'>";
        
        echo "<div class='form-group'>";
        echo "<label for='host'>Hôte de la base de données :</label>";
        echo "<input type='text' id='host' name='host' value='localhost' required>";
        echo "<small>Généralement 'localhost' pour un serveur local</small>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label for='db_name'>Nom de la base de données :</label>";
        echo "<input type='text' id='db_name' name='db_name' value='school_management' required>";
        echo "<small>Le nom de votre base de données MySQL</small>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label for='username'>Nom d'utilisateur :</label>";
        echo "<input type='text' id='username' name='username' value='root' required>";
        echo "<small>Généralement 'root' pour un serveur local</small>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label for='password'>Mot de passe :</label>";
        echo "<input type='password' id='password' name='password' value=''>";
        echo "<small>Laissez vide si pas de mot de passe (Laragon, XAMPP)</small>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label for='charset'>Charset :</label>";
        echo "<select id='charset' name='charset'>";
        echo "<option value='utf8mb4' selected>utf8mb4 (recommandé)</option>";
        echo "<option value='utf8'>utf8</option>";
        echo "</select>";
        echo "<small>utf8mb4 supporte tous les caractères Unicode</small>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<button type='submit' class='btn btn-success'>⚙️ Générer database.php</button>";
        echo "</div>";
        
        echo "</form>";
        
        // Configurations prédéfinies
        echo "<div class='info'>";
        echo "<h3>🔧 Configurations Prédéfinies</h3>";
        echo "</div>";
        
        echo "<div class='code'>";
        echo "# LARAGON (Windows)
Hôte: localhost
Base: school_management
User: root
Pass: (vide)

# XAMPP (Windows/Mac/Linux)
Hôte: localhost
Base: school_management
User: root
Pass: (vide)

# WAMP (Windows)
Hôte: localhost
Base: school_management
User: root
Pass: (vide ou votre mot de passe)

# MAMP (Mac)
Hôte: localhost
Base: school_management
User: root
Pass: root";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
