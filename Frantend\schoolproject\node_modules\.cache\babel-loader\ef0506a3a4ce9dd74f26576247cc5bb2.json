{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\NotesUnified.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport { filterNotes, canManageData, isStudent, logSecurityEvent } from '../utils/studentDataFilter';\nimport '../css/Factures.css';\nconst NotesUnified = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [notes, setNotes] = useState([]);\n  const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [showGenerateModal, setShowGenerateModal] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterMatiere, setFilterMatiere] = useState('all');\n  const [filterEtudiant, setFilterEtudiant] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [statistics, setStatistics] = useState({});\n  const [matieres, setMatieres] = useState([]);\n  const [etudiants, setEtudiants] = useState([]);\n  const [selectedEtudiant, setSelectedEtudiant] = useState(null);\n  const [selectedMatiere, setSelectedMatiere] = useState(null);\n  const [formValidation, setFormValidation] = useState({\n    etudiant_id: {\n      isValid: false,\n      message: ''\n    },\n    matiere_id: {\n      isValid: false,\n      message: ''\n    },\n    devoir_id: {\n      isValid: false,\n      message: ''\n    },\n    note: {\n      isValid: true,\n      message: ''\n    }\n  });\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    devoir_id: '',\n    matiere_id: '',\n    note: ''\n  });\n\n  // Déterminer le rôle et les permissions avec notre système unifié\n  const isEtudiant = isStudent(user);\n  const isEnseignant = (user === null || user === void 0 ? void 0 : user.role) === 'enseignant' || (user === null || user === void 0 ? void 0 : user.role) === 'Enseignant';\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n\n  // Permissions selon les spécifications\n  const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n  const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n  useEffect(() => {\n    if (canView) {\n      fetchNotes();\n      if (isEnseignant) {\n        fetchDevoirsDisponibles();\n      }\n    }\n  }, [canView, isEnseignant]);\n  const fetchNotes = async () => {\n    try {\n      console.log('🔄 Chargement des notes...');\n\n      // Déterminer le token selon le rôle\n      let authToken = 'default-token';\n      if (isEtudiant) authToken = 'etudiant-token';else if (isEnseignant) authToken = 'enseignant-token';else if (isAdmin) authToken = 'admin-token';\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n        headers: {\n          Authorization: `Bearer ${authToken}`\n        }\n      });\n      console.log('✅ Réponse API notes:', response.data);\n      if (response.data.success) {\n        const notesData = response.data.data || [];\n        setNotes(notesData);\n\n        // Calculer les statistiques\n        const stats = calculateStatistics(notesData);\n        setStatistics(stats);\n\n        // Extraire les matières et étudiants uniques pour les filtres\n        if (!isEtudiant) {\n          const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n          setMatieres(matieresUniques);\n          if (isAdmin) {\n            const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n            setEtudiants(etudiantsUniques);\n          }\n        }\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Erreur lors du chargement des notes:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403) {\n        Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n      } else {\n        Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n      }\n      setNotes([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchDevoirsDisponibles = async () => {\n    try {\n      console.log('🔄 Chargement des devoirs disponibles...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n        headers: {\n          Authorization: `Bearer enseignant-token`\n        }\n      });\n      console.log('✅ Réponse API devoirs disponibles:', response.data);\n      if (response.data.success) {\n        setDevoirsDisponibles(response.data.data || []);\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des devoirs:', error);\n      setDevoirsDisponibles([]);\n    }\n  };\n  const calculateStatistics = notesData => {\n    const total = notesData.length;\n    const moyenne_generale = total > 0 ? notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n    const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n    const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n    const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n    const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n    const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n    const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n    const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n    return {\n      total_notes: total,\n      moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n      notes_excellentes,\n      notes_bonnes,\n      notes_moyennes,\n      notes_faibles,\n      nombre_etudiants: etudiants.length,\n      nombre_matieres: matieres.length,\n      nombre_devoirs: devoirs.length\n    };\n  };\n  const handleGenerateNotes = async devoir_id => {\n    const result = await Swal.fire({\n      title: 'Générer les notes automatiquement ?',\n      text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n      icon: 'question',\n      showCancelButton: true,\n      confirmButtonColor: '#28a745',\n      cancelButtonColor: '#6c757d',\n      confirmButtonText: 'Oui, générer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n        const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          action: 'generate',\n          devoir_id: devoir_id\n        }, {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          }\n        });\n        console.log('✅ Notes générées:', response.data);\n        if (response.data.success) {\n          const details = response.data.details;\n          Swal.fire({\n            title: 'Notes générées !',\n            html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n            icon: 'success',\n            timer: 3000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n        }\n      } catch (error) {\n        var _error$response2, _error$response2$data;\n        console.error('❌ Erreur génération:', error);\n        Swal.fire('Erreur', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Erreur lors de la génération des notes', 'error');\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n      return;\n    }\n    if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n      Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n      return;\n    }\n    try {\n      const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n      const method = editingNote ? 'PUT' : 'POST';\n      const data = editingNote ? {\n        ...formData,\n        id: editingNote.id\n      } : formData;\n      console.log('🔄 Envoi note:', {\n        method,\n        data\n      });\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer enseignant-token`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('✅ Note envoyée:', response.data);\n      if (response.data.success) {\n        Swal.fire({\n          title: 'Succès !',\n          text: response.data.message,\n          icon: 'success',\n          timer: 2000,\n          showConfirmButton: false\n        });\n        setShowModal(false);\n        setEditingNote(null);\n        resetForm();\n        fetchNotes();\n        if (isEnseignant) fetchDevoirsDisponibles();\n      } else {\n        Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('❌ Erreur:', error);\n      Swal.fire('Erreur', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = note => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n      return;\n    }\n    setEditingNote(note);\n    setFormData({\n      etudiant_id: note.etudiant_id || '',\n      devoir_id: note.devoir_id || '',\n      matiere_id: note.matiere_id || '',\n      note: note.note || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Supprimer cette note ?',\n      text: 'Cette action est irréversible !',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🗑️ Suppression note ID:', id);\n        const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        console.log('✅ Note supprimée:', response.data);\n        if (response.data.success) {\n          Swal.fire({\n            title: 'Supprimé !',\n            text: response.data.message,\n            icon: 'success',\n            timer: 2000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          if (isEnseignant) fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n        }\n      } catch (error) {\n        var _error$response4, _error$response4$data;\n        console.error('❌ Erreur suppression:', error);\n        Swal.fire('Erreur', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || 'Impossible de supprimer la note', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      devoir_id: '',\n      matiere_id: '',\n      note: ''\n    });\n    setSelectedEtudiant(null);\n    setSelectedMatiere(null);\n  };\n\n  // Fonction pour gérer la sélection d'un étudiant\n  const handleEtudiantSelection = etudiantId => {\n    const etudiant = etudiants.find(e => (e.id || e.etudiant_id) === parseInt(etudiantId));\n    setSelectedEtudiant(etudiant || null);\n    setFormData(prev => ({\n      ...prev,\n      etudiant_id: etudiantId\n    }));\n\n    // Validation en temps réel\n    setFormValidation(prev => ({\n      ...prev,\n      etudiant_id: {\n        isValid: !!etudiantId && !!etudiant,\n        message: etudiantId ? etudiant ? '✅ Étudiant sélectionné' : '❌ Étudiant non trouvé' : '⚠️ Veuillez sélectionner un étudiant'\n      }\n    }));\n  };\n\n  // Fonction pour gérer la sélection d'une matière\n  const handleMatiereSelection = matiereId => {\n    const matiere = matieres.find(m => m.id === parseInt(matiereId));\n    setSelectedMatiere(matiere || null);\n    setFormData(prev => ({\n      ...prev,\n      matiere_id: matiereId\n    }));\n\n    // Validation en temps réel\n    setFormValidation(prev => ({\n      ...prev,\n      matiere_id: {\n        isValid: !!matiereId && !!matiere,\n        message: matiereId ? matiere ? '✅ Matière sélectionnée' : '❌ Matière non trouvée' : '⚠️ Veuillez sélectionner une matière'\n      }\n    }));\n  };\n\n  // Fonction pour valider la note\n  const handleNoteChange = noteValue => {\n    setFormData(prev => ({\n      ...prev,\n      note: noteValue\n    }));\n    const note = parseFloat(noteValue);\n    let validation = {\n      isValid: true,\n      message: ''\n    };\n    if (noteValue && (isNaN(note) || note < 0 || note > 20)) {\n      validation = {\n        isValid: false,\n        message: '❌ La note doit être comprise entre 0 et 20'\n      };\n    } else if (noteValue && note >= 0 && note <= 20) {\n      validation = {\n        isValid: true,\n        message: '✅ Note valide'\n      };\n    } else {\n      validation = {\n        isValid: true,\n        message: '💡 Note calculée automatiquement si vide'\n      };\n    }\n    setFormValidation(prev => ({\n      ...prev,\n      note: validation\n    }));\n  };\n\n  // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n  const securityFilteredNotes = filterNotes(notes, user);\n\n  // Log de sécurité si des données ont été filtrées\n  if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n    logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n      total: notes.length,\n      filtered: securityFilteredNotes.length\n    });\n  }\n\n  // ÉTAPE 2 : Filtrage par recherche et critères\n  const filteredNotes = securityFilteredNotes.filter(note => {\n    const searchLower = searchTerm.toLowerCase();\n    const matchesSearch = (note.etudiant_nom || '').toLowerCase().includes(searchLower) || (note.devoir_titre || '').toLowerCase().includes(searchLower) || (note.matiere_nom || '').toLowerCase().includes(searchLower) || (note.note || '').toString().includes(searchLower);\n    const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n    const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n    return matchesSearch && matchesMatiere && matchesEtudiant;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, filterMatiere, filterEtudiant]);\n  const getNoteBadge = note => {\n    const noteValue = parseFloat(note);\n    let style = {\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.9em',\n      fontWeight: 'bold'\n    };\n    if (noteValue >= 16) {\n      style = {\n        ...style,\n        backgroundColor: '#28a745',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 20\n        }\n      }, \"\\uD83C\\uDFC6 \", note, \"/20\");\n    } else if (noteValue >= 12) {\n      style = {\n        ...style,\n        backgroundColor: '#17a2b8',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDC4D \", note, \"/20\");\n    } else if (noteValue >= 10) {\n      style = {\n        ...style,\n        backgroundColor: '#ffc107',\n        color: 'black'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCCA \", note, \"/20\");\n    } else {\n      style = {\n        ...style,\n        backgroundColor: '#dc3545',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCC9 \", note, \"/20\");\n    }\n  };\n  const getHeaderTitle = () => {\n    if (isEtudiant) return '📊 Mes Notes';\n    if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n    if (isAdmin) return '🛡️ Administration - Notes';\n    return '📊 Notes';\n  };\n  const getInfoMessage = () => {\n    if (isEtudiant) {\n      return {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffc107',\n          color: '#856404'\n        },\n        text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n      };\n    } else if (isEnseignant) {\n      return {\n        style: {\n          backgroundColor: '#d4edda',\n          border: '1px solid #c3e6cb',\n          color: '#155724'\n        },\n        text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n      };\n    } else if (isAdmin) {\n      return {\n        style: {\n          backgroundColor: '#e2e3e5',\n          border: '1px solid #d6d8db',\n          color: '#383d41'\n        },\n        text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n      };\n    }\n    return null;\n  };\n  const styles = {\n    accessDenied: {\n      textAlign: 'center',\n      padding: '50px 20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      margin: '20px 0'\n    },\n    idBadge: {\n      backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n      color: 'white',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.8em',\n      fontWeight: 'bold'\n    }\n  };\n\n  // Vérification d'accès\n  if (!canView) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"factures-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.accessDenied,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 21\n      }\n    }, \"\\uD83D\\uDEAB Acc\\xE8s Refus\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 21\n      }\n    }, \"Vous n'avez pas les permissions pour acc\\xE9der aux notes.\")));\n  }\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 17\n      }\n    }, \"Chargement des notes...\"));\n  }\n  const infoMessage = getInfoMessage();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 17\n    }\n  }, getHeaderTitle()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 21\n    }\n  }, filteredNotes.length, \" note(s) trouv\\xE9e(s)\"), canManage && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => setShowGenerateModal(true),\n    style: {\n      marginRight: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/auto.png\",\n    alt: \"G\\xE9n\\xE9rer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 33\n    }\n  }), \" G\\xE9n\\xE9ration Auto\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 33\n    }\n  }), \" Nouvelle Note\")))), infoMessage && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...infoMessage.style,\n      borderRadius: '8px',\n      padding: '15px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 21\n    }\n  }, infoMessage.text)), statistics.total_notes > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n      gap: '15px',\n      backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n      border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n      borderRadius: '8px',\n      padding: '20px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: isAdmin ? '#6f42c1' : '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 25\n    }\n  }, statistics.total_notes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 25\n    }\n  }, isEtudiant ? 'Mes Notes' : 'Total Notes')), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#17a2b8'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 25\n    }\n  }, statistics.moyenne_generale), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 25\n    }\n  }, \"Moyenne\")), !isEtudiant && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 33\n    }\n  }, statistics.notes_excellentes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 33\n    }\n  }, \"Excellentes (\\u226516)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#ffc107'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 33\n    }\n  }, statistics.notes_moyennes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 33\n    }\n  }, \"Moyennes (10-12)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 33\n    }\n  }, statistics.notes_faibles), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 33\n    }\n  }, \"Faibles (<10)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#fd7e14'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 33\n    }\n  }, statistics.nombre_etudiants), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiants\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#20c997'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 33\n    }\n  }, statistics.nombre_devoirs), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 33\n    }\n  }, \"Devoirs\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-bar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/search.png\",\n    alt: \"Rechercher\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 688,\n      columnNumber: 21\n    }\n  })), !isEtudiant && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-section\",\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '10px',\n      marginTop: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: filterMatiere,\n    onChange: e => setFilterMatiere(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 29\n    }\n  }, \"\\uD83D\\uDCD6 Toutes les mati\\xE8res\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere,\n    value: matiere,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 33\n    }\n  }, matiere))), isAdmin && /*#__PURE__*/React.createElement(\"select\", {\n    value: filterEtudiant,\n    onChange: e => setFilterEtudiant(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDC64 Tous les \\xE9tudiants\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant,\n    value: etudiant,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 37\n    }\n  }, etudiant))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 13\n    }\n  }, filteredNotes.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/empty.png\",\n    alt: \"Aucune donn\\xE9e\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 25\n    }\n  }, \"Aucune note trouv\\xE9e\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 25\n    }\n  }, isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), !isEtudiant && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Devoir\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCD6 Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 747,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Note\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 49\n    }\n  }, \"\\uD83C\\uDFEB Classe\"), canManage && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 750,\n      columnNumber: 51\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 29\n    }\n  }, currentNotes.map(note => /*#__PURE__*/React.createElement(\"tr\", {\n    key: note.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 755,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.idBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 45\n    }\n  }, \"#\", note.id)), !isEtudiant && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 53\n    }\n  }, note.etudiant_nom || 'Nom non disponible'), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 53\n    }\n  }, note.etudiant_email || 'Email non disponible'))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxWidth: '200px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 49\n    }\n  }, note.devoir_titre || 'Devoir non spécifié'), note.date_remise && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 53\n    }\n  }, \"Remise: \", note.date_remise))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#fff3e0',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 45\n    }\n  }, note.matiere_nom || 'Non spécifiée')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 41\n    }\n  }, getNoteBadge(note.note)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 45\n    }\n  }, note.date_formatted || note.date_enregistrement)), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '3px 6px',\n      backgroundColor: '#f3e5f5',\n      borderRadius: '4px',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 49\n    }\n  }, note.classe_nom || 'Non spécifiée')), canManage && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(note),\n    title: \"Modifier la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(note.id),\n    title: \"Supprimer la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 826,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 831,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 846,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 21\n    }\n  }, \"Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 855,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 859,\n      columnNumber: 21\n    }\n  }, \"Suivant\")), showGenerateModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 871,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 29\n    }\n  }, \"\\uD83E\\uDD16 G\\xE9n\\xE9ration Automatique des Notes\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => setShowGenerateModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 875,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 882,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      marginBottom: '20px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 883,\n      columnNumber: 29\n    }\n  }, \"S\\xE9lectionnez un devoir pour g\\xE9n\\xE9rer automatiquement les notes \\xE0 partir des r\\xE9ponses aux quiz.\"), devoirsDisponibles.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 889,\n      columnNumber: 37\n    }\n  }, \"Aucun devoir disponible pour la g\\xE9n\\xE9ration de notes.\")) : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxHeight: '400px',\n      overflowY: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 33\n    }\n  }, devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"div\", {\n    key: devoir.id,\n    style: {\n      border: '1px solid #dee2e6',\n      borderRadius: '8px',\n      padding: '15px',\n      marginBottom: '10px',\n      backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 894,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 901,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    style: {\n      margin: '0 0 5px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 903,\n      columnNumber: 53\n    }\n  }, devoir.titre), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      fontSize: '0.9em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 904,\n      columnNumber: 53\n    }\n  }, devoir.matiere_nom, \" - \", devoir.classe_nom), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 907,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDCCA \", devoir.nombre_quiz, \" quiz \\u2022 \\uD83D\\uDC65 \", devoir.nombre_etudiants_repondus, \" r\\xE9ponses \\u2022 \\uD83D\\uDCDD \", devoir.notes_existantes, \" notes\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 910,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '2px 6px',\n      borderRadius: '4px',\n      backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n      color: devoir.peut_generer_notes ? '#155724' : '#856404'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 57\n    }\n  }, devoir.statut))), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => {\n      setShowGenerateModal(false);\n      handleGenerateNotes(devoir.id);\n    },\n    disabled: !devoir.peut_generer_notes,\n    title: devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 921,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDE80 G\\xE9n\\xE9rer\")))))))), showModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 945,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 946,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 29\n    }\n  }, editingNote ? 'Modifier la note' : 'Nouvelle note'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 956,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 959,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 960,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    style: {\n      fontWeight: '600',\n      color: '#2c3e50',\n      marginBottom: '8px',\n      display: 'block'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 961,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDC64 S\\xE9lection de l'\\xE9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => handleEtudiantSelection(e.target.value),\n    required: true,\n    disabled: editingNote,\n    style: {\n      width: '100%',\n      padding: '12px',\n      border: `2px solid ${formValidation.etudiant_id.isValid && formData.etudiant_id ? '#28a745' : formData.etudiant_id && !formValidation.etudiant_id.isValid ? '#dc3545' : '#e9ecef'}`,\n      borderRadius: '8px',\n      fontSize: '14px',\n      backgroundColor: '#fff',\n      transition: 'border-color 0.3s ease',\n      cursor: 'pointer'\n    },\n    onFocus: e => e.target.style.borderColor = '#007bff',\n    onBlur: e => e.target.style.borderColor = '#e9ecef',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 969,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 987,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDD0D Choisir un \\xE9tudiant dans la liste...\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.id || etudiant.etudiant_id,\n    value: etudiant.id || etudiant.etudiant_id,\n    style: {\n      padding: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 41\n    }\n  }, \"\\uD83D\\uDCDA \", etudiant.nom.toUpperCase(), \" \", etudiant.prenom ? etudiant.prenom : '', etudiant.groupe_nom ? ` | Groupe: ${etudiant.groupe_nom}` : '', etudiant.classe_nom ? ` | Classe: ${etudiant.classe_nom}` : ''))), etudiants.length === 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#dc3545',\n      fontSize: '12px',\n      marginTop: '5px',\n      padding: '8px',\n      backgroundColor: '#f8d7da',\n      border: '1px solid #f5c6cb',\n      borderRadius: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 37\n    }\n  }, \"\\u26A0\\uFE0F Aucun \\xE9tudiant disponible. V\\xE9rifiez votre connexion.\"), selectedEtudiant && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginTop: '15px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      border: '1px solid #dee2e6',\n      borderRadius: '8px',\n      borderLeft: '4px solid #007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1018,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"h6\", {\n    style: {\n      margin: '0 0 10px 0',\n      color: '#495057',\n      fontWeight: '600'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1026,\n      columnNumber: 41\n    }\n  }, \"\\uD83D\\uDCCB Informations de l'\\xE9tudiant s\\xE9lectionn\\xE9\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1033,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1034,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1035,\n      columnNumber: 49\n    }\n  }, \"Nom complet:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 49\n    }\n  }, selectedEtudiant.nom.toUpperCase(), \" \", selectedEtudiant.prenom || '')), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1040,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1041,\n      columnNumber: 49\n    }\n  }, \"Email:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1042,\n      columnNumber: 49\n    }\n  }, selectedEtudiant.email || 'Non renseigné')), selectedEtudiant.groupe_nom && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1047,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1048,\n      columnNumber: 53\n    }\n  }, \"Groupe:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1049,\n      columnNumber: 53\n    }\n  }, selectedEtudiant.groupe_nom)), selectedEtudiant.classe_nom && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1055,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1056,\n      columnNumber: 53\n    }\n  }, \"Classe:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 53\n    }\n  }, selectedEtudiant.classe_nom))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1067,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1068,\n      columnNumber: 33\n    }\n  }, \"Devoir *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.devoir_id,\n    onChange: e => setFormData({\n      ...formData,\n      devoir_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1069,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1075,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un devoir...\"), devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"option\", {\n    key: devoir.id,\n    value: devoir.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1077,\n      columnNumber: 41\n    }\n  }, devoir.devoir_display)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1084,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    style: {\n      fontWeight: '600',\n      color: '#2c3e50',\n      marginBottom: '8px',\n      display: 'block'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1085,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCD6 S\\xE9lection de la mati\\xE8re *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => handleMatiereSelection(e.target.value),\n    required: true,\n    disabled: editingNote,\n    style: {\n      width: '100%',\n      padding: '12px',\n      border: '2px solid #e9ecef',\n      borderRadius: '8px',\n      fontSize: '14px',\n      backgroundColor: '#fff',\n      transition: 'border-color 0.3s ease',\n      cursor: 'pointer'\n    },\n    onFocus: e => e.target.style.borderColor = '#28a745',\n    onBlur: e => e.target.style.borderColor = '#e9ecef',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1093,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1111,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDD0D Choisir une mati\\xE8re dans la liste...\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    style: {\n      padding: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1115,\n      columnNumber: 41\n    }\n  }, \"\\uD83D\\uDCDA \", matiere.nom.toUpperCase(), matiere.filiere_nom ? ` | Filière: ${matiere.filiere_nom}` : ''))), matieres.length === 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#dc3545',\n      fontSize: '12px',\n      marginTop: '5px',\n      padding: '8px',\n      backgroundColor: '#f8d7da',\n      border: '1px solid #f5c6cb',\n      borderRadius: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1126,\n      columnNumber: 37\n    }\n  }, \"\\u26A0\\uFE0F Aucune mati\\xE8re disponible. V\\xE9rifiez votre connexion.\"), selectedMatiere && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginTop: '15px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      border: '1px solid #dee2e6',\n      borderRadius: '8px',\n      borderLeft: '4px solid #28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1141,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"h6\", {\n    style: {\n      margin: '0 0 10px 0',\n      color: '#495057',\n      fontWeight: '600'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1149,\n      columnNumber: 41\n    }\n  }, \"\\uD83D\\uDCDA Informations de la mati\\xE8re s\\xE9lectionn\\xE9e\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1156,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1157,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1158,\n      columnNumber: 49\n    }\n  }, \"Mati\\xE8re:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1159,\n      columnNumber: 49\n    }\n  }, selectedMatiere.nom)), selectedMatiere.filiere_nom && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1164,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1165,\n      columnNumber: 53\n    }\n  }, \"Fili\\xE8re:\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#2c3e50'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1166,\n      columnNumber: 53\n    }\n  }, selectedMatiere.filiere_nom))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1176,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1177,\n      columnNumber: 33\n    }\n  }, \"Note (sur 20)\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    min: \"0\",\n    max: \"20\",\n    step: \"0.01\",\n    value: formData.note,\n    onChange: e => setFormData({\n      ...formData,\n      note: e.target.value\n    }),\n    placeholder: \"Laisser vide pour calcul automatique\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1178,\n      columnNumber: 33\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1187,\n      columnNumber: 33\n    }\n  }, \"Si vide, la note sera calcul\\xE9e automatiquement \\xE0 partir des r\\xE9ponses aux quiz.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1192,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1193,\n      columnNumber: 33\n    }\n  }, editingNote ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1196,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default NotesUnified;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "axios", "<PERSON><PERSON>", "AuthContext", "filterNotes", "canManageData", "isStudent", "logSecurityEvent", "NotesUnified", "user", "notes", "setNotes", "devoirsDisponibles", "setDevoirsDisponibles", "loading", "setLoading", "showModal", "setShowModal", "showGenerateModal", "setShowGenerateModal", "editingNote", "setEditingNote", "searchTerm", "setSearchTerm", "filterMatiere", "setFilterMatiere", "filterEtudiant", "setFilterEtudiant", "currentPage", "setCurrentPage", "itemsPerPage", "statistics", "setStatistics", "matieres", "set<PERSON>ati<PERSON>s", "etudiants", "setEtudiants", "selectedEtudiant", "setSelectedEtudiant", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatiere", "formValidation", "setFormValidation", "etudiant_id", "<PERSON><PERSON><PERSON><PERSON>", "message", "matiere_id", "devoir_id", "note", "formData", "setFormData", "isEtudiant", "isEnseignant", "role", "isAdmin", "canManage", "canView", "fetchNotes", "fetchDevoirsDisponibles", "console", "log", "authToken", "response", "get", "headers", "Authorization", "data", "success", "notesData", "stats", "calculateStatistics", "matieresUniques", "Set", "map", "n", "matiere_nom", "filter", "Boolean", "etudiantsUniques", "etudiant_nom", "error", "_error$response", "status", "fire", "total", "length", "moyenne_generale", "reduce", "sum", "parseFloat", "notes_excellentes", "notes_bonnes", "notes_moyennes", "notes_faibles", "devoirs", "total_notes", "Math", "round", "nombre_etudiants", "nombre_matieres", "nombre_devoirs", "handleGenerateNotes", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "post", "action", "details", "html", "notes_generees", "notes_mises_a_jour", "total_etudiants", "timer", "showConfirmButton", "_error$response2", "_error$response2$data", "handleSubmit", "e", "preventDefault", "url", "method", "id", "resetForm", "_error$response3", "_error$response3$data", "handleEdit", "handleDelete", "delete", "_error$response4", "_error$response4$data", "handleEtudiantSelection", "etudiantId", "etudiant", "find", "parseInt", "prev", "handleMatiereSelection", "matiereId", "matiere", "m", "handleNoteChange", "noteValue", "validation", "isNaN", "securityFilteredNotes", "filtered", "filteredNotes", "searchLower", "toLowerCase", "matchesSearch", "includes", "devoir_titre", "toString", "matchesMatiere", "matchesEtudiant", "indexOfLastItem", "indexOfFirstItem", "currentNotes", "slice", "totalPages", "ceil", "paginate", "pageNumber", "getNoteBadge", "style", "padding", "borderRadius", "fontSize", "fontWeight", "backgroundColor", "color", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getHeaderTitle", "getInfoMessage", "border", "styles", "accessDenied", "textAlign", "margin", "idBadge", "className", "infoMessage", "onClick", "marginRight", "src", "alt", "display", "gridTemplateColumns", "gap", "boxShadow", "Fragment", "type", "placeholder", "value", "onChange", "target", "marginTop", "key", "etudiant_email", "max<PERSON><PERSON><PERSON>", "date_remise", "date_formatted", "date_enregistrement", "classe_nom", "disabled", "marginBottom", "maxHeight", "overflowY", "devoir", "peut_generer_notes", "justifyContent", "alignItems", "titre", "nombre_quiz", "nombre_etudiants_repondus", "notes_existantes", "statut", "onSubmit", "required", "width", "transition", "cursor", "onFocus", "borderColor", "onBlur", "nom", "toUpperCase", "prenom", "groupe_nom", "borderLeft", "email", "devoir_display", "filiere_nom", "min", "max", "step"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/NotesUnified.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport {\n    filterNotes,\n    canManageData,\n    isStudent,\n    logSecurityEvent\n} from '../utils/studentDataFilter';\nimport '../css/Factures.css';\n\nconst NotesUnified = () => {\n    const { user } = useContext(AuthContext);\n    const [notes, setNotes] = useState([]);\n    const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [showGenerateModal, setShowGenerateModal] = useState(false);\n    const [editingNote, setEditingNote] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterMatiere, setFilterMatiere] = useState('all');\n    const [filterEtudiant, setFilterEtudiant] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [statistics, setStatistics] = useState({});\n    const [matieres, setMatieres] = useState([]);\n    const [etudiants, setEtudiants] = useState([]);\n    const [selectedEtudiant, setSelectedEtudiant] = useState(null);\n    const [selectedMatiere, setSelectedMatiere] = useState(null);\n    const [formValidation, setFormValidation] = useState({\n        etudiant_id: { isValid: false, message: '' },\n        matiere_id: { isValid: false, message: '' },\n        devoir_id: { isValid: false, message: '' },\n        note: { isValid: true, message: '' }\n    });\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        devoir_id: '',\n        matiere_id: '',\n        note: ''\n    });\n\n    // Déterminer le rôle et les permissions avec notre système unifié\n    const isEtudiant = isStudent(user);\n    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';\n\n    // Permissions selon les spécifications\n    const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n    const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n    useEffect(() => {\n        if (canView) {\n            fetchNotes();\n            if (isEnseignant) {\n                fetchDevoirsDisponibles();\n            }\n        }\n    }, [canView, isEnseignant]);\n\n    const fetchNotes = async () => {\n        try {\n            console.log('🔄 Chargement des notes...');\n\n            // Déterminer le token selon le rôle\n            let authToken = 'default-token';\n            if (isEtudiant) authToken = 'etudiant-token';\n            else if (isEnseignant) authToken = 'enseignant-token';\n            else if (isAdmin) authToken = 'admin-token';\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                headers: { Authorization: `Bearer ${authToken}` }\n            });\n\n            console.log('✅ Réponse API notes:', response.data);\n            if (response.data.success) {\n                const notesData = response.data.data || [];\n                setNotes(notesData);\n                \n                // Calculer les statistiques\n                const stats = calculateStatistics(notesData);\n                setStatistics(stats);\n                \n                // Extraire les matières et étudiants uniques pour les filtres\n                if (!isEtudiant) {\n                    const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n                    setMatieres(matieresUniques);\n                    \n                    if (isAdmin) {\n                        const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n                        setEtudiants(etudiantsUniques);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des notes:', error);\n            if (error.response?.status === 403) {\n                Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n            } else {\n                Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n            }\n            setNotes([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchDevoirsDisponibles = async () => {\n        try {\n            console.log('🔄 Chargement des devoirs disponibles...');\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n                headers: { Authorization: `Bearer enseignant-token` }\n            });\n\n            console.log('✅ Réponse API devoirs disponibles:', response.data);\n            if (response.data.success) {\n                setDevoirsDisponibles(response.data.data || []);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des devoirs:', error);\n            setDevoirsDisponibles([]);\n        }\n    };\n\n    const calculateStatistics = (notesData) => {\n        const total = notesData.length;\n        const moyenne_generale = total > 0 ? \n            notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n        \n        const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n        const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n        const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n        const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n        \n        const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n        const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n        const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n        \n        return {\n            total_notes: total,\n            moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n            notes_excellentes,\n            notes_bonnes,\n            notes_moyennes,\n            notes_faibles,\n            nombre_etudiants: etudiants.length,\n            nombre_matieres: matieres.length,\n            nombre_devoirs: devoirs.length\n        };\n    };\n\n    const handleGenerateNotes = async (devoir_id) => {\n        const result = await Swal.fire({\n            title: 'Générer les notes automatiquement ?',\n            text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#28a745',\n            cancelButtonColor: '#6c757d',\n            confirmButtonText: 'Oui, générer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n                \n                const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    action: 'generate',\n                    devoir_id: devoir_id\n                }, {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n\n                console.log('✅ Notes générées:', response.data);\n\n                if (response.data.success) {\n                    const details = response.data.details;\n                    Swal.fire({\n                        title: 'Notes générées !',\n                        html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n                        icon: 'success',\n                        timer: 3000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur génération:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Erreur lors de la génération des notes', 'error');\n            }\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n            return;\n        }\n\n        if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n            return;\n        }\n\n        try {\n            const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n            const method = editingNote ? 'PUT' : 'POST';\n            const data = editingNote ? { ...formData, id: editingNote.id } : formData;\n\n            console.log('🔄 Envoi note:', { method, data });\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    Authorization: `Bearer enseignant-token`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            console.log('✅ Note envoyée:', response.data);\n\n            if (response.data.success) {\n                Swal.fire({\n                    title: 'Succès !',\n                    text: response.data.message,\n                    icon: 'success',\n                    timer: 2000,\n                    showConfirmButton: false\n                });\n                setShowModal(false);\n                setEditingNote(null);\n                resetForm();\n                fetchNotes();\n                if (isEnseignant) fetchDevoirsDisponibles();\n            } else {\n                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n            }\n        } catch (error) {\n            console.error('❌ Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (note) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n            return;\n        }\n\n        setEditingNote(note);\n        setFormData({\n            etudiant_id: note.etudiant_id || '',\n            devoir_id: note.devoir_id || '',\n            matiere_id: note.matiere_id || '',\n            note: note.note || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Supprimer cette note ?',\n            text: 'Cette action est irréversible !',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🗑️ Suppression note ID:', id);\n                \n                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n\n                console.log('✅ Note supprimée:', response.data);\n\n                if (response.data.success) {\n                    Swal.fire({\n                        title: 'Supprimé !',\n                        text: response.data.message,\n                        icon: 'success',\n                        timer: 2000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    if (isEnseignant) fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur suppression:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la note', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            devoir_id: '',\n            matiere_id: '',\n            note: ''\n        });\n        setSelectedEtudiant(null);\n        setSelectedMatiere(null);\n    };\n\n    // Fonction pour gérer la sélection d'un étudiant\n    const handleEtudiantSelection = (etudiantId) => {\n        const etudiant = etudiants.find(e => (e.id || e.etudiant_id) === parseInt(etudiantId));\n        setSelectedEtudiant(etudiant || null);\n        setFormData(prev => ({ ...prev, etudiant_id: etudiantId }));\n\n        // Validation en temps réel\n        setFormValidation(prev => ({\n            ...prev,\n            etudiant_id: {\n                isValid: !!etudiantId && !!etudiant,\n                message: etudiantId ? (etudiant ? '✅ Étudiant sélectionné' : '❌ Étudiant non trouvé') : '⚠️ Veuillez sélectionner un étudiant'\n            }\n        }));\n    };\n\n    // Fonction pour gérer la sélection d'une matière\n    const handleMatiereSelection = (matiereId) => {\n        const matiere = matieres.find(m => m.id === parseInt(matiereId));\n        setSelectedMatiere(matiere || null);\n        setFormData(prev => ({ ...prev, matiere_id: matiereId }));\n\n        // Validation en temps réel\n        setFormValidation(prev => ({\n            ...prev,\n            matiere_id: {\n                isValid: !!matiereId && !!matiere,\n                message: matiereId ? (matiere ? '✅ Matière sélectionnée' : '❌ Matière non trouvée') : '⚠️ Veuillez sélectionner une matière'\n            }\n        }));\n    };\n\n    // Fonction pour valider la note\n    const handleNoteChange = (noteValue) => {\n        setFormData(prev => ({ ...prev, note: noteValue }));\n\n        const note = parseFloat(noteValue);\n        let validation = { isValid: true, message: '' };\n\n        if (noteValue && (isNaN(note) || note < 0 || note > 20)) {\n            validation = {\n                isValid: false,\n                message: '❌ La note doit être comprise entre 0 et 20'\n            };\n        } else if (noteValue && note >= 0 && note <= 20) {\n            validation = {\n                isValid: true,\n                message: '✅ Note valide'\n            };\n        } else {\n            validation = {\n                isValid: true,\n                message: '💡 Note calculée automatiquement si vide'\n            };\n        }\n\n        setFormValidation(prev => ({\n            ...prev,\n            note: validation\n        }));\n    };\n\n    // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n    const securityFilteredNotes = filterNotes(notes, user);\n\n    // Log de sécurité si des données ont été filtrées\n    if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n        logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n            total: notes.length,\n            filtered: securityFilteredNotes.length\n        });\n    }\n\n    // ÉTAPE 2 : Filtrage par recherche et critères\n    const filteredNotes = securityFilteredNotes.filter(note => {\n        const searchLower = searchTerm.toLowerCase();\n        const matchesSearch = (\n            (note.etudiant_nom || '').toLowerCase().includes(searchLower) ||\n            (note.devoir_titre || '').toLowerCase().includes(searchLower) ||\n            (note.matiere_nom || '').toLowerCase().includes(searchLower) ||\n            (note.note || '').toString().includes(searchLower)\n        );\n\n        const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n        const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n\n        return matchesSearch && matchesMatiere && matchesEtudiant;\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when filters change\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm, filterMatiere, filterEtudiant]);\n\n    const getNoteBadge = (note) => {\n        const noteValue = parseFloat(note);\n        let style = {\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.9em',\n            fontWeight: 'bold'\n        };\n\n        if (noteValue >= 16) {\n            style = {...style, backgroundColor: '#28a745', color: 'white'};\n            return <span style={style}>🏆 {note}/20</span>;\n        } else if (noteValue >= 12) {\n            style = {...style, backgroundColor: '#17a2b8', color: 'white'};\n            return <span style={style}>👍 {note}/20</span>;\n        } else if (noteValue >= 10) {\n            style = {...style, backgroundColor: '#ffc107', color: 'black'};\n            return <span style={style}>📊 {note}/20</span>;\n        } else {\n            style = {...style, backgroundColor: '#dc3545', color: 'white'};\n            return <span style={style}>📉 {note}/20</span>;\n        }\n    };\n\n    const getHeaderTitle = () => {\n        if (isEtudiant) return '📊 Mes Notes';\n        if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n        if (isAdmin) return '🛡️ Administration - Notes';\n        return '📊 Notes';\n    };\n\n    const getInfoMessage = () => {\n        if (isEtudiant) {\n            return {\n                style: { backgroundColor: '#fff3cd', border: '1px solid #ffc107', color: '#856404' },\n                text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n            };\n        } else if (isEnseignant) {\n            return {\n                style: { backgroundColor: '#d4edda', border: '1px solid #c3e6cb', color: '#155724' },\n                text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n            };\n        } else if (isAdmin) {\n            return {\n                style: { backgroundColor: '#e2e3e5', border: '1px solid #d6d8db', color: '#383d41' },\n                text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n            };\n        }\n        return null;\n    };\n\n    const styles = {\n        accessDenied: {\n            textAlign: 'center',\n            padding: '50px 20px',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '8px',\n            margin: '20px 0'\n        },\n        idBadge: {\n            backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n            color: 'white',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.8em',\n            fontWeight: 'bold'\n        }\n    };\n\n    // Vérification d'accès\n    if (!canView) {\n        return (\n            <div className=\"factures-container\">\n                <div style={styles.accessDenied}>\n                    <h2>🚫 Accès Refusé</h2>\n                    <p>Vous n'avez pas les permissions pour accéder aux notes.</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des notes...</p>\n            </div>\n        );\n    }\n\n    const infoMessage = getInfoMessage();\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>{getHeaderTitle()}</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredNotes.length} note(s) trouvée(s)\n                    </span>\n                    {canManage && (\n                        <div>\n                            <button \n                                className=\"btn btn-success\"\n                                onClick={() => setShowGenerateModal(true)}\n                                style={{ marginRight: '10px' }}\n                            >\n                                <img src=\"/auto.png\" alt=\"Générer\" /> Génération Auto\n                            </button>\n                            <button \n                                className=\"btn btn-primary\"\n                                onClick={() => setShowModal(true)}\n                            >\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvelle Note\n                            </button>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information selon le rôle */}\n            {infoMessage && (\n                <div style={{\n                    ...infoMessage.style,\n                    borderRadius: '8px',\n                    padding: '15px',\n                    margin: '20px 0'\n                }}>\n                    <p style={{ margin: '0' }}>{infoMessage.text}</p>\n                </div>\n            )}\n\n            {/* Statistiques selon le rôle */}\n            {statistics.total_notes > 0 && (\n                <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n                    gap: '15px',\n                    backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n                    border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n                    borderRadius: '8px',\n                    padding: '20px',\n                    margin: '20px 0'\n                }}>\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: isAdmin ? '#6f42c1' : '#007bff' }}>\n                            {statistics.total_notes}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>\n                            {isEtudiant ? 'Mes Notes' : 'Total Notes'}\n                        </div>\n                    </div>\n\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#17a2b8' }}>\n                            {statistics.moyenne_generale}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyenne</div>\n                    </div>\n\n                    {!isEtudiant && (\n                        <>\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>\n                                    {statistics.notes_excellentes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Excellentes (≥16)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>\n                                    {statistics.notes_moyennes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyennes (10-12)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>\n                                    {statistics.notes_faibles}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Faibles (&lt;10)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fd7e14' }}>\n                                    {statistics.nombre_etudiants}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Étudiants</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20c997' }}>\n                                    {statistics.nombre_devoirs}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Devoirs</div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            )}\n\n            {/* Filtres et recherche */}\n            <div className=\"search-section\">\n                <div className=\"search-bar\">\n                    <img src=\"/search.png\" alt=\"Rechercher\" />\n                    <input\n                        type=\"text\"\n                        placeholder={isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\"}\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                </div>\n\n                {!isEtudiant && (\n                    <div className=\"filter-section\" style={{\n                        display: 'grid',\n                        gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '10px',\n                        marginTop: '15px'\n                    }}>\n                        <select\n                            value={filterMatiere}\n                            onChange={(e) => setFilterMatiere(e.target.value)}\n                            className=\"filter-select\"\n                        >\n                            <option value=\"all\">📖 Toutes les matières</option>\n                            {matieres.map(matiere => (\n                                <option key={matiere} value={matiere}>{matiere}</option>\n                            ))}\n                        </select>\n\n                        {isAdmin && (\n                            <select\n                                value={filterEtudiant}\n                                onChange={(e) => setFilterEtudiant(e.target.value)}\n                                className=\"filter-select\"\n                            >\n                                <option value=\"all\">👤 Tous les étudiants</option>\n                                {etudiants.map(etudiant => (\n                                    <option key={etudiant} value={etudiant}>{etudiant}</option>\n                                ))}\n                            </select>\n                        )}\n                    </div>\n                )}\n            </div>\n\n            {/* Tableau des notes */}\n            <div className=\"table-container\">\n                {filteredNotes.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/empty.png\" alt=\"Aucune donnée\" />\n                        <p>Aucune note trouvée</p>\n                        <p>{isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\"}</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>🆔 ID</th>\n                                    {!isEtudiant && <th>👤 Étudiant</th>}\n                                    <th>📚 Devoir</th>\n                                    <th>📖 Matière</th>\n                                    <th>📊 Note</th>\n                                    <th>📅 Date</th>\n                                    {isAdmin && <th>🏫 Classe</th>}\n                                    {canManage && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentNotes.map((note) => (\n                                    <tr key={note.id}>\n                                        <td>\n                                            <span style={styles.idBadge}>\n                                                #{note.id}\n                                            </span>\n                                        </td>\n                                        {!isEtudiant && (\n                                            <td>\n                                                <div className=\"user-info\">\n                                                    <strong style={{ fontSize: '0.9em' }}>\n                                                        {note.etudiant_nom || 'Nom non disponible'}\n                                                    </strong>\n                                                    <br />\n                                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                                        {note.etudiant_email || 'Email non disponible'}\n                                                    </small>\n                                                </div>\n                                            </td>\n                                        )}\n                                        <td>\n                                            <div style={{ maxWidth: '200px' }}>\n                                                <strong style={{ fontSize: '0.9em' }}>\n                                                    {note.devoir_titre || 'Devoir non spécifié'}\n                                                </strong>\n                                                {note.date_remise && (\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d' }}>\n                                                        Remise: {note.date_remise}\n                                                    </div>\n                                                )}\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#fff3e0',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {note.matiere_nom || 'Non spécifiée'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            {getNoteBadge(note.note)}\n                                        </td>\n                                        <td>\n                                            <span style={{ fontSize: '0.9em' }}>\n                                                {note.date_formatted || note.date_enregistrement}\n                                            </span>\n                                        </td>\n                                        {isAdmin && (\n                                            <td>\n                                                <span style={{\n                                                    padding: '3px 6px',\n                                                    backgroundColor: '#f3e5f5',\n                                                    borderRadius: '4px',\n                                                    fontSize: '0.8em'\n                                                }}>\n                                                    {note.classe_nom || 'Non spécifiée'}\n                                                </span>\n                                            </td>\n                                        )}\n                                        {canManage && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(note)}\n                                                        title=\"Modifier la note\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(note.id)}\n                                                        title=\"Supprimer la note\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div className=\"pagination\">\n                    <button\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Précédent\n                    </button>\n\n                    <div className=\"page-info\">\n                        Page {currentPage} sur {totalPages}\n                    </div>\n\n                    <button\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Suivant\n                    </button>\n                </div>\n            )}\n\n            {/* Modal de génération automatique (enseignants uniquement) */}\n            {showGenerateModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>🤖 Génération Automatique des Notes</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => setShowGenerateModal(false)}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <div style={{ padding: '20px' }}>\n                            <p style={{ marginBottom: '20px', color: '#6c757d' }}>\n                                Sélectionnez un devoir pour générer automatiquement les notes à partir des réponses aux quiz.\n                            </p>\n\n                            {devoirsDisponibles.length === 0 ? (\n                                <div style={{ textAlign: 'center', padding: '20px' }}>\n                                    <p>Aucun devoir disponible pour la génération de notes.</p>\n                                </div>\n                            ) : (\n                                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <div key={devoir.id} style={{\n                                            border: '1px solid #dee2e6',\n                                            borderRadius: '8px',\n                                            padding: '15px',\n                                            marginBottom: '10px',\n                                            backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n                                        }}>\n                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                                <div>\n                                                    <h5 style={{ margin: '0 0 5px 0' }}>{devoir.titre}</h5>\n                                                    <p style={{ margin: '0', fontSize: '0.9em', color: '#6c757d' }}>\n                                                        {devoir.matiere_nom} - {devoir.classe_nom}\n                                                    </p>\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px' }}>\n                                                        📊 {devoir.nombre_quiz} quiz • 👥 {devoir.nombre_etudiants_repondus} réponses • 📝 {devoir.notes_existantes} notes\n                                                    </div>\n                                                    <div style={{ fontSize: '0.8em', marginTop: '5px' }}>\n                                                        <span style={{\n                                                            padding: '2px 6px',\n                                                            borderRadius: '4px',\n                                                            backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n                                                            color: devoir.peut_generer_notes ? '#155724' : '#856404'\n                                                        }}>\n                                                            {devoir.statut}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                                <button\n                                                    className=\"btn btn-success\"\n                                                    onClick={() => {\n                                                        setShowGenerateModal(false);\n                                                        handleGenerateNotes(devoir.id);\n                                                    }}\n                                                    disabled={!devoir.peut_generer_notes}\n                                                    title={devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible'}\n                                                >\n                                                    🚀 Générer\n                                                </button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier une note (enseignants uniquement) */}\n            {showModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingNote ? 'Modifier la note' : 'Nouvelle note'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingNote(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label style={{\n                                    fontWeight: '600',\n                                    color: '#2c3e50',\n                                    marginBottom: '8px',\n                                    display: 'block'\n                                }}>\n                                    👤 Sélection de l'étudiant *\n                                </label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => handleEtudiantSelection(e.target.value)}\n                                    required\n                                    disabled={editingNote}\n                                    style={{\n                                        width: '100%',\n                                        padding: '12px',\n                                        border: `2px solid ${formValidation.etudiant_id.isValid && formData.etudiant_id ? '#28a745' : formData.etudiant_id && !formValidation.etudiant_id.isValid ? '#dc3545' : '#e9ecef'}`,\n                                        borderRadius: '8px',\n                                        fontSize: '14px',\n                                        backgroundColor: '#fff',\n                                        transition: 'border-color 0.3s ease',\n                                        cursor: 'pointer'\n                                    }}\n                                    onFocus={(e) => e.target.style.borderColor = '#007bff'}\n                                    onBlur={(e) => e.target.style.borderColor = '#e9ecef'}\n                                >\n                                    <option value=\"\" style={{ color: '#6c757d' }}>\n                                        🔍 Choisir un étudiant dans la liste...\n                                    </option>\n                                    {etudiants.map((etudiant) => (\n                                        <option\n                                            key={etudiant.id || etudiant.etudiant_id}\n                                            value={etudiant.id || etudiant.etudiant_id}\n                                            style={{ padding: '8px' }}\n                                        >\n                                            📚 {etudiant.nom.toUpperCase()} {etudiant.prenom ? etudiant.prenom : ''}\n                                            {etudiant.groupe_nom ? ` | Groupe: ${etudiant.groupe_nom}` : ''}\n                                            {etudiant.classe_nom ? ` | Classe: ${etudiant.classe_nom}` : ''}\n                                        </option>\n                                    ))}\n                                </select>\n                                {etudiants.length === 0 && (\n                                    <div style={{\n                                        color: '#dc3545',\n                                        fontSize: '12px',\n                                        marginTop: '5px',\n                                        padding: '8px',\n                                        backgroundColor: '#f8d7da',\n                                        border: '1px solid #f5c6cb',\n                                        borderRadius: '4px'\n                                    }}>\n                                        ⚠️ Aucun étudiant disponible. Vérifiez votre connexion.\n                                    </div>\n                                )}\n\n                                {/* Affichage des informations détaillées de l'étudiant sélectionné */}\n                                {selectedEtudiant && (\n                                    <div style={{\n                                        marginTop: '15px',\n                                        padding: '15px',\n                                        backgroundColor: '#f8f9fa',\n                                        border: '1px solid #dee2e6',\n                                        borderRadius: '8px',\n                                        borderLeft: '4px solid #007bff'\n                                    }}>\n                                        <h6 style={{\n                                            margin: '0 0 10px 0',\n                                            color: '#495057',\n                                            fontWeight: '600'\n                                        }}>\n                                            📋 Informations de l'étudiant sélectionné\n                                        </h6>\n                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>\n                                            <div>\n                                                <strong style={{ color: '#6c757d' }}>Nom complet:</strong>\n                                                <div style={{ color: '#2c3e50', fontWeight: '500' }}>\n                                                    {selectedEtudiant.nom.toUpperCase()} {selectedEtudiant.prenom || ''}\n                                                </div>\n                                            </div>\n                                            <div>\n                                                <strong style={{ color: '#6c757d' }}>Email:</strong>\n                                                <div style={{ color: '#2c3e50' }}>\n                                                    {selectedEtudiant.email || 'Non renseigné'}\n                                                </div>\n                                            </div>\n                                            {selectedEtudiant.groupe_nom && (\n                                                <div>\n                                                    <strong style={{ color: '#6c757d' }}>Groupe:</strong>\n                                                    <div style={{ color: '#2c3e50' }}>\n                                                        {selectedEtudiant.groupe_nom}\n                                                    </div>\n                                                </div>\n                                            )}\n                                            {selectedEtudiant.classe_nom && (\n                                                <div>\n                                                    <strong style={{ color: '#6c757d' }}>Classe:</strong>\n                                                    <div style={{ color: '#2c3e50' }}>\n                                                        {selectedEtudiant.classe_nom}\n                                                    </div>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Devoir *</label>\n                                <select\n                                    value={formData.devoir_id}\n                                    onChange={(e) => setFormData({...formData, devoir_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                >\n                                    <option value=\"\">Sélectionner un devoir...</option>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <option key={devoir.id} value={devoir.id}>\n                                            {devoir.devoir_display}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label style={{\n                                    fontWeight: '600',\n                                    color: '#2c3e50',\n                                    marginBottom: '8px',\n                                    display: 'block'\n                                }}>\n                                    📖 Sélection de la matière *\n                                </label>\n                                <select\n                                    value={formData.matiere_id}\n                                    onChange={(e) => handleMatiereSelection(e.target.value)}\n                                    required\n                                    disabled={editingNote}\n                                    style={{\n                                        width: '100%',\n                                        padding: '12px',\n                                        border: '2px solid #e9ecef',\n                                        borderRadius: '8px',\n                                        fontSize: '14px',\n                                        backgroundColor: '#fff',\n                                        transition: 'border-color 0.3s ease',\n                                        cursor: 'pointer'\n                                    }}\n                                    onFocus={(e) => e.target.style.borderColor = '#28a745'}\n                                    onBlur={(e) => e.target.style.borderColor = '#e9ecef'}\n                                >\n                                    <option value=\"\" style={{ color: '#6c757d' }}>\n                                        🔍 Choisir une matière dans la liste...\n                                    </option>\n                                    {matieres.map((matiere) => (\n                                        <option\n                                            key={matiere.id}\n                                            value={matiere.id}\n                                            style={{ padding: '8px' }}\n                                        >\n                                            📚 {matiere.nom.toUpperCase()}\n                                            {matiere.filiere_nom ? ` | Filière: ${matiere.filiere_nom}` : ''}\n                                        </option>\n                                    ))}\n                                </select>\n                                {matieres.length === 0 && (\n                                    <div style={{\n                                        color: '#dc3545',\n                                        fontSize: '12px',\n                                        marginTop: '5px',\n                                        padding: '8px',\n                                        backgroundColor: '#f8d7da',\n                                        border: '1px solid #f5c6cb',\n                                        borderRadius: '4px'\n                                    }}>\n                                        ⚠️ Aucune matière disponible. Vérifiez votre connexion.\n                                    </div>\n                                )}\n\n                                {/* Affichage des informations détaillées de la matière sélectionnée */}\n                                {selectedMatiere && (\n                                    <div style={{\n                                        marginTop: '15px',\n                                        padding: '15px',\n                                        backgroundColor: '#f8f9fa',\n                                        border: '1px solid #dee2e6',\n                                        borderRadius: '8px',\n                                        borderLeft: '4px solid #28a745'\n                                    }}>\n                                        <h6 style={{\n                                            margin: '0 0 10px 0',\n                                            color: '#495057',\n                                            fontWeight: '600'\n                                        }}>\n                                            📚 Informations de la matière sélectionnée\n                                        </h6>\n                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>\n                                            <div>\n                                                <strong style={{ color: '#6c757d' }}>Matière:</strong>\n                                                <div style={{ color: '#2c3e50', fontWeight: '500' }}>\n                                                    {selectedMatiere.nom}\n                                                </div>\n                                            </div>\n                                            {selectedMatiere.filiere_nom && (\n                                                <div>\n                                                    <strong style={{ color: '#6c757d' }}>Filière:</strong>\n                                                    <div style={{ color: '#2c3e50' }}>\n                                                        {selectedMatiere.filiere_nom}\n                                                    </div>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Note (sur 20)</label>\n                                <input\n                                    type=\"number\"\n                                    min=\"0\"\n                                    max=\"20\"\n                                    step=\"0.01\"\n                                    value={formData.note}\n                                    onChange={(e) => setFormData({...formData, note: e.target.value})}\n                                    placeholder=\"Laisser vide pour calcul automatique\"\n                                />\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    Si vide, la note sera calculée automatiquement à partir des réponses aux quiz.\n                                </small>\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingNote ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingNote(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default NotesUnified;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SACIC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,gBAAgB,QACb,4BAA4B;AACnC,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGT,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC;IACjD6C,WAAW,EAAE;MAAEC,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAG,CAAC;IAC5CC,UAAU,EAAE;MAAEF,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAG,CAAC;IAC3CE,SAAS,EAAE;MAAEH,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAG,CAAC;IAC1CG,IAAI,EAAE;MAAEJ,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAG;EACvC,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC;IACrC6C,WAAW,EAAE,EAAE;IACfI,SAAS,EAAE,EAAE;IACbD,UAAU,EAAE,EAAE;IACdE,IAAI,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMG,UAAU,GAAG7C,SAAS,CAACG,IAAI,CAAC;EAClC,MAAM2C,YAAY,GAAG,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,IAAI,MAAK,YAAY,IAAI,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,IAAI,MAAK,YAAY;EAC/E,MAAMC,OAAO,GAAG,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,IAAI,MAAK,OAAO,IAAI,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,IAAI,MAAK,OAAO;;EAEhE;EACA,MAAME,SAAS,GAAGlD,aAAa,CAACI,IAAI,CAAC,CAAC,CAAC;EACvC,MAAM+C,OAAO,GAAGL,UAAU,IAAIC,YAAY,IAAIE,OAAO,CAAC,CAAC;;EAEvDvD,SAAS,CAAC,MAAM;IACZ,IAAIyD,OAAO,EAAE;MACTC,UAAU,CAAC,CAAC;MACZ,IAAIL,YAAY,EAAE;QACdM,uBAAuB,CAAC,CAAC;MAC7B;IACJ;EACJ,CAAC,EAAE,CAACF,OAAO,EAAEJ,YAAY,CAAC,CAAC;EAE3B,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;MAEzC;MACA,IAAIC,SAAS,GAAG,eAAe;MAC/B,IAAIV,UAAU,EAAEU,SAAS,GAAG,gBAAgB,CAAC,KACxC,IAAIT,YAAY,EAAES,SAAS,GAAG,kBAAkB,CAAC,KACjD,IAAIP,OAAO,EAAEO,SAAS,GAAG,aAAa;MAE3C,MAAMC,QAAQ,GAAG,MAAM7D,KAAK,CAAC8D,GAAG,CAAC,0DAA0D,EAAE;QACzFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,SAAS;QAAG;MACpD,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAClD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMC,SAAS,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CvD,QAAQ,CAACyD,SAAS,CAAC;;QAEnB;QACA,MAAMC,KAAK,GAAGC,mBAAmB,CAACF,SAAS,CAAC;QAC5CpC,aAAa,CAACqC,KAAK,CAAC;;QAEpB;QACA,IAAI,CAAClB,UAAU,EAAE;UACb,MAAMoB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;UACvF3C,WAAW,CAACqC,eAAe,CAAC;UAE5B,IAAIjB,OAAO,EAAE;YACT,MAAMwB,gBAAgB,GAAG,CAAC,GAAG,IAAIN,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACK,YAAY,CAAC,CAACH,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;YACzFzC,YAAY,CAAC0C,gBAAgB,CAAC;UAClC;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA;MACZtB,OAAO,CAACqB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAAClB,QAAQ,cAAAmB,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAChChF,IAAI,CAACiF,IAAI,CAAC,cAAc,EAAE,2DAA2D,EAAE,OAAO,CAAC;MACnG,CAAC,MAAM;QACHjF,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,iCAAiC,EAAE,OAAO,CAAC;MACnE;MACAxE,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNI,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM2C,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAEvD,MAAME,QAAQ,GAAG,MAAM7D,KAAK,CAAC8D,GAAG,CAAC,0EAA0E,EAAE;QACzGC,OAAO,EAAE;UAAEC,aAAa,EAAE;QAA0B;MACxD,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAChE,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBtD,qBAAqB,CAACiD,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACnD;IACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;MACZrB,OAAO,CAACqB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEnE,qBAAqB,CAAC,EAAE,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMyD,mBAAmB,GAAIF,SAAS,IAAK;IACvC,MAAMgB,KAAK,GAAGhB,SAAS,CAACiB,MAAM;IAC9B,MAAMC,gBAAgB,GAAGF,KAAK,GAAG,CAAC,GAC9BhB,SAAS,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEd,CAAC,KAAKc,GAAG,GAAGC,UAAU,CAACf,CAAC,CAAC1B,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGoC,KAAK,GAAG,CAAC;IAE9E,MAAMM,iBAAiB,GAAGtB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,IAAI,EAAE,CAAC,CAACqC,MAAM;IAChF,MAAMM,YAAY,GAAGvB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,IAAI,EAAE,IAAIyC,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACqC,MAAM;IACtG,MAAMO,cAAc,GAAGxB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,IAAI,EAAE,IAAIyC,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACqC,MAAM;IACxG,MAAMQ,aAAa,GAAGzB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAAC1B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACqC,MAAM;IAE3E,MAAMlD,SAAS,GAAG,CAAC,GAAG,IAAIqC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC/B,WAAW,CAAC,CAACiC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,MAAM5C,QAAQ,GAAG,CAAC,GAAG,IAAIuC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC5B,UAAU,CAAC,CAAC8B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAC/E,MAAMiB,OAAO,GAAG,CAAC,GAAG,IAAItB,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3B,SAAS,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAE7E,OAAO;MACHkB,WAAW,EAAEX,KAAK;MAClBE,gBAAgB,EAAEU,IAAI,CAACC,KAAK,CAACX,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;MAC1DI,iBAAiB;MACjBC,YAAY;MACZC,cAAc;MACdC,aAAa;MACbK,gBAAgB,EAAE/D,SAAS,CAACkD,MAAM;MAClCc,eAAe,EAAElE,QAAQ,CAACoD,MAAM;MAChCe,cAAc,EAAEN,OAAO,CAACT;IAC5B,CAAC;EACL,CAAC;EAED,MAAMgB,mBAAmB,GAAG,MAAOtD,SAAS,IAAK;IAC7C,MAAMuD,MAAM,GAAG,MAAMpG,IAAI,CAACiF,IAAI,CAAC;MAC3BoB,KAAK,EAAE,qCAAqC;MAC5CC,IAAI,EAAE,yEAAyE;MAC/EC,IAAI,EAAE,UAAU;MAChBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,cAAc;MACjCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACApD,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEb,SAAS,CAAC;QAE7E,MAAMe,QAAQ,GAAG,MAAM7D,KAAK,CAAC+G,IAAI,CAAC,0DAA0D,EAAE;UAC1FC,MAAM,EAAE,UAAU;UAClBlE,SAAS,EAAEA;QACf,CAAC,EAAE;UACCiB,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB,MAAM+C,OAAO,GAAGpD,QAAQ,CAACI,IAAI,CAACgD,OAAO;UACrChH,IAAI,CAACiF,IAAI,CAAC;YACNoB,KAAK,EAAE,kBAAkB;YACzBY,IAAI,EAAE;AAC9B,oEAAoED,OAAO,CAACE,cAAc;AAC1F,uEAAuEF,OAAO,CAACG,kBAAkB;AACjG,oEAAoEH,OAAO,CAACI,eAAe;AAC3F,yBAAyB;YACDb,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF/D,UAAU,CAAC,CAAC;UACZC,uBAAuB,CAAC,CAAC;QAC7B,CAAC,MAAM;UACHxD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,8BAA8B,EAAE,OAAO,CAAC;QACvF;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAyC,gBAAA,EAAAC,qBAAA;QACZ/D,OAAO,CAACqB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C9E,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,EAAAsC,gBAAA,GAAAzC,KAAK,CAAClB,QAAQ,cAAA2D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvD,IAAI,cAAAwD,qBAAA,uBAApBA,qBAAA,CAAsB1C,KAAK,KAAI,wCAAwC,EAAE,OAAO,CAAC;MACzG;IACJ;EACJ,CAAC;EAED,MAAM2C,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtE,SAAS,EAAE;MACZrD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,wDAAwD,EAAE,OAAO,CAAC;MACtF;IACJ;IAEA,IAAI,CAAClC,QAAQ,CAACN,WAAW,IAAI,CAACM,QAAQ,CAACF,SAAS,IAAI,CAACE,QAAQ,CAACH,UAAU,EAAE;MACtE5C,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,+CAA+C,EAAE,OAAO,CAAC;MAC7E;IACJ;IAEA,IAAI;MACA,MAAM2C,GAAG,GAAG,0DAA0D;MACtE,MAAMC,MAAM,GAAG3G,WAAW,GAAG,KAAK,GAAG,MAAM;MAC3C,MAAM8C,IAAI,GAAG9C,WAAW,GAAG;QAAE,GAAG6B,QAAQ;QAAE+E,EAAE,EAAE5G,WAAW,CAAC4G;MAAG,CAAC,GAAG/E,QAAQ;MAEzEU,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAEmE,MAAM;QAAE7D;MAAK,CAAC,CAAC;MAE/C,MAAMJ,QAAQ,GAAG,MAAM7D,KAAK,CAAC;QACzB8H,MAAM;QACND,GAAG;QACH5D,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,yBAAyB;UACxC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAE7C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBjE,IAAI,CAACiF,IAAI,CAAC;UACNoB,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAE1C,QAAQ,CAACI,IAAI,CAACrB,OAAO;UAC3B4D,IAAI,EAAE,SAAS;UACfc,KAAK,EAAE,IAAI;UACXC,iBAAiB,EAAE;QACvB,CAAC,CAAC;QACFvG,YAAY,CAAC,KAAK,CAAC;QACnBI,cAAc,CAAC,IAAI,CAAC;QACpB4G,SAAS,CAAC,CAAC;QACXxE,UAAU,CAAC,CAAC;QACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;MAC/C,CAAC,MAAM;QACHxD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,yBAAyB,EAAE,OAAO,CAAC;MAClF;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAkD,gBAAA,EAAAC,qBAAA;MACZxE,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9E,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,EAAA+C,gBAAA,GAAAlD,KAAK,CAAClB,QAAQ,cAAAoE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsBnD,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMoD,UAAU,GAAIpF,IAAI,IAAK;IACzB,IAAI,CAACO,SAAS,EAAE;MACZrD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,kDAAkD,EAAE,OAAO,CAAC;MAChF;IACJ;IAEA9D,cAAc,CAAC2B,IAAI,CAAC;IACpBE,WAAW,CAAC;MACRP,WAAW,EAAEK,IAAI,CAACL,WAAW,IAAI,EAAE;MACnCI,SAAS,EAAEC,IAAI,CAACD,SAAS,IAAI,EAAE;MAC/BD,UAAU,EAAEE,IAAI,CAACF,UAAU,IAAI,EAAE;MACjCE,IAAI,EAAEA,IAAI,CAACA,IAAI,IAAI;IACvB,CAAC,CAAC;IACF/B,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoH,YAAY,GAAG,MAAOL,EAAE,IAAK;IAC/B,IAAI,CAACzE,SAAS,EAAE;MACZrD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA,MAAMmB,MAAM,GAAG,MAAMpG,IAAI,CAACiF,IAAI,CAAC;MAC3BoB,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,gBAAgB;MACnCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACApD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoE,EAAE,CAAC;QAE3C,MAAMlE,QAAQ,GAAG,MAAM7D,KAAK,CAACqI,MAAM,CAAC,0DAA0D,EAAE;UAC5FtE,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAE;YAAE8D;UAAG;QACf,CAAC,CAAC;QAEFrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvBjE,IAAI,CAACiF,IAAI,CAAC;YACNoB,KAAK,EAAE,YAAY;YACnBC,IAAI,EAAE1C,QAAQ,CAACI,IAAI,CAACrB,OAAO;YAC3B4D,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF/D,UAAU,CAAC,CAAC;UACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;QAC/C,CAAC,MAAM;UACHxD,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,iCAAiC,EAAE,OAAO,CAAC;QAC1F;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAuD,gBAAA,EAAAC,qBAAA;QACZ7E,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C9E,IAAI,CAACiF,IAAI,CAAC,QAAQ,EAAE,EAAAoD,gBAAA,GAAAvD,KAAK,CAAClB,QAAQ,cAAAyE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrE,IAAI,cAAAsE,qBAAA,uBAApBA,qBAAA,CAAsBxD,KAAK,KAAI,iCAAiC,EAAE,OAAO,CAAC;MAClG;IACJ;EACJ,CAAC;EAED,MAAMiD,SAAS,GAAGA,CAAA,KAAM;IACpB/E,WAAW,CAAC;MACRP,WAAW,EAAE,EAAE;MACfI,SAAS,EAAE,EAAE;MACbD,UAAU,EAAE,EAAE;MACdE,IAAI,EAAE;IACV,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;IACzBE,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMiG,uBAAuB,GAAIC,UAAU,IAAK;IAC5C,MAAMC,QAAQ,GAAGxG,SAAS,CAACyG,IAAI,CAAChB,CAAC,IAAI,CAACA,CAAC,CAACI,EAAE,IAAIJ,CAAC,CAACjF,WAAW,MAAMkG,QAAQ,CAACH,UAAU,CAAC,CAAC;IACtFpG,mBAAmB,CAACqG,QAAQ,IAAI,IAAI,CAAC;IACrCzF,WAAW,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnG,WAAW,EAAE+F;IAAW,CAAC,CAAC,CAAC;;IAE3D;IACAhG,iBAAiB,CAACoG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPnG,WAAW,EAAE;QACTC,OAAO,EAAE,CAAC,CAAC8F,UAAU,IAAI,CAAC,CAACC,QAAQ;QACnC9F,OAAO,EAAE6F,UAAU,GAAIC,QAAQ,GAAG,wBAAwB,GAAG,uBAAuB,GAAI;MAC5F;IACJ,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMI,sBAAsB,GAAIC,SAAS,IAAK;IAC1C,MAAMC,OAAO,GAAGhH,QAAQ,CAAC2G,IAAI,CAACM,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKa,QAAQ,CAACG,SAAS,CAAC,CAAC;IAChExG,kBAAkB,CAACyG,OAAO,IAAI,IAAI,CAAC;IACnC/F,WAAW,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhG,UAAU,EAAEkG;IAAU,CAAC,CAAC,CAAC;;IAEzD;IACAtG,iBAAiB,CAACoG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPhG,UAAU,EAAE;QACRF,OAAO,EAAE,CAAC,CAACoG,SAAS,IAAI,CAAC,CAACC,OAAO;QACjCpG,OAAO,EAAEmG,SAAS,GAAIC,OAAO,GAAG,wBAAwB,GAAG,uBAAuB,GAAI;MAC1F;IACJ,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIC,SAAS,IAAK;IACpClG,WAAW,CAAC4F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9F,IAAI,EAAEoG;IAAU,CAAC,CAAC,CAAC;IAEnD,MAAMpG,IAAI,GAAGyC,UAAU,CAAC2D,SAAS,CAAC;IAClC,IAAIC,UAAU,GAAG;MAAEzG,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAG,CAAC;IAE/C,IAAIuG,SAAS,KAAKE,KAAK,CAACtG,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,EAAE,CAAC,EAAE;MACrDqG,UAAU,GAAG;QACTzG,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM,IAAIuG,SAAS,IAAIpG,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,EAAE,EAAE;MAC7CqG,UAAU,GAAG;QACTzG,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM;MACHwG,UAAU,GAAG;QACTzG,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACb,CAAC;IACL;IAEAH,iBAAiB,CAACoG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP9F,IAAI,EAAEqG;IACV,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGnJ,WAAW,CAACM,KAAK,EAAED,IAAI,CAAC;;EAEtD;EACA,IAAIH,SAAS,CAACG,IAAI,CAAC,IAAI8I,qBAAqB,CAAClE,MAAM,KAAK3E,KAAK,CAAC2E,MAAM,EAAE;IAClE9E,gBAAgB,CAAC,sBAAsB,EAAEE,IAAI,EAAE;MAC3C2E,KAAK,EAAE1E,KAAK,CAAC2E,MAAM;MACnBmE,QAAQ,EAAED,qBAAqB,CAAClE;IACpC,CAAC,CAAC;EACN;;EAEA;EACA,MAAMoE,aAAa,GAAGF,qBAAqB,CAAC3E,MAAM,CAAC5B,IAAI,IAAI;IACvD,MAAM0G,WAAW,GAAGpI,UAAU,CAACqI,WAAW,CAAC,CAAC;IAC5C,MAAMC,aAAa,GACf,CAAC5G,IAAI,CAAC+B,YAAY,IAAI,EAAE,EAAE4E,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAAC1G,IAAI,CAAC8G,YAAY,IAAI,EAAE,EAAEH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAAC1G,IAAI,CAAC2B,WAAW,IAAI,EAAE,EAAEgF,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC5D,CAAC1G,IAAI,CAACA,IAAI,IAAI,EAAE,EAAE+G,QAAQ,CAAC,CAAC,CAACF,QAAQ,CAACH,WAAW,CACpD;IAED,MAAMM,cAAc,GAAGxI,aAAa,KAAK,KAAK,IAAIwB,IAAI,CAAC2B,WAAW,KAAKnD,aAAa;IACpF,MAAMyI,eAAe,GAAGvI,cAAc,KAAK,KAAK,IAAIsB,IAAI,CAAC+B,YAAY,KAAKrD,cAAc;IAExF,OAAOkI,aAAa,IAAII,cAAc,IAAIC,eAAe;EAC7D,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGtI,WAAW,GAAGE,YAAY;EAClD,MAAMqI,gBAAgB,GAAGD,eAAe,GAAGpI,YAAY;EACvD,MAAMsI,YAAY,GAAGX,aAAa,CAACY,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGtE,IAAI,CAACuE,IAAI,CAACd,aAAa,CAACpE,MAAM,GAAGvD,YAAY,CAAC;EAEjE,MAAM0I,QAAQ,GAAIC,UAAU,IAAK5I,cAAc,CAAC4I,UAAU,CAAC;;EAE3D;EACA5K,KAAK,CAACE,SAAS,CAAC,MAAM;IAClB8B,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACP,UAAU,EAAEE,aAAa,EAAEE,cAAc,CAAC,CAAC;EAE/C,MAAMgJ,YAAY,GAAI1H,IAAI,IAAK;IAC3B,MAAMoG,SAAS,GAAG3D,UAAU,CAACzC,IAAI,CAAC;IAClC,IAAI2H,KAAK,GAAG;MACRC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB,CAAC;IAED,IAAI3B,SAAS,IAAI,EAAE,EAAE;MACjBuB,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAOpL,KAAA,CAAAqL,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACxI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIoG,SAAS,IAAI,EAAE,EAAE;MACxBuB,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAOpL,KAAA,CAAAqL,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACxI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIoG,SAAS,IAAI,EAAE,EAAE;MACxBuB,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAOpL,KAAA,CAAAqL,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACxI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM;MACH2H,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAOpL,KAAA,CAAAqL,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACxI,IAAI,EAAC,KAAS,CAAC;IAClD;EACJ,CAAC;EAED,MAAMyI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAItI,UAAU,EAAE,OAAO,cAAc;IACrC,IAAIC,YAAY,EAAE,OAAO,yBAAyB;IAClD,IAAIE,OAAO,EAAE,OAAO,4BAA4B;IAChD,OAAO,UAAU;EACrB,CAAC;EAED,MAAMoI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIvI,UAAU,EAAE;MACZ,OAAO;QACHwH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFzE,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAIpD,YAAY,EAAE;MACrB,OAAO;QACHuH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFzE,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAIlD,OAAO,EAAE;MAChB,OAAO;QACHqH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFzE,IAAI,EAAE;MACV,CAAC;IACL;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMoF,MAAM,GAAG;IACXC,YAAY,EAAE;MACVC,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,WAAW;MACpBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBkB,MAAM,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACLhB,eAAe,EAAE1H,OAAO,GAAG,SAAS,GAAG,SAAS;MAChD2H,KAAK,EAAE,OAAO;MACdL,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB;EACJ,CAAC;;EAED;EACA,IAAI,CAACvH,OAAO,EAAE;IACV,oBACI3D,KAAA,CAAAqL,aAAA;MAAKe,SAAS,EAAC,oBAAoB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/B3L,KAAA,CAAAqL,aAAA;MAAKP,KAAK,EAAEiB,MAAM,CAACC,YAAa;MAAAV,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC5B3L,KAAA,CAAAqL,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAAmB,CAAC,eACxB3L,KAAA,CAAAqL,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4DAA0D,CAC5D,CACJ,CAAC;EAEd;EAEA,IAAI1K,OAAO,EAAE;IACT,oBACIjB,KAAA,CAAAqL,aAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9B3L,KAAA,CAAAqL,aAAA;MAAKe,SAAS,EAAC,SAAS;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/B3L,KAAA,CAAAqL,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,yBAA0B,CAC5B,CAAC;EAEd;EAEA,MAAMU,WAAW,GAAGR,cAAc,CAAC,CAAC;EAEpC,oBACI7L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,oBAAoB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKC,cAAc,CAAC,CAAM,CAAC,eAC3B5L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3L,KAAA,CAAAqL,aAAA;IAAMe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB/B,aAAa,CAACpE,MAAM,EAAC,wBACpB,CAAC,EACN9B,SAAS,iBACN1D,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAMhL,oBAAoB,CAAC,IAAI,CAAE;IAC1CwJ,KAAK,EAAE;MAAEyB,WAAW,EAAE;IAAO,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/B3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,eAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,0BACjC,CAAC,eACT3L,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAMlL,YAAY,CAAC,IAAI,CAAE;IAAAkK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElC3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,kBACjC,CACP,CAER,CACJ,CAAC,EAGLU,WAAW,iBACRrM,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACR,GAAGuB,WAAW,CAACvB,KAAK;MACpBE,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAI,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEU,WAAW,CAAC1F,IAAQ,CAC/C,CACR,EAGAzE,UAAU,CAACgE,WAAW,GAAG,CAAC,iBACvBlG,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACR4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAElJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GmJ,GAAG,EAAE,MAAM;MACXzB,eAAe,EAAE1H,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS;MACzEwI,MAAM,EAAE,aAAarI,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;MAC/E0H,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE3H,OAAO,GAAG,SAAS,GAAG;IAAU,CAAE;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxFzJ,UAAU,CAACgE,WACX,CAAC,eACNlG,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9CrI,UAAU,GAAG,WAAW,GAAG,aAC3B,CACJ,CAAC,eAENtD,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAACuD,gBACX,CAAC,eACNzF,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CAAC,EAEL,CAACrI,UAAU,iBACRtD,KAAA,CAAAqL,aAAA,CAAArL,KAAA,CAAA8M,QAAA,qBACI9M,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAAC2D,iBACX,CAAC,eACN7F,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAAsB,CACzE,CAAC,eAEN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAAC6D,cACX,CAAC,eACN/F,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAqB,CACxE,CAAC,eAEN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAAC8D,aACX,CAAC,eACNhG,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAqB,CACxE,CAAC,eAEN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAACmE,gBACX,CAAC,eACNrG,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAc,CACjE,CAAC,eAEN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEzJ,UAAU,CAACqE,cACX,CAAC,eACNvG,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CACP,CAEL,CACR,eAGD3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,YAAY;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1C3L,KAAA,CAAAqL,aAAA;IACI0B,IAAI,EAAC,MAAM;IACXC,WAAW,EAAE1J,UAAU,GAAG,8BAA8B,GAAG,mDAAoD;IAC/G2J,KAAK,EAAExL,UAAW;IAClByL,QAAQ,EAAGnF,CAAC,IAAKrG,aAAa,CAACqG,CAAC,CAACoF,MAAM,CAACF,KAAK,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClD,CACA,CAAC,EAEL,CAACrI,UAAU,iBACRtD,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAACtB,KAAK,EAAE;MACnC4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAElJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GmJ,GAAG,EAAE,MAAM;MACXQ,SAAS,EAAE;IACf,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IACI4B,KAAK,EAAEtL,aAAc;IACrBuL,QAAQ,EAAGnF,CAAC,IAAKnG,gBAAgB,CAACmG,CAAC,CAACoF,MAAM,CAACF,KAAK,CAAE;IAClDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB3L,KAAA,CAAAqL,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qCAA8B,CAAC,EAClDvJ,QAAQ,CAACwC,GAAG,CAACwE,OAAO,iBACjBpJ,KAAA,CAAAqL,aAAA;IAAQgC,GAAG,EAAEjE,OAAQ;IAAC6D,KAAK,EAAE7D,OAAQ;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvC,OAAgB,CAC1D,CACG,CAAC,EAER3F,OAAO,iBACJzD,KAAA,CAAAqL,aAAA;IACI4B,KAAK,EAAEpL,cAAe;IACtBqL,QAAQ,EAAGnF,CAAC,IAAKjG,iBAAiB,CAACiG,CAAC,CAACoF,MAAM,CAACF,KAAK,CAAE;IACnDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB3L,KAAA,CAAAqL,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oCAA6B,CAAC,EACjDrJ,SAAS,CAACsC,GAAG,CAACkE,QAAQ,iBACnB9I,KAAA,CAAAqL,aAAA;IAAQgC,GAAG,EAAEvE,QAAS;IAACmE,KAAK,EAAEnE,QAAS;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7C,QAAiB,CAC7D,CACG,CAEX,CAER,CAAC,eAGN9I,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B/B,aAAa,CAACpE,MAAM,KAAK,CAAC,gBACvBxF,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,SAAS;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,kBAAe;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5C3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,wBAAsB,CAAC,eAC1B3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIrI,UAAU,GAAG,sCAAsC,GAAG,+CAAmD,CAC5G,CAAC,gBAENtD,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B3L,KAAA,CAAAqL,aAAA;IAAOe,SAAS,EAAC,OAAO;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,EACb,CAACrI,UAAU,iBAAItD,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpC3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eACnB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,EACflI,OAAO,iBAAIzD,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EAC7BjI,SAAS,iBAAI1D,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAChC,CACD,CAAC,eACR3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKpB,YAAY,CAAC3F,GAAG,CAAEzB,IAAI,iBACnBnD,KAAA,CAAAqL,aAAA;IAAIgC,GAAG,EAAElK,IAAI,CAACgF,EAAG;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACb3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAMP,KAAK,EAAEiB,MAAM,CAACI,OAAQ;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACxB,EAACxI,IAAI,CAACgF,EACL,CACN,CAAC,EACJ,CAAC7E,UAAU,iBACRtD,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCxI,IAAI,CAAC+B,YAAY,IAAI,oBAClB,CAAC,eACTlF,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACN3L,KAAA,CAAAqL,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDxI,IAAI,CAACmK,cAAc,IAAI,sBACrB,CACN,CACL,CACP,eACDtN,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEyC,QAAQ,EAAE;IAAQ,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCxI,IAAI,CAAC8G,YAAY,IAAI,qBAClB,CAAC,EACR9G,IAAI,CAACqK,WAAW,iBACbxN,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UACzC,EAACxI,IAAI,CAACqK,WACb,CAER,CACL,CAAC,eACLxN,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGxI,IAAI,CAAC2B,WAAW,IAAI,eACnB,CACN,CAAC,eACL9E,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKd,YAAY,CAAC1H,IAAI,CAACA,IAAI,CACvB,CAAC,eACLnD,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAMP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BxI,IAAI,CAACsK,cAAc,IAAItK,IAAI,CAACuK,mBAC3B,CACN,CAAC,EACJjK,OAAO,iBACJzD,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGxI,IAAI,CAACwK,UAAU,IAAI,eAClB,CACN,CACP,EACAjK,SAAS,iBACN1D,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B3L,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,wBAAwB;IAClCE,OAAO,EAAEA,CAAA,KAAM/D,UAAU,CAACpF,IAAI,CAAE;IAChCuD,KAAK,EAAC,kBAAkB;IAAA4E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExB3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACT3L,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,uBAAuB;IACjCE,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAACrF,IAAI,CAACgF,EAAE,CAAE;IACrCzB,KAAK,EAAC,mBAAmB;IAAA4E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLlB,UAAU,GAAG,CAAC,iBACXzK,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC5I,WAAW,GAAG,CAAC,CAAE;IACzC6L,QAAQ,EAAE7L,WAAW,KAAK,CAAE;IAC5BqK,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,iBAEO,CAAC,eAET3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAClB,EAAC5J,WAAW,EAAC,OAAK,EAAC0I,UACvB,CAAC,eAENzK,KAAA,CAAAqL,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC5I,WAAW,GAAG,CAAC,CAAE;IACzC6L,QAAQ,EAAE7L,WAAW,KAAK0I,UAAW;IACrC2B,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,SAEO,CACP,CACR,EAGAtK,iBAAiB,IAAIqC,SAAS,iBAC3B1D,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qDAAuC,CAAC,eAC5C3L,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAMhL,oBAAoB,CAAC,KAAK,CAAE;IAAAgK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3C3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B3L,KAAA,CAAAqL,aAAA;IAAGP,KAAK,EAAE;MAAE+C,YAAY,EAAE,MAAM;MAAEzC,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8GAEnD,CAAC,EAEH5K,kBAAkB,CAACyE,MAAM,KAAK,CAAC,gBAC5BxF,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEmB,SAAS,EAAE,QAAQ;MAAElB,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjD3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4DAAuD,CACzD,CAAC,gBAEN3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEgD,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjD5K,kBAAkB,CAAC6D,GAAG,CAACoJ,MAAM,iBAC1BhO,KAAA,CAAAqL,aAAA;IAAKgC,GAAG,EAAEW,MAAM,CAAC7F,EAAG;IAAC2C,KAAK,EAAE;MACxBgB,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACf8C,YAAY,EAAE,MAAM;MACpB1C,eAAe,EAAE6C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IAC7D,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAE4B,OAAO,EAAE,MAAM;MAAEwB,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnF3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAIP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAY,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEqC,MAAM,CAACI,KAAU,CAAC,eACvDpO,KAAA,CAAAqL,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE,GAAG;MAAEjB,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DqC,MAAM,CAAClJ,WAAW,EAAC,KAAG,EAACkJ,MAAM,CAACL,UAChC,CAAC,eACJ3N,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE,SAAS;MAAEgC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAChE,EAACqC,MAAM,CAACK,WAAW,EAAC,4BAAW,EAACL,MAAM,CAACM,yBAAyB,EAAC,mCAAe,EAACN,MAAM,CAACO,gBAAgB,EAAC,QAC3G,CAAC,eACNvO,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEmC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChD3L,KAAA,CAAAqL,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,KAAK;MACnBG,eAAe,EAAE6C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG,SAAS;MAClE7C,KAAK,EAAE4C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IACnD,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGqC,MAAM,CAACQ,MACN,CACL,CACJ,CAAC,eACNxO,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAM;MACXhL,oBAAoB,CAAC,KAAK,CAAC;MAC3BkF,mBAAmB,CAACwH,MAAM,CAAC7F,EAAE,CAAC;IAClC,CAAE;IACFyF,QAAQ,EAAE,CAACI,MAAM,CAACC,kBAAmB;IACrCvH,KAAK,EAAEsH,MAAM,CAACC,kBAAkB,GAAG,mBAAmB,GAAG,uBAAwB;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpF,4BAEO,CACP,CACJ,CACR,CACA,CAER,CACJ,CACJ,CACR,EAGAxK,SAAS,IAAIuC,SAAS,iBACnB1D,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpK,WAAW,GAAG,kBAAkB,GAAG,eAAoB,CAAC,eAC7DvB,KAAA,CAAAqL,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAM;MACXlL,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpB4G,SAAS,CAAC,CAAC;IACf,CAAE;IAAAkD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3L,KAAA,CAAAqL,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACN3L,KAAA,CAAAqL,aAAA;IAAMoD,QAAQ,EAAE3G,YAAa;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IAAOP,KAAK,EAAE;MACVI,UAAU,EAAE,KAAK;MACjBE,KAAK,EAAE,SAAS;MAChByC,YAAY,EAAE,KAAK;MACnBnB,OAAO,EAAE;IACb,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8CAEI,CAAC,eACR3L,KAAA,CAAAqL,aAAA;IACI4B,KAAK,EAAE7J,QAAQ,CAACN,WAAY;IAC5BoK,QAAQ,EAAGnF,CAAC,IAAKa,uBAAuB,CAACb,CAAC,CAACoF,MAAM,CAACF,KAAK,CAAE;IACzDyB,QAAQ;IACRd,QAAQ,EAAErM,WAAY;IACtBuJ,KAAK,EAAE;MACH6D,KAAK,EAAE,MAAM;MACb5D,OAAO,EAAE,MAAM;MACfe,MAAM,EAAE,aAAalJ,cAAc,CAACE,WAAW,CAACC,OAAO,IAAIK,QAAQ,CAACN,WAAW,GAAG,SAAS,GAAGM,QAAQ,CAACN,WAAW,IAAI,CAACF,cAAc,CAACE,WAAW,CAACC,OAAO,GAAG,SAAS,GAAG,SAAS,EAAE;MACnLiI,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChBE,eAAe,EAAE,MAAM;MACvByD,UAAU,EAAE,wBAAwB;MACpCC,MAAM,EAAE;IACZ,CAAE;IACFC,OAAO,EAAG/G,CAAC,IAAKA,CAAC,CAACoF,MAAM,CAACrC,KAAK,CAACiE,WAAW,GAAG,SAAU;IACvDC,MAAM,EAAGjH,CAAC,IAAKA,CAAC,CAACoF,MAAM,CAACrC,KAAK,CAACiE,WAAW,GAAG,SAAU;IAAAzD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtD3L,KAAA,CAAAqL,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAACnC,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sDAEtC,CAAC,EACRrJ,SAAS,CAACsC,GAAG,CAAEkE,QAAQ,iBACpB9I,KAAA,CAAAqL,aAAA;IACIgC,GAAG,EAAEvE,QAAQ,CAACX,EAAE,IAAIW,QAAQ,CAAChG,WAAY;IACzCmK,KAAK,EAAEnE,QAAQ,CAACX,EAAE,IAAIW,QAAQ,CAAChG,WAAY;IAC3CgI,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,eACM,EAAC7C,QAAQ,CAACmG,GAAG,CAACC,WAAW,CAAC,CAAC,EAAC,GAAC,EAACpG,QAAQ,CAACqG,MAAM,GAAGrG,QAAQ,CAACqG,MAAM,GAAG,EAAE,EACtErG,QAAQ,CAACsG,UAAU,GAAG,cAActG,QAAQ,CAACsG,UAAU,EAAE,GAAG,EAAE,EAC9DtG,QAAQ,CAAC6E,UAAU,GAAG,cAAc7E,QAAQ,CAAC6E,UAAU,EAAE,GAAG,EACzD,CACX,CACG,CAAC,EACRrL,SAAS,CAACkD,MAAM,KAAK,CAAC,iBACnBxF,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRM,KAAK,EAAE,SAAS;MAChBH,QAAQ,EAAE,MAAM;MAChBmC,SAAS,EAAE,KAAK;MAChBrC,OAAO,EAAE,KAAK;MACdI,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE;IAClB,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yEAEE,CACR,EAGAnJ,gBAAgB,iBACbxC,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRsC,SAAS,EAAE,MAAM;MACjBrC,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBqE,UAAU,EAAE;IAChB,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAIP,KAAK,EAAE;MACPoB,MAAM,EAAE,YAAY;MACpBd,KAAK,EAAE,SAAS;MAChBF,UAAU,EAAE;IAChB,CAAE;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8DAEC,CAAC,eACL3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAE4B,OAAO,EAAE,MAAM;MAAEC,mBAAmB,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzE3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAoB,CAAC,eAC1D3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEF,UAAU,EAAE;IAAM,CAAE;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/CnJ,gBAAgB,CAACyM,GAAG,CAACC,WAAW,CAAC,CAAC,EAAC,GAAC,EAAC1M,gBAAgB,CAAC2M,MAAM,IAAI,EAChE,CACJ,CAAC,eACNnP,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAc,CAAC,eACpD3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5BnJ,gBAAgB,CAAC8M,KAAK,IAAI,eAC1B,CACJ,CAAC,EACL9M,gBAAgB,CAAC4M,UAAU,iBACxBpP,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAe,CAAC,eACrD3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5BnJ,gBAAgB,CAAC4M,UACjB,CACJ,CACR,EACA5M,gBAAgB,CAACmL,UAAU,iBACxB3N,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAe,CAAC,eACrD3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5BnJ,gBAAgB,CAACmL,UACjB,CACJ,CAER,CACJ,CAER,CAAC,eAEN3N,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,UAAe,CAAC,eACvB3L,KAAA,CAAAqL,aAAA;IACI4B,KAAK,EAAE7J,QAAQ,CAACF,SAAU;IAC1BgK,QAAQ,EAAGnF,CAAC,IAAK1E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEF,SAAS,EAAE6E,CAAC,CAACoF,MAAM,CAACF;IAAK,CAAC,CAAE;IACvEyB,QAAQ;IACRd,QAAQ,EAAErM,WAAY;IAAA+J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtB3L,KAAA,CAAAqL,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAAiC,CAAC,EAClD5K,kBAAkB,CAAC6D,GAAG,CAACoJ,MAAM,iBAC1BhO,KAAA,CAAAqL,aAAA;IAAQgC,GAAG,EAAEW,MAAM,CAAC7F,EAAG;IAAC8E,KAAK,EAAEe,MAAM,CAAC7F,EAAG;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCqC,MAAM,CAACuB,cACJ,CACX,CACG,CACP,CAAC,eAENvP,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IAAOP,KAAK,EAAE;MACVI,UAAU,EAAE,KAAK;MACjBE,KAAK,EAAE,SAAS;MAChByC,YAAY,EAAE,KAAK;MACnBnB,OAAO,EAAE;IACb,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8CAEI,CAAC,eACR3L,KAAA,CAAAqL,aAAA;IACI4B,KAAK,EAAE7J,QAAQ,CAACH,UAAW;IAC3BiK,QAAQ,EAAGnF,CAAC,IAAKmB,sBAAsB,CAACnB,CAAC,CAACoF,MAAM,CAACF,KAAK,CAAE;IACxDyB,QAAQ;IACRd,QAAQ,EAAErM,WAAY;IACtBuJ,KAAK,EAAE;MACH6D,KAAK,EAAE,MAAM;MACb5D,OAAO,EAAE,MAAM;MACfe,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChBE,eAAe,EAAE,MAAM;MACvByD,UAAU,EAAE,wBAAwB;MACpCC,MAAM,EAAE;IACZ,CAAE;IACFC,OAAO,EAAG/G,CAAC,IAAKA,CAAC,CAACoF,MAAM,CAACrC,KAAK,CAACiE,WAAW,GAAG,SAAU;IACvDC,MAAM,EAAGjH,CAAC,IAAKA,CAAC,CAACoF,MAAM,CAACrC,KAAK,CAACiE,WAAW,GAAG,SAAU;IAAAzD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtD3L,KAAA,CAAAqL,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAACnC,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sDAEtC,CAAC,EACRvJ,QAAQ,CAACwC,GAAG,CAAEwE,OAAO,iBAClBpJ,KAAA,CAAAqL,aAAA;IACIgC,GAAG,EAAEjE,OAAO,CAACjB,EAAG;IAChB8E,KAAK,EAAE7D,OAAO,CAACjB,EAAG;IAClB2C,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,eACM,EAACvC,OAAO,CAAC6F,GAAG,CAACC,WAAW,CAAC,CAAC,EAC5B9F,OAAO,CAACoG,WAAW,GAAG,eAAepG,OAAO,CAACoG,WAAW,EAAE,GAAG,EAC1D,CACX,CACG,CAAC,EACRpN,QAAQ,CAACoD,MAAM,KAAK,CAAC,iBAClBxF,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRM,KAAK,EAAE,SAAS;MAChBH,QAAQ,EAAE,MAAM;MAChBmC,SAAS,EAAE,KAAK;MAChBrC,OAAO,EAAE,KAAK;MACdI,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE;IAClB,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yEAEE,CACR,EAGAjJ,eAAe,iBACZ1C,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MACRsC,SAAS,EAAE,MAAM;MACjBrC,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBqE,UAAU,EAAE;IAChB,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3L,KAAA,CAAAqL,aAAA;IAAIP,KAAK,EAAE;MACPoB,MAAM,EAAE,YAAY;MACpBd,KAAK,EAAE,SAAS;MAChBF,UAAU,EAAE;IAChB,CAAE;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAEC,CAAC,eACL3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAE4B,OAAO,EAAE,MAAM;MAAEC,mBAAmB,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzE3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAgB,CAAC,eACtD3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEF,UAAU,EAAE;IAAM,CAAE;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/CjJ,eAAe,CAACuM,GAChB,CACJ,CAAC,EACLvM,eAAe,CAAC8M,WAAW,iBACxBxP,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3L,KAAA,CAAAqL,aAAA;IAAQP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAgB,CAAC,eACtD3L,KAAA,CAAAqL,aAAA;IAAKP,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5BjJ,eAAe,CAAC8M,WAChB,CACJ,CAER,CACJ,CAER,CAAC,eAENxP,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3L,KAAA,CAAAqL,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAoB,CAAC,eAC5B3L,KAAA,CAAAqL,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACb0C,GAAG,EAAC,GAAG;IACPC,GAAG,EAAC,IAAI;IACRC,IAAI,EAAC,MAAM;IACX1C,KAAK,EAAE7J,QAAQ,CAACD,IAAK;IACrB+J,QAAQ,EAAGnF,CAAC,IAAK1E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAED,IAAI,EAAE4E,CAAC,CAACoF,MAAM,CAACF;IAAK,CAAC,CAAE;IAClED,WAAW,EAAC,sCAAsC;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrD,CAAC,eACF3L,KAAA,CAAAqL,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAO,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yFAE/C,CACN,CAAC,eAEN3L,KAAA,CAAAqL,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3L,KAAA,CAAAqL,aAAA;IAAQ0B,IAAI,EAAC,QAAQ;IAACX,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5CpK,WAAW,GAAG,aAAa,GAAG,SAC3B,CAAC,eACTvB,KAAA,CAAAqL,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACbX,SAAS,EAAC,mBAAmB;IAC7BE,OAAO,EAAEA,CAAA,KAAM;MACXlL,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpB4G,SAAS,CAAC,CAAC;IACf,CAAE;IAAAkD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAehL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}