<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Filiere API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Filiere API - Headers: " . json_encode(getallheaders()));
error_log("Filiere API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        echo json_encode(['error' => 'Filière name required']);
        exit;
    }
    $nom = trim($data['nom']);
    try {
        $stmt = $pdo->prepare("INSERT INTO Filieres (nom) VALUES (:nom)");
        $stmt->execute(['nom' => $nom]);
        echo json_encode(['success' => true, 'message' => 'Filière added successfully', 'id' => $pdo->lastInsertId()]);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("SELECT * FROM Filieres ORDER BY id DESC");
        $filieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($filieres);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);
    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom']))) {
        echo json_encode(['error' => 'Filière id and name required']);
        exit;
    }
    $id = intval($data['id']);
    $nom = trim($data['nom']);
    try {
        $stmt = $pdo->prepare("UPDATE Filieres SET nom = :nom WHERE id = :id");
        $stmt->execute(['nom' => $nom, 'id' => $id]);
        echo json_encode(['success' => true, 'message' => 'Filière updated successfully']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    if (!isset($data['id'])) {
        echo json_encode(['error' => 'Filière id required']);
        exit;
    }
    $id = intval($data['id']);
    try {
        $stmt = $pdo->prepare("DELETE FROM Filieres WHERE id = :id");
        $stmt->execute(['id' => $id]);
        echo json_encode(['success' => true, 'message' => 'Filière deleted successfully']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
