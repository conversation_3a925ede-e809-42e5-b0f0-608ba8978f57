<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>👨‍👩‍👧‍👦 SYSTÈME DE RELATIONS FAMILIALES - DÉMONSTRATION COMPLÈTE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .demo-section { background: white; border: 2px solid #007bff; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .test-button { color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; font-weight: bold; }
        .test-button:hover { color: white; text-decoration: none; transform: translateY(-2px); }
        .test-button.family { background: #007bff; }
        .test-button.family:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.success:hover { background: #218838; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0; }
        .feature-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .family-example { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 15px 0; border: 2px solid #2196f3; }
        .relation-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .relation-table th, .relation-table td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        .relation-table th { background: #007bff; color: white; }
        .legal { background: #fff3cd; color: #856404; }
        .priority { background: #e3f2fd; color: #1565c0; }
        .permission { background: #d4edda; color: #155724; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>👨‍👩‍👧‍👦 Système de Relations Familiales Flexibles</h2>";
    echo "<p>Interface complète pour gérer les relations parent-étudiant complexes :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Relations multiples</strong> : Un parent peut avoir plusieurs enfants</li>";
    echo "<li>✅ <strong>Responsables multiples</strong> : Un étudiant peut avoir plusieurs responsables</li>";
    echo "<li>✅ <strong>Types de liens</strong> : Père, Mère, Tuteur, Grand-parent, Oncle/Tante, Autre</li>";
    echo "<li>✅ <strong>Autorisations granulaires</strong> : Responsable légal, contact prioritaire, sorties, médical</li>";
    echo "<li>✅ <strong>Gestion flexible</strong> : Familles recomposées, tutelles, situations complexes</li>";
    echo "</ul>";
    echo "</div>";
    
    // Exemples de configurations familiales
    echo "<div class='step'>";
    echo "<h3>👪 Exemples de Configurations Familiales</h3>";
    
    echo "<div class='family-example'>";
    echo "<h4>🏠 Famille Traditionnelle - Famille Dupont</h4>";
    echo "<table class='relation-table'>";
    echo "<tr>";
    echo "<th>Parent</th>";
    echo "<th>Lien</th>";
    echo "<th>Enfant</th>";
    echo "<th>Responsable Légal</th>";
    echo "<th>Contact Prioritaire</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Jean Dupont</strong> (👨 Père)</td>";
    echo "<td>Père</td>";
    echo "<td>Marie Dupont (5ème A)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td class='priority'>✅ Oui</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Sophie Dupont</strong> (👩 Mère)</td>";
    echo "<td>Mère</td>";
    echo "<td>Marie Dupont (5ème A)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td>❌ Non</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Jean Dupont</strong> (👨 Père)</td>";
    echo "<td>Père</td>";
    echo "<td>Pierre Dupont (3ème B)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td class='priority'>✅ Oui</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Sophie Dupont</strong> (👩 Mère)</td>";
    echo "<td>Mère</td>";
    echo "<td>Pierre Dupont (3ème B)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td>❌ Non</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='family-example'>";
    echo "<h4>🏠 Famille Recomposée - Famille Martin-Durand</h4>";
    echo "<table class='relation-table'>";
    echo "<tr>";
    echo "<th>Parent</th>";
    echo "<th>Lien</th>";
    echo "<th>Enfant</th>";
    echo "<th>Responsable Légal</th>";
    echo "<th>Autorisations</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Paul Martin</strong> (👨 Père)</td>";
    echo "<td>Père</td>";
    echo "<td>Lucas Martin (4ème C)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td class='permission'>Sortie + Médical</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Julie Durand</strong> (👩 Mère)</td>";
    echo "<td>Autre</td>";
    echo "<td>Lucas Martin (4ème C)</td>";
    echo "<td>❌ Non</td>";
    echo "<td class='permission'>Sortie uniquement</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Julie Durand</strong> (👩 Mère)</td>";
    echo "<td>Mère</td>";
    echo "<td>Emma Durand (6ème A)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td class='permission'>Sortie + Médical</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Paul Martin</strong> (👨 Père)</td>";
    echo "<td>Autre</td>";
    echo "<td>Emma Durand (6ème A)</td>";
    echo "<td>❌ Non</td>";
    echo "<td class='permission'>Sortie uniquement</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='family-example'>";
    echo "<h4>🏠 Tutelle - Famille Leroy</h4>";
    echo "<table class='relation-table'>";
    echo "<tr>";
    echo "<th>Parent/Responsable</th>";
    echo "<th>Lien</th>";
    echo "<th>Enfant</th>";
    echo "<th>Responsable Légal</th>";
    echo "<th>Contact Prioritaire</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Michel Leroy</strong> (👤 Tuteur)</td>";
    echo "<td>Tuteur</td>";
    echo "<td>Thomas Leroy (2nde A)</td>";
    echo "<td class='legal'>✅ Oui</td>";
    echo "<td class='priority'>✅ Oui</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Marie Leroy</strong> (👴 Grand-mère)</td>";
    echo "<td>Grand-parent</td>";
    echo "<td>Thomas Leroy (2nde A)</td>";
    echo "<td>❌ Non</td>";
    echo "<td>❌ Non</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    // Structure de la base de données
    echo "<div class='step'>";
    echo "<h3>🗄️ Structure de la Base de Données</h3>";
    
    echo "<h4>👨‍👩‍👧‍👦 Table Parents (Enrichie)</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace;'>";
    echo "<pre>";
    echo "CREATE TABLE Parents (\n";
    echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
    echo "    utilisateur_id INT NOT NULL,          -- FK vers Utilisateurs\n";
    echo "    telephone VARCHAR(20),                -- Téléphone personnel\n";
    echo "    adresse TEXT,                         -- Adresse domicile\n";
    echo "    profession VARCHAR(100),              -- Métier\n";
    echo "    lieu_travail VARCHAR(100),            -- Lieu de travail\n";
    echo "    telephone_travail VARCHAR(20),        -- Téléphone professionnel\n";
    echo "    contact_urgence BOOLEAN DEFAULT FALSE, -- Contact d'urgence\n";
    echo "    notes TEXT,                           -- Informations complémentaires\n";
    echo "    \n";
    echo "    FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)\n";
    echo ");";
    echo "</pre>";
    echo "</div>";
    
    echo "<h4>🔗 Table Parent_Etudiant (Relation Avancée)</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace;'>";
    echo "<pre>";
    echo "CREATE TABLE Parent_Etudiant (\n";
    echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
    echo "    parent_id INT NOT NULL,               -- FK vers Parents\n";
    echo "    etudiant_id INT NOT NULL,             -- FK vers Etudiants\n";
    echo "    lien_parente ENUM('Père', 'Mère', 'Tuteur', 'Grand-parent', 'Oncle/Tante', 'Autre'),\n";
    echo "    responsable_legal BOOLEAN DEFAULT FALSE,    -- Autorité légale\n";
    echo "    contact_prioritaire BOOLEAN DEFAULT FALSE,  -- Contact à privilégier\n";
    echo "    autorise_sortie BOOLEAN DEFAULT TRUE,       -- Peut récupérer l'enfant\n";
    echo "    autorise_medical BOOLEAN DEFAULT FALSE,     -- Autorisé pour soins\n";
    echo "    notes TEXT,                                  -- Informations spécifiques\n";
    echo "    date_creation DATETIME DEFAULT NOW(),\n";
    echo "    date_fin DATETIME NULL,                     -- Pour relations temporaires\n";
    echo "    actif BOOLEAN DEFAULT TRUE,                 -- Relation active\n";
    echo "    \n";
    echo "    FOREIGN KEY (parent_id) REFERENCES Parents(id),\n";
    echo "    FOREIGN KEY (etudiant_id) REFERENCES Etudiants(id)\n";
    echo ");";
    echo "</pre>";
    echo "</div>";
    
    echo "<h4>🆕 Améliorations Apportées</h4>";
    echo "<ul>";
    echo "<li><strong>Liens étendus :</strong> 6 types de liens familiaux</li>";
    echo "<li><strong>Autorisations granulaires :</strong> Responsabilité légale, contact prioritaire, sorties, médical</li>";
    echo "<li><strong>Gestion temporelle :</strong> Date de création, fin, statut actif</li>";
    echo "<li><strong>Informations professionnelles :</strong> Lieu de travail, téléphone professionnel</li>";
    echo "<li><strong>Contacts d'urgence :</strong> Hiérarchisation des contacts</li>";
    echo "</ul>";
    echo "</div>";
    
    // Fonctionnalités de l'interface
    echo "<div class='step'>";
    echo "<h3">🎨 Interface de Gestion</h3>";
    
    echo "<div class='feature-grid'>";
    
    echo "<div class='feature-card'>";
    echo "<h5>👨‍👩‍👧‍👦 Gestion des Relations</h5>";
    echo "<ul>";
    echo "<li>Création de relations parent-enfant</li>";
    echo "<li>Modification des autorisations</li>";
    echo "<li>Suppression (soft delete)</li>";
    echo "<li>Historique des relations</li>";
    echo "<li>Validation des doublons</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>🔍 Recherche et Filtrage</h5>";
    echo "<ul>";
    echo "<li>Recherche par nom parent/enfant</li>";
    echo "<li>Filtrage par type de lien</li>";
    echo "<li>Tri par responsabilité légale</li>";
    echo "<li>Pagination intelligente</li>";
    echo "<li>Statistiques en temps réel</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>⚖️ Autorisations</h5>";
    echo "<ul>";
    echo "<li>Responsable légal (autorité)</li>";
    echo "<li>Contact prioritaire (urgences)</li>";
    echo "<li>Autorisation de sortie</li>";
    echo "<li>Autorisation médicale</li>";
    echo "<li>Notes personnalisées</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h5>📊 Statistiques</h5>";
    echo "<ul>";
    echo "<li>Total des relations actives</li>";
    echo "<li>Répartition par type de lien</li>";
    echo "<li>Nombre de responsables légaux</li>";
    echo "<li>Contacts prioritaires</li>";
    echo "<li>Autorisations par type</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Cas d'usage
    echo "<div class='step'>";
    echo "<h3">💡 Cas d'Usage</h3>";
    
    echo "<h4>👨‍👩‍👧‍👦 Scénario 1 : Famille Traditionnelle</h4>";
    echo "<ol>";
    echo "<li><strong>Situation :</strong> Couple marié avec 2 enfants</li>";
    echo "<li><strong>Configuration :</strong> Père et Mère responsables légaux des 2 enfants</li>";
    echo "<li><strong>Contacts :</strong> Père contact prioritaire, Mère contact secondaire</li>";
    echo "<li><strong>Autorisations :</strong> Les deux autorisés pour sorties et soins médicaux</li>";
    echo "</ol>";
    
    echo "<h4>🏠 Scénario 2 : Famille Recomposée</h4>";
    echo "<ol>";
    echo "<li><strong>Situation :</strong> Parents divorcés remariés, enfants des deux unions</li>";
    echo "<li><strong>Configuration :</strong> Parent biologique responsable légal, beau-parent avec autorisations limitées</li>";
    echo "<li><strong>Contacts :</strong> Parent biologique prioritaire</li>";
    echo "<li><strong>Autorisations :</strong> Beau-parent autorisé sorties uniquement</li>";
    echo "</ol>";
    
    echo "<h4>👤 Scénario 3 : Tutelle</h4>";
    echo "<ol>";
    echo "<li><strong>Situation :</strong> Enfant sous tutelle d'un oncle</li>";
    echo "<li><strong>Configuration :</strong> Tuteur responsable légal, grand-mère contact secondaire</li>";
    echo "<li><strong>Contacts :</strong> Tuteur contact prioritaire et d'urgence</li>";
    echo "<li><strong>Autorisations :</strong> Tuteur toutes autorisations, grand-mère aucune</li>";
    echo "</ol>";
    
    echo "<h4>👨‍👩‍👧 Scénario 4 : Garde Partagée</h4>";
    echo "<ol>";
    echo "<li><strong>Situation :</strong> Parents divorcés avec garde alternée</li>";
    echo "<li><strong>Configuration :</strong> Les deux parents responsables légaux</li>";
    echo "<li><strong>Contacts :</strong> Contact prioritaire selon période de garde</li>";
    echo "<li><strong>Autorisations :</strong> Autorisations complètes pour les deux</li>";
    echo "</ol>";
    echo "</div>";
    
    // API et fonctionnalités
    echo "<div class='step'>";
    echo "<h3">📡 API et Fonctionnalités</h3>";
    
    echo "<h4>🔗 Endpoints Disponibles</h4>";
    echo "<ul>";
    echo "<li><strong>GET /parent_etudiant/</strong> : Liste des relations avec pagination</li>";
    echo "<li><strong>GET /parent_etudiant/?action=parents</strong> : Liste des parents disponibles</li>";
    echo "<li><strong>GET /parent_etudiant/?action=etudiants</strong> : Liste des étudiants disponibles</li>";
    echo "<li><strong>GET /parent_etudiant/?action=enfants&parent_id=X</strong> : Enfants d'un parent</li>";
    echo "<li><strong>GET /parent_etudiant/?action=parents_etudiant&etudiant_id=X</strong> : Parents d'un étudiant</li>";
    echo "<li><strong>GET /parent_etudiant/?action=stats</strong> : Statistiques des relations</li>";
    echo "<li><strong>POST /parent_etudiant/</strong> : Créer une nouvelle relation</li>";
    echo "<li><strong>PUT /parent_etudiant/</strong> : Modifier une relation existante</li>";
    echo "<li><strong>DELETE /parent_etudiant/</strong> : Supprimer une relation (soft delete)</li>";
    echo "</ul>";
    
    echo "<h4>🛡️ Sécurité et Permissions</h4>";
    echo "<ul>";
    echo "<li><strong>Accès restreint :</strong> Admin et Responsables uniquement</li>";
    echo "<li><strong>Authentification JWT :</strong> Token obligatoire</li>";
    echo "<li><strong>Validation des données :</strong> Contrôles stricts</li>";
    echo "<li><strong>Prévention doublons :</strong> Contraintes d'unicité</li>";
    echo "<li><strong>Soft delete :</strong> Conservation de l'historique</li>";
    echo "</ul>";
    
    echo "<h4>📊 Format de Données</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace;'>";
    echo "<pre>";
    echo "{\n";
    echo "  \"parent_id\": 1,\n";
    echo "  \"etudiant_id\": 2,\n";
    echo "  \"lien_parente\": \"Père\",\n";
    echo "  \"responsable_legal\": true,\n";
    echo "  \"contact_prioritaire\": true,\n";
    echo "  \"autorise_sortie\": true,\n";
    echo "  \"autorise_medical\": true,\n";
    echo "  \"notes\": \"Contact principal en cas d'urgence\"\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    echo "</div>";
    
    // Interface utilisateur
    echo "<div class='demo-section'>";
    echo "<h3>🎨 Interface Utilisateur Avancée</h3>";
    
    echo "<h4>✨ Caractéristiques de Design</h4>";
    echo "<ul>";
    echo "<li><strong>Design moderne :</strong> Interface épurée et professionnelle</li>";
    echo "<li><strong>Responsive :</strong> Adaptation mobile et desktop</li>";
    echo "<li><strong>Icônes par lien :</strong> Identification visuelle immédiate</li>";
    echo "<li><strong>Couleurs distinctives :</strong> Père (bleu), Mère (rose), Tuteur (orange)</li>";
    echo "<li><strong>Badges d'autorisation :</strong> Visualisation claire des permissions</li>";
    echo "</ul>";
    
    echo "<h4>🎯 Fonctionnalités Avancées</h4>";
    echo "<ul>";
    echo "<li><strong>Recherche intelligente :</strong> Par nom parent ou enfant</li>";
    echo "<li><strong>Filtrage par lien :</strong> Affichage par type de relation</li>";
    echo "<li><strong>Modal de création :</strong> Formulaire complet avec validations</li>";
    echo "<li><strong>Édition en place :</strong> Modification rapide des autorisations</li>";
    echo "<li><strong>Statistiques visuelles :</strong> Répartition des liens familiaux</li>";
    echo "</ul>";
    
    echo "<h4>🔒 Contrôles d'Accès</h4>";
    echo "<ul>";
    echo "<li><strong>Page d'accès refusé :</strong> Pour utilisateurs non autorisés</li>";
    echo "<li><strong>Vérification des rôles :</strong> Admin et Responsables uniquement</li>";
    echo "<li><strong>Messages explicites :</strong> Information claire sur les restrictions</li>";
    echo "<li><strong>Redirection sécurisée :</strong> Gestion des tentatives d'accès</li>";
    echo "</ul>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/parent-etudiant' target='_blank' class='test-button family'>👨‍👩‍👧‍👦 Tester l'Interface Relations</a>";
    echo "</div>";
    echo "</div>";
    
    // Résultat final
    echo "<div class='demo-section'>";
    echo "<h3>🎉 SYSTÈME DE RELATIONS FAMILIALES COMPLET</h3>";
    echo "<p class='success' style='font-size: 18px;'>✅ Interface de gestion des relations parent-étudiant opérationnelle !</p>";
    
    echo "<h4>🚀 Fonctionnalités Implémentées</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Relations flexibles</strong> : Support de toutes les configurations familiales</li>";
    echo "<li>✅ <strong>Autorisations granulaires</strong> : Contrôle précis des permissions</li>";
    echo "<li>✅ <strong>Interface intuitive</strong> : Gestion visuelle des relations</li>";
    echo "<li>✅ <strong>Sécurité stricte</strong> : Accès restreint aux administrateurs</li>";
    echo "<li>✅ <strong>Recherche avancée</strong> : Filtrage et tri intelligent</li>";
    echo "<li>✅ <strong>Statistiques complètes</strong> : Vue d'ensemble des relations</li>";
    echo "</ul>";
    
    echo "<h4>🎯 Avantages Système</h4>";
    echo "<p>Le système de relations familiales apporte une valeur ajoutée significative :</p>";
    echo "<ul>";
    echo "<li>Gestion flexible de toutes les situations familiales</li>";
    echo "<li>Respect des autorisations légales et parentales</li>";
    echo "<li>Communication ciblée selon les responsabilités</li>";
    echo "<li>Traçabilité complète des relations</li>";
    echo "<li>Interface moderne et intuitive</li>";
    echo "<li>Sécurité et confidentialité renforcées</li>";
    echo "</ul>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/parent-etudiant' target='_blank' class='test-button success'>🎉 Utiliser les Relations Familiales</a>";
    echo "</div>";
    
    echo "<p class='info'><strong>👨‍👩‍👧‍👦 Le système de relations familiales flexibles est opérationnel !</strong></p>";
    
    echo "<h4>🔗 Liens Utiles</h4>";
    echo "<ul>";
    echo "<li><a href='http://localhost:3000/parent-etudiant' target='_blank'>👨‍👩‍👧‍👦 Interface Relations React</a></li>";
    echo "<li><a href='../parent_etudiant/index.php' target='_blank'>🧪 API Relations</a></li>";
    echo "<li><a href='../parent_etudiant/setup_tables.php' target='_blank'>🗄️ Configuration Tables</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
