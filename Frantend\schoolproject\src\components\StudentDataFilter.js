import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from '../context/AuthContext';

/**
 * HOC (Higher Order Component) pour filtrer les données selon le rôle étudiant
 * Les étudiants ne peuvent voir que leurs propres données
 */
const withStudentDataFilter = (WrappedComponent) => {
  return function StudentFilteredComponent(props) {
    const { user } = useContext(AuthContext);
    const [isStudentReadOnly, setIsStudentReadOnly] = useState(false);

    useEffect(() => {
      const userRole = user?.role?.toLowerCase();
      setIsStudentReadOnly(userRole === 'etudiant' || userRole === 'élève');
    }, [user]);

    // Props supplémentaires pour le composant wrappé
    const enhancedProps = {
      ...props,
      isStudentReadOnly,
      currentUserId: user?.id,
      userRole: user?.role?.toLowerCase(),
      // Fonction pour filtrer les données selon l'étudiant
      filterDataForStudent: (data, userIdField = 'utilisateur_id') => {
        if (!isStudentReadOnly) return data;
        
        // Filtrer pour ne montrer que les données de l'étudiant connecté
        return data.filter(item => {
          // Vérifier différents champs possibles pour l'ID utilisateur
          const possibleFields = [userIdField, 'etudiant_id', 'user_id', 'utilisateur_id'];
          return possibleFields.some(field => 
            item[field] && item[field].toString() === user?.id?.toString()
          );
        });
      }
    };

    return <WrappedComponent {...enhancedProps} />;
  };
};

/**
 * Composant d'alerte pour indiquer le mode lecture seule étudiant
 */
export const StudentReadOnlyAlert = ({ show = true }) => {
  const { user } = useContext(AuthContext);
  const userRole = user?.role?.toLowerCase();
  const isStudent = userRole === 'etudiant' || userRole === 'élève';

  if (!show || !isStudent) return null;

  return (
    <div style={{
      backgroundColor: '#e3f2fd',
      border: '1px solid #2196f3',
      borderRadius: '8px',
      padding: '12px 16px',
      margin: '0 0 20px 0',
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      color: '#1565c0'
    }}>
      <span style={{ fontSize: '1.2rem' }}>👨‍🎓</span>
      <div>
        <strong>Mode Étudiant - Lecture Seule</strong>
        <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
          Vous ne pouvez consulter que vos propres informations académiques.
        </div>
      </div>
    </div>
  );
};

/**
 * Hook personnalisé pour gérer les données filtrées pour les étudiants
 */
export const useStudentDataFilter = () => {
  const { user } = useContext(AuthContext);
  const userRole = user?.role?.toLowerCase();
  const isStudentReadOnly = userRole === 'etudiant' || userRole === 'élève';

  const filterDataForStudent = (data, userIdField = 'utilisateur_id') => {
    if (!isStudentReadOnly || !Array.isArray(data)) return data;
    
    return data.filter(item => {
      // Vérifier différents champs possibles pour l'ID utilisateur
      const possibleFields = [userIdField, 'etudiant_id', 'user_id', 'utilisateur_id'];
      return possibleFields.some(field => 
        item[field] && item[field].toString() === user?.id?.toString()
      );
    });
  };

  const shouldHideActions = () => isStudentReadOnly;

  const getStudentSpecificQuery = (baseQuery = '') => {
    if (!isStudentReadOnly) return baseQuery;
    
    // Ajouter une condition WHERE pour filtrer par utilisateur
    const userCondition = `utilisateur_id = ${user?.id}`;
    
    if (baseQuery.toLowerCase().includes('where')) {
      return `${baseQuery} AND ${userCondition}`;
    } else {
      return `${baseQuery} WHERE ${userCondition}`;
    }
  };

  return {
    isStudentReadOnly,
    currentUserId: user?.id,
    userRole: user?.role?.toLowerCase(),
    filterDataForStudent,
    shouldHideActions,
    getStudentSpecificQuery
  };
};

export default withStudentDataFilter;
