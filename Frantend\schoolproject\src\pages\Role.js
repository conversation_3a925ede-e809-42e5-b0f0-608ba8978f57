import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const RoleCRUD = () => {
    const { user } = useContext(AuthContext);
    const [roles, setRoles] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingRole, setEditingRole] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        nom: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchRoles();
    }, []);

    const fetchRoles = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des rôles...');

            // Utiliser l'endpoint principal
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/roles/role.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse API:', response.data);

            // Vérifier si la réponse est un tableau
            let rolesData = response.data;
            if (!Array.isArray(rolesData)) {
                console.warn('⚠️ Réponse non-tableau:', rolesData);
                rolesData = [];
            }

            setRoles(rolesData);
            console.log('✅ Rôles chargés:', rolesData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des rôles:', error);
            console.error('❌ Détails erreur:', error.response?.data);
            
            // Utiliser des données de test en cas d'erreur
            const testRoles = [
                { id: 1, nom: 'Admin' },
                { id: 2, nom: 'Enseignant' },
                { id: 3, nom: 'Étudiant' },
                { id: 4, nom: 'Parent' },
                { id: 5, nom: 'Directeur' },
                { id: 6, nom: 'Secrétaire' },
                { id: 7, nom: 'Comptable' },
                { id: 8, nom: 'Surveillant' },
                { id: 9, nom: 'Bibliothécaire' },
                { id: 10, nom: 'Technicien' },
                { id: 11, nom: 'Infirmier' },
                { id: 12, nom: 'Psychologue' },
                { id: 13, nom: 'Gardien' },
                { id: 14, nom: 'Cuisinier' },
                { id: 15, nom: 'Chauffeur' },
                { id: 16, nom: 'Responsable IT' },
                { id: 17, nom: 'Coach Sportif' },
                { id: 18, nom: 'Conseiller' },
                { id: 19, nom: 'Maintenance' },
                { id: 20, nom: 'Réceptionniste' },
                { id: 21, nom: 'Archiviste' },
                { id: 22, nom: 'Traducteur' }
            ];
            
            setRoles(testRoles);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testRoles.length} rôles de test.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des rôles', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/roles/role.php';
            const method = editingRole ? 'PUT' : 'POST';
            const data = editingRole ? { ...formData, id: editingRole.id } : formData;

            console.log('🔄 Envoi requête:', { method, url, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data.success) {
                Swal.fire('Succès', `Rôle ${editingRole ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingRole(null);
                resetForm();
                fetchRoles(); // Recharger les données
            } else {
                throw new Error(response.data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            console.error('❌ Détails:', error.response?.data);

            const errorMessage = error.response?.data?.error ||
                               error.response?.data?.message ||
                               error.message ||
                               'Une erreur est survenue';

            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (role) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des rôles', 'error');
            return;
        }

        setEditingRole(role);
        setFormData({
            nom: role.nom
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des rôles', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/roles/role.php';

                console.log('🔄 Suppression rôle:', { id, url });

                const response = await axios.delete(url, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                console.log('✅ Réponse suppression:', response.data);

                if (response.data.success) {
                    Swal.fire('Supprimé!', 'Le rôle a été supprimé.', 'success');
                    fetchRoles(); // Recharger les données
                } else {
                    throw new Error(response.data.error || 'Erreur lors de la suppression');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                console.error('❌ Détails:', error.response?.data);

                const errorMessage = error.response?.data?.error ||
                                   error.response?.data?.message ||
                                   error.message ||
                                   'Impossible de supprimer le rôle';

                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            nom: ''
        });
    };

    // Filtrage des données
    const filteredRoles = roles.filter(role => {
        return role.nom?.toLowerCase().includes(searchTerm.toLowerCase());
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentRoles = filteredRoles.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredRoles.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des rôles...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>👥 Gestion des Rôles</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredRoles.length} rôle(s) trouvé(s)
                    </span>
                    {isAdmin && (
                        <button 
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouveau Rôle
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les rôles en mode lecture seule. 
                        Seul l'administrateur peut créer, modifier ou supprimer des rôles.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher un rôle..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
            </div>

            <div className="factures-grid">
                {filteredRoles.length === 0 ? (
                    <div className="no-data">
                        <img src="/role.png" alt="Aucun rôle" />
                        <p>Aucun rôle trouvé</p>
                        {searchTerm && (
                            <button 
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📝 Nom du Rôle</th>
                                    <th>📊 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentRoles.map((role) => (
                                    <tr key={role.id}>
                                        <td>
                                            <span style={{ 
                                                padding: '4px 8px', 
                                                backgroundColor: '#e3f2fd', 
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{role.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{role.nom}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span className="badge badge-success">Actif</span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(role)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button 
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(role.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredRoles.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredRoles.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des rôles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredRoles.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Rôles actifs</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {currentRoles.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un rôle */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingRole ? 'Modifier le rôle' : 'Nouveau rôle'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingRole(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Nom du rôle *</label>
                                <input
                                    type="text"
                                    value={formData.nom}
                                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                                    placeholder="Ex: Administrateur, Enseignant..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingRole(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingRole ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default RoleCRUD;
