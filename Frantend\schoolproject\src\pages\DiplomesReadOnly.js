import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const DiplomesReadOnly = () => {
    const { user } = useContext(AuthContext);
    const [diplomes, setDiplomes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [yearFilter, setYearFilter] = useState('all');

    useEffect(() => {
        fetchDiplomes();
    }, []);

    const fetchDiplomes = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setDiplomes(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des diplômes:', error);
            Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');
        } finally {
            setLoading(false);
        }
    };

    // Obtenir les années uniques pour le filtre
    const getUniqueYears = () => {
        const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());
        return [...new Set(years)].sort((a, b) => b - a);
    };

    // Filtrage des données
    const filteredDiplomes = diplomes.filter(diplome => {
        const matchesSearch = diplome.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             diplome.titre?.toLowerCase().includes(searchTerm.toLowerCase());
        
        const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();
        const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;
        
        return matchesSearch && matchesYear;
    });

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des diplômes...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>🎓 Consultation des Diplômes</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredDiplomes.length} diplôme(s) trouvé(s)
                    </span>
                </div>
            </div>

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par nom d'étudiant ou titre de diplôme..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="year-filter">
                    <select
                        value={yearFilter}
                        onChange={(e) => setYearFilter(e.target.value)}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '120px'
                        }}
                    >
                        <option value="all">Toutes les années</option>
                        {getUniqueYears().map(year => (
                            <option key={year} value={year}>{year}</option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredDiplomes.length === 0 ? (
                    <div className="no-data">
                        <img src="/result.png" alt="Aucun diplôme" />
                        <p>Aucun diplôme trouvé</p>
                        {searchTerm && (
                            <button 
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="diplomes-cards">
                        {filteredDiplomes.map((diplome) => (
                            <div key={diplome.id} className="diplome-card" style={{
                                backgroundColor: 'white',
                                borderRadius: '12px',
                                padding: '20px',
                                marginBottom: '15px',
                                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                                border: '1px solid #e9ecef',
                                transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = 'translateY(-2px)';
                                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'translateY(0)';
                                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                            }}>
                                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
                                    <div style={{
                                        fontSize: '2.5rem',
                                        color: '#ffd700',
                                        minWidth: '60px'
                                    }}>
                                        🏆
                                    </div>
                                    <div style={{ flex: 1 }}>
                                        <h3 style={{
                                            margin: '0 0 10px 0',
                                            color: '#2c3e50',
                                            fontSize: '1.3rem'
                                        }}>
                                            {diplome.titre}
                                        </h3>
                                        <div style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '8px'
                                        }}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <span style={{ fontSize: '1.1rem' }}>👤</span>
                                                <div>
                                                    <strong>{diplome.etudiant_nom}</strong>
                                                    <br />
                                                    <small style={{ color: '#6c757d' }}>{diplome.etudiant_email}</small>
                                                </div>
                                            </div>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <span style={{ fontSize: '1.1rem' }}>📅</span>
                                                <span style={{
                                                    padding: '4px 12px',
                                                    backgroundColor: '#e3f2fd',
                                                    borderRadius: '20px',
                                                    fontSize: '0.9em',
                                                    fontWeight: '500'
                                                }}>
                                                    {new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {
                                                        year: 'numeric',
                                                        month: 'long',
                                                        day: 'numeric'
                                                    })}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Statistiques */}
            {filteredDiplomes.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredDiplomes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total diplômes</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {new Set(filteredDiplomes.map(d => d.etudiant_id)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants diplômés</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {getUniqueYears().length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Années représentées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#6f42c1', margin: '0' }}>
                            {new Set(filteredDiplomes.map(d => d.titre)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Types de diplômes</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DiplomesReadOnly;
