<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données (même style que getUsers.php)
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Récupération des informations utilisateur depuis la session ou token
session_start();
$user_info = [
    'id' => $_SESSION['user_id'] ?? 1,
    'role' => $_SESSION['user_role'] ?? 'Admin' // Pour test, changez selon votre système
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($pdo, $user_info) {
    $role = $user_info['role'];
    $user_id = $user_info['id'];
    
    if ($role === 'Admin') {
        // Admin peut voir toutes les factures
        $stmt = $pdo->prepare("
            SELECT f.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Factures f 
            JOIN Etudiants e ON f.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            ORDER BY f.mois DESC
        ");
        $stmt->execute();
    } elseif ($role === 'Parent') {
        // Parent peut voir les factures de ses enfants
        $stmt = $pdo->prepare("
            SELECT f.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Factures f 
            JOIN Etudiants e ON f.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            JOIN Parent_Etudiant pe ON e.id = pe.etudiant_id 
            JOIN Parents p ON pe.parent_id = p.id 
            WHERE p.utilisateur_id = ? 
            ORDER BY f.mois DESC
        ");
        $stmt->execute([$user_id]);
    } elseif ($role === 'Etudiant') {
        // Étudiant peut voir ses propres factures
        $stmt = $pdo->prepare("
            SELECT f.*, u.nom as etudiant_nom, u.email as etudiant_email 
            FROM Factures f 
            JOIN Etudiants e ON f.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            WHERE e.utilisateur_id = ? 
            ORDER BY f.mois DESC
        ");
        $stmt->execute([$user_id]);
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
        return;
    }
    
    $factures = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($factures);
}

function handlePost($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut créer des factures']);
        return;
    }
    
    if (!isset($input['etudiant_id'], $input['mois'], $input['montant'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes']);
        return;
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO Factures (etudiant_id, mois, montant, statut) 
        VALUES (?, ?, ?, ?)
    ");
    
    $statut = $input['statut'] ?? 'Non payé';
    $date_paiement = ($statut === 'Payé' && isset($input['date_paiement'])) ? $input['date_paiement'] : null;
    
    $stmt->execute([
        $input['etudiant_id'],
        $input['mois'],
        $input['montant'],
        $statut
    ]);
    
    if ($date_paiement) {
        $facture_id = $pdo->lastInsertId();
        $stmt = $pdo->prepare("UPDATE Factures SET date_paiement = ? WHERE id = ?");
        $stmt->execute([$date_paiement, $facture_id]);
    }
    
    echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
}

function handlePut($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut modifier des factures']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $fields = [];
    $values = [];
    
    if (isset($input['mois'])) {
        $fields[] = 'mois = ?';
        $values[] = $input['mois'];
    }
    if (isset($input['montant'])) {
        $fields[] = 'montant = ?';
        $values[] = $input['montant'];
    }
    if (isset($input['statut'])) {
        $fields[] = 'statut = ?';
        $values[] = $input['statut'];
        
        if ($input['statut'] === 'Payé' && isset($input['date_paiement'])) {
            $fields[] = 'date_paiement = ?';
            $values[] = $input['date_paiement'];
        } elseif ($input['statut'] === 'Non payé') {
            $fields[] = 'date_paiement = NULL';
        }
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à modifier']);
        return;
    }
    
    $values[] = $input['id'];
    $sql = "UPDATE Factures SET " . implode(', ', $fields) . " WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($values);
    
    echo json_encode(['success' => true]);
}

function handleDelete($pdo, $user_info, $input) {
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'admin peut supprimer des factures']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM Factures WHERE id = ?");
    $stmt->execute([$input['id']]);
    
    echo json_encode(['success' => true]);
}

// TODO: Intégrer avec votre système d'authentification existant
// function verifyToken($token) { ... }
?>
