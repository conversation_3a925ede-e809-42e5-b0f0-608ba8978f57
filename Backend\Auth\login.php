<?php

// Autoriser les requêtes CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: POST, OPTIONS");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$json = file_get_contents('php://input');
$data = json_decode($json, true);

$email = $data['email'] ?? '';
$password = $data['password'] ?? '';

$pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");

if (!empty($email) && !empty($password)) {
    // JOIN بين Utilisateurs و Roles باش نجيب nom ديال الدور
    $stmt = $pdo->prepare("
        SELECT u.*, r.nom as role_nom 
        FROM Utilisateurs u 
        LEFT JOIN Roles r ON u.role_id = r.id 
        WHERE u.email = :email
    ");
    $stmt->execute([':email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['mot_de_passe'])) {
        $token = bin2hex(random_bytes(16)); 

        echo json_encode([
            "success" => true,
            "message" => "Connexion réussie",
            "role" => $user['role_nom'] ?? 'user',  // اسم الدور، افتراضياً 'user' إذا ماكانش معرف
            "token" => $token
        ]);
    } else {
        echo json_encode(["success" => false, "message" => "Email ou mot de passe incorrect"]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Email et mot de passe sont requis"]);
}

?>
