-- ============================================================================
-- RESTRUCTURATION COMPLÈTE DE LA BASE DE DONNÉES
-- Architecture: Utilisateurs -> Rôles -> Tables Spécialisées
-- ============================================================================

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- SUPPRESSION DE L'ANCIENNE STRUCTURE (si nécessaire)
-- ============================================================================

-- Supprimer les anciennes tables dans l'ordre inverse des dépendances
DROP TABLE IF EXISTS `parent_etudiant`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `messages`;
DROP TABLE IF EXISTS `factures`;
DROP TABLE IF EXISTS `diplomes`;
DROP TABLE IF EXISTS `absences`;
DROP TABLE IF EXISTS `retards`;
DROP TABLE IF EXISTS `reponses_quiz`;
DROP TABLE IF EXISTS `quiz`;
DROP TABLE IF EXISTS `devoirs`;
DROP TABLE IF EXISTS `notes`;
DROP TABLE IF EXISTS `cours`;
DROP TABLE IF EXISTS `emplois_du_temps`;
DROP TABLE IF EXISTS `enseignements`;
DROP TABLE IF EXISTS `enseignants`;
DROP TABLE IF EXISTS `etudiants`;
DROP TABLE IF EXISTS `parents`;
DROP TABLE IF EXISTS `groupes`;
DROP TABLE IF EXISTS `classes`;
DROP TABLE IF EXISTS `niveaux`;
DROP TABLE IF EXISTS `matieres`;
DROP TABLE IF EXISTS `filieres`;
DROP TABLE IF EXISTS `utilisateurs`;
DROP TABLE IF EXISTS `roles`;

-- ============================================================================
-- CRÉATION DE LA NOUVELLE STRUCTURE
-- ============================================================================

-- 1. Table des rôles (base de l'architecture)
CREATE TABLE `roles` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(50) NOT NULL UNIQUE,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. Table des utilisateurs (table principale)
CREATE TABLE `utilisateurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100) NOT NULL,
    `prenom` VARCHAR(100) NOT NULL,
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `mot_de_passe` VARCHAR(255) NOT NULL,
    `role_id` INT NOT NULL,
    `statut` ENUM('actif', 'inactif') DEFAULT 'actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    INDEX `idx_role_id` (`role_id`),
    INDEX `idx_email` (`email`),
    INDEX `idx_statut` (`statut`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. Tables de structure académique
CREATE TABLE `filieres` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100) NOT NULL UNIQUE,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `niveaux` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(50) NOT NULL UNIQUE,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `matieres` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100) NOT NULL UNIQUE,
    `description` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `classes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100) NOT NULL,
    `filiere_id` INT NOT NULL,
    `niveau_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`filiere_id`) REFERENCES `filieres`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (`niveau_id`) REFERENCES `niveaux`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    UNIQUE KEY `unique_classe` (`nom`, `filiere_id`, `niveau_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `groupes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom` VARCHAR(100) NOT NULL,
    `classe_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`classe_id`) REFERENCES `classes`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    UNIQUE KEY `unique_groupe` (`nom`, `classe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- TABLES SPÉCIALISÉES PAR RÔLE (Architecture Cohérente)
-- ============================================================================

-- 4. Table Parents (UNIQUEMENT pour utilisateurs avec rôle "parent")
CREATE TABLE `parents` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NOT NULL UNIQUE,
    `telephone` VARCHAR(20),
    `adresse` TEXT,
    `profession` VARCHAR(100),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    INDEX `idx_utilisateur_id` (`utilisateur_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 5. Table Étudiants (UNIQUEMENT pour utilisateurs avec rôle "etudiant")
CREATE TABLE `etudiants` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NOT NULL UNIQUE,
    `groupe_id` INT,
    `numero_etudiant` VARCHAR(50) UNIQUE,
    `date_inscription` DATE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`groupe_id`) REFERENCES `groupes`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    INDEX `idx_utilisateur_id` (`utilisateur_id`),
    INDEX `idx_groupe_id` (`groupe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 6. Table Enseignants (UNIQUEMENT pour utilisateurs avec rôle "enseignant")
CREATE TABLE `enseignants` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NOT NULL UNIQUE,
    `telephone` VARCHAR(20),
    `specialite` VARCHAR(255),
    `date_embauche` DATE,
    `salaire` DECIMAL(10,2),
    `statut` ENUM('actif','inactif') DEFAULT 'actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    INDEX `idx_utilisateur_id` (`utilisateur_id`),
    INDEX `idx_statut` (`statut`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- INSERTION DES DONNÉES DE BASE
-- ============================================================================

-- Insertion des rôles de base
INSERT INTO `roles` (`nom`, `description`) VALUES
('admin', 'Administrateur du système'),
('parent', 'Parent d\'élève'),
('etudiant', 'Étudiant'),
('enseignant', 'Enseignant/Professeur');

-- Insertion des filières de base
INSERT INTO `filieres` (`nom`, `description`) VALUES
('Informatique', 'Sciences informatiques et technologies'),
('Gestion', 'Gestion et administration des entreprises'),
('Commerce', 'Commerce et marketing'),
('Comptabilité', 'Comptabilité et finance');

-- Insertion des niveaux de base
INSERT INTO `niveaux` (`nom`, `description`) VALUES
('1ère année', 'Première année d\'études'),
('2ème année', 'Deuxième année d\'études'),
('3ème année', 'Troisième année d\'études'),
('Master', 'Niveau Master');

-- Insertion des matières de base
INSERT INTO `matieres` (`nom`, `description`) VALUES
('Mathématiques', 'Mathématiques générales'),
('Français', 'Langue française'),
('Anglais', 'Langue anglaise'),
('Informatique', 'Sciences informatiques'),
('Gestion', 'Gestion d\'entreprise'),
('Comptabilité', 'Comptabilité générale');

-- ============================================================================
-- TRIGGERS POUR GARANTIR LA COHÉRENCE DES RÔLES
-- ============================================================================

-- Trigger pour vérifier que seuls les parents sont dans la table parents
DELIMITER $$
CREATE TRIGGER `check_parent_role_insert` 
BEFORE INSERT ON `parents`
FOR EACH ROW
BEGIN
    DECLARE user_role VARCHAR(50);
    
    SELECT r.nom INTO user_role
    FROM utilisateurs u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = NEW.utilisateur_id;
    
    IF user_role != 'parent' THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle "parent" peuvent être insérés dans cette table';
    END IF;
END$$

CREATE TRIGGER `check_parent_role_update` 
BEFORE UPDATE ON `parents`
FOR EACH ROW
BEGIN
    DECLARE user_role VARCHAR(50);
    
    SELECT r.nom INTO user_role
    FROM utilisateurs u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = NEW.utilisateur_id;
    
    IF user_role != 'parent' THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle "parent" peuvent être modifiés dans cette table';
    END IF;
END$$

-- Trigger pour vérifier que seuls les étudiants sont dans la table étudiants
CREATE TRIGGER `check_etudiant_role_insert` 
BEFORE INSERT ON `etudiants`
FOR EACH ROW
BEGIN
    DECLARE user_role VARCHAR(50);
    
    SELECT r.nom INTO user_role
    FROM utilisateurs u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = NEW.utilisateur_id;
    
    IF user_role != 'etudiant' THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle "etudiant" peuvent être insérés dans cette table';
    END IF;
END$$

-- Trigger pour vérifier que seuls les enseignants sont dans la table enseignants
CREATE TRIGGER `check_enseignant_role_insert` 
BEFORE INSERT ON `enseignants`
FOR EACH ROW
BEGIN
    DECLARE user_role VARCHAR(50);
    
    SELECT r.nom INTO user_role
    FROM utilisateurs u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = NEW.utilisateur_id;
    
    IF user_role != 'enseignant' THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle "enseignant" peuvent être insérés dans cette table';
    END IF;
END$$

DELIMITER ;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- VUES POUR FACILITER LES REQUÊTES
-- ============================================================================

-- Vue pour les utilisateurs avec leurs rôles
CREATE VIEW `v_utilisateurs_complets` AS
SELECT 
    u.id,
    u.nom,
    u.prenom,
    u.email,
    u.statut as statut_utilisateur,
    r.nom as role_nom,
    r.description as role_description,
    u.created_at,
    u.updated_at
FROM utilisateurs u
INNER JOIN roles r ON u.role_id = r.id;

-- Vue pour les parents complets
CREATE VIEW `v_parents_complets` AS
SELECT 
    p.id as parent_id,
    p.utilisateur_id,
    u.nom,
    u.prenom,
    u.email,
    p.telephone,
    p.adresse,
    p.profession,
    u.statut as statut_utilisateur,
    p.created_at,
    p.updated_at
FROM parents p
INNER JOIN utilisateurs u ON p.utilisateur_id = u.id
INNER JOIN roles r ON u.role_id = r.id
WHERE r.nom = 'parent';

-- ============================================================================
-- COMMENTAIRES ET DOCUMENTATION
-- ============================================================================

/*
ARCHITECTURE IMPLÉMENTÉE:

1. UTILISATEURS (Table principale)
   - Tous les utilisateurs du système
   - Champ role_id obligatoire

2. RÔLES
   - admin: Reste uniquement dans utilisateurs
   - parent: utilisateurs + parents
   - etudiant: utilisateurs + etudiants  
   - enseignant: utilisateurs + enseignants

3. COHÉRENCE GARANTIE PAR:
   - Triggers de validation des rôles
   - Contraintes de clés étrangères
   - Index pour les performances
   - Vues pour faciliter les requêtes

4. AVANTAGES:
   - Séparation claire des rôles
   - Intégrité des données garantie
   - Évolutivité et maintenabilité
   - Performance optimisée
*/
