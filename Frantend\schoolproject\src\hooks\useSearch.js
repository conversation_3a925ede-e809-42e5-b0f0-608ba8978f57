import { useState, useMemo } from 'react';

const useSearch = (data, searchFields = [], filterField = null) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterValue, setFilterValue] = useState('all');

    // Fonction de recherche
    const filteredData = useMemo(() => {
        let result = data;

        // Appliquer la recherche
        if (searchTerm && searchFields.length > 0) {
            result = result.filter(item => {
                return searchFields.some(field => {
                    const value = getNestedValue(item, field);
                    return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
                });
            });
        }

        // Appliquer le filtre
        if (filterValue !== 'all' && filterField) {
            result = result.filter(item => {
                const value = getNestedValue(item, filterField);
                return value === filterValue;
            });
        }

        return result;
    }, [data, searchTerm, filterValue, searchFields, filterField]);

    // Fonction helper pour accéder aux propriétés imbriquées
    const getNestedValue = (obj, path) => {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    // Obtenir les valeurs uniques pour le filtre
    const getUniqueFilterValues = () => {
        if (!filterField) return [];
        
        const values = data.map(item => getNestedValue(item, filterField))
            .filter(value => value !== null && value !== undefined && value !== '');
        
        return [...new Set(values)].sort();
    };

    // Réinitialiser les filtres
    const clearFilters = () => {
        setSearchTerm('');
        setFilterValue('all');
    };

    return {
        searchTerm,
        setSearchTerm,
        filterValue,
        setFilterValue,
        filteredData,
        getUniqueFilterValues,
        clearFilters,
        hasActiveFilters: searchTerm !== '' || filterValue !== 'all'
    };
};

export default useSearch;
