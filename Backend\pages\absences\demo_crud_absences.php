<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

try {
    echo "<h1>📋 DÉMONSTRATION - INTERFACE CRUD ABSENCES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .interface-card { background: white; border: 2px solid #ddd; border-radius: 10px; padding: 20px; }
        .interface-card.absences { border-color: #dc3545; }
        .interface-card.factures { border-color: #007bff; }
        .test-button { background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; font-weight: bold; }
        .test-button:hover { background: #c82333; color: white; text-decoration: none; }
        .test-button.factures { background: #007bff; }
        .test-button.factures:hover { background: #0056b3; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 5px 0; }
        .feature-list li::before { content: '✅ '; color: green; font-weight: bold; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Interface CRUD Absences - Modèle Factures</h2>";
    echo "<p>L'interface des absences a été créée en suivant exactement le modèle des factures :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Design identique</strong> : Même style, couleurs et organisation</li>";
    echo "<li>✅ <strong>Fonctionnalités CRUD</strong> : Create, Read, Update, Delete</li>";
    echo "<li>✅ <strong>Filtres et recherche</strong> : Comme les factures</li>";
    echo "<li>✅ <strong>Pagination</strong> : 10 éléments par page</li>";
    echo "<li>✅ <strong>Gestion des rôles</strong> : Admin et Enseignant peuvent gérer</li>";
    echo "</ul>";
    echo "</div>";
    
    // Vérifier la structure de la table absences
    echo "<div class='step'>";
    echo "<h3>🗄️ Structure de la Table Absences</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE Absences");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='success'>✅ Table Absences trouvée avec " . count($columns) . " colonnes</p>";
        
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Colonne</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Type</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Null</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Clé</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Défaut</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>{$column['Field']}</strong></td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column['Type']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column['Null']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column['Key']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur lors de la vérification de la table : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Vérifier les données existantes
    echo "<div class='step'>";
    echo "<h3>📊 Données Existantes</h3>";
    
    try {
        // Compter les absences
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Absences");
        $totalAbsences = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Compter les étudiants
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Etudiants");
        $totalEtudiants = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Compter les matières
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Matieres");
        $totalMatieres = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Compter les enseignants
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Enseignants");
        $totalEnseignants = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
        
        echo "<div style='background: #fff5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
        echo "<h4 style='margin: 0; color: #dc3545;'>📋 Absences</h4>";
        echo "<p style='margin: 5px 0; font-size: 24px; font-weight: bold;'>$totalAbsences</p>";
        echo "</div>";
        
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;'>";
        echo "<h4 style='margin: 0; color: #007bff;'>👤 Étudiants</h4>";
        echo "<p style='margin: 5px 0; font-size: 24px; font-weight: bold;'>$totalEtudiants</p>";
        echo "</div>";
        
        echo "<div style='background: #f0fff0; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;'>";
        echo "<h4 style='margin: 0; color: #28a745;'>📚 Matières</h4>";
        echo "<p style='margin: 5px 0; font-size: 24px; font-weight: bold;'>$totalMatieres</p>";
        echo "</div>";
        
        echo "<div style='background: #fffbf0; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
        echo "<h4 style='margin: 0; color: #ffc107;'>👨‍🏫 Enseignants</h4>";
        echo "<p style='margin: 5px 0; font-size: 24px; font-weight: bold;'>$totalEnseignants</p>";
        echo "</div>";
        
        echo "</div>";
        
        if ($totalAbsences > 0) {
            echo "<p class='success'>✅ Des données d'absences existent pour les tests</p>";
        } else {
            echo "<p class='warning'>⚠️ Aucune absence trouvée - Vous pouvez en créer via l'interface</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur lors de la vérification des données : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Comparaison avec les factures
    echo "<div class='step'>";
    echo "<h3>🔄 Comparaison Absences vs Factures</h3>";
    
    echo "<div class='comparison'>";
    
    echo "<div class='interface-card factures'>";
    echo "<h4>💰 Interface Factures (Modèle)</h4>";
    echo "<ul class='feature-list'>";
    echo "<li>Design professionnel avec couleurs bleues</li>";
    echo "<li>Filtres par statut (Payé/Non payé)</li>";
    echo "<li>Recherche par étudiant</li>";
    echo "<li>Pagination 10 éléments</li>";
    echo "<li>CRUD complet pour Admin</li>";
    echo "<li>Badges colorés pour statuts</li>";
    echo "<li>Responsive design</li>";
    echo "</ul>";
    echo "<p><strong>Rôles :</strong> Admin (CRUD), Autres (Lecture)</p>";
    echo "</div>";
    
    echo "<div class='interface-card absences'>";
    echo "<h4>📋 Interface Absences (Nouvelle)</h4>";
    echo "<ul class='feature-list'>";
    echo "<li>Design identique avec couleurs rouges</li>";
    echo "<li>Filtres par justification (Justifiée/Non justifiée)</li>";
    echo "<li>Recherche par étudiant/matière/enseignant</li>";
    echo "<li>Pagination 10 éléments</li>";
    echo "<li>CRUD complet pour Admin et Enseignant</li>";
    echo "<li>Badges colorés pour statuts</li>";
    echo "<li>Responsive design</li>";
    echo "</ul>";
    echo "<p><strong>Rôles :</strong> Admin + Enseignant (CRUD), Autres (Lecture)</p>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Fonctionnalités implémentées
    echo "<div class='step'>";
    echo "<h3>🛠️ Fonctionnalités Implémentées</h3>";
    
    echo "<h4>✅ 1. Opérations CRUD</h4>";
    echo "<ul>";
    echo "<li><strong>Create :</strong> Ajouter une nouvelle absence</li>";
    echo "<li><strong>Read :</strong> Afficher la liste des absences avec filtres</li>";
    echo "<li><strong>Update :</strong> Modifier une absence existante</li>";
    echo "<li><strong>Delete :</strong> Supprimer une absence</li>";
    echo "</ul>";
    
    echo "<h4>✅ 2. Gestion des Rôles</h4>";
    echo "<ul>";
    echo "<li><strong>Admin :</strong> Accès complet CRUD à toutes les absences</li>";
    echo "<li><strong>Enseignant :</strong> CRUD pour les absences de ses classes</li>";
    echo "<li><strong>Parent :</strong> Lecture seule des absences de ses enfants</li>";
    echo "<li><strong>Étudiant :</strong> Lecture seule de ses propres absences</li>";
    echo "</ul>";
    
    echo "<h4>✅ 3. Interface Utilisateur</h4>";
    echo "<ul>";
    echo "<li><strong>Design cohérent :</strong> Même style que les factures</li>";
    echo "<li><strong>Filtres avancés :</strong> Recherche et filtrage par statut</li>";
    echo "<li><strong>Pagination :</strong> Navigation par pages</li>";
    echo "<li><strong>Responsive :</strong> Adapté mobile et desktop</li>";
    echo "</ul>";
    
    echo "<h4>✅ 4. Champs de Données</h4>";
    echo "<ul>";
    echo "<li><strong>Étudiant :</strong> Sélection depuis la liste</li>";
    echo "<li><strong>Matière :</strong> Optionnel, sélection depuis la liste</li>";
    echo "<li><strong>Enseignant :</strong> Auto-assigné ou sélectionnable</li>";
    echo "<li><strong>Date d'absence :</strong> Sélecteur de date</li>";
    echo "<li><strong>Justification :</strong> Texte libre optionnel</li>";
    echo "</ul>";
    echo "</div>";
    
    // Tests disponibles
    echo "<div class='step'>";
    echo "<h3>🧪 Tests Disponibles</h3>";
    
    echo "<h4>🎯 Test de l'Interface</h4>";
    echo "<p>Testez la nouvelle interface CRUD des absences :</p>";
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/absences' class='test-button'>📋 Interface Absences</a>";
    echo "<a href='http://localhost:3000/factures' class='test-button factures'>💰 Interface Factures (Comparaison)</a>";
    echo "</div>";
    
    echo "<h4>📋 Points à Vérifier</h4>";
    echo "<ol>";
    echo "<li><strong>Design :</strong> Vérifiez que l'interface ressemble aux factures</li>";
    echo "<li><strong>Couleurs :</strong> Rouge pour absences, bleu pour factures</li>";
    echo "<li><strong>Filtres :</strong> Testez la recherche et les filtres par statut</li>";
    echo "<li><strong>CRUD :</strong> Créez, modifiez et supprimez des absences</li>";
    echo "<li><strong>Pagination :</strong> Naviguez entre les pages</li>";
    echo "<li><strong>Responsive :</strong> Testez sur mobile</li>";
    echo "</ol>";
    
    echo "<h4>🔄 Test de Comparaison</h4>";
    echo "<p>Ouvrez les deux interfaces côte à côte pour comparer :</p>";
    echo "<ul>";
    echo "<li><strong>Structure :</strong> Même organisation des éléments</li>";
    echo "<li><strong>Fonctionnalités :</strong> Même type d'opérations</li>";
    echo "<li><strong>Navigation :</strong> Même système de pagination</li>";
    echo "<li><strong>Formulaires :</strong> Même style de modals</li>";
    echo "</ul>";
    echo "</div>";
    
    // API Backend
    echo "<div class='step'>";
    echo "<h3>🔌 API Backend</h3>";
    
    echo "<h4>📡 Endpoints Disponibles</h4>";
    echo "<ul>";
    echo "<li><strong>GET</strong> /Backend/pages/absences/ - Lister les absences</li>";
    echo "<li><strong>POST</strong> /Backend/pages/absences/ - Créer une absence</li>";
    echo "<li><strong>PUT</strong> /Backend/pages/absences/ - Modifier une absence</li>";
    echo "<li><strong>DELETE</strong> /Backend/pages/absences/ - Supprimer une absence</li>";
    echo "</ul>";
    
    echo "<h4>🔒 Sécurité et Permissions</h4>";
    echo "<ul>";
    echo "<li><strong>Authentification :</strong> Token JWT requis</li>";
    echo "<li><strong>Autorisation :</strong> Vérification des rôles</li>";
    echo "<li><strong>Validation :</strong> Contrôle des données</li>";
    echo "<li><strong>Relations :</strong> Vérification des FK</li>";
    echo "</ul>";
    
    echo "<h4>📊 Test API Direct</h4>";
    echo "<p>L'API est accessible à :</p>";
    echo "<code style='background: #f8f9fa; padding: 10px; border-radius: 4px; display: block; margin: 10px 0;'>";
    echo "http://localhost/Project_PFE/Backend/pages/absences/";
    echo "</code>";
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎉 INTERFACE CRUD ABSENCES CRÉÉE</h3>";
    echo "<p class='success' style='font-size: 18px;'>✅ L'interface CRUD des absences est maintenant opérationnelle !</p>";
    
    echo "<h4>🏆 Réalisations</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0;'>";
    
    echo "<div style='background: #fff5f5; padding: 15px; border-radius: 8px;'>";
    echo "<h5>🎨 Design</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>Interface identique aux factures</li>";
    echo "<li>Couleurs cohérentes (rouge)</li>";
    echo "<li>Responsive design</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px;'>";
    echo "<h5>⚙️ Fonctionnalités</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>CRUD complet</li>";
    echo "<li>Filtres et recherche</li>";
    echo "<li>Pagination</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f0fff0; padding: 15px; border-radius: 8px;'>";
    echo "<h5>🔒 Sécurité</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>Gestion des rôles</li>";
    echo "<li>Validation des données</li>";
    echo "<li>API sécurisée</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fffbf0; padding: 15px; border-radius: 8px;'>";
    echo "<h5>📱 Expérience</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>Interface intuitive</li>";
    echo "<li>Navigation fluide</li>";
    echo "<li>Feedback utilisateur</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<p class='info'><strong>🚀 Votre système de gestion des absences est maintenant aussi professionnel que celui des factures !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
