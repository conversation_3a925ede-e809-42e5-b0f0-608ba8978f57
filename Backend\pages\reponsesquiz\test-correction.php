<?php
header('Content-Type: text/html; charset=utf-8');

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Erreur de connexion à la base de données: ' . $e->getMessage());
}

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Correction - ReponsesQuiz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #ffeaa7; }
        h1, h2, h3 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .test-case { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 Test de Correction - ReponsesQuiz</h1>";

try {
    // 1. Vérifier la structure de la table
    echo "<h2>📊 1. Vérification de la Structure</h2>";
    
    $stmt = $pdo->query("DESCRIBE reponsesquiz");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Défaut</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td><strong>{$col['Field']}</strong></td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Fonction de validation corrigée
    echo "<h2>🔧 2. Test de la Fonction de Validation</h2>";
    
    function validateResponse($pdo, $quiz_id, $reponse_etudiant) {
        try {
            // Récupérer la réponse correcte du quiz
            $stmt = $pdo->prepare("SELECT reponse_correcte FROM quiz WHERE id = ?");
            $stmt->execute([$quiz_id]);
            $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$quiz || empty($quiz['reponse_correcte'])) {
                // Si pas de quiz ou pas de réponse correcte définie, retourner NULL (non évalué)
                return null;
            }
            
            // Normaliser les réponses pour la comparaison
            $reponse_etudiant_norm = trim(strtolower($reponse_etudiant));
            $reponse_correcte_norm = trim(strtolower($quiz['reponse_correcte']));
            
            // Comparaison exacte
            $est_correct = ($reponse_etudiant_norm === $reponse_correcte_norm);
            
            // Retourner 1 pour correct, 0 pour incorrect
            return $est_correct ? 1 : 0;
            
        } catch (Exception $e) {
            error_log("Erreur validation réponse: " . $e->getMessage());
            // En cas d'erreur, retourner NULL (non évalué)
            return null;
        }
    }
    
    // Récupérer quelques quiz pour les tests
    $stmt = $pdo->query("SELECT id, question, reponse_correcte FROM quiz LIMIT 5");
    $quiz_tests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($quiz_tests) > 0) {
        echo "<h3>🧪 Tests de Validation</h3>";
        
        foreach ($quiz_tests as $quiz) {
            echo "<div class='test-case'>";
            echo "<h4>Quiz ID: {$quiz['id']}</h4>";
            echo "<p><strong>Question:</strong> " . htmlspecialchars($quiz['question']) . "</p>";
            echo "<p><strong>Réponse correcte:</strong> " . htmlspecialchars($quiz['reponse_correcte']) . "</p>";
            
            // Tests avec différentes réponses
            $test_reponses = [
                $quiz['reponse_correcte'], // Réponse exacte
                strtoupper($quiz['reponse_correcte']), // Réponse en majuscules
                ' ' . $quiz['reponse_correcte'] . ' ', // Réponse avec espaces
                $quiz['reponse_correcte'] . 'x', // Réponse incorrecte
                '' // Réponse vide
            ];
            
            echo "<table>";
            echo "<tr><th>Réponse Testée</th><th>Résultat</th><th>Statut</th></tr>";
            
            foreach ($test_reponses as $test_reponse) {
                $resultat = validateResponse($pdo, $quiz['id'], $test_reponse);
                $statut = '';
                if ($resultat === 1) {
                    $statut = '<span style="color: #28a745; font-weight: bold;">✅ Correct</span>';
                } elseif ($resultat === 0) {
                    $statut = '<span style="color: #dc3545; font-weight: bold;">❌ Incorrect</span>';
                } else {
                    $statut = '<span style="color: #6c757d; font-weight: bold;">⏳ Non évalué</span>';
                }
                
                echo "<tr>";
                echo "<td>'" . htmlspecialchars($test_reponse) . "'</td>";
                echo "<td>" . ($resultat === null ? 'NULL' : $resultat) . "</td>";
                echo "<td>$statut</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        }
    } else {
        echo "<p class='warning'>⚠️ Aucun quiz trouvé pour les tests</p>";
    }
    
    // 3. Test d'insertion directe
    echo "<h2>💾 3. Test d'Insertion Directe</h2>";
    
    // Vérifier s'il y a des étudiants
    $stmt = $pdo->query("SELECT id FROM etudiants LIMIT 1");
    $etudiant = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($etudiant && count($quiz_tests) > 0) {
        $quiz_test = $quiz_tests[0];
        $etudiant_id = $etudiant['id'];
        
        // Supprimer toute réponse existante pour ce test
        $stmt = $pdo->prepare("DELETE FROM reponsesquiz WHERE quiz_id = ? AND etudiant_id = ? AND reponse = 'TEST_CORRECTION'");
        $stmt->execute([$quiz_test['id'], $etudiant_id]);
        
        try {
            // Test avec une réponse correcte
            $est_correct = validateResponse($pdo, $quiz_test['id'], $quiz_test['reponse_correcte']);
            
            $stmt = $pdo->prepare("
                INSERT INTO reponsesquiz (quiz_id, etudiant_id, reponse, est_correct) 
                VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $quiz_test['id'],
                $etudiant_id,
                'TEST_CORRECTION',
                $est_correct
            ]);
            
            echo "<p class='success'>✅ Insertion réussie avec est_correct = " . ($est_correct === null ? 'NULL' : $est_correct) . "</p>";
            
            // Vérifier l'insertion
            $stmt = $pdo->prepare("SELECT * FROM reponsesquiz WHERE quiz_id = ? AND etudiant_id = ? AND reponse = 'TEST_CORRECTION'");
            $stmt->execute([$quiz_test['id'], $etudiant_id]);
            $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($inserted) {
                echo "<table>";
                echo "<tr><th>Champ</th><th>Valeur</th></tr>";
                foreach ($inserted as $key => $value) {
                    echo "<tr><td><strong>$key</strong></td><td>" . ($value === null ? 'NULL' : htmlspecialchars($value)) . "</td></tr>";
                }
                echo "</table>";
                
                // Nettoyer
                $stmt = $pdo->prepare("DELETE FROM reponsesquiz WHERE id = ?");
                $stmt->execute([$inserted['id']]);
                echo "<p class='info'>ℹ️ Données de test nettoyées</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors de l'insertion: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Pas d'étudiant ou de quiz disponible pour le test d'insertion</p>";
    }
    
    // 4. Test de l'API
    echo "<h2>🔌 4. Test de l'API</h2>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php";
    
    // Test GET
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer etudiant-token']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "<p class='success'>✅ API GET fonctionne correctement</p>";
            echo "<p><strong>Nombre de réponses:</strong> " . (isset($data['count']) ? $data['count'] : 'N/A') . "</p>";
        } else {
            echo "<p class='warning'>⚠️ API répond mais format inattendu</p>";
        }
    } else {
        echo "<p class='error'>❌ API ne répond pas (Code: $httpCode)</p>";
    }
    
    // 5. Résumé de la correction
    echo "<h2>✅ 5. Résumé de la Correction</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🔧 Corrections Apportées</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Fonction validateResponse</strong> : Retourne maintenant 1, 0, ou NULL (jamais de chaîne vide)</li>";
    echo "<li>✅ <strong>Gestion des cas limites</strong> : Quiz inexistant ou réponse correcte vide</li>";
    echo "<li>✅ <strong>Type de données</strong> : est_correct reçoit toujours un entier ou NULL</li>";
    echo "<li>✅ <strong>Normalisation</strong> : Comparaison insensible à la casse et aux espaces</li>";
    echo "<li>✅ <strong>Gestion d'erreurs</strong> : Retour NULL en cas de problème</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🎯 Comportement Attendu</h3>";
    echo "<ul>";
    echo "<li><strong>est_correct = 1</strong> : Réponse correcte</li>";
    echo "<li><strong>est_correct = 0</strong> : Réponse incorrecte</li>";
    echo "<li><strong>est_correct = NULL</strong> : Non évalué (quiz inexistant, erreur, etc.)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>📋 Prochaines Étapes</h3>";
    echo "<ol>";
    echo "<li>Tester l'ajout d'une réponse depuis l'interface React</li>";
    echo "<li>Vérifier que l'erreur SQLSTATE[HY000] n'apparaît plus</li>";
    echo "<li>Valider l'évaluation automatique des réponses</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<p style='text-align: center; margin-top: 30px;'>";
    echo "<a href='api.php' target='_blank' class='btn btn-primary'>Tester l'API</a> ";
    echo "<a href='setup-et-test.php' target='_blank' class='btn btn-success'>Setup Complet</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Erreur</h3>";
    echo "<p>Erreur lors du test : " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "    </div>
</body>
</html>";
?>
