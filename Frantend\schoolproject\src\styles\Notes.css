/* Styles pour la page Notes Dynamiques */

.notes-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background: #f8f9fa;
    min-height: 100vh;
}

/* En-tête de la page */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.page-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 28px;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.total-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 14px;
}

/* Bouton d'ajout */
.add-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.add-button:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.add-button img {
    width: 16px;
    height: 16px;
}

/* Statistiques */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 32px;
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.stat-card p {
    margin: 0;
    color: #6c757d;
    font-weight: 500;
}

/* Filtres */
.filters-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 14px;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Tableau */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #dee2e6;
    font-size: 14px;
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Informations dans le tableau */
.etudiant-info,
.devoir-info {
    display: flex;
    flex-direction: column;
}

.etudiant-info strong,
.devoir-info strong {
    color: #2c3e50;
    font-size: 14px;
}

.etudiant-info small,
.devoir-info small {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
}

/* Badges */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-primary {
    background: #e3f2fd;
    color: #1976d2;
}

/* Notes */
.note-value {
    font-weight: bold;
    font-size: 16px;
    padding: 4px 8px;
    border-radius: 6px;
}

.note-value.success {
    background: #d4edda;
    color: #155724;
}

.note-value.danger {
    background: #f8d7da;
    color: #721c24;
}

/* Statistiques quiz */
.quiz-stats {
    font-size: 12px;
    color: #6c757d;
}

.text-muted {
    color: #6c757d;
    font-style: italic;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn {
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn img {
    width: 14px;
    height: 14px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.pagination .btn {
    padding: 8px 12px;
    font-size: 14px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Pas de données */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-data img {
    width: 64px;
    height: 64px;
    opacity: 0.5;
    margin-bottom: 20px;
}

.no-data p {
    margin: 10px 0;
    font-size: 16px;
}

/* Loading */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.close-btn img {
    width: 20px;
    height: 20px;
}

/* Formulaire */
.form-group {
    margin-bottom: 20px;
    padding: 0 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.required {
    color: #dc3545;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #dee2e6;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* Responsive */
@media (max-width: 768px) {
    .notes-container {
        padding: 10px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .filters-container {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 6px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
