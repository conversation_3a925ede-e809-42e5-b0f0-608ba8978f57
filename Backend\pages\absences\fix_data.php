<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔧 CORRECTION DES DONNÉES ABSENCES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .sql-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .test-button:hover { background: #0056b3; color: white; text-decoration: none; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🔧 Correction Automatique des Problèmes</h2>";
    echo "<p>Correction des données pour résoudre les problèmes d'affichage des absences</p>";
    echo "</div>";
    
    // 1. Vérifier et corriger la structure de la table
    echo "<div class='step'>";
    echo "<h3>📋 1. Vérification/Correction Structure Table</h3>";
    
    try {
        // Vérifier si la colonne enseignant_id existe
        $stmt = $pdo->query("SHOW COLUMNS FROM Absences LIKE 'enseignant_id'");
        if ($stmt->rowCount() == 0) {
            echo "<p class='warning'>⚠️ Colonne enseignant_id manquante, ajout en cours...</p>";
            
            $pdo->exec("ALTER TABLE Absences ADD COLUMN enseignant_id INT NULL AFTER matiere_id");
            $pdo->exec("ALTER TABLE Absences ADD FOREIGN KEY (enseignant_id) REFERENCES Enseignants(id)");
            
            echo "<p class='success'>✅ Colonne enseignant_id ajoutée avec clé étrangère</p>";
        } else {
            echo "<p class='success'>✅ Colonne enseignant_id existe déjà</p>";
        }
        
        // Afficher la structure finale
        $stmt = $pdo->query("DESCRIBE Absences");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 Structure finale de la table Absences :</h4>";
        echo "<div class='sql-block'>";
        foreach ($columns as $column) {
            echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur structure table : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Créer des rôles si manquants
    echo "<div class='step'>";
    echo "<h3>👥 2. Création des Rôles</h3>";
    
    $roles = [
        'Admin' => 'Administrateur du système',
        'Enseignant' => 'Professeur/Enseignant',
        'Etudiant' => 'Élève/Étudiant',
        'Parent' => 'Parent d\'élève'
    ];
    
    foreach ($roles as $role_name => $description) {
        try {
            $stmt = $pdo->prepare("SELECT id FROM Roles WHERE nom = ?");
            $stmt->execute([$role_name]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Roles (nom, description) VALUES (?, ?)");
                $stmt->execute([$role_name, $description]);
                echo "<p class='success'>✅ Rôle '$role_name' créé</p>";
            } else {
                echo "<p class='info'>ℹ️ Rôle '$role_name' existe déjà</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur création rôle '$role_name' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // 3. Créer des utilisateurs de test
    echo "<div class='step'>";
    echo "<h3>👤 3. Création d'Utilisateurs de Test</h3>";
    
    // Récupérer les IDs des rôles
    $stmt = $pdo->query("SELECT id, nom FROM Roles");
    $roles_map = [];
    while ($row = $stmt->fetch()) {
        $roles_map[$row['nom']] = $row['id'];
    }
    
    // Utilisateurs de test
    $test_users = [
        ['nom' => 'Admin', 'prenom' => 'Super', 'email' => '<EMAIL>', 'role' => 'Admin'],
        ['nom' => 'Dupont', 'prenom' => 'Jean', 'email' => '<EMAIL>', 'role' => 'Enseignant'],
        ['nom' => 'Martin', 'prenom' => 'Marie', 'email' => '<EMAIL>', 'role' => 'Enseignant'],
        ['nom' => 'Durand', 'prenom' => 'Pierre', 'email' => '<EMAIL>', 'role' => 'Etudiant'],
        ['nom' => 'Leroy', 'prenom' => 'Sophie', 'email' => '<EMAIL>', 'role' => 'Etudiant']
    ];
    
    foreach ($test_users as $user_data) {
        try {
            $stmt = $pdo->prepare("SELECT id FROM Utilisateurs WHERE email = ?");
            $stmt->execute([$user_data['email']]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("
                    INSERT INTO Utilisateurs (nom, prenom, email, mot_de_passe, role_id, actif) 
                    VALUES (?, ?, ?, ?, ?, 1)
                ");
                $stmt->execute([
                    $user_data['nom'],
                    $user_data['prenom'],
                    $user_data['email'],
                    password_hash('password123', PASSWORD_DEFAULT),
                    $roles_map[$user_data['role']]
                ]);
                echo "<p class='success'>✅ Utilisateur {$user_data['prenom']} {$user_data['nom']} ({$user_data['role']}) créé</p>";
            } else {
                echo "<p class='info'>ℹ️ Utilisateur {$user_data['email']} existe déjà</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur création utilisateur {$user_data['email']} : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // 4. Créer des enseignants
    echo "<div class='step'>";
    echo "<h3>👨‍🏫 4. Création d'Enseignants</h3>";
    
    try {
        $stmt = $pdo->query("
            SELECT u.id, u.nom, u.prenom 
            FROM Utilisateurs u 
            JOIN Roles r ON u.role_id = r.id 
            WHERE r.nom = 'Enseignant'
        ");
        $enseignant_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($enseignant_users as $user) {
            $stmt = $pdo->prepare("SELECT id FROM Enseignants WHERE utilisateur_id = ?");
            $stmt->execute([$user['id']]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Enseignants (utilisateur_id) VALUES (?)");
                $stmt->execute([$user['id']]);
                echo "<p class='success'>✅ Enseignant {$user['prenom']} {$user['nom']} créé</p>";
            } else {
                echo "<p class='info'>ℹ️ Enseignant {$user['prenom']} {$user['nom']} existe déjà</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur création enseignants : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 5. Créer des étudiants
    echo "<div class='step'>";
    echo "<h3>👨‍🎓 5. Création d'Étudiants</h3>";
    
    try {
        $stmt = $pdo->query("
            SELECT u.id, u.nom, u.prenom 
            FROM Utilisateurs u 
            JOIN Roles r ON u.role_id = r.id 
            WHERE r.nom = 'Etudiant'
        ");
        $etudiant_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($etudiant_users as $user) {
            $stmt = $pdo->prepare("SELECT id FROM Etudiants WHERE utilisateur_id = ?");
            $stmt->execute([$user['id']]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Etudiants (utilisateur_id, numero_etudiant) VALUES (?, ?)");
                $numero = 'ETU' . str_pad($user['id'], 4, '0', STR_PAD_LEFT);
                $stmt->execute([$user['id'], $numero]);
                echo "<p class='success'>✅ Étudiant {$user['prenom']} {$user['nom']} créé (N° $numero)</p>";
            } else {
                echo "<p class='info'>ℹ️ Étudiant {$user['prenom']} {$user['nom']} existe déjà</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur création étudiants : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 6. Créer des matières
    echo "<div class='step'>";
    echo "<h3>📚 6. Création de Matières</h3>";
    
    $matieres = ['Mathématiques', 'Français', 'Histoire', 'Géographie', 'Sciences', 'Anglais', 'Sport'];
    
    foreach ($matieres as $matiere_nom) {
        try {
            $stmt = $pdo->prepare("SELECT id FROM Matieres WHERE nom = ?");
            $stmt->execute([$matiere_nom]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Matieres (nom, description) VALUES (?, ?)");
                $stmt->execute([$matiere_nom, "Cours de $matiere_nom"]);
                echo "<p class='success'>✅ Matière '$matiere_nom' créée</p>";
            } else {
                echo "<p class='info'>ℹ️ Matière '$matiere_nom' existe déjà</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur création matière '$matiere_nom' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // 7. Créer des absences de test
    echo "<div class='step'>";
    echo "<h3">📅 7. Création d'Absences de Test</h3>";
    
    try {
        // Récupérer les IDs nécessaires
        $stmt = $pdo->query("SELECT id FROM Etudiants LIMIT 3");
        $etudiants = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $stmt = $pdo->query("SELECT id FROM Enseignants LIMIT 2");
        $enseignants = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $stmt = $pdo->query("SELECT id FROM Matieres LIMIT 3");
        $matieres = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($etudiants) && !empty($enseignants) && !empty($matieres)) {
            $absences_test = [
                [
                    'etudiant_id' => $etudiants[0],
                    'matiere_id' => $matieres[0],
                    'enseignant_id' => $enseignants[0],
                    'date_absence' => date('Y-m-d', strtotime('-5 days')),
                    'justification' => 'Maladie - Certificat médical'
                ],
                [
                    'etudiant_id' => $etudiants[1] ?? $etudiants[0],
                    'matiere_id' => $matieres[1] ?? $matieres[0],
                    'enseignant_id' => $enseignants[1] ?? $enseignants[0],
                    'date_absence' => date('Y-m-d', strtotime('-3 days')),
                    'justification' => 'Rendez-vous médical'
                ],
                [
                    'etudiant_id' => $etudiants[2] ?? $etudiants[0],
                    'matiere_id' => $matieres[2] ?? $matieres[0],
                    'enseignant_id' => $enseignants[0],
                    'date_absence' => date('Y-m-d', strtotime('-1 day')),
                    'justification' => null
                ]
            ];
            
            foreach ($absences_test as $absence) {
                $stmt = $pdo->prepare("
                    INSERT INTO Absences (etudiant_id, matiere_id, enseignant_id, date_absence, justification) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $absence['etudiant_id'],
                    $absence['matiere_id'],
                    $absence['enseignant_id'],
                    $absence['date_absence'],
                    $absence['justification']
                ]);
                echo "<p class='success'>✅ Absence de test créée pour le " . $absence['date_absence'] . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Impossible de créer des absences : données manquantes</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur création absences : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 8. Vérification finale
    echo "<div class='step'>";
    echo "<h3">✅ 8. Vérification Finale</h3>";
    
    try {
        // Test de la requête complète
        $stmt = $pdo->prepare("
            SELECT a.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Absences a 
            JOIN Etudiants e ON a.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON a.matiere_id = m.id
            LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            ORDER BY a.date_absence DESC
            LIMIT 3
        ");
        $stmt->execute();
        $absences = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 Test de la requête API :</h4>";
        
        if (!empty($absences)) {
            echo "<p class='success'>✅ " . count($absences) . " absence(s) trouvée(s)</p>";
            
            foreach ($absences as $absence) {
                $enseignant = $absence['enseignant_nom'] ?: 'NON DÉFINI';
                $matiere = $absence['matiere_nom'] ?: 'NON DÉFINIE';
                $justif = $absence['justification'] ?: 'Non justifiée';
                
                echo "<div class='sql-block'>";
                echo "ID: {$absence['id']} | ";
                echo "Étudiant: {$absence['etudiant_nom']} | ";
                echo "Matière: $matiere | ";
                echo "Enseignant: $enseignant | ";
                echo "Date: {$absence['date_absence']} | ";
                echo "Justification: $justif";
                echo "</div>";
            }
            
            // Vérifier si tous les enseignants sont définis
            $enseignants_definis = 0;
            foreach ($absences as $absence) {
                if (!empty($absence['enseignant_nom'])) {
                    $enseignants_definis++;
                }
            }
            
            if ($enseignants_definis === count($absences)) {
                echo "<p class='success'>🎉 SUCCÈS ! Tous les enseignants sont correctement affichés</p>";
            } else {
                echo "<p class='warning'>⚠️ $enseignants_definis/" . count($absences) . " enseignants définis</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Aucune absence trouvée</p>";
        }
        
        // Statistiques finales
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Absences");
        $total_absences = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Enseignants");
        $total_enseignants = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Etudiants");
        $total_etudiants = $stmt->fetch()['count'];
        
        echo "<h4>📊 Statistiques Finales :</h4>";
        echo "<ul>";
        echo "<li><strong>$total_absences</strong> absence(s) dans la base</li>";
        echo "<li><strong>$total_enseignants</strong> enseignant(s) dans la base</li>";
        echo "<li><strong>$total_etudiants</strong> étudiant(s) dans la base</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur vérification finale : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Instructions finales
    echo "<div class='step'>";
    echo "<h3>🎯 9. Instructions Finales</h3>";
    
    echo "<h4>✅ Prochaines Étapes :</h4>";
    echo "<ol>";
    echo "<li><strong>Tester l'API :</strong> Vérifier que les données sont correctes</li>";
    echo "<li><strong>Actualiser React :</strong> Recharger la page des absences</li>";
    echo "<li><strong>Vérifier les boutons :</strong> S'assurer d'être connecté en Admin/Enseignant</li>";
    echo "<li><strong>Tester les fonctionnalités :</strong> Ajouter/Modifier/Supprimer</li>";
    echo "</ol>";
    
    echo "<h4>🔗 Liens de Test :</h4>";
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost/Project_PFE/Backend/pages/absences/' target='_blank' class='test-button'>🧪 Tester API</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='test-button'>🎯 Interface React</a>";
    echo "<a href='diagnostic.php' target='_blank' class='test-button'>🔍 Diagnostic</a>";
    echo "</div>";
    
    echo "<h4>🐛 Si les problèmes persistent :</h4>";
    echo "<ul>";
    echo "<li>Vérifiez la console du navigateur (F12 → Console)</li>";
    echo "<li>Vérifiez l'onglet Network pour les erreurs API</li>";
    echo "<li>Assurez-vous d'être connecté avec le bon rôle</li>";
    echo "<li>Videz le cache du navigateur</li>";
    echo "</ul>";
    
    echo "<p class='success'>🎉 <strong>Correction terminée !</strong> Les problèmes devraient maintenant être résolus.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
