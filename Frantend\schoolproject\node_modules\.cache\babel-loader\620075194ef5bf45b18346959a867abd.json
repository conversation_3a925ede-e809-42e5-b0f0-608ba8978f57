{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\NotesUnified.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport { filterNotes, canManageData, isStudent, logSecurityEvent } from '../utils/studentDataFilter';\nimport '../css/Factures.css';\nconst NotesUnified = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [notes, setNotes] = useState([]);\n  const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [showGenerateModal, setShowGenerateModal] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterMatiere, setFilterMatiere] = useState('all');\n  const [filterEtudiant, setFilterEtudiant] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [statistics, setStatistics] = useState({});\n  const [matieres, setMatieres] = useState([]);\n  const [etudiants, setEtudiants] = useState([]);\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    devoir_id: '',\n    matiere_id: '',\n    note: ''\n  });\n\n  // Déterminer le rôle et les permissions avec notre système unifié\n  const isEtudiant = isStudent(user);\n  const isEnseignant = (user === null || user === void 0 ? void 0 : user.role) === 'enseignant' || (user === null || user === void 0 ? void 0 : user.role) === 'Enseignant';\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n\n  // Permissions selon les spécifications\n  const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n  const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n  useEffect(() => {\n    if (canView) {\n      fetchNotes();\n      if (isEnseignant) {\n        fetchDevoirsDisponibles();\n        fetchEtudiants();\n        fetchMatieres();\n      }\n    }\n  }, [canView, isEnseignant]);\n  const fetchNotes = async () => {\n    try {\n      console.log('🔄 Chargement des notes...');\n\n      // Déterminer le token selon le rôle\n      let authToken = 'default-token';\n      if (isEtudiant) authToken = 'etudiant-token';else if (isEnseignant) authToken = 'enseignant-token';else if (isAdmin) authToken = 'admin-token';\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n        headers: {\n          Authorization: `Bearer ${authToken}`\n        }\n      });\n      console.log('✅ Réponse API notes:', response.data);\n      if (response.data.success) {\n        const notesData = response.data.data || [];\n        setNotes(notesData);\n\n        // Calculer les statistiques\n        const stats = calculateStatistics(notesData);\n        setStatistics(stats);\n\n        // Extraire les matières et étudiants uniques pour les filtres (seulement si pas déjà chargés)\n        if (!isEtudiant && !isEnseignant) {\n          // Pour les admins, utiliser les noms des notes pour les filtres uniquement\n          if (isAdmin && matieres.length === 0) {\n            const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n            setMatieres(matieresUniques);\n          }\n          if (isAdmin && etudiants.length === 0) {\n            const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n            setEtudiants(etudiantsUniques);\n          }\n        }\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Erreur lors du chargement des notes:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403) {\n        Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n      } else {\n        Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n      }\n      setNotes([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      console.log('🔄 Chargement des étudiants...');\n\n      // Utiliser l'authentification réelle\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n      if (response.data.success) {\n        setEtudiants(response.data.etudiants);\n        console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n        console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n      } else {\n        console.error('❌ Erreur API étudiants:', response.data.error);\n        setEtudiants([]);\n      }\n    } catch (error) {\n      var _error$response2;\n      console.error('❌ Erreur lors du chargement des étudiants:', error);\n      console.error('❌ Détails erreur:', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n      setEtudiants([]);\n    }\n  };\n  const fetchMatieres = async () => {\n    try {\n      console.log('🔄 Chargement des matières...');\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG MATIERES API Response:', response.data);\n\n      // L'API des matières retourne directement un tableau, pas un objet avec success\n      if (Array.isArray(response.data)) {\n        setMatieres(response.data);\n        console.log('✅ Matières chargées:', response.data.length);\n        console.log('📚 Première matière:', response.data[0]);\n      } else if (response.data.error) {\n        console.error('❌ Erreur API matières:', response.data.error);\n        setMatieres([]);\n      } else {\n        console.warn('⚠️ Format de réponse inattendu:', response.data);\n        setMatieres([]);\n      }\n    } catch (error) {\n      var _error$response3;\n      console.error('❌ Erreur lors du chargement des matières:', error);\n      console.error('❌ Détails erreur:', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message);\n      setMatieres([]);\n    }\n  };\n  const fetchDevoirsDisponibles = async () => {\n    try {\n      console.log('🔄 Chargement des devoirs disponibles...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n        headers: {\n          Authorization: `Bearer enseignant-token`\n        }\n      });\n      console.log('✅ Réponse API devoirs disponibles:', response.data);\n      if (response.data.success) {\n        setDevoirsDisponibles(response.data.data || []);\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des devoirs:', error);\n      setDevoirsDisponibles([]);\n    }\n  };\n  const calculateStatistics = notesData => {\n    const total = notesData.length;\n    const moyenne_generale = total > 0 ? notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n    const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n    const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n    const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n    const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n    const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n    const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n    const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n    return {\n      total_notes: total,\n      moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n      notes_excellentes,\n      notes_bonnes,\n      notes_moyennes,\n      notes_faibles,\n      nombre_etudiants: etudiants.length,\n      nombre_matieres: matieres.length,\n      nombre_devoirs: devoirs.length\n    };\n  };\n  const handleGenerateNotes = async devoir_id => {\n    const result = await Swal.fire({\n      title: 'Générer les notes automatiquement ?',\n      text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n      icon: 'question',\n      showCancelButton: true,\n      confirmButtonColor: '#28a745',\n      cancelButtonColor: '#6c757d',\n      confirmButtonText: 'Oui, générer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n        const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          action: 'generate',\n          devoir_id: devoir_id\n        }, {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          }\n        });\n        console.log('✅ Notes générées:', response.data);\n        if (response.data.success) {\n          const details = response.data.details;\n          Swal.fire({\n            title: 'Notes générées !',\n            html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n            icon: 'success',\n            timer: 3000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n        }\n      } catch (error) {\n        var _error$response4, _error$response4$data;\n        console.error('❌ Erreur génération:', error);\n        Swal.fire('Erreur', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || 'Erreur lors de la génération des notes', 'error');\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n      return;\n    }\n    if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n      Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n      return;\n    }\n    try {\n      const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n      const method = editingNote ? 'PUT' : 'POST';\n      const data = editingNote ? {\n        ...formData,\n        id: editingNote.id\n      } : formData;\n      console.log('🔄 Envoi note:', {\n        method,\n        data\n      });\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer enseignant-token`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('✅ Note envoyée:', response.data);\n      if (response.data.success) {\n        Swal.fire({\n          title: 'Succès !',\n          text: response.data.message,\n          icon: 'success',\n          timer: 2000,\n          showConfirmButton: false\n        });\n        setShowModal(false);\n        setEditingNote(null);\n        resetForm();\n        fetchNotes();\n        if (isEnseignant) fetchDevoirsDisponibles();\n      } else {\n        Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n      }\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('❌ Erreur:', error);\n      Swal.fire('Erreur', ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = note => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n      return;\n    }\n    setEditingNote(note);\n    setFormData({\n      etudiant_id: note.etudiant_id || '',\n      devoir_id: note.devoir_id || '',\n      matiere_id: note.matiere_id || '',\n      note: note.note || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Supprimer cette note ?',\n      text: 'Cette action est irréversible !',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🗑️ Suppression note ID:', id);\n        const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        console.log('✅ Note supprimée:', response.data);\n        if (response.data.success) {\n          Swal.fire({\n            title: 'Supprimé !',\n            text: response.data.message,\n            icon: 'success',\n            timer: 2000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          if (isEnseignant) fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n        }\n      } catch (error) {\n        var _error$response6, _error$response6$data;\n        console.error('❌ Erreur suppression:', error);\n        Swal.fire('Erreur', ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.error) || 'Impossible de supprimer la note', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      devoir_id: '',\n      matiere_id: '',\n      note: ''\n    });\n  };\n\n  // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n  const securityFilteredNotes = filterNotes(notes, user);\n\n  // Log de sécurité si des données ont été filtrées\n  if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n    logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n      total: notes.length,\n      filtered: securityFilteredNotes.length\n    });\n  }\n\n  // ÉTAPE 2 : Filtrage par recherche et critères\n  const filteredNotes = securityFilteredNotes.filter(note => {\n    const searchLower = searchTerm.toLowerCase();\n    const matchesSearch = (note.etudiant_nom || '').toLowerCase().includes(searchLower) || (note.devoir_titre || '').toLowerCase().includes(searchLower) || (note.matiere_nom || '').toLowerCase().includes(searchLower) || (note.note || '').toString().includes(searchLower);\n    const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n    const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n    return matchesSearch && matchesMatiere && matchesEtudiant;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, filterMatiere, filterEtudiant]);\n  const getNoteBadge = note => {\n    const noteValue = parseFloat(note);\n    let style = {\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.9em',\n      fontWeight: 'bold'\n    };\n    if (noteValue >= 16) {\n      style = {\n        ...style,\n        backgroundColor: '#28a745',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 20\n        }\n      }, \"\\uD83C\\uDFC6 \", note, \"/20\");\n    } else if (noteValue >= 12) {\n      style = {\n        ...style,\n        backgroundColor: '#17a2b8',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDC4D \", note, \"/20\");\n    } else if (noteValue >= 10) {\n      style = {\n        ...style,\n        backgroundColor: '#ffc107',\n        color: 'black'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCCA \", note, \"/20\");\n    } else {\n      style = {\n        ...style,\n        backgroundColor: '#dc3545',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCC9 \", note, \"/20\");\n    }\n  };\n  const getHeaderTitle = () => {\n    if (isEtudiant) return '📊 Mes Notes';\n    if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n    if (isAdmin) return '🛡️ Administration - Notes';\n    return '📊 Notes';\n  };\n  const getInfoMessage = () => {\n    if (isEtudiant) {\n      return {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffc107',\n          color: '#856404'\n        },\n        text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n      };\n    } else if (isEnseignant) {\n      return {\n        style: {\n          backgroundColor: '#d4edda',\n          border: '1px solid #c3e6cb',\n          color: '#155724'\n        },\n        text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n      };\n    } else if (isAdmin) {\n      return {\n        style: {\n          backgroundColor: '#e2e3e5',\n          border: '1px solid #d6d8db',\n          color: '#383d41'\n        },\n        text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n      };\n    }\n    return null;\n  };\n  const styles = {\n    accessDenied: {\n      textAlign: 'center',\n      padding: '50px 20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      margin: '20px 0'\n    },\n    idBadge: {\n      backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n      color: 'white',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.8em',\n      fontWeight: 'bold'\n    }\n  };\n\n  // Vérification d'accès\n  if (!canView) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"factures-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.accessDenied,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 21\n      }\n    }, \"\\uD83D\\uDEAB Acc\\xE8s Refus\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 21\n      }\n    }, \"Vous n'avez pas les permissions pour acc\\xE9der aux notes.\")));\n  }\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 17\n      }\n    }, \"Chargement des notes...\"));\n  }\n  const infoMessage = getInfoMessage();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 17\n    }\n  }, getHeaderTitle()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 21\n    }\n  }, filteredNotes.length, \" note(s) trouv\\xE9e(s)\"), canManage && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => setShowGenerateModal(true),\n    style: {\n      marginRight: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/auto.png\",\n    alt: \"G\\xE9n\\xE9rer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 33\n    }\n  }), \" G\\xE9n\\xE9ration Auto\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 33\n    }\n  }), \" Nouvelle Note\")))), infoMessage && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...infoMessage.style,\n      borderRadius: '8px',\n      padding: '15px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 21\n    }\n  }, infoMessage.text)), statistics.total_notes > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n      gap: '15px',\n      backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n      border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n      borderRadius: '8px',\n      padding: '20px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: isAdmin ? '#6f42c1' : '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 25\n    }\n  }, statistics.total_notes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 25\n    }\n  }, isEtudiant ? 'Mes Notes' : 'Total Notes')), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#17a2b8'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 25\n    }\n  }, statistics.moyenne_generale), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 25\n    }\n  }, \"Moyenne\")), !isEtudiant && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 33\n    }\n  }, statistics.notes_excellentes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 33\n    }\n  }, \"Excellentes (\\u226516)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#ffc107'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 33\n    }\n  }, statistics.notes_moyennes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 33\n    }\n  }, \"Moyennes (10-12)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 33\n    }\n  }, statistics.notes_faibles), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 33\n    }\n  }, \"Faibles (<10)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#fd7e14'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 33\n    }\n  }, statistics.nombre_etudiants), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiants\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#20c997'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 33\n    }\n  }, statistics.nombre_devoirs), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 33\n    }\n  }, \"Devoirs\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-bar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/search.png\",\n    alt: \"Rechercher\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 21\n    }\n  })), !isEtudiant && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-section\",\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '10px',\n      marginTop: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: filterMatiere,\n    onChange: e => setFilterMatiere(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 29\n    }\n  }, \"\\uD83D\\uDCD6 Toutes les mati\\xE8res\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere,\n    value: matiere,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 33\n    }\n  }, matiere))), isAdmin && /*#__PURE__*/React.createElement(\"select\", {\n    value: filterEtudiant,\n    onChange: e => setFilterEtudiant(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 705,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDC64 Tous les \\xE9tudiants\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant,\n    value: etudiant,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 37\n    }\n  }, etudiant))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 13\n    }\n  }, filteredNotes.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 723,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/empty.png\",\n    alt: \"Aucune donn\\xE9e\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 724,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 25\n    }\n  }, \"Aucune note trouv\\xE9e\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 25\n    }\n  }, isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 729,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), !isEtudiant && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Devoir\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCD6 Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 737,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Note\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 49\n    }\n  }, \"\\uD83C\\uDFEB Classe\"), canManage && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 51\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 29\n    }\n  }, currentNotes.map(note => /*#__PURE__*/React.createElement(\"tr\", {\n    key: note.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.idBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 747,\n      columnNumber: 45\n    }\n  }, \"#\", note.id)), !isEtudiant && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 754,\n      columnNumber: 53\n    }\n  }, note.etudiant_nom || 'Nom non disponible'), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 53\n    }\n  }, note.etudiant_email || 'Email non disponible'))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxWidth: '200px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 765,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 766,\n      columnNumber: 49\n    }\n  }, note.devoir_titre || 'Devoir non spécifié'), note.date_remise && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 53\n    }\n  }, \"Remise: \", note.date_remise))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#fff3e0',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 777,\n      columnNumber: 45\n    }\n  }, note.matiere_nom || 'Non spécifiée')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 41\n    }\n  }, getNoteBadge(note.note)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 45\n    }\n  }, note.date_formatted || note.date_enregistrement)), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '3px 6px',\n      backgroundColor: '#f3e5f5',\n      borderRadius: '4px',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 49\n    }\n  }, note.classe_nom || 'Non spécifiée')), canManage && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 807,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(note),\n    title: \"Modifier la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 814,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(note.id),\n    title: \"Supprimer la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 836,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 21\n    }\n  }, \"Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 845,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 21\n    }\n  }, \"Suivant\")), showGenerateModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 29\n    }\n  }, \"\\uD83E\\uDD16 G\\xE9n\\xE9ration Automatique des Notes\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => setShowGenerateModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      marginBottom: '20px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 29\n    }\n  }, \"S\\xE9lectionnez un devoir pour g\\xE9n\\xE9rer automatiquement les notes \\xE0 partir des r\\xE9ponses aux quiz.\"), devoirsDisponibles.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 878,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 37\n    }\n  }, \"Aucun devoir disponible pour la g\\xE9n\\xE9ration de notes.\")) : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxHeight: '400px',\n      overflowY: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 882,\n      columnNumber: 33\n    }\n  }, devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"div\", {\n    key: devoir.id,\n    style: {\n      border: '1px solid #dee2e6',\n      borderRadius: '8px',\n      padding: '15px',\n      marginBottom: '10px',\n      backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 884,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    style: {\n      margin: '0 0 5px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 893,\n      columnNumber: 53\n    }\n  }, devoir.titre), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      fontSize: '0.9em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 894,\n      columnNumber: 53\n    }\n  }, devoir.matiere_nom, \" - \", devoir.classe_nom), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDCCA \", devoir.nombre_quiz, \" quiz \\u2022 \\uD83D\\uDC65 \", devoir.nombre_etudiants_repondus, \" r\\xE9ponses \\u2022 \\uD83D\\uDCDD \", devoir.notes_existantes, \" notes\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 900,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '2px 6px',\n      borderRadius: '4px',\n      backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n      color: devoir.peut_generer_notes ? '#155724' : '#856404'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 901,\n      columnNumber: 57\n    }\n  }, devoir.statut))), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => {\n      setShowGenerateModal(false);\n      handleGenerateNotes(devoir.id);\n    },\n    disabled: !devoir.peut_generer_notes,\n    title: devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDE80 G\\xE9n\\xE9rer\")))))))), showModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 934,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 935,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 29\n    }\n  }, editingNote ? 'Modifier la note' : 'Nouvelle note'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 946,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 949,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 950,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    style: {\n      borderColor: formData.etudiant_id ? '#28a745' : '#ced4da',\n      borderWidth: '2px',\n      borderStyle: 'solid'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 963,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant...\"), etudiants.length > 0 ? etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.id || etudiant.etudiant_id,\n    value: etudiant.id || etudiant.etudiant_id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 966,\n      columnNumber: 45\n    }\n  }, \"\\uD83D\\uDCDA \", etudiant.nom || etudiant.nom_prenom, \" | Groupe: \", etudiant.groupe_nom || 'N/A', \" | Classe: \", etudiant.classe_nom || 'N/A')) : /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    disabled: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 971,\n      columnNumber: 41\n    }\n  }, \"Chargement des \\xE9tudiants...\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 976,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 977,\n      columnNumber: 33\n    }\n  }, \"Devoir *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.devoir_id,\n    onChange: e => setFormData({\n      ...formData,\n      devoir_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 978,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 984,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un devoir...\"), devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"option\", {\n    key: devoir.id,\n    value: devoir.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 986,\n      columnNumber: 41\n    }\n  }, devoir.devoir_display)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 993,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 994,\n      columnNumber: 33\n    }\n  }, \"Mati\\xE8re *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => setFormData({\n      ...formData,\n      matiere_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    style: {\n      borderColor: formData.matiere_id ? '#28a745' : '#ced4da',\n      borderWidth: '2px',\n      borderStyle: 'solid'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 995,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1006,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner une mati\\xE8re...\"), matieres.length > 0 ? matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1009,\n      columnNumber: 45\n    }\n  }, \"\\uD83D\\uDCD6 \", matiere.nom, \" | Fili\\xE8re: \", matiere.filiere_nom || 'N/A')) : /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    disabled: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1014,\n      columnNumber: 41\n    }\n  }, \"Chargement des mati\\xE8res...\"))), formData.etudiant_id && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#e8f4fd',\n      border: '2px solid #007bff',\n      borderRadius: '12px',\n      padding: '15px',\n      margin: '15px 0',\n      boxShadow: '0 2px 8px rgba(0,123,255,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1021,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    style: {\n      color: '#007bff',\n      margin: '0 0 10px 0',\n      fontSize: '16px',\n      fontWeight: 'bold',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1029,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 Informations de l'\\xE9tudiant\"), (() => {\n    const etudiant = etudiants.find(e => (e.id || e.etudiant_id) == formData.etudiant_id);\n    return etudiant ? /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        fontSize: '14px',\n        lineHeight: '1.6'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 53\n      }\n    }, \"\\uD83D\\uDCDA Nom:\"), \" \", etudiant.nom || etudiant.nom_prenom), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1048,\n        columnNumber: 53\n      }\n    }, \"\\uD83D\\uDC65 Groupe:\"), \" \", etudiant.groupe_nom || 'Non assigné'), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1050,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1051,\n        columnNumber: 53\n      }\n    }, \"\\uD83C\\uDFEB Classe:\"), \" \", etudiant.classe_nom || 'Non assignée'), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1054,\n        columnNumber: 53\n      }\n    }, \"\\uD83C\\uDF93 Fili\\xE8re:\"), \" \", etudiant.filiere_nom || 'Non spécifiée')) : /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        color: '#6c757d',\n        fontStyle: 'italic'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1058,\n        columnNumber: 45\n      }\n    }, \"Informations de l'\\xE9tudiant non disponibles\");\n  })()), formData.matiere_id && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#f0f9ff',\n      border: '2px solid #10b981',\n      borderRadius: '12px',\n      padding: '15px',\n      margin: '15px 0',\n      boxShadow: '0 2px 8px rgba(16,185,129,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1068,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    style: {\n      color: '#10b981',\n      margin: '0 0 10px 0',\n      fontSize: '16px',\n      fontWeight: 'bold',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1076,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCD6 Informations de la mati\\xE8re\"), (() => {\n    const matiere = matieres.find(m => (m.id || m.matiere_id) == formData.matiere_id);\n    return matiere ? /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        fontSize: '14px',\n        lineHeight: '1.6'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1091,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1092,\n        columnNumber: 53\n      }\n    }, \"\\uD83D\\uDCDA Nom:\"), \" \", matiere.nom || matiere.nom_matiere), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1094,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 53\n      }\n    }, \"\\uD83C\\uDF93 Fili\\xE8re:\"), \" \", matiere.filiere_nom || 'Non spécifiée'), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        margin: '5px 0'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1097,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1098,\n        columnNumber: 53\n      }\n    }, \"\\uD83D\\uDCCA Coefficient:\"), \" \", matiere.coefficient || 'Non défini')) : /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        color: '#6c757d',\n        fontStyle: 'italic'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1102,\n        columnNumber: 45\n      }\n    }, \"Informations de la mati\\xE8re non disponibles\");\n  })()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1110,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1111,\n      columnNumber: 33\n    }\n  }, \"Note (sur 20)\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    min: \"0\",\n    max: \"20\",\n    step: \"0.01\",\n    value: formData.note,\n    onChange: e => setFormData({\n      ...formData,\n      note: e.target.value\n    }),\n    placeholder: \"Laisser vide pour calcul automatique\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 33\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1121,\n      columnNumber: 33\n    }\n  }, \"Si vide, la note sera calcul\\xE9e automatiquement \\xE0 partir des r\\xE9ponses aux quiz.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1126,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1127,\n      columnNumber: 33\n    }\n  }, editingNote ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1130,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default NotesUnified;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "axios", "<PERSON><PERSON>", "AuthContext", "filterNotes", "canManageData", "isStudent", "logSecurityEvent", "NotesUnified", "user", "notes", "setNotes", "devoirsDisponibles", "setDevoirsDisponibles", "loading", "setLoading", "showModal", "setShowModal", "showGenerateModal", "setShowGenerateModal", "editingNote", "setEditingNote", "searchTerm", "setSearchTerm", "filterMatiere", "setFilterMatiere", "filterEtudiant", "setFilterEtudiant", "currentPage", "setCurrentPage", "itemsPerPage", "statistics", "setStatistics", "matieres", "set<PERSON>ati<PERSON>s", "etudiants", "setEtudiants", "formData", "setFormData", "etudiant_id", "devoir_id", "matiere_id", "note", "isEtudiant", "isEnseignant", "role", "isAdmin", "canManage", "canView", "fetchNotes", "fetchDevoirsDisponibles", "fetchEtudiants", "fetchMatieres", "console", "log", "authToken", "response", "get", "headers", "Authorization", "data", "success", "notesData", "stats", "calculateStatistics", "length", "matieresUniques", "Set", "map", "n", "matiere_nom", "filter", "Boolean", "etudiantsUniques", "etudiant_nom", "error", "_error$response", "status", "fire", "token", "localStorage", "getItem", "_error$response2", "message", "Array", "isArray", "warn", "_error$response3", "total", "moyenne_generale", "reduce", "sum", "parseFloat", "notes_excellentes", "notes_bonnes", "notes_moyennes", "notes_faibles", "devoirs", "total_notes", "Math", "round", "nombre_etudiants", "nombre_matieres", "nombre_devoirs", "handleGenerateNotes", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "post", "action", "details", "html", "notes_generees", "notes_mises_a_jour", "total_etudiants", "timer", "showConfirmButton", "_error$response4", "_error$response4$data", "handleSubmit", "e", "preventDefault", "url", "method", "id", "resetForm", "_error$response5", "_error$response5$data", "handleEdit", "handleDelete", "delete", "_error$response6", "_error$response6$data", "securityFilteredNotes", "filtered", "filteredNotes", "searchLower", "toLowerCase", "matchesSearch", "includes", "devoir_titre", "toString", "matchesMatiere", "matchesEtudiant", "indexOfLastItem", "indexOfFirstItem", "currentNotes", "slice", "totalPages", "ceil", "paginate", "pageNumber", "getNoteBadge", "noteValue", "style", "padding", "borderRadius", "fontSize", "fontWeight", "backgroundColor", "color", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getHeaderTitle", "getInfoMessage", "border", "styles", "accessDenied", "textAlign", "margin", "idBadge", "className", "infoMessage", "onClick", "marginRight", "src", "alt", "display", "gridTemplateColumns", "gap", "boxShadow", "Fragment", "type", "placeholder", "value", "onChange", "target", "marginTop", "matiere", "key", "etudiant", "etudiant_email", "max<PERSON><PERSON><PERSON>", "date_remise", "date_formatted", "date_enregistrement", "classe_nom", "disabled", "marginBottom", "maxHeight", "overflowY", "devoir", "peut_generer_notes", "justifyContent", "alignItems", "titre", "nombre_quiz", "nombre_etudiants_repondus", "notes_existantes", "statut", "onSubmit", "required", "borderColor", "borderWidth", "borderStyle", "nom", "nom_prenom", "groupe_nom", "devoir_display", "filiere_nom", "find", "lineHeight", "fontStyle", "m", "nom_matiere", "coefficient", "min", "max", "step"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/NotesUnified.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport {\n    filterNotes,\n    canManageData,\n    isStudent,\n    logSecurityEvent\n} from '../utils/studentDataFilter';\nimport '../css/Factures.css';\n\nconst NotesUnified = () => {\n    const { user } = useContext(AuthContext);\n    const [notes, setNotes] = useState([]);\n    const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [showGenerateModal, setShowGenerateModal] = useState(false);\n    const [editingNote, setEditingNote] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterMatiere, setFilterMatiere] = useState('all');\n    const [filterEtudiant, setFilterEtudiant] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [statistics, setStatistics] = useState({});\n    const [matieres, setMatieres] = useState([]);\n    const [etudiants, setEtudiants] = useState([]);\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        devoir_id: '',\n        matiere_id: '',\n        note: ''\n    });\n\n    // Déterminer le rôle et les permissions avec notre système unifié\n    const isEtudiant = isStudent(user);\n    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';\n\n    // Permissions selon les spécifications\n    const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n    const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n    useEffect(() => {\n        if (canView) {\n            fetchNotes();\n            if (isEnseignant) {\n                fetchDevoirsDisponibles();\n                fetchEtudiants();\n                fetchMatieres();\n            }\n        }\n    }, [canView, isEnseignant]);\n\n    const fetchNotes = async () => {\n        try {\n            console.log('🔄 Chargement des notes...');\n\n            // Déterminer le token selon le rôle\n            let authToken = 'default-token';\n            if (isEtudiant) authToken = 'etudiant-token';\n            else if (isEnseignant) authToken = 'enseignant-token';\n            else if (isAdmin) authToken = 'admin-token';\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                headers: { Authorization: `Bearer ${authToken}` }\n            });\n\n            console.log('✅ Réponse API notes:', response.data);\n            if (response.data.success) {\n                const notesData = response.data.data || [];\n                setNotes(notesData);\n                \n                // Calculer les statistiques\n                const stats = calculateStatistics(notesData);\n                setStatistics(stats);\n                \n                // Extraire les matières et étudiants uniques pour les filtres (seulement si pas déjà chargés)\n                if (!isEtudiant && !isEnseignant) {\n                    // Pour les admins, utiliser les noms des notes pour les filtres uniquement\n                    if (isAdmin && matieres.length === 0) {\n                        const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n                        setMatieres(matieresUniques);\n                    }\n\n                    if (isAdmin && etudiants.length === 0) {\n                        const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n                        setEtudiants(etudiantsUniques);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des notes:', error);\n            if (error.response?.status === 403) {\n                Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n            } else {\n                Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n            }\n            setNotes([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            console.log('🔄 Chargement des étudiants...');\n\n            // Utiliser l'authentification réelle\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n\n            if (response.data.success) {\n                setEtudiants(response.data.etudiants);\n                console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n                console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n            } else {\n                console.error('❌ Erreur API étudiants:', response.data.error);\n                setEtudiants([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des étudiants:', error);\n            console.error('❌ Détails erreur:', error.response?.data || error.message);\n            setEtudiants([]);\n        }\n    };\n\n    const fetchMatieres = async () => {\n        try {\n            console.log('🔄 Chargement des matières...');\n\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG MATIERES API Response:', response.data);\n\n            // L'API des matières retourne directement un tableau, pas un objet avec success\n            if (Array.isArray(response.data)) {\n                setMatieres(response.data);\n                console.log('✅ Matières chargées:', response.data.length);\n                console.log('📚 Première matière:', response.data[0]);\n            } else if (response.data.error) {\n                console.error('❌ Erreur API matières:', response.data.error);\n                setMatieres([]);\n            } else {\n                console.warn('⚠️ Format de réponse inattendu:', response.data);\n                setMatieres([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des matières:', error);\n            console.error('❌ Détails erreur:', error.response?.data || error.message);\n            setMatieres([]);\n        }\n    };\n\n    const fetchDevoirsDisponibles = async () => {\n        try {\n            console.log('🔄 Chargement des devoirs disponibles...');\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n                headers: { Authorization: `Bearer enseignant-token` }\n            });\n\n            console.log('✅ Réponse API devoirs disponibles:', response.data);\n            if (response.data.success) {\n                setDevoirsDisponibles(response.data.data || []);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des devoirs:', error);\n            setDevoirsDisponibles([]);\n        }\n    };\n\n    const calculateStatistics = (notesData) => {\n        const total = notesData.length;\n        const moyenne_generale = total > 0 ? \n            notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n        \n        const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n        const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n        const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n        const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n        \n        const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n        const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n        const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n        \n        return {\n            total_notes: total,\n            moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n            notes_excellentes,\n            notes_bonnes,\n            notes_moyennes,\n            notes_faibles,\n            nombre_etudiants: etudiants.length,\n            nombre_matieres: matieres.length,\n            nombre_devoirs: devoirs.length\n        };\n    };\n\n    const handleGenerateNotes = async (devoir_id) => {\n        const result = await Swal.fire({\n            title: 'Générer les notes automatiquement ?',\n            text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#28a745',\n            cancelButtonColor: '#6c757d',\n            confirmButtonText: 'Oui, générer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n                \n                const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    action: 'generate',\n                    devoir_id: devoir_id\n                }, {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n\n                console.log('✅ Notes générées:', response.data);\n\n                if (response.data.success) {\n                    const details = response.data.details;\n                    Swal.fire({\n                        title: 'Notes générées !',\n                        html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n                        icon: 'success',\n                        timer: 3000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur génération:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Erreur lors de la génération des notes', 'error');\n            }\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n            return;\n        }\n\n        if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n            return;\n        }\n\n        try {\n            const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n            const method = editingNote ? 'PUT' : 'POST';\n            const data = editingNote ? { ...formData, id: editingNote.id } : formData;\n\n            console.log('🔄 Envoi note:', { method, data });\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    Authorization: `Bearer enseignant-token`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            console.log('✅ Note envoyée:', response.data);\n\n            if (response.data.success) {\n                Swal.fire({\n                    title: 'Succès !',\n                    text: response.data.message,\n                    icon: 'success',\n                    timer: 2000,\n                    showConfirmButton: false\n                });\n                setShowModal(false);\n                setEditingNote(null);\n                resetForm();\n                fetchNotes();\n                if (isEnseignant) fetchDevoirsDisponibles();\n            } else {\n                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n            }\n        } catch (error) {\n            console.error('❌ Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (note) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n            return;\n        }\n\n        setEditingNote(note);\n        setFormData({\n            etudiant_id: note.etudiant_id || '',\n            devoir_id: note.devoir_id || '',\n            matiere_id: note.matiere_id || '',\n            note: note.note || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Supprimer cette note ?',\n            text: 'Cette action est irréversible !',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🗑️ Suppression note ID:', id);\n                \n                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n\n                console.log('✅ Note supprimée:', response.data);\n\n                if (response.data.success) {\n                    Swal.fire({\n                        title: 'Supprimé !',\n                        text: response.data.message,\n                        icon: 'success',\n                        timer: 2000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    if (isEnseignant) fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur suppression:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la note', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            devoir_id: '',\n            matiere_id: '',\n            note: ''\n        });\n    };\n\n    // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n    const securityFilteredNotes = filterNotes(notes, user);\n\n    // Log de sécurité si des données ont été filtrées\n    if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n        logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n            total: notes.length,\n            filtered: securityFilteredNotes.length\n        });\n    }\n\n    // ÉTAPE 2 : Filtrage par recherche et critères\n    const filteredNotes = securityFilteredNotes.filter(note => {\n        const searchLower = searchTerm.toLowerCase();\n        const matchesSearch = (\n            (note.etudiant_nom || '').toLowerCase().includes(searchLower) ||\n            (note.devoir_titre || '').toLowerCase().includes(searchLower) ||\n            (note.matiere_nom || '').toLowerCase().includes(searchLower) ||\n            (note.note || '').toString().includes(searchLower)\n        );\n\n        const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n        const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n\n        return matchesSearch && matchesMatiere && matchesEtudiant;\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when filters change\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm, filterMatiere, filterEtudiant]);\n\n    const getNoteBadge = (note) => {\n        const noteValue = parseFloat(note);\n        let style = {\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.9em',\n            fontWeight: 'bold'\n        };\n\n        if (noteValue >= 16) {\n            style = {...style, backgroundColor: '#28a745', color: 'white'};\n            return <span style={style}>🏆 {note}/20</span>;\n        } else if (noteValue >= 12) {\n            style = {...style, backgroundColor: '#17a2b8', color: 'white'};\n            return <span style={style}>👍 {note}/20</span>;\n        } else if (noteValue >= 10) {\n            style = {...style, backgroundColor: '#ffc107', color: 'black'};\n            return <span style={style}>📊 {note}/20</span>;\n        } else {\n            style = {...style, backgroundColor: '#dc3545', color: 'white'};\n            return <span style={style}>📉 {note}/20</span>;\n        }\n    };\n\n    const getHeaderTitle = () => {\n        if (isEtudiant) return '📊 Mes Notes';\n        if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n        if (isAdmin) return '🛡️ Administration - Notes';\n        return '📊 Notes';\n    };\n\n    const getInfoMessage = () => {\n        if (isEtudiant) {\n            return {\n                style: { backgroundColor: '#fff3cd', border: '1px solid #ffc107', color: '#856404' },\n                text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n            };\n        } else if (isEnseignant) {\n            return {\n                style: { backgroundColor: '#d4edda', border: '1px solid #c3e6cb', color: '#155724' },\n                text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n            };\n        } else if (isAdmin) {\n            return {\n                style: { backgroundColor: '#e2e3e5', border: '1px solid #d6d8db', color: '#383d41' },\n                text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n            };\n        }\n        return null;\n    };\n\n    const styles = {\n        accessDenied: {\n            textAlign: 'center',\n            padding: '50px 20px',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '8px',\n            margin: '20px 0'\n        },\n        idBadge: {\n            backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n            color: 'white',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.8em',\n            fontWeight: 'bold'\n        }\n    };\n\n    // Vérification d'accès\n    if (!canView) {\n        return (\n            <div className=\"factures-container\">\n                <div style={styles.accessDenied}>\n                    <h2>🚫 Accès Refusé</h2>\n                    <p>Vous n'avez pas les permissions pour accéder aux notes.</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des notes...</p>\n            </div>\n        );\n    }\n\n    const infoMessage = getInfoMessage();\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>{getHeaderTitle()}</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredNotes.length} note(s) trouvée(s)\n                    </span>\n                    {canManage && (\n                        <div>\n                            <button \n                                className=\"btn btn-success\"\n                                onClick={() => setShowGenerateModal(true)}\n                                style={{ marginRight: '10px' }}\n                            >\n                                <img src=\"/auto.png\" alt=\"Générer\" /> Génération Auto\n                            </button>\n                            <button \n                                className=\"btn btn-primary\"\n                                onClick={() => setShowModal(true)}\n                            >\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvelle Note\n                            </button>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information selon le rôle */}\n            {infoMessage && (\n                <div style={{\n                    ...infoMessage.style,\n                    borderRadius: '8px',\n                    padding: '15px',\n                    margin: '20px 0'\n                }}>\n                    <p style={{ margin: '0' }}>{infoMessage.text}</p>\n                </div>\n            )}\n\n            {/* Statistiques selon le rôle */}\n            {statistics.total_notes > 0 && (\n                <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n                    gap: '15px',\n                    backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n                    border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n                    borderRadius: '8px',\n                    padding: '20px',\n                    margin: '20px 0'\n                }}>\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: isAdmin ? '#6f42c1' : '#007bff' }}>\n                            {statistics.total_notes}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>\n                            {isEtudiant ? 'Mes Notes' : 'Total Notes'}\n                        </div>\n                    </div>\n\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#17a2b8' }}>\n                            {statistics.moyenne_generale}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyenne</div>\n                    </div>\n\n                    {!isEtudiant && (\n                        <>\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>\n                                    {statistics.notes_excellentes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Excellentes (≥16)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>\n                                    {statistics.notes_moyennes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyennes (10-12)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>\n                                    {statistics.notes_faibles}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Faibles (&lt;10)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fd7e14' }}>\n                                    {statistics.nombre_etudiants}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Étudiants</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20c997' }}>\n                                    {statistics.nombre_devoirs}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Devoirs</div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            )}\n\n            {/* Filtres et recherche */}\n            <div className=\"search-section\">\n                <div className=\"search-bar\">\n                    <img src=\"/search.png\" alt=\"Rechercher\" />\n                    <input\n                        type=\"text\"\n                        placeholder={isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\"}\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                </div>\n\n                {!isEtudiant && (\n                    <div className=\"filter-section\" style={{\n                        display: 'grid',\n                        gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '10px',\n                        marginTop: '15px'\n                    }}>\n                        <select\n                            value={filterMatiere}\n                            onChange={(e) => setFilterMatiere(e.target.value)}\n                            className=\"filter-select\"\n                        >\n                            <option value=\"all\">📖 Toutes les matières</option>\n                            {matieres.map(matiere => (\n                                <option key={matiere} value={matiere}>{matiere}</option>\n                            ))}\n                        </select>\n\n                        {isAdmin && (\n                            <select\n                                value={filterEtudiant}\n                                onChange={(e) => setFilterEtudiant(e.target.value)}\n                                className=\"filter-select\"\n                            >\n                                <option value=\"all\">👤 Tous les étudiants</option>\n                                {etudiants.map(etudiant => (\n                                    <option key={etudiant} value={etudiant}>{etudiant}</option>\n                                ))}\n                            </select>\n                        )}\n                    </div>\n                )}\n            </div>\n\n            {/* Tableau des notes */}\n            <div className=\"table-container\">\n                {filteredNotes.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/empty.png\" alt=\"Aucune donnée\" />\n                        <p>Aucune note trouvée</p>\n                        <p>{isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\"}</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>🆔 ID</th>\n                                    {!isEtudiant && <th>👤 Étudiant</th>}\n                                    <th>📚 Devoir</th>\n                                    <th>📖 Matière</th>\n                                    <th>📊 Note</th>\n                                    <th>📅 Date</th>\n                                    {isAdmin && <th>🏫 Classe</th>}\n                                    {canManage && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentNotes.map((note) => (\n                                    <tr key={note.id}>\n                                        <td>\n                                            <span style={styles.idBadge}>\n                                                #{note.id}\n                                            </span>\n                                        </td>\n                                        {!isEtudiant && (\n                                            <td>\n                                                <div className=\"user-info\">\n                                                    <strong style={{ fontSize: '0.9em' }}>\n                                                        {note.etudiant_nom || 'Nom non disponible'}\n                                                    </strong>\n                                                    <br />\n                                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                                        {note.etudiant_email || 'Email non disponible'}\n                                                    </small>\n                                                </div>\n                                            </td>\n                                        )}\n                                        <td>\n                                            <div style={{ maxWidth: '200px' }}>\n                                                <strong style={{ fontSize: '0.9em' }}>\n                                                    {note.devoir_titre || 'Devoir non spécifié'}\n                                                </strong>\n                                                {note.date_remise && (\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d' }}>\n                                                        Remise: {note.date_remise}\n                                                    </div>\n                                                )}\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#fff3e0',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {note.matiere_nom || 'Non spécifiée'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            {getNoteBadge(note.note)}\n                                        </td>\n                                        <td>\n                                            <span style={{ fontSize: '0.9em' }}>\n                                                {note.date_formatted || note.date_enregistrement}\n                                            </span>\n                                        </td>\n                                        {isAdmin && (\n                                            <td>\n                                                <span style={{\n                                                    padding: '3px 6px',\n                                                    backgroundColor: '#f3e5f5',\n                                                    borderRadius: '4px',\n                                                    fontSize: '0.8em'\n                                                }}>\n                                                    {note.classe_nom || 'Non spécifiée'}\n                                                </span>\n                                            </td>\n                                        )}\n                                        {canManage && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(note)}\n                                                        title=\"Modifier la note\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(note.id)}\n                                                        title=\"Supprimer la note\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div className=\"pagination\">\n                    <button\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Précédent\n                    </button>\n\n                    <div className=\"page-info\">\n                        Page {currentPage} sur {totalPages}\n                    </div>\n\n                    <button\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Suivant\n                    </button>\n                </div>\n            )}\n\n            {/* Modal de génération automatique (enseignants uniquement) */}\n            {showGenerateModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>🤖 Génération Automatique des Notes</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => setShowGenerateModal(false)}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <div style={{ padding: '20px' }}>\n                            <p style={{ marginBottom: '20px', color: '#6c757d' }}>\n                                Sélectionnez un devoir pour générer automatiquement les notes à partir des réponses aux quiz.\n                            </p>\n\n                            {devoirsDisponibles.length === 0 ? (\n                                <div style={{ textAlign: 'center', padding: '20px' }}>\n                                    <p>Aucun devoir disponible pour la génération de notes.</p>\n                                </div>\n                            ) : (\n                                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <div key={devoir.id} style={{\n                                            border: '1px solid #dee2e6',\n                                            borderRadius: '8px',\n                                            padding: '15px',\n                                            marginBottom: '10px',\n                                            backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n                                        }}>\n                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                                <div>\n                                                    <h5 style={{ margin: '0 0 5px 0' }}>{devoir.titre}</h5>\n                                                    <p style={{ margin: '0', fontSize: '0.9em', color: '#6c757d' }}>\n                                                        {devoir.matiere_nom} - {devoir.classe_nom}\n                                                    </p>\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px' }}>\n                                                        📊 {devoir.nombre_quiz} quiz • 👥 {devoir.nombre_etudiants_repondus} réponses • 📝 {devoir.notes_existantes} notes\n                                                    </div>\n                                                    <div style={{ fontSize: '0.8em', marginTop: '5px' }}>\n                                                        <span style={{\n                                                            padding: '2px 6px',\n                                                            borderRadius: '4px',\n                                                            backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n                                                            color: devoir.peut_generer_notes ? '#155724' : '#856404'\n                                                        }}>\n                                                            {devoir.statut}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                                <button\n                                                    className=\"btn btn-success\"\n                                                    onClick={() => {\n                                                        setShowGenerateModal(false);\n                                                        handleGenerateNotes(devoir.id);\n                                                    }}\n                                                    disabled={!devoir.peut_generer_notes}\n                                                    title={devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible'}\n                                                >\n                                                    🚀 Générer\n                                                </button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier une note (enseignants uniquement) */}\n            {showModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingNote ? 'Modifier la note' : 'Nouvelle note'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingNote(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant *</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                    style={{\n                                        borderColor: formData.etudiant_id ? '#28a745' : '#ced4da',\n                                        borderWidth: '2px',\n                                        borderStyle: 'solid'\n                                    }}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant...</option>\n                                    {etudiants.length > 0 ? (\n                                        etudiants.map(etudiant => (\n                                            <option key={etudiant.id || etudiant.etudiant_id} value={etudiant.id || etudiant.etudiant_id}>\n                                                📚 {etudiant.nom || etudiant.nom_prenom} | Groupe: {etudiant.groupe_nom || 'N/A'} | Classe: {etudiant.classe_nom || 'N/A'}\n                                            </option>\n                                        ))\n                                    ) : (\n                                        <option value=\"\" disabled>Chargement des étudiants...</option>\n                                    )}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Devoir *</label>\n                                <select\n                                    value={formData.devoir_id}\n                                    onChange={(e) => setFormData({...formData, devoir_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                >\n                                    <option value=\"\">Sélectionner un devoir...</option>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <option key={devoir.id} value={devoir.id}>\n                                            {devoir.devoir_display}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Matière *</label>\n                                <select\n                                    value={formData.matiere_id}\n                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                    style={{\n                                        borderColor: formData.matiere_id ? '#28a745' : '#ced4da',\n                                        borderWidth: '2px',\n                                        borderStyle: 'solid'\n                                    }}\n                                >\n                                    <option value=\"\">Sélectionner une matière...</option>\n                                    {matieres.length > 0 ? (\n                                        matieres.map(matiere => (\n                                            <option key={matiere.id} value={matiere.id}>\n                                                📖 {matiere.nom} | Filière: {matiere.filiere_nom || 'N/A'}\n                                            </option>\n                                        ))\n                                    ) : (\n                                        <option value=\"\" disabled>Chargement des matières...</option>\n                                    )}\n                                </select>\n                            </div>\n\n                            {/* Panneau d'informations de l'étudiant sélectionné */}\n                            {formData.etudiant_id && (\n                                <div style={{\n                                    backgroundColor: '#e8f4fd',\n                                    border: '2px solid #007bff',\n                                    borderRadius: '12px',\n                                    padding: '15px',\n                                    margin: '15px 0',\n                                    boxShadow: '0 2px 8px rgba(0,123,255,0.1)'\n                                }}>\n                                    <h4 style={{\n                                        color: '#007bff',\n                                        margin: '0 0 10px 0',\n                                        fontSize: '16px',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px'\n                                    }}>\n                                        👤 Informations de l'étudiant\n                                    </h4>\n                                    {(() => {\n                                        const etudiant = etudiants.find(e => (e.id || e.etudiant_id) == formData.etudiant_id);\n                                        return etudiant ? (\n                                            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>📚 Nom:</strong> {etudiant.nom || etudiant.nom_prenom}\n                                                </p>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>👥 Groupe:</strong> {etudiant.groupe_nom || 'Non assigné'}\n                                                </p>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>🏫 Classe:</strong> {etudiant.classe_nom || 'Non assignée'}\n                                                </p>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>🎓 Filière:</strong> {etudiant.filiere_nom || 'Non spécifiée'}\n                                                </p>\n                                            </div>\n                                        ) : (\n                                            <p style={{ color: '#6c757d', fontStyle: 'italic' }}>\n                                                Informations de l'étudiant non disponibles\n                                            </p>\n                                        );\n                                    })()}\n                                </div>\n                            )}\n\n                            {/* Panneau d'informations de la matière sélectionnée */}\n                            {formData.matiere_id && (\n                                <div style={{\n                                    backgroundColor: '#f0f9ff',\n                                    border: '2px solid #10b981',\n                                    borderRadius: '12px',\n                                    padding: '15px',\n                                    margin: '15px 0',\n                                    boxShadow: '0 2px 8px rgba(16,185,129,0.1)'\n                                }}>\n                                    <h4 style={{\n                                        color: '#10b981',\n                                        margin: '0 0 10px 0',\n                                        fontSize: '16px',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px'\n                                    }}>\n                                        📖 Informations de la matière\n                                    </h4>\n                                    {(() => {\n                                        const matiere = matieres.find(m => (m.id || m.matiere_id) == formData.matiere_id);\n                                        return matiere ? (\n                                            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>📚 Nom:</strong> {matiere.nom || matiere.nom_matiere}\n                                                </p>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>🎓 Filière:</strong> {matiere.filiere_nom || 'Non spécifiée'}\n                                                </p>\n                                                <p style={{ margin: '5px 0' }}>\n                                                    <strong>📊 Coefficient:</strong> {matiere.coefficient || 'Non défini'}\n                                                </p>\n                                            </div>\n                                        ) : (\n                                            <p style={{ color: '#6c757d', fontStyle: 'italic' }}>\n                                                Informations de la matière non disponibles\n                                            </p>\n                                        );\n                                    })()}\n                                </div>\n                            )}\n\n                            <div className=\"form-group\">\n                                <label>Note (sur 20)</label>\n                                <input\n                                    type=\"number\"\n                                    min=\"0\"\n                                    max=\"20\"\n                                    step=\"0.01\"\n                                    value={formData.note}\n                                    onChange={(e) => setFormData({...formData, note: e.target.value})}\n                                    placeholder=\"Laisser vide pour calcul automatique\"\n                                />\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    Si vide, la note sera calculée automatiquement à partir des réponses aux quiz.\n                                </small>\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingNote ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingNote(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default NotesUnified;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SACIC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,gBAAgB,QACb,4BAA4B;AACnC,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGT,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC;IACrCyC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGrC,SAAS,CAACG,IAAI,CAAC;EAClC,MAAMmC,YAAY,GAAG,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,YAAY,IAAI,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,YAAY;EAC/E,MAAMC,OAAO,GAAG,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO,IAAI,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO;;EAEhE;EACA,MAAME,SAAS,GAAG1C,aAAa,CAACI,IAAI,CAAC,CAAC,CAAC;EACvC,MAAMuC,OAAO,GAAGL,UAAU,IAAIC,YAAY,IAAIE,OAAO,CAAC,CAAC;;EAEvD/C,SAAS,CAAC,MAAM;IACZ,IAAIiD,OAAO,EAAE;MACTC,UAAU,CAAC,CAAC;MACZ,IAAIL,YAAY,EAAE;QACdM,uBAAuB,CAAC,CAAC;QACzBC,cAAc,CAAC,CAAC;QAChBC,aAAa,CAAC,CAAC;MACnB;IACJ;EACJ,CAAC,EAAE,CAACJ,OAAO,EAAEJ,YAAY,CAAC,CAAC;EAE3B,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAI,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;MAEzC;MACA,IAAIC,SAAS,GAAG,eAAe;MAC/B,IAAIZ,UAAU,EAAEY,SAAS,GAAG,gBAAgB,CAAC,KACxC,IAAIX,YAAY,EAAEW,SAAS,GAAG,kBAAkB,CAAC,KACjD,IAAIT,OAAO,EAAES,SAAS,GAAG,aAAa;MAE3C,MAAMC,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,GAAG,CAAC,0DAA0D,EAAE;QACzFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,SAAS;QAAG;MACpD,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAClD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMC,SAAS,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CjD,QAAQ,CAACmD,SAAS,CAAC;;QAEnB;QACA,MAAMC,KAAK,GAAGC,mBAAmB,CAACF,SAAS,CAAC;QAC5C9B,aAAa,CAAC+B,KAAK,CAAC;;QAEpB;QACA,IAAI,CAACpB,UAAU,IAAI,CAACC,YAAY,EAAE;UAC9B;UACA,IAAIE,OAAO,IAAIb,QAAQ,CAACgC,MAAM,KAAK,CAAC,EAAE;YAClC,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;YACvFtC,WAAW,CAACgC,eAAe,CAAC;UAChC;UAEA,IAAIpB,OAAO,IAAIX,SAAS,CAAC8B,MAAM,KAAK,CAAC,EAAE;YACnC,MAAMQ,gBAAgB,GAAG,CAAC,GAAG,IAAIN,GAAG,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACK,YAAY,CAAC,CAACH,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;YACzFpC,YAAY,CAACqC,gBAAgB,CAAC;UAClC;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA;MACZvB,OAAO,CAACsB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACnB,QAAQ,cAAAoB,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAChC3E,IAAI,CAAC4E,IAAI,CAAC,cAAc,EAAE,2DAA2D,EAAE,OAAO,CAAC;MACnG,CAAC,MAAM;QACH5E,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,iCAAiC,EAAE,OAAO,CAAC;MACnE;MACAnE,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNI,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMoC,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMzB,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,GAAG,CAAC,mEAAmE,EAAE;QAClGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUoB,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF1B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAE9D,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBzB,YAAY,CAACoB,QAAQ,CAACI,IAAI,CAACzB,SAAS,CAAC;QACrCkB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAACzB,SAAS,CAAC8B,MAAM,CAAC;QACnEZ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAACzB,SAAS,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC,MAAM;QACHkB,OAAO,CAACsB,KAAK,CAAC,yBAAyB,EAAEnB,QAAQ,CAACI,IAAI,CAACe,KAAK,CAAC;QAC7DvC,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MAAA,IAAAO,gBAAA;MACZ7B,OAAO,CAACsB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEtB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,EAAE,EAAAO,gBAAA,GAAAP,KAAK,CAACnB,QAAQ,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBtB,IAAI,KAAIe,KAAK,CAACQ,OAAO,CAAC;MACzE/C,YAAY,CAAC,EAAE,CAAC;IACpB;EACJ,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAE5C,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMzB,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,GAAG,CAAC,iEAAiE,EAAE;QAChGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUoB,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF1B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,QAAQ,CAACI,IAAI,CAAC;;MAE7D;MACA,IAAIwB,KAAK,CAACC,OAAO,CAAC7B,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC9B1B,WAAW,CAACsB,QAAQ,CAACI,IAAI,CAAC;QAC1BP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAACK,MAAM,CAAC;QACzDZ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM,IAAIJ,QAAQ,CAACI,IAAI,CAACe,KAAK,EAAE;QAC5BtB,OAAO,CAACsB,KAAK,CAAC,wBAAwB,EAAEnB,QAAQ,CAACI,IAAI,CAACe,KAAK,CAAC;QAC5DzC,WAAW,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACHmB,OAAO,CAACiC,IAAI,CAAC,iCAAiC,EAAE9B,QAAQ,CAACI,IAAI,CAAC;QAC9D1B,WAAW,CAAC,EAAE,CAAC;MACnB;IACJ,CAAC,CAAC,OAAOyC,KAAK,EAAE;MAAA,IAAAY,gBAAA;MACZlC,OAAO,CAACsB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEtB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,EAAE,EAAAY,gBAAA,GAAAZ,KAAK,CAACnB,QAAQ,cAAA+B,gBAAA,uBAAdA,gBAAA,CAAgB3B,IAAI,KAAIe,KAAK,CAACQ,OAAO,CAAC;MACzEjD,WAAW,CAAC,EAAE,CAAC;IACnB;EACJ,CAAC;EAED,MAAMgB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACAG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAEvD,MAAME,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,GAAG,CAAC,0EAA0E,EAAE;QACzGC,OAAO,EAAE;UAAEC,aAAa,EAAE;QAA0B;MACxD,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAChE,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBhD,qBAAqB,CAAC2C,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACnD;IACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;MACZtB,OAAO,CAACsB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE9D,qBAAqB,CAAC,EAAE,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMmD,mBAAmB,GAAIF,SAAS,IAAK;IACvC,MAAM0B,KAAK,GAAG1B,SAAS,CAACG,MAAM;IAC9B,MAAMwB,gBAAgB,GAAGD,KAAK,GAAG,CAAC,GAC9B1B,SAAS,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEtB,CAAC,KAAKsB,GAAG,GAAGC,UAAU,CAACvB,CAAC,CAAC3B,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG8C,KAAK,GAAG,CAAC;IAE9E,MAAMK,iBAAiB,GAAG/B,SAAS,CAACS,MAAM,CAACF,CAAC,IAAIuB,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,IAAI,EAAE,CAAC,CAACuB,MAAM;IAChF,MAAM6B,YAAY,GAAGhC,SAAS,CAACS,MAAM,CAACF,CAAC,IAAIuB,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,IAAI,EAAE,IAAIkD,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACuB,MAAM;IACtG,MAAM8B,cAAc,GAAGjC,SAAS,CAACS,MAAM,CAACF,CAAC,IAAIuB,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,IAAI,EAAE,IAAIkD,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACuB,MAAM;IACxG,MAAM+B,aAAa,GAAGlC,SAAS,CAACS,MAAM,CAACF,CAAC,IAAIuB,UAAU,CAACvB,CAAC,CAAC3B,IAAI,CAAC,GAAG,EAAE,CAAC,CAACuB,MAAM;IAE3E,MAAM9B,SAAS,GAAG,CAAC,GAAG,IAAIgC,GAAG,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC9B,WAAW,CAAC,CAACgC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,MAAMvC,QAAQ,GAAG,CAAC,GAAG,IAAIkC,GAAG,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC5B,UAAU,CAAC,CAAC8B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAC/E,MAAMyB,OAAO,GAAG,CAAC,GAAG,IAAI9B,GAAG,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,CAAC,CAAC+B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAE7E,OAAO;MACH0B,WAAW,EAAEV,KAAK;MAClBC,gBAAgB,EAAEU,IAAI,CAACC,KAAK,CAACX,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;MAC1DI,iBAAiB;MACjBC,YAAY;MACZC,cAAc;MACdC,aAAa;MACbK,gBAAgB,EAAElE,SAAS,CAAC8B,MAAM;MAClCqC,eAAe,EAAErE,QAAQ,CAACgC,MAAM;MAChCsC,cAAc,EAAEN,OAAO,CAAChC;IAC5B,CAAC;EACL,CAAC;EAED,MAAMuC,mBAAmB,GAAG,MAAOhE,SAAS,IAAK;IAC7C,MAAMiE,MAAM,GAAG,MAAMvG,IAAI,CAAC4E,IAAI,CAAC;MAC3B4B,KAAK,EAAE,qCAAqC;MAC5CC,IAAI,EAAE,yEAAyE;MAC/EC,IAAI,EAAE,UAAU;MAChBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,cAAc;MACjCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA7D,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEd,SAAS,CAAC;QAE7E,MAAMgB,QAAQ,GAAG,MAAMvD,KAAK,CAACkH,IAAI,CAAC,0DAA0D,EAAE;UAC1FC,MAAM,EAAE,UAAU;UAClB5E,SAAS,EAAEA;QACf,CAAC,EAAE;UACCkB,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB,MAAMwD,OAAO,GAAG7D,QAAQ,CAACI,IAAI,CAACyD,OAAO;UACrCnH,IAAI,CAAC4E,IAAI,CAAC;YACN4B,KAAK,EAAE,kBAAkB;YACzBY,IAAI,EAAE;AAC9B,oEAAoED,OAAO,CAACE,cAAc;AAC1F,uEAAuEF,OAAO,CAACG,kBAAkB;AACjG,oEAAoEH,OAAO,CAACI,eAAe;AAC3F,yBAAyB;YACDb,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF1E,UAAU,CAAC,CAAC;UACZC,uBAAuB,CAAC,CAAC;QAC7B,CAAC,MAAM;UACHhD,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAEtB,QAAQ,CAACI,IAAI,CAACe,KAAK,IAAI,8BAA8B,EAAE,OAAO,CAAC;QACvF;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAiD,gBAAA,EAAAC,qBAAA;QACZxE,OAAO,CAACsB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CzE,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,EAAA8C,gBAAA,GAAAjD,KAAK,CAACnB,QAAQ,cAAAoE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsBlD,KAAK,KAAI,wCAAwC,EAAE,OAAO,CAAC;MACzG;IACJ;EACJ,CAAC;EAED,MAAMmD,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACjF,SAAS,EAAE;MACZ7C,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,wDAAwD,EAAE,OAAO,CAAC;MACtF;IACJ;IAEA,IAAI,CAACzC,QAAQ,CAACE,WAAW,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAI,CAACH,QAAQ,CAACI,UAAU,EAAE;MACtEvC,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,+CAA+C,EAAE,OAAO,CAAC;MAC7E;IACJ;IAEA,IAAI;MACA,MAAMmD,GAAG,GAAG,0DAA0D;MACtE,MAAMC,MAAM,GAAG9G,WAAW,GAAG,KAAK,GAAG,MAAM;MAC3C,MAAMwC,IAAI,GAAGxC,WAAW,GAAG;QAAE,GAAGiB,QAAQ;QAAE8F,EAAE,EAAE/G,WAAW,CAAC+G;MAAG,CAAC,GAAG9F,QAAQ;MAEzEgB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAE4E,MAAM;QAAEtE;MAAK,CAAC,CAAC;MAE/C,MAAMJ,QAAQ,GAAG,MAAMvD,KAAK,CAAC;QACzBiI,MAAM;QACND,GAAG;QACHrE,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,yBAAyB;UACxC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAE7C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB3D,IAAI,CAAC4E,IAAI,CAAC;UACN4B,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAEnD,QAAQ,CAACI,IAAI,CAACuB,OAAO;UAC3ByB,IAAI,EAAE,SAAS;UACfc,KAAK,EAAE,IAAI;UACXC,iBAAiB,EAAE;QACvB,CAAC,CAAC;QACF1G,YAAY,CAAC,KAAK,CAAC;QACnBI,cAAc,CAAC,IAAI,CAAC;QACpB+G,SAAS,CAAC,CAAC;QACXnF,UAAU,CAAC,CAAC;QACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;MAC/C,CAAC,MAAM;QACHhD,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAEtB,QAAQ,CAACI,IAAI,CAACe,KAAK,IAAI,yBAAyB,EAAE,OAAO,CAAC;MAClF;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAA0D,gBAAA,EAAAC,qBAAA;MACZjF,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzE,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,EAAAuD,gBAAA,GAAA1D,KAAK,CAACnB,QAAQ,cAAA6E,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzE,IAAI,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsB3D,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAM4D,UAAU,GAAI7F,IAAI,IAAK;IACzB,IAAI,CAACK,SAAS,EAAE;MACZ7C,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,kDAAkD,EAAE,OAAO,CAAC;MAChF;IACJ;IAEAzD,cAAc,CAACqB,IAAI,CAAC;IACpBJ,WAAW,CAAC;MACRC,WAAW,EAAEG,IAAI,CAACH,WAAW,IAAI,EAAE;MACnCC,SAAS,EAAEE,IAAI,CAACF,SAAS,IAAI,EAAE;MAC/BC,UAAU,EAAEC,IAAI,CAACD,UAAU,IAAI,EAAE;MACjCC,IAAI,EAAEA,IAAI,CAACA,IAAI,IAAI;IACvB,CAAC,CAAC;IACFzB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuH,YAAY,GAAG,MAAOL,EAAE,IAAK;IAC/B,IAAI,CAACpF,SAAS,EAAE;MACZ7C,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA,MAAM2B,MAAM,GAAG,MAAMvG,IAAI,CAAC4E,IAAI,CAAC;MAC3B4B,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,gBAAgB;MACnCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA7D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6E,EAAE,CAAC;QAE3C,MAAM3E,QAAQ,GAAG,MAAMvD,KAAK,CAACwI,MAAM,CAAC,0DAA0D,EAAE;UAC5F/E,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAE;YAAEuE;UAAG;QACf,CAAC,CAAC;QAEF9E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB3D,IAAI,CAAC4E,IAAI,CAAC;YACN4B,KAAK,EAAE,YAAY;YACnBC,IAAI,EAAEnD,QAAQ,CAACI,IAAI,CAACuB,OAAO;YAC3ByB,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF1E,UAAU,CAAC,CAAC;UACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;QAC/C,CAAC,MAAM;UACHhD,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAEtB,QAAQ,CAACI,IAAI,CAACe,KAAK,IAAI,iCAAiC,EAAE,OAAO,CAAC;QAC1F;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAA+D,gBAAA,EAAAC,qBAAA;QACZtF,OAAO,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CzE,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,EAAA4D,gBAAA,GAAA/D,KAAK,CAACnB,QAAQ,cAAAkF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9E,IAAI,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsBhE,KAAK,KAAI,iCAAiC,EAAE,OAAO,CAAC;MAClG;IACJ;EACJ,CAAC;EAED,MAAMyD,SAAS,GAAGA,CAAA,KAAM;IACpB9F,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE;IACV,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMkG,qBAAqB,GAAGxI,WAAW,CAACM,KAAK,EAAED,IAAI,CAAC;;EAEtD;EACA,IAAIH,SAAS,CAACG,IAAI,CAAC,IAAImI,qBAAqB,CAAC3E,MAAM,KAAKvD,KAAK,CAACuD,MAAM,EAAE;IAClE1D,gBAAgB,CAAC,sBAAsB,EAAEE,IAAI,EAAE;MAC3C+E,KAAK,EAAE9E,KAAK,CAACuD,MAAM;MACnB4E,QAAQ,EAAED,qBAAqB,CAAC3E;IACpC,CAAC,CAAC;EACN;;EAEA;EACA,MAAM6E,aAAa,GAAGF,qBAAqB,CAACrE,MAAM,CAAC7B,IAAI,IAAI;IACvD,MAAMqG,WAAW,GAAGzH,UAAU,CAAC0H,WAAW,CAAC,CAAC;IAC5C,MAAMC,aAAa,GACf,CAACvG,IAAI,CAACgC,YAAY,IAAI,EAAE,EAAEsE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAACrG,IAAI,CAACyG,YAAY,IAAI,EAAE,EAAEH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAACrG,IAAI,CAAC4B,WAAW,IAAI,EAAE,EAAE0E,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC5D,CAACrG,IAAI,CAACA,IAAI,IAAI,EAAE,EAAE0G,QAAQ,CAAC,CAAC,CAACF,QAAQ,CAACH,WAAW,CACpD;IAED,MAAMM,cAAc,GAAG7H,aAAa,KAAK,KAAK,IAAIkB,IAAI,CAAC4B,WAAW,KAAK9C,aAAa;IACpF,MAAM8H,eAAe,GAAG5H,cAAc,KAAK,KAAK,IAAIgB,IAAI,CAACgC,YAAY,KAAKhD,cAAc;IAExF,OAAOuH,aAAa,IAAII,cAAc,IAAIC,eAAe;EAC7D,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG3H,WAAW,GAAGE,YAAY;EAClD,MAAM0H,gBAAgB,GAAGD,eAAe,GAAGzH,YAAY;EACvD,MAAM2H,YAAY,GAAGX,aAAa,CAACY,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGxD,IAAI,CAACyD,IAAI,CAACd,aAAa,CAAC7E,MAAM,GAAGnC,YAAY,CAAC;EAEjE,MAAM+H,QAAQ,GAAIC,UAAU,IAAKjI,cAAc,CAACiI,UAAU,CAAC;;EAE3D;EACAjK,KAAK,CAACE,SAAS,CAAC,MAAM;IAClB8B,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACP,UAAU,EAAEE,aAAa,EAAEE,cAAc,CAAC,CAAC;EAE/C,MAAMqI,YAAY,GAAIrH,IAAI,IAAK;IAC3B,MAAMsH,SAAS,GAAGpE,UAAU,CAAClD,IAAI,CAAC;IAClC,IAAIuH,KAAK,GAAG;MACRC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB,CAAC;IAED,IAAIL,SAAS,IAAI,EAAE,EAAE;MACjBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO1K,KAAA,CAAA2K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIsH,SAAS,IAAI,EAAE,EAAE;MACxBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO1K,KAAA,CAAA2K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIsH,SAAS,IAAI,EAAE,EAAE;MACxBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO1K,KAAA,CAAA2K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM;MACHuH,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO1K,KAAA,CAAA2K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD;EACJ,CAAC;EAED,MAAMqI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIpI,UAAU,EAAE,OAAO,cAAc;IACrC,IAAIC,YAAY,EAAE,OAAO,yBAAyB;IAClD,IAAIE,OAAO,EAAE,OAAO,4BAA4B;IAChD,OAAO,UAAU;EACrB,CAAC;EAED,MAAMkI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIrI,UAAU,EAAE;MACZ,OAAO;QACHsH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpF5D,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAI/D,YAAY,EAAE;MACrB,OAAO;QACHqH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpF5D,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAI7D,OAAO,EAAE;MAChB,OAAO;QACHmH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpF5D,IAAI,EAAE;MACV,CAAC;IACL;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMuE,MAAM,GAAG;IACXC,YAAY,EAAE;MACVC,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,WAAW;MACpBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBkB,MAAM,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACLhB,eAAe,EAAExH,OAAO,GAAG,SAAS,GAAG,SAAS;MAChDyH,KAAK,EAAE,OAAO;MACdL,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB;EACJ,CAAC;;EAED;EACA,IAAI,CAACrH,OAAO,EAAE;IACV,oBACInD,KAAA,CAAA2K,aAAA;MAAKe,SAAS,EAAC,oBAAoB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/BjL,KAAA,CAAA2K,aAAA;MAAKP,KAAK,EAAEiB,MAAM,CAACC,YAAa;MAAAV,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC5BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAAmB,CAAC,eACxBjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4DAA0D,CAC5D,CACJ,CAAC;EAEd;EAEA,IAAIhK,OAAO,EAAE;IACT,oBACIjB,KAAA,CAAA2K,aAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BjL,KAAA,CAAA2K,aAAA;MAAKe,SAAS,EAAC,SAAS;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,yBAA0B,CAC5B,CAAC;EAEd;EAEA,MAAMU,WAAW,GAAGR,cAAc,CAAC,CAAC;EAEpC,oBACInL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,oBAAoB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKC,cAAc,CAAC,CAAM,CAAC,eAC3BlL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjL,KAAA,CAAA2K,aAAA;IAAMe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBhC,aAAa,CAAC7E,MAAM,EAAC,wBACpB,CAAC,EACNlB,SAAS,iBACNlD,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAMtK,oBAAoB,CAAC,IAAI,CAAE;IAC1C8I,KAAK,EAAE;MAAEyB,WAAW,EAAE;IAAO,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/BjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,eAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,0BACjC,CAAC,eACTjL,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAMxK,YAAY,CAAC,IAAI,CAAE;IAAAwJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,kBACjC,CACP,CAER,CACJ,CAAC,EAGLU,WAAW,iBACR3L,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACR,GAAGuB,WAAW,CAACvB,KAAK;MACpBE,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAI,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEU,WAAW,CAAC7E,IAAQ,CAC/C,CACR,EAGA5E,UAAU,CAACmE,WAAW,GAAG,CAAC,iBACvBrG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACR4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAEhJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GiJ,GAAG,EAAE,MAAM;MACXzB,eAAe,EAAExH,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS;MACzEsI,MAAM,EAAE,aAAanI,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;MAC/EwH,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAEzH,OAAO,GAAG,SAAS,GAAG;IAAU,CAAE;IAAA2H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxF/I,UAAU,CAACmE,WACX,CAAC,eACNrG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9CnI,UAAU,GAAG,WAAW,GAAG,aAC3B,CACJ,CAAC,eAEN9C,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAAC0D,gBACX,CAAC,eACN5F,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CAAC,EAEL,CAACnI,UAAU,iBACR9C,KAAA,CAAA2K,aAAA,CAAA3K,KAAA,CAAAoM,QAAA,qBACIpM,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAAC8D,iBACX,CAAC,eACNhG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAAsB,CACzE,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAACgE,cACX,CAAC,eACNlG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAqB,CACxE,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAACiE,aACX,CAAC,eACNnG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAqB,CACxE,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAACsE,gBACX,CAAC,eACNxG,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAc,CACjE,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClE/I,UAAU,CAACwE,cACX,CAAC,eACN1G,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CACP,CAEL,CACR,eAGDjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,YAAY;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1CjL,KAAA,CAAA2K,aAAA;IACI0B,IAAI,EAAC,MAAM;IACXC,WAAW,EAAExJ,UAAU,GAAG,8BAA8B,GAAG,mDAAoD;IAC/GyJ,KAAK,EAAE9K,UAAW;IAClB+K,QAAQ,EAAGtE,CAAC,IAAKxG,aAAa,CAACwG,CAAC,CAACuE,MAAM,CAACF,KAAK,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClD,CACA,CAAC,EAEL,CAACnI,UAAU,iBACR9C,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAACtB,KAAK,EAAE;MACnC4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAEhJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GiJ,GAAG,EAAE,MAAM;MACXQ,SAAS,EAAE;IACf,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IACI4B,KAAK,EAAE5K,aAAc;IACrB6K,QAAQ,EAAGtE,CAAC,IAAKtG,gBAAgB,CAACsG,CAAC,CAACuE,MAAM,CAACF,KAAK,CAAE;IAClDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBjL,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qCAA8B,CAAC,EAClD7I,QAAQ,CAACmC,GAAG,CAACoI,OAAO,iBACjB3M,KAAA,CAAA2K,aAAA;IAAQiC,GAAG,EAAED,OAAQ;IAACJ,KAAK,EAAEI,OAAQ;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE0B,OAAgB,CAC1D,CACG,CAAC,EAER1J,OAAO,iBACJjD,KAAA,CAAA2K,aAAA;IACI4B,KAAK,EAAE1K,cAAe;IACtB2K,QAAQ,EAAGtE,CAAC,IAAKpG,iBAAiB,CAACoG,CAAC,CAACuE,MAAM,CAACF,KAAK,CAAE;IACnDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBjL,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oCAA6B,CAAC,EACjD3I,SAAS,CAACiC,GAAG,CAACsI,QAAQ,iBACnB7M,KAAA,CAAA2K,aAAA;IAAQiC,GAAG,EAAEC,QAAS;IAACN,KAAK,EAAEM,QAAS;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE4B,QAAiB,CAC7D,CACG,CAEX,CAER,CAAC,eAGN7M,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BhC,aAAa,CAAC7E,MAAM,KAAK,CAAC,gBACvBpE,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,SAAS;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,kBAAe;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5CjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,wBAAsB,CAAC,eAC1BjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAInI,UAAU,GAAG,sCAAsC,GAAG,+CAAmD,CAC5G,CAAC,gBAEN9C,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BjL,KAAA,CAAA2K,aAAA;IAAOe,SAAS,EAAC,OAAO;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,EACb,CAACnI,UAAU,iBAAI9C,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpCjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eACnBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,EACfhI,OAAO,iBAAIjD,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EAC7B/H,SAAS,iBAAIlD,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAChC,CACD,CAAC,eACRjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKrB,YAAY,CAACrF,GAAG,CAAE1B,IAAI,iBACnB7C,KAAA,CAAA2K,aAAA;IAAIiC,GAAG,EAAE/J,IAAI,CAACyF,EAAG;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACbjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAMP,KAAK,EAAEiB,MAAM,CAACI,OAAQ;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACxB,EAACpI,IAAI,CAACyF,EACL,CACN,CAAC,EACJ,CAACxF,UAAU,iBACR9C,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBjL,KAAA,CAAA2K,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCpI,IAAI,CAACgC,YAAY,IAAI,oBAClB,CAAC,eACT7E,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNjL,KAAA,CAAA2K,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDpI,IAAI,CAACiK,cAAc,IAAI,sBACrB,CACN,CACL,CACP,eACD9M,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAE2C,QAAQ,EAAE;IAAQ,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BjL,KAAA,CAAA2K,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCpI,IAAI,CAACyG,YAAY,IAAI,qBAClB,CAAC,EACRzG,IAAI,CAACmK,WAAW,iBACbhN,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UACzC,EAACpI,IAAI,CAACmK,WACb,CAER,CACL,CAAC,eACLhN,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGpI,IAAI,CAAC4B,WAAW,IAAI,eACnB,CACN,CAAC,eACLzE,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKf,YAAY,CAACrH,IAAI,CAACA,IAAI,CACvB,CAAC,eACL7C,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAMP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BpI,IAAI,CAACoK,cAAc,IAAIpK,IAAI,CAACqK,mBAC3B,CACN,CAAC,EACJjK,OAAO,iBACJjD,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGpI,IAAI,CAACsK,UAAU,IAAI,eAClB,CACN,CACP,EACAjK,SAAS,iBACNlD,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BjL,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,wBAAwB;IAClCE,OAAO,EAAEA,CAAA,KAAMlD,UAAU,CAAC7F,IAAI,CAAE;IAChCgE,KAAK,EAAC,kBAAkB;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExBjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTjL,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,uBAAuB;IACjCE,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC9F,IAAI,CAACyF,EAAE,CAAE;IACrCzB,KAAK,EAAC,mBAAmB;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLnB,UAAU,GAAG,CAAC,iBACX9J,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAACjI,WAAW,GAAG,CAAC,CAAE;IACzCqL,QAAQ,EAAErL,WAAW,KAAK,CAAE;IAC5B2J,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,iBAEO,CAAC,eAETjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAClB,EAAClJ,WAAW,EAAC,OAAK,EAAC+H,UACvB,CAAC,eAEN9J,KAAA,CAAA2K,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAACjI,WAAW,GAAG,CAAC,CAAE;IACzCqL,QAAQ,EAAErL,WAAW,KAAK+H,UAAW;IACrC4B,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,SAEO,CACP,CACR,EAGA5J,iBAAiB,IAAI6B,SAAS,iBAC3BlD,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qDAAuC,CAAC,eAC5CjL,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAMtK,oBAAoB,CAAC,KAAK,CAAE;IAAAsJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3CjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BjL,KAAA,CAAA2K,aAAA;IAAGP,KAAK,EAAE;MAAEiD,YAAY,EAAE,MAAM;MAAE3C,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8GAEnD,CAAC,EAEHlK,kBAAkB,CAACqD,MAAM,KAAK,CAAC,gBAC5BpE,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEmB,SAAS,EAAE,QAAQ;MAAElB,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjDjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4DAAuD,CACzD,CAAC,gBAENjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEkD,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDlK,kBAAkB,CAACwD,GAAG,CAACiJ,MAAM,iBAC1BxN,KAAA,CAAA2K,aAAA;IAAKiC,GAAG,EAAEY,MAAM,CAAClF,EAAG;IAAC8B,KAAK,EAAE;MACxBgB,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfgD,YAAY,EAAE,MAAM;MACpB5C,eAAe,EAAE+C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IAC7D,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAE4B,OAAO,EAAE,MAAM;MAAE0B,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAA/C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnFjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjL,KAAA,CAAA2K,aAAA;IAAIP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAY,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEuC,MAAM,CAACI,KAAU,CAAC,eACvD5N,KAAA,CAAA2K,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE,GAAG;MAAEjB,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DuC,MAAM,CAAC/I,WAAW,EAAC,KAAG,EAAC+I,MAAM,CAACL,UAChC,CAAC,eACJnN,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE,SAAS;MAAEgC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAChE,EAACuC,MAAM,CAACK,WAAW,EAAC,4BAAW,EAACL,MAAM,CAACM,yBAAyB,EAAC,mCAAe,EAACN,MAAM,CAACO,gBAAgB,EAAC,QAC3G,CAAC,eACN/N,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEmC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDjL,KAAA,CAAA2K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,KAAK;MACnBG,eAAe,EAAE+C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG,SAAS;MAClE/C,KAAK,EAAE8C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IACnD,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGuC,MAAM,CAACQ,MACN,CACL,CACJ,CAAC,eACNhO,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAM;MACXtK,oBAAoB,CAAC,KAAK,CAAC;MAC3BqF,mBAAmB,CAAC6G,MAAM,CAAClF,EAAE,CAAC;IAClC,CAAE;IACF8E,QAAQ,EAAE,CAACI,MAAM,CAACC,kBAAmB;IACrC5G,KAAK,EAAE2G,MAAM,CAACC,kBAAkB,GAAG,mBAAmB,GAAG,uBAAwB;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpF,4BAEO,CACP,CACJ,CACR,CACA,CAER,CACJ,CACJ,CACR,EAGA9J,SAAS,IAAI+B,SAAS,iBACnBlD,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK1J,WAAW,GAAG,kBAAkB,GAAG,eAAoB,CAAC,eAC7DvB,KAAA,CAAA2K,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAM;MACXxK,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpB+G,SAAS,CAAC,CAAC;IACf,CAAE;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFjL,KAAA,CAAA2K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNjL,KAAA,CAAA2K,aAAA;IAAMsD,QAAQ,EAAEhG,YAAa;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAiB,CAAC,eACzBjL,KAAA,CAAA2K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACE,WAAY;IAC5B8J,QAAQ,EAAGtE,CAAC,IAAKzF,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAEwF,CAAC,CAACuE,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE2B,QAAQ;IACRd,QAAQ,EAAE7L,WAAY;IACtB6I,KAAK,EAAE;MACH+D,WAAW,EAAE3L,QAAQ,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;MACzD0L,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACjB,CAAE;IAAAzD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFjL,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAmC,CAAC,EACpD3I,SAAS,CAAC8B,MAAM,GAAG,CAAC,GACjB9B,SAAS,CAACiC,GAAG,CAACsI,QAAQ,iBAClB7M,KAAA,CAAA2K,aAAA;IAAQiC,GAAG,EAAEC,QAAQ,CAACvE,EAAE,IAAIuE,QAAQ,CAACnK,WAAY;IAAC6J,KAAK,EAAEM,QAAQ,CAACvE,EAAE,IAAIuE,QAAQ,CAACnK,WAAY;IAAAkI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eACvF,EAAC4B,QAAQ,CAACyB,GAAG,IAAIzB,QAAQ,CAAC0B,UAAU,EAAC,aAAW,EAAC1B,QAAQ,CAAC2B,UAAU,IAAI,KAAK,EAAC,aAAW,EAAC3B,QAAQ,CAACM,UAAU,IAAI,KAChH,CACX,CAAC,gBAEFnN,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAACa,QAAQ;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAmC,CAE7D,CACP,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,UAAe,CAAC,eACvBjL,KAAA,CAAA2K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACG,SAAU;IAC1B6J,QAAQ,EAAGtE,CAAC,IAAKzF,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,SAAS,EAAEuF,CAAC,CAACuE,MAAM,CAACF;IAAK,CAAC,CAAE;IACvE2B,QAAQ;IACRd,QAAQ,EAAE7L,WAAY;IAAAqJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtBjL,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAAiC,CAAC,EAClDlK,kBAAkB,CAACwD,GAAG,CAACiJ,MAAM,iBAC1BxN,KAAA,CAAA2K,aAAA;IAAQiC,GAAG,EAAEY,MAAM,CAAClF,EAAG;IAACiE,KAAK,EAAEiB,MAAM,CAAClF,EAAG;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCuC,MAAM,CAACiB,cACJ,CACX,CACG,CACP,CAAC,eAENzO,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,cAAgB,CAAC,eACxBjL,KAAA,CAAA2K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACI,UAAW;IAC3B4J,QAAQ,EAAGtE,CAAC,IAAKzF,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,UAAU,EAAEsF,CAAC,CAACuE,MAAM,CAACF;IAAK,CAAC,CAAE;IACxE2B,QAAQ;IACRd,QAAQ,EAAE7L,WAAY;IACtB6I,KAAK,EAAE;MACH+D,WAAW,EAAE3L,QAAQ,CAACI,UAAU,GAAG,SAAS,GAAG,SAAS;MACxDwL,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACjB,CAAE;IAAAzD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFjL,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAmC,CAAC,EACpD7I,QAAQ,CAACgC,MAAM,GAAG,CAAC,GAChBhC,QAAQ,CAACmC,GAAG,CAACoI,OAAO,iBAChB3M,KAAA,CAAA2K,aAAA;IAAQiC,GAAG,EAAED,OAAO,CAACrE,EAAG;IAACiE,KAAK,EAAEI,OAAO,CAACrE,EAAG;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eACrC,EAAC0B,OAAO,CAAC2B,GAAG,EAAC,iBAAY,EAAC3B,OAAO,CAAC+B,WAAW,IAAI,KAChD,CACX,CAAC,gBAEF1O,KAAA,CAAA2K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAACa,QAAQ;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAAkC,CAE5D,CACP,CAAC,EAGLzI,QAAQ,CAACE,WAAW,iBACjB1C,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRK,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,MAAM;MACpBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE,QAAQ;MAChBW,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAIP,KAAK,EAAE;MACPM,KAAK,EAAE,SAAS;MAChBc,MAAM,EAAE,YAAY;MACpBjB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBwB,OAAO,EAAE,MAAM;MACf2B,UAAU,EAAE,QAAQ;MACpBzB,GAAG,EAAE;IACT,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAEC,CAAC,EACJ,CAAC,MAAM;IACJ,MAAM4B,QAAQ,GAAGvK,SAAS,CAACqM,IAAI,CAACzG,CAAC,IAAI,CAACA,CAAC,CAACI,EAAE,IAAIJ,CAAC,CAACxF,WAAW,KAAKF,QAAQ,CAACE,WAAW,CAAC;IACrF,OAAOmK,QAAQ,gBACX7M,KAAA,CAAA2K,aAAA;MAAKP,KAAK,EAAE;QAAEG,QAAQ,EAAE,MAAM;QAAEqE,UAAU,EAAE;MAAM,CAAE;MAAAhE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChDjL,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,mBAAe,CAAC,KAAC,EAAC4B,QAAQ,CAACyB,GAAG,IAAIzB,QAAQ,CAAC0B,UACpD,CAAC,eACJvO,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,sBAAkB,CAAC,KAAC,EAAC4B,QAAQ,CAAC2B,UAAU,IAAI,aACrD,CAAC,eACJxO,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,sBAAkB,CAAC,KAAC,EAAC4B,QAAQ,CAACM,UAAU,IAAI,cACrD,CAAC,eACJnN,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,0BAAmB,CAAC,KAAC,EAAC4B,QAAQ,CAAC6B,WAAW,IAAI,eACvD,CACF,CAAC,gBAEN1O,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEM,KAAK,EAAE,SAAS;QAAEmE,SAAS,EAAE;MAAS,CAAE;MAAAjE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,+CAElD,CACN;EACL,CAAC,EAAE,CACF,CACR,EAGAzI,QAAQ,CAACI,UAAU,iBAChB5C,KAAA,CAAA2K,aAAA;IAAKP,KAAK,EAAE;MACRK,eAAe,EAAE,SAAS;MAC1BW,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,MAAM;MACpBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE,QAAQ;MAChBW,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjL,KAAA,CAAA2K,aAAA;IAAIP,KAAK,EAAE;MACPM,KAAK,EAAE,SAAS;MAChBc,MAAM,EAAE,YAAY;MACpBjB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBwB,OAAO,EAAE,MAAM;MACf2B,UAAU,EAAE,QAAQ;MACpBzB,GAAG,EAAE;IACT,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAEC,CAAC,EACJ,CAAC,MAAM;IACJ,MAAM0B,OAAO,GAAGvK,QAAQ,CAACuM,IAAI,CAACG,CAAC,IAAI,CAACA,CAAC,CAACxG,EAAE,IAAIwG,CAAC,CAAClM,UAAU,KAAKJ,QAAQ,CAACI,UAAU,CAAC;IACjF,OAAO+J,OAAO,gBACV3M,KAAA,CAAA2K,aAAA;MAAKP,KAAK,EAAE;QAAEG,QAAQ,EAAE,MAAM;QAAEqE,UAAU,EAAE;MAAM,CAAE;MAAAhE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChDjL,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,mBAAe,CAAC,KAAC,EAAC0B,OAAO,CAAC2B,GAAG,IAAI3B,OAAO,CAACoC,WAClD,CAAC,eACJ/O,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,0BAAmB,CAAC,KAAC,EAAC0B,OAAO,CAAC+B,WAAW,IAAI,eACtD,CAAC,eACJ1O,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAQ,CAAE;MAAAZ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,2BAAuB,CAAC,KAAC,EAAC0B,OAAO,CAACqC,WAAW,IAAI,YAC1D,CACF,CAAC,gBAENhP,KAAA,CAAA2K,aAAA;MAAGP,KAAK,EAAE;QAAEM,KAAK,EAAE,SAAS;QAAEmE,SAAS,EAAE;MAAS,CAAE;MAAAjE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,+CAElD,CACN;EACL,CAAC,EAAE,CACF,CACR,eAEDjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjL,KAAA,CAAA2K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAoB,CAAC,eAC5BjL,KAAA,CAAA2K,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACb4C,GAAG,EAAC,GAAG;IACPC,GAAG,EAAC,IAAI;IACRC,IAAI,EAAC,MAAM;IACX5C,KAAK,EAAE/J,QAAQ,CAACK,IAAK;IACrB2J,QAAQ,EAAGtE,CAAC,IAAKzF,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,IAAI,EAAEqF,CAAC,CAACuE,MAAM,CAACF;IAAK,CAAC,CAAE;IAClED,WAAW,EAAC,sCAAsC;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrD,CAAC,eACFjL,KAAA,CAAA2K,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAO,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yFAE/C,CACN,CAAC,eAENjL,KAAA,CAAA2K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjL,KAAA,CAAA2K,aAAA;IAAQ0B,IAAI,EAAC,QAAQ;IAACX,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C1J,WAAW,GAAG,aAAa,GAAG,SAC3B,CAAC,eACTvB,KAAA,CAAA2K,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACbX,SAAS,EAAC,mBAAmB;IAC7BE,OAAO,EAAEA,CAAA,KAAM;MACXxK,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpB+G,SAAS,CAAC,CAAC;IACf,CAAE;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAetK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}