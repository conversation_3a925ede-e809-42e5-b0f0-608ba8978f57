<?php
header('Content-Type: text/html; charset=utf-8');

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Erreur de connexion à la base de données: ' . $e->getMessage());
}

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup et Test - ReponsesQuiz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #ffeaa7; }
        h1, h2, h3 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 Setup et Test Complet - ReponsesQuiz</h1>";

try {
    // 1. Vérifier/Créer la table ReponsesQuiz
    echo "<h2>📊 1. Configuration de la Base de Données</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'reponsesquiz'");
    if (!$stmt->fetch()) {
        $createTable = "
            CREATE TABLE `reponsesquiz` (
                `id` INT(10) NOT NULL AUTO_INCREMENT,
                `quiz_id` INT(10) NULL DEFAULT NULL,
                `etudiant_id` INT(10) NULL DEFAULT NULL,
                `reponse` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
                `est_correct` TINYINT(1) NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `quiz_id` (`quiz_id`) USING BTREE,
                INDEX `etudiant_id` (`etudiant_id`) USING BTREE,
                CONSTRAINT `reponsesquiz_ibfk_1` FOREIGN KEY (`quiz_id`) REFERENCES `quiz` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
                CONSTRAINT `reponsesquiz_ibfk_2` FOREIGN KEY (`etudiant_id`) REFERENCES `etudiants` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION
            ) COLLATE='utf8mb4_general_ci' ENGINE=InnoDB AUTO_INCREMENT=1;
        ";
        $pdo->exec($createTable);
        echo "<p class='success'>✅ Table ReponsesQuiz créée avec succès</p>";
    } else {
        echo "<p class='info'>ℹ️ Table ReponsesQuiz existe déjà</p>";
    }
    
    // 2. Vérifier les dépendances
    echo "<h2>🔗 2. Vérification des Dépendances</h2>";
    
    $dependencies = [
        'quiz' => 'Table Quiz (questions)',
        'etudiants' => 'Table Étudiants',
        'utilisateurs' => 'Table Utilisateurs',
        'devoirs' => 'Table Devoirs',
        'matieres' => 'Table Matières',
        'classes' => 'Table Classes'
    ];
    
    $missingTables = [];
    $existingTables = [];
    
    foreach ($dependencies as $table => $description) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "<p class='success'>✅ $description trouvée</p>";
            $existingTables[] = $table;
        } else {
            echo "<p class='error'>❌ $description manquante</p>";
            $missingTables[] = $table;
        }
    }
    
    // 3. Créer des données de test si les tables existent
    echo "<h2>🧪 3. Création de Données de Test</h2>";
    
    if (count($missingTables) === 0) {
        // Compter les données existantes
        $counts = [];
        foreach ($existingTables as $table) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $counts[$table] = $stmt->fetch()['count'];
        }
        
        echo "<div class='grid'>";
        foreach ($counts as $table => $count) {
            echo "<div class='card'>";
            echo "<h4>📊 Table $table</h4>";
            echo "<p><strong>$count</strong> enregistrement(s)</p>";
            echo "</div>";
        }
        echo "</div>";
        
        // Créer des données de test si nécessaire
        if ($counts['quiz'] > 0 && $counts['etudiants'] > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM reponsesquiz");
            $reponsesCount = $stmt->fetch()['count'];
            
            if ($reponsesCount < 5) {
                echo "<h3>📝 Création de réponses de test</h3>";
                
                // Récupérer quelques quiz et étudiants
                $stmt = $pdo->query("SELECT id, question, reponse_correcte FROM quiz LIMIT 3");
                $quiz_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $stmt = $pdo->query("SELECT id FROM etudiants LIMIT 2");
                $etudiants_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $reponses_test = [
                    ['reponse' => 'Paris', 'description' => 'Réponse correcte en géographie'],
                    ['reponse' => 'Lyon', 'description' => 'Réponse incorrecte en géographie'],
                    ['reponse' => '4', 'description' => 'Réponse correcte en mathématiques'],
                    ['reponse' => '5', 'description' => 'Réponse incorrecte en mathématiques'],
                    ['reponse' => 'être', 'description' => 'Réponse correcte en français']
                ];
                
                $created = 0;
                foreach ($reponses_test as $index => $reponse_data) {
                    if (isset($quiz_list[$index % count($quiz_list)]) && isset($etudiants_list[$index % count($etudiants_list)])) {
                        $quiz_id = $quiz_list[$index % count($quiz_list)]['id'];
                        $etudiant_id = $etudiants_list[$index % count($etudiants_list)]['id'];
                        
                        // Vérifier si cette combinaison n'existe pas déjà
                        $stmt = $pdo->prepare("SELECT id FROM reponsesquiz WHERE quiz_id = ? AND etudiant_id = ?");
                        $stmt->execute([$quiz_id, $etudiant_id]);
                        
                        if (!$stmt->fetch()) {
                            // Calculer si la réponse est correcte
                            $reponse_correcte = $quiz_list[$index % count($quiz_list)]['reponse_correcte'];
                            $est_correct = (strtolower(trim($reponse_data['reponse'])) === strtolower(trim($reponse_correcte)));
                            
                            $stmt = $pdo->prepare("
                                INSERT INTO reponsesquiz (quiz_id, etudiant_id, reponse, est_correct) 
                                VALUES (?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $quiz_id,
                                $etudiant_id,
                                $reponse_data['reponse'],
                                $est_correct
                            ]);
                            
                            echo "<p class='success'>✅ {$reponse_data['description']} créée</p>";
                            $created++;
                        }
                    }
                }
                
                echo "<p class='info'>📊 $created nouvelles réponses créées</p>";
            } else {
                echo "<p class='info'>ℹ️ Données de test déjà présentes ($reponsesCount réponses)</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Impossible de créer des données de test : quiz ou étudiants manquants</p>";
        }
    } else {
        echo "<div class='warning'>";
        echo "<h3>⚠️ Tables manquantes détectées</h3>";
        echo "<p>Les tables suivantes sont requises : " . implode(', ', $missingTables) . "</p>";
        echo "<p>Veuillez d'abord créer les interfaces Quiz et Étudiants.</p>";
        echo "</div>";
    }
    
    // 4. Test des APIs
    echo "<h2>🔌 4. Test des APIs</h2>";
    
    $apis = [
        'api.php' => 'API principale CRUD',
        'quiz-disponibles.php' => 'API quiz disponibles'
    ];
    
    foreach ($apis as $file => $description) {
        $url = "http://localhost/Project_PFE/Backend/pages/reponsesquiz/$file";
        
        // Test avec différents rôles
        $roles = [
            'etudiant-token' => 'Étudiant',
            'enseignant-token' => 'Enseignant',
            'admin-token' => 'Admin'
        ];
        
        echo "<h4>🔗 $description</h4>";
        
        foreach ($roles as $token => $role_name) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ["Authorization: Bearer $token"]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if ($data && (isset($data['success']) || is_array($data))) {
                    echo "<p class='success'>✅ $role_name : API fonctionne</p>";
                } else {
                    echo "<p class='warning'>⚠️ $role_name : API répond mais format inattendu</p>";
                }
            } else {
                echo "<p class='error'>❌ $role_name : API ne répond pas (Code: $httpCode)</p>";
            }
        }
    }
    
    // 5. Test des permissions spécifiques
    echo "<h2>🔐 5. Test des Permissions</h2>";
    
    echo "<div class='grid'>";
    
    // Test Étudiant
    echo "<div class='card'>";
    echo "<h4>🎓 Permissions Étudiant</h4>";
    echo "<ul>";
    echo "<li>✅ CRUD complet sur ses réponses</li>";
    echo "<li>❌ Ne voit PAS le champ est_correct</li>";
    echo "<li>❌ Ne voit PAS les réponses correctes</li>";
    echo "<li>👁️ Voit uniquement ses propres réponses</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test Enseignant
    echo "<div class='card'>";
    echo "<h4>👨‍🏫 Permissions Enseignant</h4>";
    echo "<ul>";
    echo "<li>👁️ Consultation de toutes les réponses</li>";
    echo "<li>✅ Voit le champ est_correct</li>";
    echo "<li>✅ Voit les réponses correctes</li>";
    echo "<li>❌ Ne peut PAS modifier/supprimer</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test Admin
    echo "<div class='card'>";
    echo "<h4>👨‍💼 Permissions Admin</h4>";
    echo "<ul>";
    echo "<li>👁️ Accès en lecture seule uniquement</li>";
    echo "<li>📊 Vue d'ensemble complète</li>";
    echo "<li>🔍 Filtres avancés</li>";
    echo "<li>❌ Aucune modification possible</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    // 6. Statistiques finales
    echo "<h2>📊 6. Statistiques du Système</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reponsesquiz");
    $total_reponses = $stmt->fetch()['total'];
    
    if ($total_reponses > 0) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM reponsesquiz WHERE est_correct = 1");
        $reponses_correctes = $stmt->fetch()['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM reponsesquiz WHERE est_correct = 0");
        $reponses_incorrectes = $stmt->fetch()['total'];
        
        $stmt = $pdo->query("SELECT COUNT(DISTINCT etudiant_id) as total FROM reponsesquiz");
        $etudiants_actifs = $stmt->fetch()['total'];
        
        $stmt = $pdo->query("SELECT COUNT(DISTINCT quiz_id) as total FROM reponsesquiz");
        $quiz_repondus = $stmt->fetch()['total'];
        
        $taux_reussite = $total_reponses > 0 ? round(($reponses_correctes / $total_reponses) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Métrique</th><th>Valeur</th><th>Description</th></tr>";
        echo "<tr><td>Total réponses</td><td><strong>$total_reponses</strong></td><td>Toutes les réponses enregistrées</td></tr>";
        echo "<tr><td>Réponses correctes</td><td><strong style='color: #28a745;'>$reponses_correctes</strong></td><td>Réponses validées comme correctes</td></tr>";
        echo "<tr><td>Réponses incorrectes</td><td><strong style='color: #dc3545;'>$reponses_incorrectes</strong></td><td>Réponses validées comme incorrectes</td></tr>";
        echo "<tr><td>Taux de réussite</td><td><strong style='color: #007bff;'>{$taux_reussite}%</strong></td><td>Pourcentage de bonnes réponses</td></tr>";
        echo "<tr><td>Étudiants actifs</td><td><strong>$etudiants_actifs</strong></td><td>Étudiants ayant répondu</td></tr>";
        echo "<tr><td>Quiz avec réponses</td><td><strong>$quiz_repondus</strong></td><td>Quiz ayant reçu des réponses</td></tr>";
        echo "</table>";
    } else {
        echo "<p class='info'>ℹ️ Aucune réponse enregistrée pour le moment</p>";
    }
    
    // 7. Instructions d'utilisation
    echo "<h2>🚀 7. Instructions d'Utilisation</h2>";
    
    echo "<div class='success'>";
    echo "<h3>✅ Système ReponsesQuiz Opérationnel !</h3>";
    echo "<p><strong>Spécifications respectées :</strong></p>";
    echo "<ul>";
    echo "<li>🔐 <strong>Permissions par rôle</strong> : Contrôles stricts selon les spécifications</li>";
    echo "<li>🎓 <strong>Étudiants</strong> : CRUD complet sans voir les corrections</li>";
    echo "<li>👨‍🏫 <strong>Enseignants</strong> : Consultation avec visibilité des corrections</li>";
    echo "<li>👨‍💼 <strong>Admins</strong> : Vue d'ensemble en lecture seule</li>";
    echo "<li>🤖 <strong>Évaluation automatique</strong> : Comparaison avec réponses correctes</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🔗 Liens Utiles</h3>";
    echo "<p style='text-align: center;'>";
    echo "<a href='api.php' target='_blank' class='btn btn-primary'>Test API CRUD</a> ";
    echo "<a href='quiz-disponibles.php' target='_blank' class='btn btn-primary'>Quiz Disponibles</a> ";
    echo "<a href='../../../Frantend/schoolproject/public/' target='_blank' class='btn btn-success'>Application React</a>";
    echo "</p>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>📋 Prochaines Étapes</h3>";
    echo "<ol>";
    echo "<li>Ajouter la route React : <code>/reponses-quiz</code></li>";
    echo "<li>Tester avec différents rôles utilisateur</li>";
    echo "<li>Vérifier les permissions et l'évaluation automatique</li>";
    echo "<li>Personnaliser les styles si nécessaire</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Erreur</h3>";
    echo "<p>Erreur lors du setup : " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "    </div>
</body>
</html>";
?>
