<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🏗️ CRÉATION DE LA NOUVELLE STRUCTURE PARENTS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .sql-code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Architecture Cible</h2>";
    echo "<p><strong>Votre vision :</strong></p>";
    echo "<ul>";
    echo "<li>✅ Utilisateurs d'abord dans table Utilisateurs avec rôle</li>";
    echo "<li>✅ Si rôle = 'parent' → informations supplémentaires dans table Parents</li>";
    echo "<li>✅ Table Parents contient uniquement données complémentaires</li>";
    echo "<li>✅ Liaison via utilisateur_id (clé étrangère)</li>";
    echo "</ul>";
    echo "</div>";
    
    // Étape 1 : Vérifier les prérequis
    echo "<div class='step'>";
    echo "<h3>1. 🔍 Vérification des Prérequis</h3>";
    
    // Vérifier que la table utilisateurs existe
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table 'utilisateurs' existe</p>";
            
            // Vérifier la structure
            $stmt = $pdo->query("DESCRIBE utilisateurs");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $hasRoleId = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'role_id') {
                    $hasRoleId = true;
                    break;
                }
            }
            
            if ($hasRoleId) {
                echo "<p class='success'>✅ Colonne 'role_id' présente</p>";
            } else {
                echo "<p class='error'>❌ Colonne 'role_id' manquante dans table utilisateurs</p>";
                echo "<p class='warning'>💡 Vous devez d'abord créer/modifier la table utilisateurs</p>";
            }
        } else {
            echo "<p class='error'>❌ Table 'utilisateurs' n'existe pas</p>";
            echo "<p class='warning'>💡 Création de la table utilisateurs...</p>";
            
            // Créer la table utilisateurs si elle n'existe pas
            $createUsersTable = "
                CREATE TABLE `utilisateurs` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `nom` VARCHAR(100) NOT NULL,
                    `prenom` VARCHAR(100) NOT NULL,
                    `email` VARCHAR(255) NOT NULL UNIQUE,
                    `mot_de_passe` VARCHAR(255) NOT NULL,
                    `role_id` INT,
                    `statut` ENUM('actif', 'inactif') DEFAULT 'actif',
                    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX `idx_role_id` (`role_id`),
                    INDEX `idx_email` (`email`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ";
            
            $pdo->exec($createUsersTable);
            echo "<p class='success'>✅ Table 'utilisateurs' créée</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification utilisateurs : " . $e->getMessage() . "</p>";
    }
    
    // Vérifier que la table roles existe
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table 'roles' existe</p>";
            
            // Vérifier qu'il y a un rôle 'parent'
            $stmt = $pdo->prepare("SELECT id FROM roles WHERE nom = 'parent'");
            $stmt->execute();
            if ($stmt->fetch()) {
                echo "<p class='success'>✅ Rôle 'parent' existe</p>";
            } else {
                echo "<p class='warning'>⚠️ Rôle 'parent' manquant, création...</p>";
                $stmt = $pdo->prepare("INSERT IGNORE INTO roles (nom, description) VALUES ('parent', 'Parent d\'élève')");
                $stmt->execute();
                echo "<p class='success'>✅ Rôle 'parent' créé</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Table 'roles' manquante, création...</p>";
            
            // Créer la table roles
            $createRolesTable = "
                CREATE TABLE `roles` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `nom` VARCHAR(50) NOT NULL UNIQUE,
                    `description` TEXT,
                    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ";
            
            $pdo->exec($createRolesTable);
            echo "<p class='success'>✅ Table 'roles' créée</p>";
            
            // Insérer les rôles de base
            $insertRoles = "
                INSERT INTO `roles` (`nom`, `description`) VALUES
                ('admin', 'Administrateur du système'),
                ('parent', 'Parent d\'élève'),
                ('etudiant', 'Étudiant'),
                ('enseignant', 'Enseignant/Professeur')
            ";
            
            $pdo->exec($insertRoles);
            echo "<p class='success'>✅ Rôles de base créés</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification roles : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 2 : Créer la nouvelle table Parents
    echo "<div class='step'>";
    echo "<h3>2. 🏗️ Création de la Table Parents</h3>";
    
    echo "<h4>📋 Structure selon vos spécifications :</h4>";
    echo "<div class='sql-code'>";
    echo "CREATE TABLE Parents (<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;id INT AUTO_INCREMENT PRIMARY KEY,<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;utilisateur_id INT NOT NULL,<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;telephone VARCHAR(20),<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;adresse TEXT,<br>";
    echo "&nbsp;&nbsp;&nbsp;&nbsp;FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)<br>";
    echo ");";
    echo "</div>";
    
    try {
        $createParentsTable = "
            CREATE TABLE `parents` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `utilisateur_id` INT NOT NULL,
                `telephone` VARCHAR(20),
                `adresse` TEXT,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                UNIQUE KEY `unique_utilisateur_id` (`utilisateur_id`),
                INDEX `idx_utilisateur_id` (`utilisateur_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $pdo->exec($createParentsTable);
        echo "<p class='success'>✅ Table 'parents' créée avec succès</p>";
        
        // Afficher la structure créée
        $stmt = $pdo->query("DESCRIBE parents");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📊 Structure de la table créée :</h4>";
        echo "<table>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur création table parents : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 3 : Créer les triggers de sécurité
    echo "<div class='step'>";
    echo "<h3>3. ⚡ Création des Triggers de Sécurité</h3>";
    echo "<p class='info'>Ces triggers garantissent que seuls les utilisateurs avec le rôle 'parent' peuvent être insérés.</p>";
    
    try {
        // Trigger pour INSERT
        $triggerInsert = "
            CREATE TRIGGER `check_parent_role_insert` 
            BEFORE INSERT ON `parents`
            FOR EACH ROW
            BEGIN
                DECLARE user_role VARCHAR(50);
                
                SELECT r.nom INTO user_role
                FROM utilisateurs u
                JOIN roles r ON u.role_id = r.id
                WHERE u.id = NEW.utilisateur_id;
                
                IF user_role IS NULL THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'Erreur: L\\'utilisateur spécifié n\\'existe pas';
                ELSEIF user_role != 'parent' THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle \"parent\" peuvent être insérés dans cette table';
                END IF;
            END
        ";
        
        $pdo->exec($triggerInsert);
        echo "<p class='success'>✅ Trigger INSERT créé</p>";
        
        // Trigger pour UPDATE
        $triggerUpdate = "
            CREATE TRIGGER `check_parent_role_update` 
            BEFORE UPDATE ON `parents`
            FOR EACH ROW
            BEGIN
                DECLARE user_role VARCHAR(50);
                
                SELECT r.nom INTO user_role
                FROM utilisateurs u
                JOIN roles r ON u.role_id = r.id
                WHERE u.id = NEW.utilisateur_id;
                
                IF user_role IS NULL THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'Erreur: L\\'utilisateur spécifié n\\'existe pas';
                ELSEIF user_role != 'parent' THEN
                    SIGNAL SQLSTATE '45000' 
                    SET MESSAGE_TEXT = 'Erreur: Seuls les utilisateurs avec le rôle \"parent\" peuvent être modifiés dans cette table';
                END IF;
            END
        ";
        
        $pdo->exec($triggerUpdate);
        echo "<p class='success'>✅ Trigger UPDATE créé</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur création triggers : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 4 : Créer une vue pour faciliter les requêtes
    echo "<div class='step'>";
    echo "<h3>4. 👁️ Création d'une Vue Facilitée</h3>";
    
    try {
        $createView = "
            CREATE VIEW `v_parents_complets` AS
            SELECT 
                p.id as parent_id,
                p.utilisateur_id,
                u.nom,
                u.prenom,
                u.email,
                p.telephone,
                p.adresse,
                u.statut as statut_utilisateur,
                r.nom as role_nom,
                p.created_at,
                p.updated_at
            FROM parents p
            INNER JOIN utilisateurs u ON p.utilisateur_id = u.id
            INNER JOIN roles r ON u.role_id = r.id
            WHERE r.nom = 'parent'
            ORDER BY p.id DESC
        ";
        
        $pdo->exec($createView);
        echo "<p class='success'>✅ Vue 'v_parents_complets' créée</p>";
        echo "<p style='margin-left: 20px; color: #666;'>Cette vue facilite les requêtes en joignant automatiquement les tables utilisateurs, parents et roles.</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur création vue : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Étape 5 : Test de la structure
    echo "<div class='step'>";
    echo "<h3>5. 🧪 Test de la Structure</h3>";
    
    try {
        // Vérifier les contraintes
        $stmt = $pdo->query("
            SELECT 
                CONSTRAINT_NAME,
                CONSTRAINT_TYPE,
                TABLE_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.TABLE_CONSTRAINTS tc
            LEFT JOIN information_schema.KEY_COLUMN_USAGE kcu 
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            WHERE tc.TABLE_NAME = 'parents'
            AND tc.TABLE_SCHEMA = DATABASE()
        ");
        
        $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($constraints) > 0) {
            echo "<h4>🔗 Contraintes créées :</h4>";
            echo "<table>";
            echo "<tr><th>Nom</th><th>Type</th><th>Table</th><th>Référence</th></tr>";
            foreach ($constraints as $constraint) {
                echo "<tr>";
                echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
                echo "<td>{$constraint['CONSTRAINT_TYPE']}</td>";
                echo "<td>{$constraint['TABLE_NAME']}</td>";
                echo "<td>{$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Vérifier les triggers
        $stmt = $pdo->query("SHOW TRIGGERS LIKE 'parents'");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($triggers) > 0) {
            echo "<h4>⚡ Triggers créés :</h4>";
            echo "<table>";
            echo "<tr><th>Trigger</th><th>Event</th><th>Table</th><th>Timing</th></tr>";
            foreach ($triggers as $trigger) {
                echo "<tr>";
                echo "<td>{$trigger['Trigger']}</td>";
                echo "<td>{$trigger['Event']}</td>";
                echo "<td>{$trigger['Table']}</td>";
                echo "<td>{$trigger['Timing']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<p class='success'>✅ Structure complète et fonctionnelle</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur test structure : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎯 STRUCTURE PARENTS CRÉÉE AVEC SUCCÈS</h3>";
    echo "<p class='success' style='font-size: 18px;'>🏗️ NOUVELLE STRUCTURE PARENTS OPÉRATIONNELLE !</p>";
    
    echo "<h4>✅ Éléments créés :</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Table 'parents'</strong> selon vos spécifications exactes</li>";
    echo "<li>✅ <strong>Clé étrangère</strong> vers table utilisateurs</li>";
    echo "<li>✅ <strong>Contrainte unique</strong> sur utilisateur_id (un utilisateur = un parent max)</li>";
    echo "<li>✅ <strong>Triggers de sécurité</strong> (seuls les rôles 'parent' autorisés)</li>";
    echo "<li>✅ <strong>Vue facilitée</strong> pour les requêtes complexes</li>";
    echo "<li>✅ <strong>Index</strong> pour les performances</li>";
    echo "</ul>";
    
    echo "<h4>🎯 Architecture respectée :</h4>";
    echo "<ul>";
    echo "<li>✅ Utilisateurs d'abord dans table Utilisateurs avec role_id</li>";
    echo "<li>✅ Si rôle = 'parent' → données complémentaires dans table Parents</li>";
    echo "<li>✅ Table Parents contient uniquement téléphone et adresse</li>";
    echo "<li>✅ Liaison via utilisateur_id (clé étrangère)</li>";
    echo "<li>✅ Cohérence garantie par triggers</li>";
    echo "</ul>";
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><a href='create_parents_backend.php'>Créer le backend API pour Parents</a></li>";
    echo "<li><a href='create_test_parents_data.php'>Créer des données de test</a></li>";
    echo "<li><a href='create_parents_frontend.php'>Créer l'interface similaire aux rôles</a></li>";
    echo "</ol>";
    
    echo "<p class='info'><strong>La structure Parents est maintenant prête selon votre architecture idéale !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
