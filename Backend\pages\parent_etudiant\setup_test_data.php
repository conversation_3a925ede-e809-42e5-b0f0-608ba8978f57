<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

echo "<h1>🔧 Configuration et test des données Parent-Étudiant</h1>";

// 1. Vérifier que la table parent_etudiant existe
echo "<h2>1. Vérification de la table parent_etudiant</h2>";
try {
    $stmt = $pdo->query("DESCRIBE parent_etudiant");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ Table parent_etudiant trouvée avec les colonnes :</p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>{$column['Field']}</strong> - {$column['Type']} - {$column['Null']} - {$column['Key']}</li>";
    }
    echo "</ul>";
} catch (PDOException $e) {
    echo "<p>❌ Erreur : Table parent_etudiant non trouvée. Création...</p>";
    
    // Créer la table si elle n'existe pas
    $createTable = "
    CREATE TABLE `parent_etudiant` (
        `id` INT(10) NOT NULL AUTO_INCREMENT,
        `parent_id` INT(10) NULL DEFAULT NULL,
        `etudiant_id` INT(10) NULL DEFAULT NULL,
        `lien_parente` ENUM('Père','Mère','Tuteur','Autre') NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `parent_id` (`parent_id`) USING BTREE,
        INDEX `etudiant_id` (`etudiant_id`) USING BTREE,
        CONSTRAINT `parent_etudiant_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `parents` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
        CONSTRAINT `parent_etudiant_ibfk_2` FOREIGN KEY (`etudiant_id`) REFERENCES `etudiants` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION
    )
    COLLATE='utf8mb4_general_ci'
    ENGINE=InnoDB
    AUTO_INCREMENT=1;
    ";
    
    try {
        $pdo->exec($createTable);
        echo "<p>✅ Table parent_etudiant créée avec succès</p>";
    } catch (PDOException $e) {
        echo "<p>❌ Erreur lors de la création de la table : " . $e->getMessage() . "</p>";
    }
}

// 2. Vérifier les données dans les tables parents et etudiants
echo "<h2>2. Vérification des données parents</h2>";
try {
    $stmt = $pdo->query("
        SELECT p.id, p.utilisateur_id, u.nom, u.email, p.telephone 
        FROM parents p 
        JOIN utilisateurs u ON p.utilisateur_id = u.id 
        LIMIT 5
    ");
    $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($parents) > 0) {
        echo "<p>✅ " . count($parents) . " parents trouvés :</p>";
        echo "<ul>";
        foreach ($parents as $parent) {
            echo "<li>ID: {$parent['id']} - {$parent['nom']} ({$parent['email']}) - Tel: {$parent['telephone']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>⚠️ Aucun parent trouvé dans la base de données</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Erreur lors de la lecture des parents : " . $e->getMessage() . "</p>";
}

echo "<h2>3. Vérification des données étudiants</h2>";
try {
    $stmt = $pdo->query("
        SELECT e.id, e.utilisateur_id, u.nom, u.email 
        FROM etudiants e 
        JOIN utilisateurs u ON e.utilisateur_id = u.id 
        LIMIT 5
    ");
    $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($etudiants) > 0) {
        echo "<p>✅ " . count($etudiants) . " étudiants trouvés :</p>";
        echo "<ul>";
        foreach ($etudiants as $etudiant) {
            echo "<li>ID: {$etudiant['id']} - {$etudiant['nom']} ({$etudiant['email']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>⚠️ Aucun étudiant trouvé dans la base de données</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Erreur lors de la lecture des étudiants : " . $e->getMessage() . "</p>";
}

// 4. Vérifier les relations existantes
echo "<h2>4. Relations parent-étudiant existantes</h2>";
try {
    $stmt = $pdo->query("
        SELECT 
            pe.id,
            pe.parent_id,
            pe.etudiant_id,
            pe.lien_parente,
            up.nom as parent_nom,
            up.email as parent_email,
            ue.nom as etudiant_nom,
            ue.email as etudiant_email
        FROM parent_etudiant pe
        JOIN parents p ON pe.parent_id = p.id
        JOIN utilisateurs up ON p.utilisateur_id = up.id
        JOIN etudiants e ON pe.etudiant_id = e.id
        JOIN utilisateurs ue ON e.utilisateur_id = ue.id
        ORDER BY pe.id
    ");
    $relations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($relations) > 0) {
        echo "<p>✅ " . count($relations) . " relations trouvées :</p>";
        echo "<ul>";
        foreach ($relations as $relation) {
            echo "<li>ID: {$relation['id']} - Parent: {$relation['parent_nom']} ({$relation['lien_parente']}) → Étudiant: {$relation['etudiant_nom']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>⚠️ Aucune relation parent-étudiant trouvée</p>";
        
        // Créer quelques relations de test si possible
        if (count($parents) > 0 && count($etudiants) > 0) {
            echo "<h3>Création de relations de test...</h3>";
            
            try {
                // Relation 1 : Premier parent avec premier étudiant
                $stmt = $pdo->prepare("
                    INSERT INTO parent_etudiant (parent_id, etudiant_id, lien_parente) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$parents[0]['id'], $etudiants[0]['id'], 'Père']);
                echo "<p>✅ Relation créée : {$parents[0]['nom']} (Père) → {$etudiants[0]['nom']}</p>";
                
                // Relation 2 : Si on a au moins 2 parents et 2 étudiants
                if (count($parents) > 1 && count($etudiants) > 1) {
                    $stmt->execute([$parents[1]['id'], $etudiants[1]['id'], 'Mère']);
                    echo "<p>✅ Relation créée : {$parents[1]['nom']} (Mère) → {$etudiants[1]['nom']}</p>";
                }
                
                // Relation 3 : Même parent avec différent étudiant (si possible)
                if (count($etudiants) > 1) {
                    $stmt->execute([$parents[0]['id'], $etudiants[1]['id'], 'Tuteur']);
                    echo "<p>✅ Relation créée : {$parents[0]['nom']} (Tuteur) → {$etudiants[1]['nom']}</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p>⚠️ Erreur lors de la création des relations de test : " . $e->getMessage() . "</p>";
            }
        }
    }
} catch (PDOException $e) {
    echo "<p>❌ Erreur lors de la lecture des relations : " . $e->getMessage() . "</p>";
}

// 5. Test de l'API
echo "<h2>5. Test de l'API</h2>";
echo "<p>🔗 <a href='test_api.html' target='_blank'>Ouvrir l'interface de test API</a></p>";
echo "<p>🔗 <a href='index.php' target='_blank'>Tester l'API directement</a></p>";

echo "<h2>6. Instructions</h2>";
echo "<ul>";
echo "<li>✅ Vérifiez que la table parent_etudiant existe et contient des données</li>";
echo "<li>🧪 Utilisez l'interface de test pour vérifier les opérations CRUD</li>";
echo "<li>🌐 Testez l'interface React à l'adresse : <strong>http://localhost:3000/parent-etudiant</strong></li>";
echo "<li>🔐 Connectez-vous avec un compte Admin pour avoir accès complet</li>";
echo "<li>👁️ Connectez-vous avec un compte Enseignant pour avoir accès en lecture seule</li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
h1, h2, h3 { color: #2c3e50; }
ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
