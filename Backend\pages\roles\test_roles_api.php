<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST API RÔLES - DIAGNOSTIC COMPLET</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
    </style>";
    
    echo "<div class='success'>";
    echo "<h2>🎯 DIAGNOSTIC API RÔLES POUR USEREDITMMODAL</h2>";
    echo "<p><strong>Vérification complète du chargement des rôles</strong></p>";
    echo "</div>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='success'>";
        echo "<h3>✅ Connexion à la Base de Données Réussie</h3>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Erreur de Connexion</h3>";
        echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
        exit();
    }
    
    // Vérifier l'existence de la table roles
    echo "<div class='info'>";
    echo "<h3>🔍 Vérification Table Rôles</h3>";
    echo "</div>";
    
    $table_name = null;
    $tables_to_check = ['roles', 'Roles', 'ROLES'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $table_name = $table;
                echo "<div class='success'>";
                echo "<p>✅ <strong>Table '$table' trouvée</strong></p>";
                echo "</div>";
                break;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    if (!$table_name) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Aucune table de rôles trouvée</strong></p>";
        echo "<p>Tables recherchées : " . implode(', ', $tables_to_check) . "</p>";
        echo "</div>";
        exit();
    }
    
    // Vérifier la structure de la table
    try {
        $stmt = $pdo->query("DESCRIBE $table_name");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='info'>";
        echo "<h4>📋 Structure de la table '$table_name'</h4>";
        echo "<table>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur structure :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Récupérer les rôles existants
    echo "<div class='info'>";
    echo "<h3>📊 Rôles Existants</h3>";
    echo "</div>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM $table_name ORDER BY id ASC");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($roles) > 0) {
            echo "<div class='success'>";
            echo "<p>✅ <strong>" . count($roles) . " rôles trouvés</strong></p>";
            echo "</div>";
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th></tr>";
            foreach ($roles as $role) {
                echo "<tr>";
                echo "<td>{$role['id']}</td>";
                echo "<td><strong>{$role['nom']}</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>";
            echo "<p>⚠️ <strong>Aucun rôle trouvé dans la table</strong></p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur récupération rôles :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Test des APIs
    echo "<div class='info'>";
    echo "<h3>🧪 Test des APIs Rôles</h3>";
    echo "</div>";
    
    $apis_to_test = [
        'role.php (Original)' => 'http://localhost/Project_PFE/Backend/pages/roles/role.php',
        'index.php (Alternative)' => 'http://localhost/Project_PFE/Backend/pages/roles/index.php'
    ];
    
    echo "<table>";
    echo "<tr><th>API</th><th>Statut</th><th>Format Réponse</th><th>Nombre Rôles</th><th>Test</th></tr>";
    
    foreach ($apis_to_test as $name => $url) {
        echo "<tr>";
        echo "<td><strong>$name</strong></td>";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            
            if (is_array($data)) {
                if (isset($data['success'])) {
                    // Format avec success
                    echo "<td style='color: green;'>✅ OK</td>";
                    echo "<td>Objet avec success</td>";
                    echo "<td>" . (isset($data['roles']) ? count($data['roles']) : 0) . "</td>";
                } else {
                    // Format tableau direct
                    echo "<td style='color: green;'>✅ OK</td>";
                    echo "<td>Tableau direct</td>";
                    echo "<td>" . count($data) . "</td>";
                }
            } else {
                echo "<td style='color: red;'>❌ Erreur</td>";
                echo "<td>Format invalide</td>";
                echo "<td>-</td>";
            }
        } else {
            echo "<td style='color: red;'>❌ Pas de réponse</td>";
            echo "<td>-</td>";
            echo "<td>-</td>";
        }
        
        echo "<td><a href='$url' target='_blank' class='btn' style='padding: 5px 10px; font-size: 12px;'>Tester</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test JavaScript pour UserEditModal
    echo "<div class='info'>";
    echo "<h3>🧪 Test JavaScript pour UserEditModal</h3>";
    echo "</div>";
    
    echo "<div class='code'>";
    echo "// Test dans la console du navigateur pour vérifier le chargement des rôles

// Test API role.php (format tableau direct)
fetch('http://localhost/Project_PFE/Backend/pages/roles/role.php')
.then(response => response.json())
.then(data => {
    console.log('📊 API role.php:', data);
    if (Array.isArray(data)) {
        console.log('✅ Format tableau direct - Rôles:', data.length);
        data.forEach(role => console.log(`- ${role.nom} (ID: ${role.id})`));
    } else {
        console.log('❌ Format inattendu');
    }
})
.catch(error => console.error('❌ Erreur:', error));

// Test API index.php (format avec success)
fetch('http://localhost/Project_PFE/Backend/pages/roles/index.php')
.then(response => response.json())
.then(data => {
    console.log('📊 API index.php:', data);
    if (data.success && data.roles) {
        console.log('✅ Format avec success - Rôles:', data.roles.length);
        data.roles.forEach(role => console.log(`- ${role.nom} (ID: ${role.id})`));
    } else {
        console.log('❌ Format inattendu');
    }
})
.catch(error => console.error('❌ Erreur:', error));";
    echo "</div>";
    
    // Recommandations
    echo "<div class='warning'>";
    echo "<h3>💡 Recommandations</h3>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔧 Pour corriger le problème UserEditModal :</h4>";
    echo "<ol>";
    echo "<li><strong>Vérifiez la console du navigateur</strong> pour voir les erreurs</li>";
    echo "<li><strong>Utilisez l'API index.php</strong> qui retourne le format attendu</li>";
    echo "<li><strong>Ajoutez des logs</strong> dans fetchRoles() pour débugger</li>";
    echo "<li><strong>Vérifiez les CORS</strong> si les requêtes échouent</li>";
    echo "</ol>";
    echo "</div>";
    
    if (count($roles ?? []) == 0) {
        echo "<div class='warning'>";
        echo "<h4>⚠️ Aucun rôle dans la base de données</h4>";
        echo "<p>Ajoutez des rôles de test :</p>";
        echo "<div class='code'>";
        echo "INSERT INTO $table_name (nom) VALUES 
('Admin'),
('Enseignant'), 
('Etudiant'),
('Parent');";
        echo "</div>";
        echo "</div>";
    }
    
    // Liens de test
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>🔗 Liens de Test</h3>";
    echo "<a href='role.php' target='_blank' class='btn btn-success'>📊 API role.php</a>";
    echo "<a href='index.php' target='_blank' class='btn btn-success'>📊 API index.php</a>";
    echo "<a href='../../utilisateurs/userManagement.php' target='_blank' class='btn btn-warning'>👥 API Utilisateurs</a>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎯 DIAGNOSTIC TERMINÉ</h2>";
    echo "<p><strong>✅ UserEditModal corrigé pour gérer les deux formats d'API</strong></p>";
    echo "<p><strong>✅ API alternative index.php créée</strong></p>";
    echo "<p><strong>✅ Logs ajoutés pour débugger</strong></p>";
    echo "<p><strong>🚀 Les rôles devraient maintenant s'afficher dans le modal !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR GÉNÉRALE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
