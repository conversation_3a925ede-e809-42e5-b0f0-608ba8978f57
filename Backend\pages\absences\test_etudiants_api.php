<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST API ÉTUDIANTS POUR ABSENCES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .test-button:hover { background: #0056b3; color: white; text-decoration: none; }
        .select-demo { padding: 10px; border: 1px solid #ccc; border-radius: 4px; width: 300px; margin: 10px 0; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🧪 Test de l'API Étudiants pour le Formulaire d'Absences</h2>";
    echo "<p>Vérification que la liste déroulante des étudiants peut être remplie correctement</p>";
    echo "</div>";
    
    // 1. Test direct de l'API getEtudiants.php
    echo "<div class='step'>";
    echo "<h3>🔌 1. Test API getEtudiants.php</h3>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php";
    
    echo "<h4>🔗 URL testée :</h4>";
    echo "<p><a href='$api_url' target='_blank'>$api_url</a></p>";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API accessible</p>";
            
            $data = json_decode($response, true);
            if ($data) {
                echo "<p class='success'>✅ Réponse JSON valide</p>";
                
                echo "<h4>📋 Réponse complète :</h4>";
                echo "<div class='json-block'>";
                echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo "</div>";
                
                if (isset($data['success']) && $data['success']) {
                    echo "<p class='success'>✅ API retourne success: true</p>";
                    
                    if (isset($data['etudiants']) && is_array($data['etudiants'])) {
                        $etudiants = $data['etudiants'];
                        echo "<p class='success'>✅ " . count($etudiants) . " étudiant(s) trouvé(s)</p>";
                        
                        if (!empty($etudiants)) {
                            echo "<h4>👨‍🎓 Premier étudiant (exemple) :</h4>";
                            $first_student = $etudiants[0];
                            echo "<div class='json-block'>";
                            echo json_encode($first_student, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            echo "</div>";
                            
                            // Vérifier les champs nécessaires
                            $required_fields = ['id', 'nom', 'prenom'];
                            $missing_fields = [];
                            
                            foreach ($required_fields as $field) {
                                if (!isset($first_student[$field])) {
                                    $missing_fields[] = $field;
                                }
                            }
                            
                            if (empty($missing_fields)) {
                                echo "<p class='success'>✅ Tous les champs requis sont présents</p>";
                            } else {
                                echo "<p class='error'>❌ Champs manquants : " . implode(', ', $missing_fields) . "</p>";
                            }
                        } else {
                            echo "<p class='warning'>⚠️ Aucun étudiant dans la réponse</p>";
                        }
                    } else {
                        echo "<p class='error'>❌ Champ 'etudiants' manquant ou invalide</p>";
                    }
                } else {
                    echo "<p class='error'>❌ API retourne success: false</p>";
                    if (isset($data['error'])) {
                        echo "<p class='error'>Erreur : " . $data['error'] . "</p>";
                    }
                }
            } else {
                echo "<p class='error'>❌ Réponse JSON invalide</p>";
                echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
            echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test cURL : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Test de la base de données directement
    echo "<div class='step'>";
    echo "<h3>🗄️ 2. Test Direct Base de Données</h3>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion DB réussie</p>";
        
        // Requête similaire à celle de l'API
        $stmt = $pdo->prepare("
            SELECT e.id, u.nom, u.prenom, u.email, c.nom as classe_nom, e.numero_etudiant
            FROM Etudiants e
            JOIN Utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN Classes c ON e.classe_id = c.id
            ORDER BY u.nom, u.prenom
            LIMIT 5
        ");
        $stmt->execute();
        $etudiants_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='info'>📊 " . count($etudiants_db) . " étudiant(s) trouvé(s) en DB</p>";
        
        if (!empty($etudiants_db)) {
            echo "<h4>👨‍🎓 Étudiants en base (5 premiers) :</h4>";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #007bff; color: white;'>";
            echo "<th>ID</th><th>Nom</th><th>Prénom</th><th>Email</th><th>Classe</th><th>Numéro</th>";
            echo "</tr>";
            
            foreach ($etudiants_db as $etudiant) {
                echo "<tr>";
                echo "<td>{$etudiant['id']}</td>";
                echo "<td>{$etudiant['nom']}</td>";
                echo "<td>{$etudiant['prenom']}</td>";
                echo "<td>{$etudiant['email']}</td>";
                echo "<td>" . ($etudiant['classe_nom'] ?: 'Sans classe') . "</td>";
                echo "<td>" . ($etudiant['numero_etudiant'] ?: '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ Aucun étudiant trouvé en base de données</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur DB : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. Simulation du select HTML
    echo "<div class='step'>";
    echo "<h3>🎯 3. Simulation du Select HTML</h3>";
    
    echo "<h4>📋 Aperçu de la liste déroulante :</h4>";
    
    if (isset($etudiants_db) && !empty($etudiants_db)) {
        echo "<select class='select-demo'>";
        echo "<option value=''>Sélectionner un étudiant</option>";
        
        foreach ($etudiants_db as $etudiant) {
            $display_text = $etudiant['nom'] . ' ' . $etudiant['prenom'];
            if ($etudiant['classe_nom']) {
                $display_text .= ' - ' . $etudiant['classe_nom'];
            } else {
                $display_text .= ' - Sans classe';
            }
            
            echo "<option value='{$etudiant['id']}'>$display_text</option>";
        }
        
        echo "</select>";
        
        echo "<p class='success'>✅ La liste déroulante devrait s'afficher comme ci-dessus</p>";
    } else {
        echo "<select class='select-demo'>";
        echo "<option value=''>Sélectionner un étudiant</option>";
        echo "</select>";
        
        echo "<p class='error'>❌ Liste vide - aucun étudiant disponible</p>";
    }
    echo "</div>";
    
    // 4. Code JavaScript pour React
    echo "<div class='step'>";
    echo "<h3>💻 4. Code JavaScript pour React</h3>";
    
    echo "<h4>🔧 Fonction fetchEtudiants corrigée :</h4>";
    echo "<div class='json-block'>";
    echo "const fetchEtudiants = async () => {\n";
    echo "    try {\n";
    echo "        const token = localStorage.getItem('token');\n";
    echo "        const response = await axios.get(\n";
    echo "            'http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php',\n";
    echo "            { headers: { Authorization: `Bearer \${token}` } }\n";
    echo "        );\n";
    echo "        \n";
    echo "        console.log('🔍 API Response:', response.data);\n";
    echo "        \n";
    echo "        if (response.data.success) {\n";
    echo "            setEtudiants(response.data.etudiants);\n";
    echo "            console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n";
    echo "        } else {\n";
    echo "            console.error('❌ Erreur API:', response.data.error);\n";
    echo "            setEtudiants([]);\n";
    echo "        }\n";
    echo "    } catch (error) {\n";
    echo "        console.error('❌ Erreur:', error);\n";
    echo "        setEtudiants([]);\n";
    echo "    }\n";
    echo "};\n";
    echo "</div>";
    
    echo "<h4>🎯 JSX du select corrigé :</h4>";
    echo "<div class='json-block'>";
    echo "<select\n";
    echo "    value={formData.etudiant_id}\n";
    echo "    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n";
    echo "    required\n";
    echo ">\n";
    echo "    <option value=\"\">Sélectionner un étudiant</option>\n";
    echo "    {etudiants.map((etudiant) => (\n";
    echo "        <option key={etudiant.id} value={etudiant.id}>\n";
    echo "            {etudiant.nom} {etudiant.prenom} - {etudiant.classe_nom || 'Sans classe'}\n";
    echo "        </option>\n";
    echo "    ))}\n";
    echo "</select>\n";
    echo "</div>";
    echo "</div>";
    
    // 5. Instructions de débogage
    echo "<div class='step'>";
    echo "<h3>🐛 5. Instructions de Débogage React</h3>";
    
    echo "<h4>🔍 Dans la console du navigateur (F12) :</h4>";
    echo "<ol>";
    echo "<li><strong>Vérifier les logs :</strong> Chercher '🔍 DEBUG ABSENCES' et '📚 Étudiants disponibles'</li>";
    echo "<li><strong>Vérifier l'API :</strong> Onglet Network → Voir l'appel à getEtudiants.php</li>";
    echo "<li><strong>Vérifier les données :</strong> etudiants.length devrait être > 0</li>";
    echo "<li><strong>Vérifier le token :</strong> S'assurer d'être connecté</li>";
    echo "</ol>";
    
    echo "<h4>✅ Checklist de Vérification :</h4>";
    echo "<ul>";
    echo "<li>☐ API getEtudiants.php accessible</li>";
    echo "<li>☐ Réponse JSON valide avec success: true</li>";
    echo "<li>☐ Tableau etudiants non vide</li>";
    echo "<li>☐ Champs id, nom, prenom présents</li>";
    echo "<li>☐ fetchEtudiants() appelée dans useEffect</li>";
    echo "<li>☐ setEtudiants() met à jour le state</li>";
    echo "<li>☐ Select map() sur le bon tableau</li>";
    echo "<li>☐ Token d'authentification valide</li>";
    echo "</ul>";
    
    echo "<h4>🔧 Si le problème persiste :</h4>";
    echo "<ol>";
    echo "<li><strong>Vérifier l'authentification :</strong> Token JWT valide</li>";
    echo "<li><strong>Créer des étudiants de test :</strong> Si la table est vide</li>";
    echo "<li><strong>Tester l'API directement :</strong> <a href='$api_url' target='_blank'>$api_url</a></li>";
    echo "<li><strong>Vérifier les CORS :</strong> Headers Access-Control-Allow-Origin</li>";
    echo "</ol>";
    echo "</div>";
    
    // 6. Liens de test
    echo "<div class='step'>";
    echo "<h3>🔗 6. Liens de Test</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$api_url' target='_blank' class='test-button'>🧪 Tester API Étudiants</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='test-button'>🎯 Interface Absences</a>";
    echo "<a href='fix_data.php' target='_blank' class='test-button'>🔧 Créer Données Test</a>";
    echo "</div>";
    
    echo "<h4>🎯 Résultat Attendu :</h4>";
    echo "<p>Après les corrections, la liste déroulante des étudiants dans le formulaire d'absence devrait :</p>";
    echo "<ul>";
    echo "<li>✅ Se remplir automatiquement au chargement</li>";
    echo "<li>✅ Afficher 'Nom Prénom - Classe' pour chaque étudiant</li>";
    echo "<li>✅ Permettre la sélection d'un étudiant</li>";
    echo "<li>✅ Envoyer l'ID correct lors de la soumission</li>";
    echo "</ul>";
    
    echo "<p class='success'>🎉 <strong>La liste déroulante des étudiants devrait maintenant fonctionner !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
