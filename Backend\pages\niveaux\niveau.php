<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Niveau API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Niveau API - Headers: " . json_encode(getallheaders()));
error_log("Niveau API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("POST Data: " . json_encode($data));

    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("POST Error: Nom du niveau requis");
        echo json_encode(['success' => false, 'error' => 'Nom du niveau requis']);
        exit;
    }

    $nom = trim($data['nom']);

    // Vérifier si le niveau existe déjà
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE nom = :nom");
        $checkStmt->execute(['nom' => $nom]);
        if ($checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Ce niveau existe déjà']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Niveaux (nom) VALUES (:nom)");
        $stmt->execute(['nom' => $nom]);
        $newId = $pdo->lastInsertId();
        error_log("Niveau created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Niveau ajouté avec succès', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }
} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("SELECT * FROM Niveaux ORDER BY id DESC");
        $niveaux = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("GET Success: " . count($niveaux) . " niveaux found");
        echo json_encode($niveaux);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }
} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("PUT Data: " . json_encode($data));

    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("PUT Error: ID et nom du niveau requis");
        echo json_encode(['success' => false, 'error' => 'ID et nom du niveau requis']);
        exit;
    }

    $id = intval($data['id']);
    $nom = trim($data['nom']);

    // Vérifier si le niveau existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Niveau non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau exists error: " . $e->getMessage());
    }

    // Vérifier si le nom n'est pas déjà utilisé par un autre niveau
    try {
        $checkNameStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE nom = :nom AND id != :id");
        $checkNameStmt->execute(['nom' => $nom, 'id' => $id]);
        if ($checkNameStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Ce nom de niveau est déjà utilisé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau name error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("UPDATE Niveaux SET nom = :nom WHERE id = :id");
        $result = $stmt->execute(['nom' => $nom, 'id' => $id]);
        if ($result) {
            error_log("Niveau updated successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Niveau modifié avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Échec de la mise à jour']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }
} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("DELETE Data: " . json_encode($data));

    if (!isset($data['id'])) {
        error_log("DELETE Error: ID du niveau requis");
        echo json_encode(['success' => false, 'error' => 'ID du niveau requis']);
        exit;
    }

    $id = intval($data['id']);

    // Vérifier si le niveau existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Niveaux WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Niveau non trouvé']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check niveau exists error: " . $e->getMessage());
    }

    // Vérifier les dépendances (si le niveau est utilisé dans d'autres tables)
    try {
        // Exemple : vérifier si le niveau est utilisé dans la table Classes
        $checkDependencyStmt = $pdo->prepare("SELECT COUNT(*) as count FROM Classes WHERE niveau_id = :id");
        $checkDependencyStmt->execute(['id' => $id]);
        $dependency = $checkDependencyStmt->fetch();
        if ($dependency['count'] > 0) {
            echo json_encode(['success' => false, 'error' => 'Impossible de supprimer ce niveau car il est utilisé par des classes']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check dependencies error: " . $e->getMessage());
        // Continue même si la vérification des dépendances échoue
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM Niveaux WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);
        if ($result && $stmt->rowCount() > 0) {
            error_log("Niveau deleted successfully with ID: " . $id);
            echo json_encode(['success' => true, 'message' => 'Niveau supprimé avec succès']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Aucun niveau supprimé']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erreur de base de données: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
