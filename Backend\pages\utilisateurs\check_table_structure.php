<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔍 VÉRIFICATION STRUCTURE TABLE UTILISATEURS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='success'>";
        echo "<h2>✅ CONNEXION À LA BASE DE DONNÉES RÉUSSIE</h2>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h2>❌ ERREUR DE CONNEXION</h2>";
        echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
        exit();
    }
    
    // Vérifier la structure de la table utilisateurs
    echo "<div class='info'>";
    echo "<h3>📋 Structure de la Table 'utilisateurs'</h3>";
    echo "</div>";
    
    try {
        $stmt = $pdo->query("DESCRIBE utilisateurs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        $column_names = [];
        foreach ($columns as $column) {
            $column_names[] = $column['Field'];
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Vérifier si la colonne 'prenom' existe
        if (in_array('prenom', $column_names)) {
            echo "<div class='success'>";
            echo "<p>✅ <strong>Colonne 'prenom' trouvée</strong></p>";
            echo "</div>";
        } else {
            echo "<div class='warning'>";
            echo "<p>⚠️ <strong>Colonne 'prenom' MANQUANTE</strong></p>";
            echo "<p>Colonnes disponibles : " . implode(', ', $column_names) . "</p>";
            echo "</div>";
        }
        
        // Afficher quelques exemples de données
        echo "<div class='info'>";
        echo "<h3>📊 Exemples de Données</h3>";
        echo "</div>";
        
        $stmt = $pdo->query("SELECT * FROM utilisateurs LIMIT 3");
        $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sample_data) > 0) {
            echo "<table>";
            echo "<tr>";
            foreach (array_keys($sample_data[0]) as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";
            
            foreach ($sample_data as $row) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    if ($key === 'mot_de_passe') {
                        echo "<td>[MASQUÉ]</td>";
                    } else {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>";
            echo "<p>⚠️ <strong>Aucune donnée trouvée dans la table utilisateurs</strong></p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur lors de la vérification :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Vérifier la table roles
    echo "<div class='info'>";
    echo "<h3>📋 Structure de la Table 'roles'</h3>";
    echo "</div>";
    
    try {
        $stmt = $pdo->query("DESCRIBE roles");
        $role_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        foreach ($role_columns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Afficher les rôles disponibles
        $stmt = $pdo->query("SELECT * FROM roles");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($roles) > 0) {
            echo "<h4>🎭 Rôles Disponibles</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th></tr>";
            foreach ($roles as $role) {
                echo "<tr><td>{$role['id']}</td><td><strong>{$role['nom']}</strong></td></tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur table roles :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Recommandations
    echo "<div class='warning'>";
    echo "<h3>💡 Recommandations de Correction</h3>";
    echo "</div>";
    
    if (!in_array('prenom', $column_names)) {
        echo "<div class='info'>";
        echo "<h4>🔧 Option 1: Ajouter la colonne 'prenom'</h4>";
        echo "<div class='code'>";
        echo "ALTER TABLE utilisateurs ADD COLUMN prenom VARCHAR(100) AFTER nom;";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>🔧 Option 2: Modifier l'API pour ne pas utiliser 'prenom'</h4>";
        echo "<p>Adapter le code pour utiliser uniquement les colonnes existantes</p>";
        echo "</div>";
    }
    
    // Test de requête corrigée
    echo "<div class='info'>";
    echo "<h3>🧪 Test de Requête Corrigée</h3>";
    echo "</div>";
    
    try {
        // Construire une requête adaptée aux colonnes disponibles
        $select_fields = ['u.id', 'u.nom', 'u.email', 'u.role_id', 'r.nom as role_nom'];
        
        if (in_array('prenom', $column_names)) {
            $select_fields[] = 'u.prenom';
        }
        
        $sql = "SELECT " . implode(', ', $select_fields) . "
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                LIMIT 1";
        
        echo "<div class='code'>";
        echo "Requête adaptée :\n$sql";
        echo "</div>";
        
        $stmt = $pdo->query($sql);
        $test_result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_result) {
            echo "<div class='success'>";
            echo "<p>✅ <strong>Requête corrigée fonctionne !</strong></p>";
            echo "<p>Résultat : " . json_encode($test_result, JSON_PRETTY_PRINT) . "</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur test requête :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='success'>";
    echo "<h2>🎯 DIAGNOSTIC TERMINÉ</h2>";
    echo "<p><strong>Utilisez les informations ci-dessus pour corriger l'API</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR GÉNÉRALE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
