<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔍 VÉRIFICATION STRUCTURE TOUTES LES TABLES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='success'>";
        echo "<h2>✅ CONNEXION À LA BASE DE DONNÉES RÉUSSIE</h2>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h2>❌ ERREUR DE CONNEXION</h2>";
        echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
        exit();
    }
    
    // Tables à vérifier
    $tables_to_check = [
        'utilisateurs' => 'Table principale des utilisateurs',
        'roles' => 'Table des rôles',
        'etudiants' => 'Table des étudiants',
        'enseignants' => 'Table des enseignants',
        'parents' => 'Table des parents',
        'classes' => 'Table des classes',
        'filieres' => 'Table des filières',
        'niveaux' => 'Table des niveaux'
    ];
    
    $table_structures = [];
    
    foreach ($tables_to_check as $table => $description) {
        echo "<div class='info'>";
        echo "<h3>📋 $description ($table)</h3>";
        echo "</div>";
        
        try {
            // Vérifier si la table existe
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                echo "<div class='error'>";
                echo "<p>❌ <strong>Table '$table' n'existe pas</strong></p>";
                echo "</div>";
                $table_structures[$table] = false;
                continue;
            }
            
            // Récupérer la structure de la table
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $column_names = array_column($columns, 'Field');
            $table_structures[$table] = $column_names;
            
            echo "<table>";
            echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td><strong>{$column['Field']}</strong></td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div class='success'>";
            echo "<p>✅ <strong>Colonnes disponibles :</strong> " . implode(', ', $column_names) . "</p>";
            echo "</div>";
            
            // Afficher quelques exemples de données
            try {
                $stmt = $pdo->query("SELECT * FROM $table LIMIT 2");
                $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($sample_data) > 0) {
                    echo "<h4>📊 Exemples de données</h4>";
                    echo "<table>";
                    echo "<tr>";
                    foreach (array_keys($sample_data[0]) as $header) {
                        echo "<th>$header</th>";
                    }
                    echo "</tr>";
                    
                    foreach ($sample_data as $row) {
                        echo "<tr>";
                        foreach ($row as $key => $value) {
                            if ($key === 'mot_de_passe') {
                                echo "<td>[MASQUÉ]</td>";
                            } else {
                                echo "<td>" . htmlspecialchars($value) . "</td>";
                            }
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<div class='warning'>";
                    echo "<p>⚠️ <strong>Table '$table' vide</strong></p>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='warning'>";
                echo "<p>⚠️ <strong>Impossible de lire les données de '$table' :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<p>❌ <strong>Erreur avec la table '$table' :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
            $table_structures[$table] = false;
        }
    }
    
    // Analyse des problèmes potentiels
    echo "<div class='warning'>";
    echo "<h3>⚠️ Analyse des Problèmes Potentiels</h3>";
    echo "</div>";
    
    $issues = [];
    
    // Vérifier les colonnes problématiques
    if (isset($table_structures['etudiants']) && is_array($table_structures['etudiants'])) {
        if (!in_array('classe_id', $table_structures['etudiants'])) {
            $issues[] = "Colonne 'classe_id' manquante dans la table 'etudiants'";
        }
    }
    
    if (isset($table_structures['enseignants']) && is_array($table_structures['enseignants'])) {
        if (!in_array('nom_prenom', $table_structures['enseignants'])) {
            $issues[] = "Colonne 'nom_prenom' manquante dans la table 'enseignants'";
        }
    }
    
    if (count($issues) > 0) {
        echo "<div class='error'>";
        echo "<h4>❌ Problèmes Détectés</h4>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li><strong>$issue</strong></li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='success'>";
        echo "<p>✅ <strong>Aucun problème majeur détecté</strong></p>";
        echo "</div>";
    }
    
    // Générer une requête SQL adaptée
    echo "<div class='info'>";
    echo "<h3>🔧 Requête SQL Adaptée</h3>";
    echo "</div>";
    
    // Construire une requête qui fonctionne avec la structure actuelle
    $select_fields = ['u.id', 'u.nom', 'u.email', 'u.role_id', 'r.nom as role_nom'];
    $joins = ['LEFT JOIN roles r ON u.role_id = r.id'];
    
    // Ajouter les jointures selon les tables disponibles
    if (isset($table_structures['enseignants']) && is_array($table_structures['enseignants'])) {
        $joins[] = 'LEFT JOIN enseignants e ON u.id = e.utilisateur_id';
        if (in_array('nom_prenom', $table_structures['enseignants'])) {
            $select_fields[] = 'e.nom_prenom as enseignant_nom_prenom';
        }
    }
    
    if (isset($table_structures['etudiants']) && is_array($table_structures['etudiants'])) {
        $joins[] = 'LEFT JOIN etudiants et ON u.id = et.utilisateur_id';
        
        // Ajouter classe_id seulement si la colonne existe
        if (in_array('classe_id', $table_structures['etudiants']) && 
            isset($table_structures['classes']) && is_array($table_structures['classes'])) {
            $joins[] = 'LEFT JOIN classes c ON et.classe_id = c.id';
            $select_fields[] = 'c.nom as classe_nom';
            
            // Ajouter filière et niveau si disponibles
            if (isset($table_structures['filieres']) && is_array($table_structures['filieres'])) {
                $joins[] = 'LEFT JOIN filieres f ON c.filiere_id = f.id';
                $select_fields[] = 'f.nom as filiere_nom';
            }
            
            if (isset($table_structures['niveaux']) && is_array($table_structures['niveaux'])) {
                $joins[] = 'LEFT JOIN niveaux n ON c.niveau_id = n.id';
                $select_fields[] = 'n.nom as niveau_nom';
            }
        }
    }
    
    if (isset($table_structures['parents']) && is_array($table_structures['parents'])) {
        $joins[] = 'LEFT JOIN parents p ON u.id = p.utilisateur_id';
        if (in_array('telephone', $table_structures['parents'])) {
            $select_fields[] = 'p.telephone as parent_telephone';
        }
        if (in_array('adresse', $table_structures['parents'])) {
            $select_fields[] = 'p.adresse as parent_adresse';
        }
    }
    
    $sql = "SELECT " . implode(', ', $select_fields) . "\n";
    $sql .= "FROM utilisateurs u\n";
    $sql .= implode("\n", $joins) . "\n";
    $sql .= "WHERE u.id = ?";
    
    echo "<div class='code'>";
    echo "Requête SQL adaptée à votre structure :\n\n$sql";
    echo "</div>";
    
    // Test de la requête
    echo "<div class='info'>";
    echo "<h3>🧪 Test de la Requête Adaptée</h3>";
    echo "</div>";
    
    try {
        // Récupérer un utilisateur pour le test
        $stmt = $pdo->query("SELECT id FROM utilisateurs LIMIT 1");
        $test_user_id = $stmt->fetchColumn();
        
        if ($test_user_id) {
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$test_user_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                echo "<div class='success'>";
                echo "<h4>✅ Requête Fonctionne Parfaitement</h4>";
                echo "<p><strong>Utilisateur testé :</strong> ID $test_user_id</p>";
                echo "</div>";
                
                echo "<div class='code'>";
                echo "Résultat :\n" . json_encode($result, JSON_PRETTY_PRINT);
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<p>❌ <strong>Aucun résultat retourné</strong></p>";
                echo "</div>";
            }
        } else {
            echo "<div class='warning'>";
            echo "<p>⚠️ <strong>Aucun utilisateur trouvé pour le test</strong></p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur test requête :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='success'>";
    echo "<h2>🎯 DIAGNOSTIC TERMINÉ</h2>";
    echo "<p><strong>Utilisez la requête SQL adaptée pour corriger l'API</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR GÉNÉRALE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
