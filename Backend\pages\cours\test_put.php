<?php
/**
 * Script de test pour vérifier la réception des données PUT
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

// Gérer le header X-HTTP-Method-Override pour les requêtes PUT via POST
if ($method === 'POST' && isset($_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'])) {
    $method = $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'];
    error_log("Method override detected via header: " . $method);
}

// Gérer le champ _method pour les requêtes PUT via POST
if ($method === 'POST' && isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
    $method = 'PUT';
    error_log("Method override detected via _method field: " . $method);
}

echo json_encode([
    'status' => 'Test PUT Data Reception',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $method,
    'original_method' => $_SERVER['REQUEST_METHOD'],
    'data' => [
        'POST' => $_POST,
        'FILES' => $_FILES,
        'GET' => $_GET,
        'SERVER_METHOD' => $_SERVER['REQUEST_METHOD'],
        'HEADERS' => [
            'Authorization' => $_SERVER['HTTP_AUTHORIZATION'] ?? 'non défini',
            'Content-Type' => $_SERVER['CONTENT_TYPE'] ?? 'non défini',
            'X-HTTP-Method-Override' => $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'] ?? 'non défini'
        ]
    ],
    'validation' => [
        'has_id' => isset($_POST['id']),
        'id_value' => $_POST['id'] ?? 'non défini',
        'id_is_numeric' => isset($_POST['id']) ? is_numeric($_POST['id']) : false,
        'has_titre' => isset($_POST['titre']),
        'titre_value' => $_POST['titre'] ?? 'non défini',
        'has_matiere_id' => isset($_POST['matiere_id']),
        'matiere_id_value' => $_POST['matiere_id'] ?? 'non défini',
        'has_classe_id' => isset($_POST['classe_id']),
        'classe_id_value' => $_POST['classe_id'] ?? 'non défini',
        'has_date_publication' => isset($_POST['date_publication']),
        'date_publication_value' => $_POST['date_publication'] ?? 'non défini',
        'has_file' => isset($_FILES['fichier_pdf']) && $_FILES['fichier_pdf']['error'] === UPLOAD_ERR_OK
    ]
], JSON_PRETTY_PRINT);
?>
