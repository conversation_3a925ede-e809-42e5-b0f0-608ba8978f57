<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Fonction d'authentification et récupération du rôle
function getAuthenticatedUser($pdo) {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        return null;
    }

    // Simulation d'authentification basée sur le token
    // En production, vous devriez vérifier le token dans votre base de données
    if (strpos($token, 'etudiant') !== false) {
        // Récupérer un vrai étudiant de la base de données
        $stmt = $pdo->query("SELECT id FROM etudiants LIMIT 1");
        $etudiant = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$etudiant) {
            // Aucun étudiant trouvé, retourner null
            return null;
        }

        return [
            'id' => 1,
            'role' => 'etudiant',
            'etudiant_id' => $etudiant['id'], // ID réel dans la table etudiants
            'email' => '<EMAIL>'
        ];
    } elseif (strpos($token, 'enseignant') !== false) {
        return [
            'id' => 2,
            'role' => 'enseignant',
            'email' => '<EMAIL>'
        ];
    } elseif (strpos($token, 'admin') !== false) {
        return [
            'id' => 3,
            'role' => 'admin',
            'email' => '<EMAIL>'
        ];
    }

    return null;
}

// Fonction de validation automatique des réponses
function validateResponse($pdo, $quiz_id, $reponse_etudiant) {
    try {
        // Récupérer la réponse correcte du quiz
        $stmt = $pdo->prepare("SELECT reponse_correcte FROM quiz WHERE id = ?");
        $stmt->execute([$quiz_id]);
        $quiz = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$quiz || empty($quiz['reponse_correcte'])) {
            // Si pas de quiz ou pas de réponse correcte définie, retourner NULL (non évalué)
            return null;
        }

        // Normaliser les réponses pour la comparaison
        $reponse_etudiant_norm = trim(strtolower($reponse_etudiant));
        $reponse_correcte_norm = trim(strtolower($quiz['reponse_correcte']));

        // Comparaison exacte (peut être étendue avec des algorithmes plus sophistiqués)
        $est_correct = ($reponse_etudiant_norm === $reponse_correcte_norm);

        // Retourner 1 pour correct, 0 pour incorrect
        return $est_correct ? 1 : 0;

    } catch (Exception $e) {
        error_log("Erreur validation réponse: " . $e->getMessage());
        // En cas d'erreur, retourner NULL (non évalué)
        return null;
    }
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $user = getAuthenticatedUser($pdo);

    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentification requise ou aucun étudiant trouvé']);
        exit();
    }
    
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user);
            break;
        case 'POST':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePost($pdo, $user, $input);
            break;
        case 'PUT':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePut($pdo, $user, $input);
            break;
        case 'DELETE':
            $input = json_decode(file_get_contents("php://input"), true);
            handleDelete($pdo, $user, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

function handleGet($pdo, $user) {
    try {
        $role = $user['role'];
        
        // Construction de la requête selon le rôle
        $baseQuery = "
            SELECT 
                rq.id,
                rq.quiz_id,
                rq.etudiant_id,
                rq.reponse,
                " . ($role === 'etudiant' ? 'NULL as est_correct' : 'rq.est_correct') . ",
                q.question,
                " . ($role === 'etudiant' ? 'NULL as reponse_correcte' : 'q.reponse_correcte') . ",
                d.titre as devoir_titre,
                d.description as devoir_description,
                d.date_remise,
                m.nom as matiere_nom,
                c.nom as classe_nom,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') as timestamp_server
            FROM reponsesquiz rq
            LEFT JOIN quiz q ON rq.quiz_id = q.id
            LEFT JOIN devoirs d ON q.devoir_id = d.id
            LEFT JOIN matieres m ON d.matiere_id = m.id
            LEFT JOIN classes c ON d.classe_id = c.id
            LEFT JOIN etudiants e ON rq.etudiant_id = e.id
            LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
        ";
        
        // Filtrage selon le rôle
        if ($role === 'etudiant') {
            // L'étudiant ne voit que ses propres réponses
            $query = $baseQuery . " WHERE rq.etudiant_id = ? ORDER BY rq.id DESC";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$user['etudiant_id']]);
        } else {
            // Enseignants et admins voient toutes les réponses
            $query = $baseQuery . " ORDER BY rq.id DESC";
            $stmt = $pdo->prepare($query);
            $stmt->execute();
        }
        
        $reponses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ajouter des métadonnées selon le rôle
        $response = [
            'success' => true,
            'data' => $reponses,
            'count' => count($reponses),
            'role' => $role,
            'permissions' => [
                'can_create' => $role === 'etudiant',
                'can_update' => $role === 'etudiant',
                'can_delete' => $role === 'etudiant',
                'can_see_corrections' => $role !== 'etudiant'
            ]
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des réponses: ' . $e->getMessage()]);
    }
}

function handlePost($pdo, $user, $input) {
    // Seuls les étudiants peuvent créer des réponses
    if ($user['role'] !== 'etudiant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les étudiants peuvent créer des réponses']);
        return;
    }
    
    // Validation des données d'entrée
    if (!isset($input['quiz_id']) || !isset($input['reponse'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (quiz_id et reponse requis)']);
        return;
    }
    
    if (empty(trim($input['reponse']))) {
        http_response_code(400);
        echo json_encode(['error' => 'La réponse ne peut pas être vide']);
        return;
    }
    
    try {
        // Vérifier que le quiz existe
        $stmt = $pdo->prepare("SELECT id, question, reponse_correcte FROM quiz WHERE id = ?");
        $stmt->execute([$input['quiz_id']]);
        $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$quiz) {
            http_response_code(400);
            echo json_encode(['error' => 'Le quiz sélectionné n\'existe pas']);
            return;
        }
        
        // Vérifier que l'étudiant n'a pas déjà répondu à ce quiz
        $stmt = $pdo->prepare("SELECT id FROM reponsesquiz WHERE quiz_id = ? AND etudiant_id = ?");
        $stmt->execute([$input['quiz_id'], $user['etudiant_id']]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Vous avez déjà répondu à ce quiz. Vous pouvez modifier votre réponse existante.']);
            return;
        }
        
        // Validation automatique de la réponse
        $est_correct = validateResponse($pdo, $input['quiz_id'], $input['reponse']);

        // Insérer la réponse avec gestion correcte de est_correct
        $stmt = $pdo->prepare("
            INSERT INTO reponsesquiz (quiz_id, etudiant_id, reponse, est_correct)
            VALUES (?, ?, ?, ?)
        ");

        $stmt->execute([
            $input['quiz_id'],
            $user['etudiant_id'],
            trim($input['reponse']),
            $est_correct // Sera 1, 0, ou NULL
        ]);
        
        $response = [
            'success' => true,
            'message' => 'Réponse enregistrée avec succès',
            'id' => $pdo->lastInsertId(),
            'quiz_question' => $quiz['question']
            // Note: on ne retourne jamais est_correct à l'étudiant
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

function handlePut($pdo, $user, $input) {
    // Seuls les étudiants peuvent modifier leurs réponses
    if ($user['role'] !== 'etudiant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les étudiants peuvent modifier leurs réponses']);
        return;
    }
    
    if (!isset($input['id']) || !isset($input['reponse'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de la réponse et nouvelle réponse requis']);
        return;
    }
    
    if (empty(trim($input['reponse']))) {
        http_response_code(400);
        echo json_encode(['error' => 'La réponse ne peut pas être vide']);
        return;
    }
    
    try {
        // Vérifier que la réponse existe et appartient à l'étudiant
        $stmt = $pdo->prepare("SELECT quiz_id FROM reponsesquiz WHERE id = ? AND etudiant_id = ?");
        $stmt->execute([$input['id'], $user['etudiant_id']]);
        $reponse = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$reponse) {
            http_response_code(404);
            echo json_encode(['error' => 'Réponse non trouvée ou vous n\'avez pas le droit de la modifier']);
            return;
        }
        
        // Revalider la réponse
        $est_correct = validateResponse($pdo, $reponse['quiz_id'], $input['reponse']);

        // Mettre à jour la réponse avec gestion correcte de est_correct
        $stmt = $pdo->prepare("
            UPDATE reponsesquiz
            SET reponse = ?, est_correct = ?
            WHERE id = ? AND etudiant_id = ?
        ");

        $stmt->execute([
            trim($input['reponse']),
            $est_correct, // Sera 1, 0, ou NULL
            $input['id'],
            $user['etudiant_id']
        ]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Réponse modifiée avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'Aucune modification nécessaire'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

function handleDelete($pdo, $user, $input) {
    // Seuls les étudiants peuvent supprimer leurs réponses
    if ($user['role'] !== 'etudiant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les étudiants peuvent supprimer leurs réponses']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de la réponse manquant']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM reponsesquiz WHERE id = ? AND etudiant_id = ?");
        $stmt->execute([$input['id'], $user['etudiant_id']]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Réponse supprimée avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Réponse non trouvée ou vous n\'avez pas le droit de la supprimer'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
