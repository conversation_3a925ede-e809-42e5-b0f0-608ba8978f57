import React, { useState } from 'react';

const EnseignantFormDebug = ({ onSuccess, onError }) => {
  const [formData, setFormData] = useState({
    utilisateur_id: '',
    telephone: '',
    specialite: '',
    date_embauche: '',
    salaire: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState([]);

  // Fonction pour ajouter des informations de debug
  const addDebugInfo = (message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    const debugEntry = {
      timestamp,
      message,
      data: data ? JSON.stringify(data, null, 2) : null
    };
    setDebugInfo(prev => [...prev, debugEntry]);
    console.log(`[${timestamp}] ${message}`, data);
  };

  // Gestion d'erreur détaillée
  const handleError = (error, context = '') => {
    addDebugInfo(`❌ Erreur ${context}`, {
      name: error.name,
      message: error.message,
      stack: error.stack
    });

    let userMessage = 'Erreur inconnue';

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      userMessage = 'Erreur de connexion au serveur. Vérifiez que le serveur est démarré.';
    } else if (error.message.includes('HTTP')) {
      userMessage = `Erreur serveur: ${error.message}`;
    } else if (error.message.includes('JSON')) {
      userMessage = 'Erreur de format de données du serveur';
    } else if (error.message) {
      userMessage = error.message;
    }

    if (onError) {
      onError(userMessage);
    } else {
      alert(userMessage);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setDebugInfo([]); // Reset debug info

    addDebugInfo('🚀 Début de la soumission', formData);

    try {
      // Validation côté client
      if (!formData.utilisateur_id) {
        throw new Error('Utilisateur ID requis');
      }

      const url = 'http://localhost/Project_PFE/Backend/pages/enseignants/enseignant_debug.php';
      addDebugInfo('📡 Envoi de la requête', { url, data: formData });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      addDebugInfo('📊 Réponse reçue', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      // Vérifier le status HTTP
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Lire la réponse comme texte d'abord
      const responseText = await response.text();
      addDebugInfo('📄 Texte de réponse', { text: responseText });

      if (!responseText) {
        throw new Error('Réponse vide du serveur');
      }

      // Parser le JSON
      let data;
      try {
        data = JSON.parse(responseText);
        addDebugInfo('✅ JSON parsé avec succès', data);
      } catch (jsonError) {
        addDebugInfo('❌ Erreur parsing JSON', {
          error: jsonError.message,
          responseText: responseText.substring(0, 500)
        });
        throw new Error(`Réponse non-JSON du serveur: ${responseText.substring(0, 100)}...`);
      }

      // Vérifier la réponse métier
      if (data.success) {
        addDebugInfo('🎉 Succès', data);
        
        if (onSuccess) {
          onSuccess(data);
        } else {
          alert('Enseignant créé avec succès !');
        }

        // Reset du formulaire
        setFormData({
          utilisateur_id: '',
          telephone: '',
          specialite: '',
          date_embauche: '',
          salaire: ''
        });
      } else {
        const errorMessage = data.error || 'Erreur inconnue du serveur';
        addDebugInfo('❌ Erreur métier', { error: errorMessage });
        throw new Error(errorMessage);
      }

    } catch (error) {
      handleError(error, 'lors de la soumission');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Formulaire Enseignant avec Debug</h2>
      
      <form onSubmit={handleSubmit} style={{ marginBottom: '30px' }}>
        <div style={{ marginBottom: '15px' }}>
          <label>
            Utilisateur ID:
            <input
              type="number"
              name="utilisateur_id"
              value={formData.utilisateur_id}
              onChange={handleChange}
              required
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </label>
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>
            Téléphone:
            <input
              type="text"
              name="telephone"
              value={formData.telephone}
              onChange={handleChange}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </label>
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>
            Spécialité:
            <input
              type="text"
              name="specialite"
              value={formData.specialite}
              onChange={handleChange}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </label>
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>
            Date d'embauche:
            <input
              type="date"
              name="date_embauche"
              value={formData.date_embauche}
              onChange={handleChange}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </label>
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>
            Salaire:
            <input
              type="number"
              step="0.01"
              name="salaire"
              value={formData.salaire}
              onChange={handleChange}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </label>
        </div>

        <button
          type="submit"
          disabled={loading}
          style={{
            padding: '10px 20px',
            backgroundColor: loading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Création en cours...' : 'Créer Enseignant'}
        </button>
      </form>

      {/* Informations de debug */}
      {debugInfo.length > 0 && (
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          padding: '15px'
        }}>
          <h3>🔍 Informations de Debug</h3>
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {debugInfo.map((info, index) => (
              <div key={index} style={{
                marginBottom: '10px',
                padding: '10px',
                backgroundColor: 'white',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                <div style={{ fontWeight: 'bold', color: '#007bff' }}>
                  [{info.timestamp}] {info.message}
                </div>
                {info.data && (
                  <pre style={{
                    marginTop: '5px',
                    backgroundColor: '#f1f3f4',
                    padding: '5px',
                    borderRadius: '2px',
                    overflow: 'auto',
                    fontSize: '11px'
                  }}>
                    {info.data}
                  </pre>
                )}
              </div>
            ))}
          </div>
          
          <button
            onClick={() => setDebugInfo([])}
            style={{
              marginTop: '10px',
              padding: '5px 10px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Effacer Debug
          </button>
        </div>
      )}
    </div>
  );
};

export default EnseignantFormDebug;
