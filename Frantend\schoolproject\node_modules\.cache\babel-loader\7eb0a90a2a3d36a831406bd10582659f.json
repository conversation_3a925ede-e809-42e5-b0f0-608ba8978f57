{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\NotesUnified.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport { filterNotes, canManageData, isStudent, logSecurityEvent } from '../utils/studentDataFilter';\nimport '../css/Factures.css';\nconst NotesUnified = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [notes, setNotes] = useState([]);\n  const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [showGenerateModal, setShowGenerateModal] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterMatiere, setFilterMatiere] = useState('all');\n  const [filterEtudiant, setFilterEtudiant] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [statistics, setStatistics] = useState({});\n  const [matieres, setMatieres] = useState([]);\n  const [etudiants, setEtudiants] = useState([]);\n  const [selectedEtudiant, setSelectedEtudiant] = useState(null);\n  const [selectedMatiere, setSelectedMatiere] = useState(null);\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    devoir_id: '',\n    matiere_id: '',\n    note: ''\n  });\n\n  // Déterminer le rôle et les permissions avec notre système unifié\n  const isEtudiant = isStudent(user);\n  const isEnseignant = (user === null || user === void 0 ? void 0 : user.role) === 'enseignant' || (user === null || user === void 0 ? void 0 : user.role) === 'Enseignant';\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n\n  // Permissions selon les spécifications\n  const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n  const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n  useEffect(() => {\n    if (canView) {\n      fetchNotes();\n      if (isEnseignant) {\n        fetchDevoirsDisponibles();\n      }\n    }\n  }, [canView, isEnseignant]);\n  const fetchNotes = async () => {\n    try {\n      console.log('🔄 Chargement des notes...');\n\n      // Déterminer le token selon le rôle\n      let authToken = 'default-token';\n      if (isEtudiant) authToken = 'etudiant-token';else if (isEnseignant) authToken = 'enseignant-token';else if (isAdmin) authToken = 'admin-token';\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n        headers: {\n          Authorization: `Bearer ${authToken}`\n        }\n      });\n      console.log('✅ Réponse API notes:', response.data);\n      if (response.data.success) {\n        const notesData = response.data.data || [];\n        setNotes(notesData);\n\n        // Calculer les statistiques\n        const stats = calculateStatistics(notesData);\n        setStatistics(stats);\n\n        // Extraire les matières et étudiants uniques pour les filtres\n        if (!isEtudiant) {\n          const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n          setMatieres(matieresUniques);\n          if (isAdmin) {\n            const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n            setEtudiants(etudiantsUniques);\n          }\n        }\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Erreur lors du chargement des notes:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403) {\n        Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n      } else {\n        Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n      }\n      setNotes([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchDevoirsDisponibles = async () => {\n    try {\n      console.log('🔄 Chargement des devoirs disponibles...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n        headers: {\n          Authorization: `Bearer enseignant-token`\n        }\n      });\n      console.log('✅ Réponse API devoirs disponibles:', response.data);\n      if (response.data.success) {\n        setDevoirsDisponibles(response.data.data || []);\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des devoirs:', error);\n      setDevoirsDisponibles([]);\n    }\n  };\n  const calculateStatistics = notesData => {\n    const total = notesData.length;\n    const moyenne_generale = total > 0 ? notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n    const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n    const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n    const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n    const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n    const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n    const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n    const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n    return {\n      total_notes: total,\n      moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n      notes_excellentes,\n      notes_bonnes,\n      notes_moyennes,\n      notes_faibles,\n      nombre_etudiants: etudiants.length,\n      nombre_matieres: matieres.length,\n      nombre_devoirs: devoirs.length\n    };\n  };\n  const handleGenerateNotes = async devoir_id => {\n    const result = await Swal.fire({\n      title: 'Générer les notes automatiquement ?',\n      text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n      icon: 'question',\n      showCancelButton: true,\n      confirmButtonColor: '#28a745',\n      cancelButtonColor: '#6c757d',\n      confirmButtonText: 'Oui, générer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n        const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          action: 'generate',\n          devoir_id: devoir_id\n        }, {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          }\n        });\n        console.log('✅ Notes générées:', response.data);\n        if (response.data.success) {\n          const details = response.data.details;\n          Swal.fire({\n            title: 'Notes générées !',\n            html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n            icon: 'success',\n            timer: 3000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n        }\n      } catch (error) {\n        var _error$response2, _error$response2$data;\n        console.error('❌ Erreur génération:', error);\n        Swal.fire('Erreur', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Erreur lors de la génération des notes', 'error');\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n      return;\n    }\n    if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n      Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n      return;\n    }\n    try {\n      const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n      const method = editingNote ? 'PUT' : 'POST';\n      const data = editingNote ? {\n        ...formData,\n        id: editingNote.id\n      } : formData;\n      console.log('🔄 Envoi note:', {\n        method,\n        data\n      });\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer enseignant-token`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('✅ Note envoyée:', response.data);\n      if (response.data.success) {\n        Swal.fire({\n          title: 'Succès !',\n          text: response.data.message,\n          icon: 'success',\n          timer: 2000,\n          showConfirmButton: false\n        });\n        setShowModal(false);\n        setEditingNote(null);\n        resetForm();\n        fetchNotes();\n        if (isEnseignant) fetchDevoirsDisponibles();\n      } else {\n        Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('❌ Erreur:', error);\n      Swal.fire('Erreur', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = note => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n      return;\n    }\n    setEditingNote(note);\n    setFormData({\n      etudiant_id: note.etudiant_id || '',\n      devoir_id: note.devoir_id || '',\n      matiere_id: note.matiere_id || '',\n      note: note.note || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Supprimer cette note ?',\n      text: 'Cette action est irréversible !',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        console.log('🗑️ Suppression note ID:', id);\n        const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n          headers: {\n            Authorization: `Bearer enseignant-token`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        console.log('✅ Note supprimée:', response.data);\n        if (response.data.success) {\n          Swal.fire({\n            title: 'Supprimé !',\n            text: response.data.message,\n            icon: 'success',\n            timer: 2000,\n            showConfirmButton: false\n          });\n          fetchNotes();\n          if (isEnseignant) fetchDevoirsDisponibles();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n        }\n      } catch (error) {\n        var _error$response4, _error$response4$data;\n        console.error('❌ Erreur suppression:', error);\n        Swal.fire('Erreur', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || 'Impossible de supprimer la note', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      devoir_id: '',\n      matiere_id: '',\n      note: ''\n    });\n    setSelectedEtudiant(null);\n    setSelectedMatiere(null);\n  };\n\n  // Fonction pour gérer la sélection d'un étudiant\n  const handleEtudiantSelection = etudiantId => {\n    const etudiant = etudiants.find(e => (e.id || e.etudiant_id) === parseInt(etudiantId));\n    setSelectedEtudiant(etudiant || null);\n    setFormData(prev => ({\n      ...prev,\n      etudiant_id: etudiantId\n    }));\n  };\n\n  // Fonction pour gérer la sélection d'une matière\n  const handleMatiereSelection = matiereId => {\n    const matiere = matieres.find(m => m.id === parseInt(matiereId));\n    setSelectedMatiere(matiere || null);\n    setFormData(prev => ({\n      ...prev,\n      matiere_id: matiereId\n    }));\n  };\n\n  // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n  const securityFilteredNotes = filterNotes(notes, user);\n\n  // Log de sécurité si des données ont été filtrées\n  if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n    logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n      total: notes.length,\n      filtered: securityFilteredNotes.length\n    });\n  }\n\n  // ÉTAPE 2 : Filtrage par recherche et critères\n  const filteredNotes = securityFilteredNotes.filter(note => {\n    const searchLower = searchTerm.toLowerCase();\n    const matchesSearch = (note.etudiant_nom || '').toLowerCase().includes(searchLower) || (note.devoir_titre || '').toLowerCase().includes(searchLower) || (note.matiere_nom || '').toLowerCase().includes(searchLower) || (note.note || '').toString().includes(searchLower);\n    const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n    const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n    return matchesSearch && matchesMatiere && matchesEtudiant;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, filterMatiere, filterEtudiant]);\n  const getNoteBadge = note => {\n    const noteValue = parseFloat(note);\n    let style = {\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.9em',\n      fontWeight: 'bold'\n    };\n    if (noteValue >= 16) {\n      style = {\n        ...style,\n        backgroundColor: '#28a745',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 20\n        }\n      }, \"\\uD83C\\uDFC6 \", note, \"/20\");\n    } else if (noteValue >= 12) {\n      style = {\n        ...style,\n        backgroundColor: '#17a2b8',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDC4D \", note, \"/20\");\n    } else if (noteValue >= 10) {\n      style = {\n        ...style,\n        backgroundColor: '#ffc107',\n        color: 'black'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCCA \", note, \"/20\");\n    } else {\n      style = {\n        ...style,\n        backgroundColor: '#dc3545',\n        color: 'white'\n      };\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: style,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 20\n        }\n      }, \"\\uD83D\\uDCC9 \", note, \"/20\");\n    }\n  };\n  const getHeaderTitle = () => {\n    if (isEtudiant) return '📊 Mes Notes';\n    if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n    if (isAdmin) return '🛡️ Administration - Notes';\n    return '📊 Notes';\n  };\n  const getInfoMessage = () => {\n    if (isEtudiant) {\n      return {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffc107',\n          color: '#856404'\n        },\n        text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n      };\n    } else if (isEnseignant) {\n      return {\n        style: {\n          backgroundColor: '#d4edda',\n          border: '1px solid #c3e6cb',\n          color: '#155724'\n        },\n        text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n      };\n    } else if (isAdmin) {\n      return {\n        style: {\n          backgroundColor: '#e2e3e5',\n          border: '1px solid #d6d8db',\n          color: '#383d41'\n        },\n        text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n      };\n    }\n    return null;\n  };\n  const styles = {\n    accessDenied: {\n      textAlign: 'center',\n      padding: '50px 20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      margin: '20px 0'\n    },\n    idBadge: {\n      backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n      color: 'white',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.8em',\n      fontWeight: 'bold'\n    }\n  };\n\n  // Vérification d'accès\n  if (!canView) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"factures-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.accessDenied,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 21\n      }\n    }, \"\\uD83D\\uDEAB Acc\\xE8s Refus\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 21\n      }\n    }, \"Vous n'avez pas les permissions pour acc\\xE9der aux notes.\")));\n  }\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 17\n      }\n    }, \"Chargement des notes...\"));\n  }\n  const infoMessage = getInfoMessage();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 17\n    }\n  }, getHeaderTitle()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 21\n    }\n  }, filteredNotes.length, \" note(s) trouv\\xE9e(s)\"), canManage && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => setShowGenerateModal(true),\n    style: {\n      marginRight: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/auto.png\",\n    alt: \"G\\xE9n\\xE9rer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 33\n    }\n  }), \" G\\xE9n\\xE9ration Auto\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 33\n    }\n  }), \" Nouvelle Note\")))), infoMessage && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...infoMessage.style,\n      borderRadius: '8px',\n      padding: '15px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 21\n    }\n  }, infoMessage.text)), statistics.total_notes > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n      gap: '15px',\n      backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n      border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n      borderRadius: '8px',\n      padding: '20px',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: isAdmin ? '#6f42c1' : '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 25\n    }\n  }, statistics.total_notes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 25\n    }\n  }, isEtudiant ? 'Mes Notes' : 'Total Notes')), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#17a2b8'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 25\n    }\n  }, statistics.moyenne_generale), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 25\n    }\n  }, \"Moyenne\")), !isEtudiant && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 33\n    }\n  }, statistics.notes_excellentes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 33\n    }\n  }, \"Excellentes (\\u226516)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#ffc107'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 33\n    }\n  }, statistics.notes_moyennes), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 33\n    }\n  }, \"Moyennes (10-12)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 33\n    }\n  }, statistics.notes_faibles), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 33\n    }\n  }, \"Faibles (<10)\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#fd7e14'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 33\n    }\n  }, statistics.nombre_etudiants), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiants\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '15px',\n      backgroundColor: 'white',\n      borderRadius: '6px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '24px',\n      fontWeight: 'bold',\n      color: '#20c997'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 33\n    }\n  }, statistics.nombre_devoirs), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '12px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 33\n    }\n  }, \"Devoirs\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-bar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/search.png\",\n    alt: \"Rechercher\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 21\n    }\n  })), !isEtudiant && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-section\",\n    style: {\n      display: 'grid',\n      gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '10px',\n      marginTop: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: filterMatiere,\n    onChange: e => setFilterMatiere(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 29\n    }\n  }, \"\\uD83D\\uDCD6 Toutes les mati\\xE8res\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere,\n    value: matiere,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 33\n    }\n  }, matiere))), isAdmin && /*#__PURE__*/React.createElement(\"select\", {\n    value: filterEtudiant,\n    onChange: e => setFilterEtudiant(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDC64 Tous les \\xE9tudiants\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant,\n    value: etudiant,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 37\n    }\n  }, etudiant))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 13\n    }\n  }, filteredNotes.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/empty.png\",\n    alt: \"Aucune donn\\xE9e\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 25\n    }\n  }, \"Aucune note trouv\\xE9e\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 25\n    }\n  }, isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 688,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), !isEtudiant && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Devoir\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCD6 Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Note\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 49\n    }\n  }, \"\\uD83C\\uDFEB Classe\"), canManage && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 51\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 29\n    }\n  }, currentNotes.map(note => /*#__PURE__*/React.createElement(\"tr\", {\n    key: note.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.idBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 45\n    }\n  }, \"#\", note.id)), !isEtudiant && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 709,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 53\n    }\n  }, note.etudiant_nom || 'Nom non disponible'), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 53\n    }\n  }, note.etudiant_email || 'Email non disponible'))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxWidth: '200px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 49\n    }\n  }, note.devoir_titre || 'Devoir non spécifié'), note.date_remise && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 53\n    }\n  }, \"Remise: \", note.date_remise))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#fff3e0',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 45\n    }\n  }, note.matiere_nom || 'Non spécifiée')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 41\n    }\n  }, getNoteBadge(note.note)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 45\n    }\n  }, note.date_formatted || note.date_enregistrement)), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 751,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '3px 6px',\n      backgroundColor: '#f3e5f5',\n      borderRadius: '4px',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 49\n    }\n  }, note.classe_nom || 'Non spécifiée')), canManage && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(note),\n    title: \"Modifier la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 765,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(note.id),\n    title: \"Supprimer la note\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 777,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 793,\n      columnNumber: 21\n    }\n  }, \"Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 21\n    }\n  }, \"Suivant\")), showGenerateModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 820,\n      columnNumber: 29\n    }\n  }, \"\\uD83E\\uDD16 G\\xE9n\\xE9ration Automatique des Notes\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => setShowGenerateModal(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 825,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      marginBottom: '20px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 829,\n      columnNumber: 29\n    }\n  }, \"S\\xE9lectionnez un devoir pour g\\xE9n\\xE9rer automatiquement les notes \\xE0 partir des r\\xE9ponses aux quiz.\"), devoirsDisponibles.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 37\n    }\n  }, \"Aucun devoir disponible pour la g\\xE9n\\xE9ration de notes.\")) : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxHeight: '400px',\n      overflowY: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 33\n    }\n  }, devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"div\", {\n    key: devoir.id,\n    style: {\n      border: '1px solid #dee2e6',\n      borderRadius: '8px',\n      padding: '15px',\n      marginBottom: '10px',\n      backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"h5\", {\n    style: {\n      margin: '0 0 5px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 53\n    }\n  }, devoir.titre), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      fontSize: '0.9em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 53\n    }\n  }, devoir.matiere_nom, \" - \", devoir.classe_nom), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 53\n    }\n  }, \"\\uD83D\\uDCCA \", devoir.nombre_quiz, \" quiz \\u2022 \\uD83D\\uDC65 \", devoir.nombre_etudiants_repondus, \" r\\xE9ponses \\u2022 \\uD83D\\uDCDD \", devoir.notes_existantes, \" notes\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '0.8em',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 856,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '2px 6px',\n      borderRadius: '4px',\n      backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n      color: devoir.peut_generer_notes ? '#155724' : '#856404'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 57\n    }\n  }, devoir.statut))), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-success\",\n    onClick: () => {\n      setShowGenerateModal(false);\n      handleGenerateNotes(devoir.id);\n    },\n    disabled: !devoir.peut_generer_notes,\n    title: devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 867,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDE80 G\\xE9n\\xE9rer\")))))))), showModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 893,\n      columnNumber: 29\n    }\n  }, editingNote ? 'Modifier la note' : 'Nouvelle note'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 894,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 907,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant...\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 33\n    }\n  }, \"Devoir *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.devoir_id,\n    onChange: e => setFormData({\n      ...formData,\n      devoir_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 921,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un devoir...\"), devoirsDisponibles.map(devoir => /*#__PURE__*/React.createElement(\"option\", {\n    key: devoir.id,\n    value: devoir.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 41\n    }\n  }, devoir.devoir_display)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 33\n    }\n  }, \"Mati\\xE8re *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => setFormData({\n      ...formData,\n      matiere_id: e.target.value\n    }),\n    required: true,\n    disabled: editingNote,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner une mati\\xE8re...\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 949,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 950,\n      columnNumber: 33\n    }\n  }, \"Note (sur 20)\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    min: \"0\",\n    max: \"20\",\n    step: \"0.01\",\n    value: formData.note,\n    onChange: e => setFormData({\n      ...formData,\n      note: e.target.value\n    }),\n    placeholder: \"Laisser vide pour calcul automatique\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 33\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 960,\n      columnNumber: 33\n    }\n  }, \"Si vide, la note sera calcul\\xE9e automatiquement \\xE0 partir des r\\xE9ponses aux quiz.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 965,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 966,\n      columnNumber: 33\n    }\n  }, editingNote ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingNote(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 969,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default NotesUnified;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "axios", "<PERSON><PERSON>", "AuthContext", "filterNotes", "canManageData", "isStudent", "logSecurityEvent", "NotesUnified", "user", "notes", "setNotes", "devoirsDisponibles", "setDevoirsDisponibles", "loading", "setLoading", "showModal", "setShowModal", "showGenerateModal", "setShowGenerateModal", "editingNote", "setEditingNote", "searchTerm", "setSearchTerm", "filterMatiere", "setFilterMatiere", "filterEtudiant", "setFilterEtudiant", "currentPage", "setCurrentPage", "itemsPerPage", "statistics", "setStatistics", "matieres", "set<PERSON>ati<PERSON>s", "etudiants", "setEtudiants", "selectedEtudiant", "setSelectedEtudiant", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatiere", "formData", "setFormData", "etudiant_id", "devoir_id", "matiere_id", "note", "isEtudiant", "isEnseignant", "role", "isAdmin", "canManage", "canView", "fetchNotes", "fetchDevoirsDisponibles", "console", "log", "authToken", "response", "get", "headers", "Authorization", "data", "success", "notesData", "stats", "calculateStatistics", "matieresUniques", "Set", "map", "n", "matiere_nom", "filter", "Boolean", "etudiantsUniques", "etudiant_nom", "error", "_error$response", "status", "fire", "total", "length", "moyenne_generale", "reduce", "sum", "parseFloat", "notes_excellentes", "notes_bonnes", "notes_moyennes", "notes_faibles", "devoirs", "total_notes", "Math", "round", "nombre_etudiants", "nombre_matieres", "nombre_devoirs", "handleGenerateNotes", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "post", "action", "details", "html", "notes_generees", "notes_mises_a_jour", "total_etudiants", "timer", "showConfirmButton", "_error$response2", "_error$response2$data", "handleSubmit", "e", "preventDefault", "url", "method", "id", "message", "resetForm", "_error$response3", "_error$response3$data", "handleEdit", "handleDelete", "delete", "_error$response4", "_error$response4$data", "handleEtudiantSelection", "etudiantId", "etudiant", "find", "parseInt", "prev", "handleMatiereSelection", "matiereId", "matiere", "m", "securityFilteredNotes", "filtered", "filteredNotes", "searchLower", "toLowerCase", "matchesSearch", "includes", "devoir_titre", "toString", "matchesMatiere", "matchesEtudiant", "indexOfLastItem", "indexOfFirstItem", "currentNotes", "slice", "totalPages", "ceil", "paginate", "pageNumber", "getNoteBadge", "noteValue", "style", "padding", "borderRadius", "fontSize", "fontWeight", "backgroundColor", "color", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getHeaderTitle", "getInfoMessage", "border", "styles", "accessDenied", "textAlign", "margin", "idBadge", "className", "infoMessage", "onClick", "marginRight", "src", "alt", "display", "gridTemplateColumns", "gap", "boxShadow", "Fragment", "type", "placeholder", "value", "onChange", "target", "marginTop", "key", "etudiant_email", "max<PERSON><PERSON><PERSON>", "date_remise", "date_formatted", "date_enregistrement", "classe_nom", "disabled", "marginBottom", "maxHeight", "overflowY", "devoir", "peut_generer_notes", "justifyContent", "alignItems", "titre", "nombre_quiz", "nombre_etudiants_repondus", "notes_existantes", "statut", "onSubmit", "required", "devoir_display", "min", "max", "step"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/NotesUnified.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport {\n    filterNotes,\n    canManageData,\n    isStudent,\n    logSecurityEvent\n} from '../utils/studentDataFilter';\nimport '../css/Factures.css';\n\nconst NotesUnified = () => {\n    const { user } = useContext(AuthContext);\n    const [notes, setNotes] = useState([]);\n    const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [showGenerateModal, setShowGenerateModal] = useState(false);\n    const [editingNote, setEditingNote] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterMatiere, setFilterMatiere] = useState('all');\n    const [filterEtudiant, setFilterEtudiant] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [statistics, setStatistics] = useState({});\n    const [matieres, setMatieres] = useState([]);\n    const [etudiants, setEtudiants] = useState([]);\n    const [selectedEtudiant, setSelectedEtudiant] = useState(null);\n    const [selectedMatiere, setSelectedMatiere] = useState(null);\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        devoir_id: '',\n        matiere_id: '',\n        note: ''\n    });\n\n    // Déterminer le rôle et les permissions avec notre système unifié\n    const isEtudiant = isStudent(user);\n    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';\n\n    // Permissions selon les spécifications\n    const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes\n    const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)\n\n    useEffect(() => {\n        if (canView) {\n            fetchNotes();\n            if (isEnseignant) {\n                fetchDevoirsDisponibles();\n            }\n        }\n    }, [canView, isEnseignant]);\n\n    const fetchNotes = async () => {\n        try {\n            console.log('🔄 Chargement des notes...');\n\n            // Déterminer le token selon le rôle\n            let authToken = 'default-token';\n            if (isEtudiant) authToken = 'etudiant-token';\n            else if (isEnseignant) authToken = 'enseignant-token';\n            else if (isAdmin) authToken = 'admin-token';\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                headers: { Authorization: `Bearer ${authToken}` }\n            });\n\n            console.log('✅ Réponse API notes:', response.data);\n            if (response.data.success) {\n                const notesData = response.data.data || [];\n                setNotes(notesData);\n                \n                // Calculer les statistiques\n                const stats = calculateStatistics(notesData);\n                setStatistics(stats);\n                \n                // Extraire les matières et étudiants uniques pour les filtres\n                if (!isEtudiant) {\n                    const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];\n                    setMatieres(matieresUniques);\n                    \n                    if (isAdmin) {\n                        const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];\n                        setEtudiants(etudiantsUniques);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des notes:', error);\n            if (error.response?.status === 403) {\n                Swal.fire('Accès refusé', 'Vous n\\'avez pas les permissions pour consulter les notes', 'error');\n            } else {\n                Swal.fire('Erreur', 'Impossible de charger les notes', 'error');\n            }\n            setNotes([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchDevoirsDisponibles = async () => {\n        try {\n            console.log('🔄 Chargement des devoirs disponibles...');\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {\n                headers: { Authorization: `Bearer enseignant-token` }\n            });\n\n            console.log('✅ Réponse API devoirs disponibles:', response.data);\n            if (response.data.success) {\n                setDevoirsDisponibles(response.data.data || []);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des devoirs:', error);\n            setDevoirsDisponibles([]);\n        }\n    };\n\n    const calculateStatistics = (notesData) => {\n        const total = notesData.length;\n        const moyenne_generale = total > 0 ? \n            notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;\n        \n        const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;\n        const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;\n        const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;\n        const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;\n        \n        const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];\n        const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];\n        const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];\n        \n        return {\n            total_notes: total,\n            moyenne_generale: Math.round(moyenne_generale * 100) / 100,\n            notes_excellentes,\n            notes_bonnes,\n            notes_moyennes,\n            notes_faibles,\n            nombre_etudiants: etudiants.length,\n            nombre_matieres: matieres.length,\n            nombre_devoirs: devoirs.length\n        };\n    };\n\n    const handleGenerateNotes = async (devoir_id) => {\n        const result = await Swal.fire({\n            title: 'Générer les notes automatiquement ?',\n            text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#28a745',\n            cancelButtonColor: '#6c757d',\n            confirmButtonText: 'Oui, générer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);\n                \n                const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    action: 'generate',\n                    devoir_id: devoir_id\n                }, {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n\n                console.log('✅ Notes générées:', response.data);\n\n                if (response.data.success) {\n                    const details = response.data.details;\n                    Swal.fire({\n                        title: 'Notes générées !',\n                        html: `\n                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>\n                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>\n                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>\n                        `,\n                        icon: 'success',\n                        timer: 3000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur génération:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Erreur lors de la génération des notes', 'error');\n            }\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');\n            return;\n        }\n\n        if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {\n            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n            return;\n        }\n\n        try {\n            const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';\n            const method = editingNote ? 'PUT' : 'POST';\n            const data = editingNote ? { ...formData, id: editingNote.id } : formData;\n\n            console.log('🔄 Envoi note:', { method, data });\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    Authorization: `Bearer enseignant-token`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            console.log('✅ Note envoyée:', response.data);\n\n            if (response.data.success) {\n                Swal.fire({\n                    title: 'Succès !',\n                    text: response.data.message,\n                    icon: 'success',\n                    timer: 2000,\n                    showConfirmButton: false\n                });\n                setShowModal(false);\n                setEditingNote(null);\n                resetForm();\n                fetchNotes();\n                if (isEnseignant) fetchDevoirsDisponibles();\n            } else {\n                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n            }\n        } catch (error) {\n            console.error('❌ Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (note) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');\n            return;\n        }\n\n        setEditingNote(note);\n        setFormData({\n            etudiant_id: note.etudiant_id || '',\n            devoir_id: note.devoir_id || '',\n            matiere_id: note.matiere_id || '',\n            note: note.note || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!canManage) {\n            Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Supprimer cette note ?',\n            text: 'Cette action est irréversible !',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                console.log('🗑️ Suppression note ID:', id);\n                \n                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {\n                    headers: { \n                        Authorization: `Bearer enseignant-token`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n\n                console.log('✅ Note supprimée:', response.data);\n\n                if (response.data.success) {\n                    Swal.fire({\n                        title: 'Supprimé !',\n                        text: response.data.message,\n                        icon: 'success',\n                        timer: 2000,\n                        showConfirmButton: false\n                    });\n                    fetchNotes();\n                    if (isEnseignant) fetchDevoirsDisponibles();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur suppression:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la note', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            devoir_id: '',\n            matiere_id: '',\n            note: ''\n        });\n        setSelectedEtudiant(null);\n        setSelectedMatiere(null);\n    };\n\n    // Fonction pour gérer la sélection d'un étudiant\n    const handleEtudiantSelection = (etudiantId) => {\n        const etudiant = etudiants.find(e => (e.id || e.etudiant_id) === parseInt(etudiantId));\n        setSelectedEtudiant(etudiant || null);\n        setFormData(prev => ({ ...prev, etudiant_id: etudiantId }));\n    };\n\n    // Fonction pour gérer la sélection d'une matière\n    const handleMatiereSelection = (matiereId) => {\n        const matiere = matieres.find(m => m.id === parseInt(matiereId));\n        setSelectedMatiere(matiere || null);\n        setFormData(prev => ({ ...prev, matiere_id: matiereId }));\n    };\n\n    // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes\n    const securityFilteredNotes = filterNotes(notes, user);\n\n    // Log de sécurité si des données ont été filtrées\n    if (isStudent(user) && securityFilteredNotes.length !== notes.length) {\n        logSecurityEvent('NOTE_ACCESS_FILTERED', user, {\n            total: notes.length,\n            filtered: securityFilteredNotes.length\n        });\n    }\n\n    // ÉTAPE 2 : Filtrage par recherche et critères\n    const filteredNotes = securityFilteredNotes.filter(note => {\n        const searchLower = searchTerm.toLowerCase();\n        const matchesSearch = (\n            (note.etudiant_nom || '').toLowerCase().includes(searchLower) ||\n            (note.devoir_titre || '').toLowerCase().includes(searchLower) ||\n            (note.matiere_nom || '').toLowerCase().includes(searchLower) ||\n            (note.note || '').toString().includes(searchLower)\n        );\n\n        const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;\n        const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;\n\n        return matchesSearch && matchesMatiere && matchesEtudiant;\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when filters change\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm, filterMatiere, filterEtudiant]);\n\n    const getNoteBadge = (note) => {\n        const noteValue = parseFloat(note);\n        let style = {\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.9em',\n            fontWeight: 'bold'\n        };\n\n        if (noteValue >= 16) {\n            style = {...style, backgroundColor: '#28a745', color: 'white'};\n            return <span style={style}>🏆 {note}/20</span>;\n        } else if (noteValue >= 12) {\n            style = {...style, backgroundColor: '#17a2b8', color: 'white'};\n            return <span style={style}>👍 {note}/20</span>;\n        } else if (noteValue >= 10) {\n            style = {...style, backgroundColor: '#ffc107', color: 'black'};\n            return <span style={style}>📊 {note}/20</span>;\n        } else {\n            style = {...style, backgroundColor: '#dc3545', color: 'white'};\n            return <span style={style}>📉 {note}/20</span>;\n        }\n    };\n\n    const getHeaderTitle = () => {\n        if (isEtudiant) return '📊 Mes Notes';\n        if (isEnseignant) return '👨‍🏫 Gestion des Notes';\n        if (isAdmin) return '🛡️ Administration - Notes';\n        return '📊 Notes';\n    };\n\n    const getInfoMessage = () => {\n        if (isEtudiant) {\n            return {\n                style: { backgroundColor: '#fff3cd', border: '1px solid #ffc107', color: '#856404' },\n                text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'\n            };\n        } else if (isEnseignant) {\n            return {\n                style: { backgroundColor: '#d4edda', border: '1px solid #c3e6cb', color: '#155724' },\n                text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'\n            };\n        } else if (isAdmin) {\n            return {\n                style: { backgroundColor: '#e2e3e5', border: '1px solid #d6d8db', color: '#383d41' },\n                text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\\'est possible.'\n            };\n        }\n        return null;\n    };\n\n    const styles = {\n        accessDenied: {\n            textAlign: 'center',\n            padding: '50px 20px',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '8px',\n            margin: '20px 0'\n        },\n        idBadge: {\n            backgroundColor: isAdmin ? '#6f42c1' : '#007bff',\n            color: 'white',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.8em',\n            fontWeight: 'bold'\n        }\n    };\n\n    // Vérification d'accès\n    if (!canView) {\n        return (\n            <div className=\"factures-container\">\n                <div style={styles.accessDenied}>\n                    <h2>🚫 Accès Refusé</h2>\n                    <p>Vous n'avez pas les permissions pour accéder aux notes.</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des notes...</p>\n            </div>\n        );\n    }\n\n    const infoMessage = getInfoMessage();\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>{getHeaderTitle()}</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredNotes.length} note(s) trouvée(s)\n                    </span>\n                    {canManage && (\n                        <div>\n                            <button \n                                className=\"btn btn-success\"\n                                onClick={() => setShowGenerateModal(true)}\n                                style={{ marginRight: '10px' }}\n                            >\n                                <img src=\"/auto.png\" alt=\"Générer\" /> Génération Auto\n                            </button>\n                            <button \n                                className=\"btn btn-primary\"\n                                onClick={() => setShowModal(true)}\n                            >\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvelle Note\n                            </button>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information selon le rôle */}\n            {infoMessage && (\n                <div style={{\n                    ...infoMessage.style,\n                    borderRadius: '8px',\n                    padding: '15px',\n                    margin: '20px 0'\n                }}>\n                    <p style={{ margin: '0' }}>{infoMessage.text}</p>\n                </div>\n            )}\n\n            {/* Statistiques selon le rôle */}\n            {statistics.total_notes > 0 && (\n                <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',\n                    gap: '15px',\n                    backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',\n                    border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,\n                    borderRadius: '8px',\n                    padding: '20px',\n                    margin: '20px 0'\n                }}>\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: isAdmin ? '#6f42c1' : '#007bff' }}>\n                            {statistics.total_notes}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>\n                            {isEtudiant ? 'Mes Notes' : 'Total Notes'}\n                        </div>\n                    </div>\n\n                    <div style={{\n                        textAlign: 'center',\n                        padding: '15px',\n                        backgroundColor: 'white',\n                        borderRadius: '6px',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    }}>\n                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#17a2b8' }}>\n                            {statistics.moyenne_generale}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyenne</div>\n                    </div>\n\n                    {!isEtudiant && (\n                        <>\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>\n                                    {statistics.notes_excellentes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Excellentes (≥16)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>\n                                    {statistics.notes_moyennes}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyennes (10-12)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>\n                                    {statistics.notes_faibles}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Faibles (&lt;10)</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fd7e14' }}>\n                                    {statistics.nombre_etudiants}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Étudiants</div>\n                            </div>\n\n                            <div style={{\n                                textAlign: 'center',\n                                padding: '15px',\n                                backgroundColor: 'white',\n                                borderRadius: '6px',\n                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                            }}>\n                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20c997' }}>\n                                    {statistics.nombre_devoirs}\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Devoirs</div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            )}\n\n            {/* Filtres et recherche */}\n            <div className=\"search-section\">\n                <div className=\"search-bar\">\n                    <img src=\"/search.png\" alt=\"Rechercher\" />\n                    <input\n                        type=\"text\"\n                        placeholder={isEtudiant ? \"Rechercher dans vos notes...\" : \"Rechercher par étudiant, devoir, matière, note...\"}\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                </div>\n\n                {!isEtudiant && (\n                    <div className=\"filter-section\" style={{\n                        display: 'grid',\n                        gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '10px',\n                        marginTop: '15px'\n                    }}>\n                        <select\n                            value={filterMatiere}\n                            onChange={(e) => setFilterMatiere(e.target.value)}\n                            className=\"filter-select\"\n                        >\n                            <option value=\"all\">📖 Toutes les matières</option>\n                            {matieres.map(matiere => (\n                                <option key={matiere} value={matiere}>{matiere}</option>\n                            ))}\n                        </select>\n\n                        {isAdmin && (\n                            <select\n                                value={filterEtudiant}\n                                onChange={(e) => setFilterEtudiant(e.target.value)}\n                                className=\"filter-select\"\n                            >\n                                <option value=\"all\">👤 Tous les étudiants</option>\n                                {etudiants.map(etudiant => (\n                                    <option key={etudiant} value={etudiant}>{etudiant}</option>\n                                ))}\n                            </select>\n                        )}\n                    </div>\n                )}\n            </div>\n\n            {/* Tableau des notes */}\n            <div className=\"table-container\">\n                {filteredNotes.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/empty.png\" alt=\"Aucune donnée\" />\n                        <p>Aucune note trouvée</p>\n                        <p>{isEtudiant ? \"Aucune note n'a encore été attribuée\" : \"Modifiez vos critères de recherche ou filtres\"}</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>🆔 ID</th>\n                                    {!isEtudiant && <th>👤 Étudiant</th>}\n                                    <th>📚 Devoir</th>\n                                    <th>📖 Matière</th>\n                                    <th>📊 Note</th>\n                                    <th>📅 Date</th>\n                                    {isAdmin && <th>🏫 Classe</th>}\n                                    {canManage && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentNotes.map((note) => (\n                                    <tr key={note.id}>\n                                        <td>\n                                            <span style={styles.idBadge}>\n                                                #{note.id}\n                                            </span>\n                                        </td>\n                                        {!isEtudiant && (\n                                            <td>\n                                                <div className=\"user-info\">\n                                                    <strong style={{ fontSize: '0.9em' }}>\n                                                        {note.etudiant_nom || 'Nom non disponible'}\n                                                    </strong>\n                                                    <br />\n                                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                                        {note.etudiant_email || 'Email non disponible'}\n                                                    </small>\n                                                </div>\n                                            </td>\n                                        )}\n                                        <td>\n                                            <div style={{ maxWidth: '200px' }}>\n                                                <strong style={{ fontSize: '0.9em' }}>\n                                                    {note.devoir_titre || 'Devoir non spécifié'}\n                                                </strong>\n                                                {note.date_remise && (\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d' }}>\n                                                        Remise: {note.date_remise}\n                                                    </div>\n                                                )}\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#fff3e0',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {note.matiere_nom || 'Non spécifiée'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            {getNoteBadge(note.note)}\n                                        </td>\n                                        <td>\n                                            <span style={{ fontSize: '0.9em' }}>\n                                                {note.date_formatted || note.date_enregistrement}\n                                            </span>\n                                        </td>\n                                        {isAdmin && (\n                                            <td>\n                                                <span style={{\n                                                    padding: '3px 6px',\n                                                    backgroundColor: '#f3e5f5',\n                                                    borderRadius: '4px',\n                                                    fontSize: '0.8em'\n                                                }}>\n                                                    {note.classe_nom || 'Non spécifiée'}\n                                                </span>\n                                            </td>\n                                        )}\n                                        {canManage && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(note)}\n                                                        title=\"Modifier la note\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(note.id)}\n                                                        title=\"Supprimer la note\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div className=\"pagination\">\n                    <button\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Précédent\n                    </button>\n\n                    <div className=\"page-info\">\n                        Page {currentPage} sur {totalPages}\n                    </div>\n\n                    <button\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Suivant\n                    </button>\n                </div>\n            )}\n\n            {/* Modal de génération automatique (enseignants uniquement) */}\n            {showGenerateModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>🤖 Génération Automatique des Notes</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => setShowGenerateModal(false)}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <div style={{ padding: '20px' }}>\n                            <p style={{ marginBottom: '20px', color: '#6c757d' }}>\n                                Sélectionnez un devoir pour générer automatiquement les notes à partir des réponses aux quiz.\n                            </p>\n\n                            {devoirsDisponibles.length === 0 ? (\n                                <div style={{ textAlign: 'center', padding: '20px' }}>\n                                    <p>Aucun devoir disponible pour la génération de notes.</p>\n                                </div>\n                            ) : (\n                                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <div key={devoir.id} style={{\n                                            border: '1px solid #dee2e6',\n                                            borderRadius: '8px',\n                                            padding: '15px',\n                                            marginBottom: '10px',\n                                            backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'\n                                        }}>\n                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                                <div>\n                                                    <h5 style={{ margin: '0 0 5px 0' }}>{devoir.titre}</h5>\n                                                    <p style={{ margin: '0', fontSize: '0.9em', color: '#6c757d' }}>\n                                                        {devoir.matiere_nom} - {devoir.classe_nom}\n                                                    </p>\n                                                    <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px' }}>\n                                                        📊 {devoir.nombre_quiz} quiz • 👥 {devoir.nombre_etudiants_repondus} réponses • 📝 {devoir.notes_existantes} notes\n                                                    </div>\n                                                    <div style={{ fontSize: '0.8em', marginTop: '5px' }}>\n                                                        <span style={{\n                                                            padding: '2px 6px',\n                                                            borderRadius: '4px',\n                                                            backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',\n                                                            color: devoir.peut_generer_notes ? '#155724' : '#856404'\n                                                        }}>\n                                                            {devoir.statut}\n                                                        </span>\n                                                    </div>\n                                                </div>\n                                                <button\n                                                    className=\"btn btn-success\"\n                                                    onClick={() => {\n                                                        setShowGenerateModal(false);\n                                                        handleGenerateNotes(devoir.id);\n                                                    }}\n                                                    disabled={!devoir.peut_generer_notes}\n                                                    title={devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible'}\n                                                >\n                                                    🚀 Générer\n                                                </button>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier une note (enseignants uniquement) */}\n            {showModal && canManage && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingNote ? 'Modifier la note' : 'Nouvelle note'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingNote(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant *</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant...</option>\n                                    {/* Les options seront chargées dynamiquement */}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Devoir *</label>\n                                <select\n                                    value={formData.devoir_id}\n                                    onChange={(e) => setFormData({...formData, devoir_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                >\n                                    <option value=\"\">Sélectionner un devoir...</option>\n                                    {devoirsDisponibles.map(devoir => (\n                                        <option key={devoir.id} value={devoir.id}>\n                                            {devoir.devoir_display}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Matière *</label>\n                                <select\n                                    value={formData.matiere_id}\n                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}\n                                    required\n                                    disabled={editingNote}\n                                >\n                                    <option value=\"\">Sélectionner une matière...</option>\n                                    {/* Les options seront chargées dynamiquement */}\n                                </select>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Note (sur 20)</label>\n                                <input\n                                    type=\"number\"\n                                    min=\"0\"\n                                    max=\"20\"\n                                    step=\"0.01\"\n                                    value={formData.note}\n                                    onChange={(e) => setFormData({...formData, note: e.target.value})}\n                                    placeholder=\"Laisser vide pour calcul automatique\"\n                                />\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    Si vide, la note sera calculée automatiquement à partir des réponses aux quiz.\n                                </small>\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingNote ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingNote(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default NotesUnified;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SACIC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,gBAAgB,QACb,4BAA4B;AACnC,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGT,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC;IACrC6C,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGzC,SAAS,CAACG,IAAI,CAAC;EAClC,MAAMuC,YAAY,GAAG,CAAAvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK,YAAY,IAAI,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK,YAAY;EAC/E,MAAMC,OAAO,GAAG,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK,OAAO,IAAI,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK,OAAO;;EAEhE;EACA,MAAME,SAAS,GAAG9C,aAAa,CAACI,IAAI,CAAC,CAAC,CAAC;EACvC,MAAM2C,OAAO,GAAGL,UAAU,IAAIC,YAAY,IAAIE,OAAO,CAAC,CAAC;;EAEvDnD,SAAS,CAAC,MAAM;IACZ,IAAIqD,OAAO,EAAE;MACTC,UAAU,CAAC,CAAC;MACZ,IAAIL,YAAY,EAAE;QACdM,uBAAuB,CAAC,CAAC;MAC7B;IACJ;EACJ,CAAC,EAAE,CAACF,OAAO,EAAEJ,YAAY,CAAC,CAAC;EAE3B,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;MAEzC;MACA,IAAIC,SAAS,GAAG,eAAe;MAC/B,IAAIV,UAAU,EAAEU,SAAS,GAAG,gBAAgB,CAAC,KACxC,IAAIT,YAAY,EAAES,SAAS,GAAG,kBAAkB,CAAC,KACjD,IAAIP,OAAO,EAAEO,SAAS,GAAG,aAAa;MAE3C,MAAMC,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,GAAG,CAAC,0DAA0D,EAAE;QACzFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,SAAS;QAAG;MACpD,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAClD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMC,SAAS,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CnD,QAAQ,CAACqD,SAAS,CAAC;;QAEnB;QACA,MAAMC,KAAK,GAAGC,mBAAmB,CAACF,SAAS,CAAC;QAC5ChC,aAAa,CAACiC,KAAK,CAAC;;QAEpB;QACA,IAAI,CAAClB,UAAU,EAAE;UACb,MAAMoB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;UACvFvC,WAAW,CAACiC,eAAe,CAAC;UAE5B,IAAIjB,OAAO,EAAE;YACT,MAAMwB,gBAAgB,GAAG,CAAC,GAAG,IAAIN,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACK,YAAY,CAAC,CAACH,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;YACzFrC,YAAY,CAACsC,gBAAgB,CAAC;UAClC;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA;MACZtB,OAAO,CAACqB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAAClB,QAAQ,cAAAmB,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAChC5E,IAAI,CAAC6E,IAAI,CAAC,cAAc,EAAE,2DAA2D,EAAE,OAAO,CAAC;MACnG,CAAC,MAAM;QACH7E,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,iCAAiC,EAAE,OAAO,CAAC;MACnE;MACApE,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNI,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMuC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAEvD,MAAME,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,GAAG,CAAC,0EAA0E,EAAE;QACzGC,OAAO,EAAE;UAAEC,aAAa,EAAE;QAA0B;MACxD,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAChE,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBlD,qBAAqB,CAAC6C,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACnD;IACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;MACZrB,OAAO,CAACqB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE/D,qBAAqB,CAAC,EAAE,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMqD,mBAAmB,GAAIF,SAAS,IAAK;IACvC,MAAMgB,KAAK,GAAGhB,SAAS,CAACiB,MAAM;IAC9B,MAAMC,gBAAgB,GAAGF,KAAK,GAAG,CAAC,GAC9BhB,SAAS,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEd,CAAC,KAAKc,GAAG,GAAGC,UAAU,CAACf,CAAC,CAACxB,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGkC,KAAK,GAAG,CAAC;IAE9E,MAAMM,iBAAiB,GAAGtB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,IAAI,EAAE,CAAC,CAACmC,MAAM;IAChF,MAAMM,YAAY,GAAGvB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,IAAI,EAAE,IAAIuC,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,GAAG,EAAE,CAAC,CAACmC,MAAM;IACtG,MAAMO,cAAc,GAAGxB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,IAAI,EAAE,IAAIuC,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,GAAG,EAAE,CAAC,CAACmC,MAAM;IACxG,MAAMQ,aAAa,GAAGzB,SAAS,CAACQ,MAAM,CAACF,CAAC,IAAIe,UAAU,CAACf,CAAC,CAACxB,IAAI,CAAC,GAAG,EAAE,CAAC,CAACmC,MAAM;IAE3E,MAAM9C,SAAS,GAAG,CAAC,GAAG,IAAIiC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3B,WAAW,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,MAAMxC,QAAQ,GAAG,CAAC,GAAG,IAAImC,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzB,UAAU,CAAC,CAAC2B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAC/E,MAAMiB,OAAO,GAAG,CAAC,GAAG,IAAItB,GAAG,CAACJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1B,SAAS,CAAC,CAAC4B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAE7E,OAAO;MACHkB,WAAW,EAAEX,KAAK;MAClBE,gBAAgB,EAAEU,IAAI,CAACC,KAAK,CAACX,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;MAC1DI,iBAAiB;MACjBC,YAAY;MACZC,cAAc;MACdC,aAAa;MACbK,gBAAgB,EAAE3D,SAAS,CAAC8C,MAAM;MAClCc,eAAe,EAAE9D,QAAQ,CAACgD,MAAM;MAChCe,cAAc,EAAEN,OAAO,CAACT;IAC5B,CAAC;EACL,CAAC;EAED,MAAMgB,mBAAmB,GAAG,MAAOrD,SAAS,IAAK;IAC7C,MAAMsD,MAAM,GAAG,MAAMhG,IAAI,CAAC6E,IAAI,CAAC;MAC3BoB,KAAK,EAAE,qCAAqC;MAC5CC,IAAI,EAAE,yEAAyE;MAC/EC,IAAI,EAAE,UAAU;MAChBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,cAAc;MACjCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACApD,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEZ,SAAS,CAAC;QAE7E,MAAMc,QAAQ,GAAG,MAAMzD,KAAK,CAAC2G,IAAI,CAAC,0DAA0D,EAAE;UAC1FC,MAAM,EAAE,UAAU;UAClBjE,SAAS,EAAEA;QACf,CAAC,EAAE;UACCgB,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB,MAAM+C,OAAO,GAAGpD,QAAQ,CAACI,IAAI,CAACgD,OAAO;UACrC5G,IAAI,CAAC6E,IAAI,CAAC;YACNoB,KAAK,EAAE,kBAAkB;YACzBY,IAAI,EAAE;AAC9B,oEAAoED,OAAO,CAACE,cAAc;AAC1F,uEAAuEF,OAAO,CAACG,kBAAkB;AACjG,oEAAoEH,OAAO,CAACI,eAAe;AAC3F,yBAAyB;YACDb,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF/D,UAAU,CAAC,CAAC;UACZC,uBAAuB,CAAC,CAAC;QAC7B,CAAC,MAAM;UACHpD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,8BAA8B,EAAE,OAAO,CAAC;QACvF;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAyC,gBAAA,EAAAC,qBAAA;QACZ/D,OAAO,CAACqB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C1E,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,EAAAsC,gBAAA,GAAAzC,KAAK,CAAClB,QAAQ,cAAA2D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvD,IAAI,cAAAwD,qBAAA,uBAApBA,qBAAA,CAAsB1C,KAAK,KAAI,wCAAwC,EAAE,OAAO,CAAC;MACzG;IACJ;EACJ,CAAC;EAED,MAAM2C,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtE,SAAS,EAAE;MACZjD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,wDAAwD,EAAE,OAAO,CAAC;MACtF;IACJ;IAEA,IAAI,CAACtC,QAAQ,CAACE,WAAW,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAI,CAACH,QAAQ,CAACI,UAAU,EAAE;MACtE3C,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,+CAA+C,EAAE,OAAO,CAAC;MAC7E;IACJ;IAEA,IAAI;MACA,MAAM2C,GAAG,GAAG,0DAA0D;MACtE,MAAMC,MAAM,GAAGvG,WAAW,GAAG,KAAK,GAAG,MAAM;MAC3C,MAAM0C,IAAI,GAAG1C,WAAW,GAAG;QAAE,GAAGqB,QAAQ;QAAEmF,EAAE,EAAExG,WAAW,CAACwG;MAAG,CAAC,GAAGnF,QAAQ;MAEzEc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAEmE,MAAM;QAAE7D;MAAK,CAAC,CAAC;MAE/C,MAAMJ,QAAQ,GAAG,MAAMzD,KAAK,CAAC;QACzB0H,MAAM;QACND,GAAG;QACH5D,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,yBAAyB;UACxC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,QAAQ,CAACI,IAAI,CAAC;MAE7C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB7D,IAAI,CAAC6E,IAAI,CAAC;UACNoB,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAE1C,QAAQ,CAACI,IAAI,CAAC+D,OAAO;UAC3BxB,IAAI,EAAE,SAAS;UACfc,KAAK,EAAE,IAAI;UACXC,iBAAiB,EAAE;QACvB,CAAC,CAAC;QACFnG,YAAY,CAAC,KAAK,CAAC;QACnBI,cAAc,CAAC,IAAI,CAAC;QACpByG,SAAS,CAAC,CAAC;QACXzE,UAAU,CAAC,CAAC;QACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;MAC/C,CAAC,MAAM;QACHpD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,yBAAyB,EAAE,OAAO,CAAC;MAClF;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACZzE,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1E,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,EAAAgD,gBAAA,GAAAnD,KAAK,CAAClB,QAAQ,cAAAqE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjE,IAAI,cAAAkE,qBAAA,uBAApBA,qBAAA,CAAsBpD,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMqD,UAAU,GAAInF,IAAI,IAAK;IACzB,IAAI,CAACK,SAAS,EAAE;MACZjD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,kDAAkD,EAAE,OAAO,CAAC;MAChF;IACJ;IAEA1D,cAAc,CAACyB,IAAI,CAAC;IACpBJ,WAAW,CAAC;MACRC,WAAW,EAAEG,IAAI,CAACH,WAAW,IAAI,EAAE;MACnCC,SAAS,EAAEE,IAAI,CAACF,SAAS,IAAI,EAAE;MAC/BC,UAAU,EAAEC,IAAI,CAACD,UAAU,IAAI,EAAE;MACjCC,IAAI,EAAEA,IAAI,CAACA,IAAI,IAAI;IACvB,CAAC,CAAC;IACF7B,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiH,YAAY,GAAG,MAAON,EAAE,IAAK;IAC/B,IAAI,CAACzE,SAAS,EAAE;MACZjD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA,MAAMmB,MAAM,GAAG,MAAMhG,IAAI,CAAC6E,IAAI,CAAC;MAC3BoB,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,gBAAgB;MACnCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACApD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoE,EAAE,CAAC;QAE3C,MAAMlE,QAAQ,GAAG,MAAMzD,KAAK,CAACkI,MAAM,CAAC,0DAA0D,EAAE;UAC5FvE,OAAO,EAAE;YACLC,aAAa,EAAE,yBAAyB;YACxC,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAE;YAAE8D;UAAG;QACf,CAAC,CAAC;QAEFrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE/C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB7D,IAAI,CAAC6E,IAAI,CAAC;YACNoB,KAAK,EAAE,YAAY;YACnBC,IAAI,EAAE1C,QAAQ,CAACI,IAAI,CAAC+D,OAAO;YAC3BxB,IAAI,EAAE,SAAS;YACfc,KAAK,EAAE,IAAI;YACXC,iBAAiB,EAAE;UACvB,CAAC,CAAC;UACF/D,UAAU,CAAC,CAAC;UACZ,IAAIL,YAAY,EAAEM,uBAAuB,CAAC,CAAC;QAC/C,CAAC,MAAM;UACHpD,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAErB,QAAQ,CAACI,IAAI,CAACc,KAAK,IAAI,iCAAiC,EAAE,OAAO,CAAC;QAC1F;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAwD,gBAAA,EAAAC,qBAAA;QACZ9E,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C1E,IAAI,CAAC6E,IAAI,CAAC,QAAQ,EAAE,EAAAqD,gBAAA,GAAAxD,KAAK,CAAClB,QAAQ,cAAA0E,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtE,IAAI,cAAAuE,qBAAA,uBAApBA,qBAAA,CAAsBzD,KAAK,KAAI,iCAAiC,EAAE,OAAO,CAAC;MAClG;IACJ;EACJ,CAAC;EAED,MAAMkD,SAAS,GAAGA,CAAA,KAAM;IACpBpF,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE;IACV,CAAC,CAAC;IACFR,mBAAmB,CAAC,IAAI,CAAC;IACzBE,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM8F,uBAAuB,GAAIC,UAAU,IAAK;IAC5C,MAAMC,QAAQ,GAAGrG,SAAS,CAACsG,IAAI,CAACjB,CAAC,IAAI,CAACA,CAAC,CAACI,EAAE,IAAIJ,CAAC,CAAC7E,WAAW,MAAM+F,QAAQ,CAACH,UAAU,CAAC,CAAC;IACtFjG,mBAAmB,CAACkG,QAAQ,IAAI,IAAI,CAAC;IACrC9F,WAAW,CAACiG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhG,WAAW,EAAE4F;IAAW,CAAC,CAAC,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMK,sBAAsB,GAAIC,SAAS,IAAK;IAC1C,MAAMC,OAAO,GAAG7G,QAAQ,CAACwG,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKc,QAAQ,CAACG,SAAS,CAAC,CAAC;IAChErG,kBAAkB,CAACsG,OAAO,IAAI,IAAI,CAAC;IACnCpG,WAAW,CAACiG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9F,UAAU,EAAEgG;IAAU,CAAC,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMG,qBAAqB,GAAG5I,WAAW,CAACM,KAAK,EAAED,IAAI,CAAC;;EAEtD;EACA,IAAIH,SAAS,CAACG,IAAI,CAAC,IAAIuI,qBAAqB,CAAC/D,MAAM,KAAKvE,KAAK,CAACuE,MAAM,EAAE;IAClE1E,gBAAgB,CAAC,sBAAsB,EAAEE,IAAI,EAAE;MAC3CuE,KAAK,EAAEtE,KAAK,CAACuE,MAAM;MACnBgE,QAAQ,EAAED,qBAAqB,CAAC/D;IACpC,CAAC,CAAC;EACN;;EAEA;EACA,MAAMiE,aAAa,GAAGF,qBAAqB,CAACxE,MAAM,CAAC1B,IAAI,IAAI;IACvD,MAAMqG,WAAW,GAAG7H,UAAU,CAAC8H,WAAW,CAAC,CAAC;IAC5C,MAAMC,aAAa,GACf,CAACvG,IAAI,CAAC6B,YAAY,IAAI,EAAE,EAAEyE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAACrG,IAAI,CAACyG,YAAY,IAAI,EAAE,EAAEH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC7D,CAACrG,IAAI,CAACyB,WAAW,IAAI,EAAE,EAAE6E,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC5D,CAACrG,IAAI,CAACA,IAAI,IAAI,EAAE,EAAE0G,QAAQ,CAAC,CAAC,CAACF,QAAQ,CAACH,WAAW,CACpD;IAED,MAAMM,cAAc,GAAGjI,aAAa,KAAK,KAAK,IAAIsB,IAAI,CAACyB,WAAW,KAAK/C,aAAa;IACpF,MAAMkI,eAAe,GAAGhI,cAAc,KAAK,KAAK,IAAIoB,IAAI,CAAC6B,YAAY,KAAKjD,cAAc;IAExF,OAAO2H,aAAa,IAAII,cAAc,IAAIC,eAAe;EAC7D,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG/H,WAAW,GAAGE,YAAY;EAClD,MAAM8H,gBAAgB,GAAGD,eAAe,GAAG7H,YAAY;EACvD,MAAM+H,YAAY,GAAGX,aAAa,CAACY,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGnE,IAAI,CAACoE,IAAI,CAACd,aAAa,CAACjE,MAAM,GAAGnD,YAAY,CAAC;EAEjE,MAAMmI,QAAQ,GAAIC,UAAU,IAAKrI,cAAc,CAACqI,UAAU,CAAC;;EAE3D;EACArK,KAAK,CAACE,SAAS,CAAC,MAAM;IAClB8B,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACP,UAAU,EAAEE,aAAa,EAAEE,cAAc,CAAC,CAAC;EAE/C,MAAMyI,YAAY,GAAIrH,IAAI,IAAK;IAC3B,MAAMsH,SAAS,GAAG/E,UAAU,CAACvC,IAAI,CAAC;IAClC,IAAIuH,KAAK,GAAG;MACRC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB,CAAC;IAED,IAAIL,SAAS,IAAI,EAAE,EAAE;MACjBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO9K,KAAA,CAAA+K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIsH,SAAS,IAAI,EAAE,EAAE;MACxBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO9K,KAAA,CAAA+K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM,IAAIsH,SAAS,IAAI,EAAE,EAAE;MACxBC,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO9K,KAAA,CAAA+K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD,CAAC,MAAM;MACHuH,KAAK,GAAG;QAAC,GAAGA,KAAK;QAAEK,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC;MAC9D,oBAAO9K,KAAA,CAAA+K,aAAA;QAAMP,KAAK,EAAEA,KAAM;QAAAQ,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,eAAG,EAACpI,IAAI,EAAC,KAAS,CAAC;IAClD;EACJ,CAAC;EAED,MAAMqI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIpI,UAAU,EAAE,OAAO,cAAc;IACrC,IAAIC,YAAY,EAAE,OAAO,yBAAyB;IAClD,IAAIE,OAAO,EAAE,OAAO,4BAA4B;IAChD,OAAO,UAAU;EACrB,CAAC;EAED,MAAMkI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIrI,UAAU,EAAE;MACZ,OAAO;QACHsH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFvE,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAIpD,YAAY,EAAE;MACrB,OAAO;QACHqH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFvE,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAIlD,OAAO,EAAE;MAChB,OAAO;QACHmH,KAAK,EAAE;UAAEK,eAAe,EAAE,SAAS;UAAEW,MAAM,EAAE,mBAAmB;UAAEV,KAAK,EAAE;QAAU,CAAC;QACpFvE,IAAI,EAAE;MACV,CAAC;IACL;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMkF,MAAM,GAAG;IACXC,YAAY,EAAE;MACVC,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,WAAW;MACpBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBkB,MAAM,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACLhB,eAAe,EAAExH,OAAO,GAAG,SAAS,GAAG,SAAS;MAChDyH,KAAK,EAAE,OAAO;MACdL,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB;EACJ,CAAC;;EAED;EACA,IAAI,CAACrH,OAAO,EAAE;IACV,oBACIvD,KAAA,CAAA+K,aAAA;MAAKe,SAAS,EAAC,oBAAoB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/BrL,KAAA,CAAA+K,aAAA;MAAKP,KAAK,EAAEiB,MAAM,CAACC,YAAa;MAAAV,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC5BrL,KAAA,CAAA+K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAAmB,CAAC,eACxBrL,KAAA,CAAA+K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4DAA0D,CAC5D,CACJ,CAAC;EAEd;EAEA,IAAIpK,OAAO,EAAE;IACT,oBACIjB,KAAA,CAAA+K,aAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BrL,KAAA,CAAA+K,aAAA;MAAKe,SAAS,EAAC,SAAS;MAAAd,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BrL,KAAA,CAAA+K,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,yBAA0B,CAC5B,CAAC;EAEd;EAEA,MAAMU,WAAW,GAAGR,cAAc,CAAC,CAAC;EAEpC,oBACIvL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,oBAAoB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKC,cAAc,CAAC,CAAM,CAAC,eAC3BtL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBrL,KAAA,CAAA+K,aAAA;IAAMe,SAAS,EAAC,aAAa;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBhC,aAAa,CAACjE,MAAM,EAAC,wBACpB,CAAC,EACN9B,SAAS,iBACNtD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAM1K,oBAAoB,CAAC,IAAI,CAAE;IAC1CkJ,KAAK,EAAE;MAAEyB,WAAW,EAAE;IAAO,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/BrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,eAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,0BACjC,CAAC,eACTrL,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAM5K,YAAY,CAAC,IAAI,CAAE;IAAA4J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,kBACjC,CACP,CAER,CACJ,CAAC,EAGLU,WAAW,iBACR/L,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACR,GAAGuB,WAAW,CAACvB,KAAK;MACpBE,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAI,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEU,WAAW,CAACxF,IAAQ,CAC/C,CACR,EAGArE,UAAU,CAAC4D,WAAW,GAAG,CAAC,iBACvB9F,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACR4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAEhJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GiJ,GAAG,EAAE,MAAM;MACXzB,eAAe,EAAExH,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS;MACzEsI,MAAM,EAAE,aAAanI,OAAO,GAAG,SAAS,GAAGH,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;MAC/EwH,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE;IACZ,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAEzH,OAAO,GAAG,SAAS,GAAG;IAAU,CAAE;IAAA2H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxFnJ,UAAU,CAAC4D,WACX,CAAC,eACN9F,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9CnI,UAAU,GAAG,WAAW,GAAG,aAC3B,CACJ,CAAC,eAENlD,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAACmD,gBACX,CAAC,eACNrF,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CAAC,EAEL,CAACnI,UAAU,iBACRlD,KAAA,CAAA+K,aAAA,CAAA/K,KAAA,CAAAwM,QAAA,qBACIxM,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAACuD,iBACX,CAAC,eACNzF,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAAsB,CACzE,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAACyD,cACX,CAAC,eACN3F,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAqB,CACxE,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAAC0D,aACX,CAAC,eACN5F,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAqB,CACxE,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAAC+D,gBACX,CAAC,eACNjG,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAc,CACjE,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MACRmB,SAAS,EAAE,QAAQ;MACnBlB,OAAO,EAAE,MAAM;MACfI,eAAe,EAAE,OAAO;MACxBH,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE;IACf,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClEnJ,UAAU,CAACiE,cACX,CAAC,eACNnG,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC/D,CACP,CAEL,CACR,eAGDrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,YAAY;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1CrL,KAAA,CAAA+K,aAAA;IACI0B,IAAI,EAAC,MAAM;IACXC,WAAW,EAAExJ,UAAU,GAAG,8BAA8B,GAAG,mDAAoD;IAC/GyJ,KAAK,EAAElL,UAAW;IAClBmL,QAAQ,EAAGjF,CAAC,IAAKjG,aAAa,CAACiG,CAAC,CAACkF,MAAM,CAACF,KAAK,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClD,CACA,CAAC,EAEL,CAACnI,UAAU,iBACRlD,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAACtB,KAAK,EAAE;MACnC4B,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAEhJ,OAAO,GAAG,sCAAsC,GAAG,sCAAsC;MAC9GiJ,GAAG,EAAE,MAAM;MACXQ,SAAS,EAAE;IACf,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IACI4B,KAAK,EAAEhL,aAAc;IACrBiL,QAAQ,EAAGjF,CAAC,IAAK/F,gBAAgB,CAAC+F,CAAC,CAACkF,MAAM,CAACF,KAAK,CAAE;IAClDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBrL,KAAA,CAAA+K,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qCAA8B,CAAC,EAClDjJ,QAAQ,CAACoC,GAAG,CAACyE,OAAO,iBACjBjJ,KAAA,CAAA+K,aAAA;IAAQgC,GAAG,EAAE9D,OAAQ;IAAC0D,KAAK,EAAE1D,OAAQ;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpC,OAAgB,CAC1D,CACG,CAAC,EAER5F,OAAO,iBACJrD,KAAA,CAAA+K,aAAA;IACI4B,KAAK,EAAE9K,cAAe;IACtB+K,QAAQ,EAAGjF,CAAC,IAAK7F,iBAAiB,CAAC6F,CAAC,CAACkF,MAAM,CAACF,KAAK,CAAE;IACnDb,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBrL,KAAA,CAAA+K,aAAA;IAAQ4B,KAAK,EAAC,KAAK;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oCAA6B,CAAC,EACjD/I,SAAS,CAACkC,GAAG,CAACmE,QAAQ,iBACnB3I,KAAA,CAAA+K,aAAA;IAAQgC,GAAG,EAAEpE,QAAS;IAACgE,KAAK,EAAEhE,QAAS;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE1C,QAAiB,CAC7D,CACG,CAEX,CAER,CAAC,eAGN3I,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BhC,aAAa,CAACjE,MAAM,KAAK,CAAC,gBACvBpF,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,SAAS;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,kBAAe;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5CrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,wBAAsB,CAAC,eAC1BrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAInI,UAAU,GAAG,sCAAsC,GAAG,+CAAmD,CAC5G,CAAC,gBAENlD,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,kBAAkB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BrL,KAAA,CAAA+K,aAAA;IAAOe,SAAS,EAAC,OAAO;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,EACb,CAACnI,UAAU,iBAAIlD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpCrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eACnBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,EACfhI,OAAO,iBAAIrD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EAC7B/H,SAAS,iBAAItD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAChC,CACD,CAAC,eACRrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKrB,YAAY,CAACxF,GAAG,CAAEvB,IAAI,iBACnBjD,KAAA,CAAA+K,aAAA;IAAIgC,GAAG,EAAE9J,IAAI,CAAC8E,EAAG;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACbrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAMP,KAAK,EAAEiB,MAAM,CAACI,OAAQ;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACxB,EAACpI,IAAI,CAAC8E,EACL,CACN,CAAC,EACJ,CAAC7E,UAAU,iBACRlD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBrL,KAAA,CAAA+K,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCpI,IAAI,CAAC6B,YAAY,IAAI,oBAClB,CAAC,eACT9E,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNrL,KAAA,CAAA+K,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDpI,IAAI,CAAC+J,cAAc,IAAI,sBACrB,CACN,CACL,CACP,eACDhN,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEyC,QAAQ,EAAE;IAAQ,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BrL,KAAA,CAAA+K,aAAA;IAAQP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCpI,IAAI,CAACyG,YAAY,IAAI,qBAClB,CAAC,EACRzG,IAAI,CAACiK,WAAW,iBACblN,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UACzC,EAACpI,IAAI,CAACiK,WACb,CAER,CACL,CAAC,eACLlN,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGpI,IAAI,CAACyB,WAAW,IAAI,eACnB,CACN,CAAC,eACL1E,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKf,YAAY,CAACrH,IAAI,CAACA,IAAI,CACvB,CAAC,eACLjD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAMP,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BpI,IAAI,CAACkK,cAAc,IAAIlK,IAAI,CAACmK,mBAC3B,CACN,CAAC,EACJ/J,OAAO,iBACJrD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBI,eAAe,EAAE,SAAS;MAC1BH,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGpI,IAAI,CAACoK,UAAU,IAAI,eAClB,CACN,CACP,EACA/J,SAAS,iBACNtD,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BrL,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,wBAAwB;IAClCE,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACnF,IAAI,CAAE;IAChCqD,KAAK,EAAC,kBAAkB;IAAA0E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExBrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTrL,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,uBAAuB;IACjCE,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACpF,IAAI,CAAC8E,EAAE,CAAE;IACrCzB,KAAK,EAAC,mBAAmB;IAAA0E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLnB,UAAU,GAAG,CAAC,iBACXlK,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAACrI,WAAW,GAAG,CAAC,CAAE;IACzCuL,QAAQ,EAAEvL,WAAW,KAAK,CAAE;IAC5B+J,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,iBAEO,CAAC,eAETrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,WAAW;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAClB,EAACtJ,WAAW,EAAC,OAAK,EAACmI,UACvB,CAAC,eAENlK,KAAA,CAAA+K,aAAA;IACIiB,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAACrI,WAAW,GAAG,CAAC,CAAE;IACzCuL,QAAQ,EAAEvL,WAAW,KAAKmI,UAAW;IACrC4B,SAAS,EAAC,yBAAyB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,SAEO,CACP,CACR,EAGAhK,iBAAiB,IAAIiC,SAAS,iBAC3BtD,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qDAAuC,CAAC,eAC5CrL,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAM1K,oBAAoB,CAAC,KAAK,CAAE;IAAA0J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3CrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BrL,KAAA,CAAA+K,aAAA;IAAGP,KAAK,EAAE;MAAE+C,YAAY,EAAE,MAAM;MAAEzC,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8GAEnD,CAAC,EAEHtK,kBAAkB,CAACqE,MAAM,KAAK,CAAC,gBAC5BpF,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEmB,SAAS,EAAE,QAAQ;MAAElB,OAAO,EAAE;IAAO,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjDrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4DAAuD,CACzD,CAAC,gBAENrL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEgD,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDtK,kBAAkB,CAACyD,GAAG,CAACkJ,MAAM,iBAC1B1N,KAAA,CAAA+K,aAAA;IAAKgC,GAAG,EAAEW,MAAM,CAAC3F,EAAG;IAACyC,KAAK,EAAE;MACxBgB,MAAM,EAAE,mBAAmB;MAC3Bd,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACf8C,YAAY,EAAE,MAAM;MACpB1C,eAAe,EAAE6C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IAC7D,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErL,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAE4B,OAAO,EAAE,MAAM;MAAEwB,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnFrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrL,KAAA,CAAA+K,aAAA;IAAIP,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAY,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEqC,MAAM,CAACI,KAAU,CAAC,eACvD9N,KAAA,CAAA+K,aAAA;IAAGP,KAAK,EAAE;MAAEoB,MAAM,EAAE,GAAG;MAAEjB,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DqC,MAAM,CAAChJ,WAAW,EAAC,KAAG,EAACgJ,MAAM,CAACL,UAChC,CAAC,eACJrN,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEG,KAAK,EAAE,SAAS;MAAEgC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAChE,EAACqC,MAAM,CAACK,WAAW,EAAC,4BAAW,EAACL,MAAM,CAACM,yBAAyB,EAAC,mCAAe,EAACN,MAAM,CAACO,gBAAgB,EAAC,QAC3G,CAAC,eACNjO,KAAA,CAAA+K,aAAA;IAAKP,KAAK,EAAE;MAAEG,QAAQ,EAAE,OAAO;MAAEmC,SAAS,EAAE;IAAM,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDrL,KAAA,CAAA+K,aAAA;IAAMP,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,KAAK;MACnBG,eAAe,EAAE6C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG,SAAS;MAClE7C,KAAK,EAAE4C,MAAM,CAACC,kBAAkB,GAAG,SAAS,GAAG;IACnD,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGqC,MAAM,CAACQ,MACN,CACL,CACJ,CAAC,eACNlO,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,iBAAiB;IAC3BE,OAAO,EAAEA,CAAA,KAAM;MACX1K,oBAAoB,CAAC,KAAK,CAAC;MAC3B8E,mBAAmB,CAACsH,MAAM,CAAC3F,EAAE,CAAC;IAClC,CAAE;IACFuF,QAAQ,EAAE,CAACI,MAAM,CAACC,kBAAmB;IACrCrH,KAAK,EAAEoH,MAAM,CAACC,kBAAkB,GAAG,mBAAmB,GAAG,uBAAwB;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpF,4BAEO,CACP,CACJ,CACR,CACA,CAER,CACJ,CACJ,CACR,EAGAlK,SAAS,IAAImC,SAAS,iBACnBtD,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,cAAc;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK9J,WAAW,GAAG,kBAAkB,GAAG,eAAoB,CAAC,eAC7DvB,KAAA,CAAA+K,aAAA;IACIe,SAAS,EAAC,WAAW;IACrBE,OAAO,EAAEA,CAAA,KAAM;MACX5K,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpByG,SAAS,CAAC,CAAC;IACf,CAAE;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrL,KAAA,CAAA+K,aAAA;IAAKmB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNrL,KAAA,CAAA+K,aAAA;IAAMoD,QAAQ,EAAEzG,YAAa;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAiB,CAAC,eACzBrL,KAAA,CAAA+K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACE,WAAY;IAC5B8J,QAAQ,EAAGjF,CAAC,IAAK9E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAE6E,CAAC,CAACkF,MAAM,CAACF;IAAK,CAAC,CAAE;IACzEyB,QAAQ;IACRd,QAAQ,EAAE/L,WAAY;IAAAyJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtBrL,KAAA,CAAA+K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAmC,CAEhD,CACP,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,UAAe,CAAC,eACvBrL,KAAA,CAAA+K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACG,SAAU;IAC1B6J,QAAQ,EAAGjF,CAAC,IAAK9E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,SAAS,EAAE4E,CAAC,CAACkF,MAAM,CAACF;IAAK,CAAC,CAAE;IACvEyB,QAAQ;IACRd,QAAQ,EAAE/L,WAAY;IAAAyJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtBrL,KAAA,CAAA+K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAAiC,CAAC,EAClDtK,kBAAkB,CAACyD,GAAG,CAACkJ,MAAM,iBAC1B1N,KAAA,CAAA+K,aAAA;IAAQgC,GAAG,EAAEW,MAAM,CAAC3F,EAAG;IAAC4E,KAAK,EAAEe,MAAM,CAAC3F,EAAG;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCqC,MAAM,CAACW,cACJ,CACX,CACG,CACP,CAAC,eAENrO,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,cAAgB,CAAC,eACxBrL,KAAA,CAAA+K,aAAA;IACI4B,KAAK,EAAE/J,QAAQ,CAACI,UAAW;IAC3B4J,QAAQ,EAAGjF,CAAC,IAAK9E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,UAAU,EAAE2E,CAAC,CAACkF,MAAM,CAACF;IAAK,CAAC,CAAE;IACxEyB,QAAQ;IACRd,QAAQ,EAAE/L,WAAY;IAAAyJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtBrL,KAAA,CAAA+K,aAAA;IAAQ4B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAmC,CAEhD,CACP,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,YAAY;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrL,KAAA,CAAA+K,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAoB,CAAC,eAC5BrL,KAAA,CAAA+K,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACb6B,GAAG,EAAC,GAAG;IACPC,GAAG,EAAC,IAAI;IACRC,IAAI,EAAC,MAAM;IACX7B,KAAK,EAAE/J,QAAQ,CAACK,IAAK;IACrB2J,QAAQ,EAAGjF,CAAC,IAAK9E,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,IAAI,EAAE0E,CAAC,CAACkF,MAAM,CAACF;IAAK,CAAC,CAAE;IAClED,WAAW,EAAC,sCAAsC;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrD,CAAC,eACFrL,KAAA,CAAA+K,aAAA;IAAOP,KAAK,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAO,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yFAE/C,CACN,CAAC,eAENrL,KAAA,CAAA+K,aAAA;IAAKe,SAAS,EAAC,eAAe;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrL,KAAA,CAAA+K,aAAA;IAAQ0B,IAAI,EAAC,QAAQ;IAACX,SAAS,EAAC,iBAAiB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C9J,WAAW,GAAG,aAAa,GAAG,SAC3B,CAAC,eACTvB,KAAA,CAAA+K,aAAA;IACI0B,IAAI,EAAC,QAAQ;IACbX,SAAS,EAAC,mBAAmB;IAC7BE,OAAO,EAAEA,CAAA,KAAM;MACX5K,YAAY,CAAC,KAAK,CAAC;MACnBI,cAAc,CAAC,IAAI,CAAC;MACpByG,SAAS,CAAC,CAAC;IACf,CAAE;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAe1K,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}