<?php
/**
 * API Absences Simplifiée pour Debug
 * Version ultra-simple sans authentification
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Connexion à la base de données
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($pdo);
            break;
        case 'POST':
            handlePost($pdo);
            break;
        case 'PUT':
            handlePut($pdo);
            break;
        case 'DELETE':
            handleDelete($pdo);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Erreur serveur: ' . $e->getMessage(),
        'file' => __FILE__,
        'line' => __LINE__
    ]);
}

function handleGet($pdo) {
    try {
        // Requête simple pour récupérer les absences
        $stmt = $pdo->query("
            SELECT a.id, a.etudiant_id, a.matiere_id, a.enseignant_id, 
                   a.date_absence, a.justification,
                   CONCAT(u.nom, ' ', u.prenom) as etudiant_nom,
                   u.email as etudiant_email,
                   m.nom as matiere_nom,
                   CONCAT(ue.nom, ' ', ue.prenom) as enseignant_nom
            FROM Absences a 
            LEFT JOIN Etudiants e ON a.etudiant_id = e.id 
            LEFT JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON a.matiere_id = m.id
            LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            ORDER BY a.date_absence DESC
        ");
        
        $absences = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Si aucune absence, retourner un tableau vide
        if (empty($absences)) {
            echo json_encode([]);
            return;
        }
        
        // Nettoyer les données pour React
        $result = [];
        foreach ($absences as $absence) {
            $result[] = [
                'id' => (int)$absence['id'],
                'etudiant_id' => (int)$absence['etudiant_id'],
                'matiere_id' => $absence['matiere_id'] ? (int)$absence['matiere_id'] : null,
                'enseignant_id' => $absence['enseignant_id'] ? (int)$absence['enseignant_id'] : null,
                'date_absence' => $absence['date_absence'],
                'justification' => $absence['justification'],
                'etudiant_nom' => $absence['etudiant_nom'] ?: 'Étudiant inconnu',
                'etudiant_email' => $absence['etudiant_email'] ?: '',
                'matiere_nom' => $absence['matiere_nom'] ?: 'Non définie',
                'enseignant_nom' => $absence['enseignant_nom'] ?: 'Non défini'
            ];
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération: ' . $e->getMessage()]);
    }
}

function handlePost($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Données JSON invalides']);
            return;
        }
        
        // Validation des champs requis
        if (empty($input['etudiant_id']) || empty($input['date_absence'])) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Champs requis manquants',
                'required' => ['etudiant_id', 'date_absence'],
                'received' => $input
            ]);
            return;
        }
        
        // Nettoyer les données
        $etudiant_id = (int)$input['etudiant_id'];
        $matiere_id = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
        $enseignant_id = !empty($input['enseignant_id']) ? (int)$input['enseignant_id'] : null;
        $date_absence = $input['date_absence'];
        $justification = !empty($input['justification']) ? $input['justification'] : null;
        
        // Vérifier que l'étudiant existe
        $stmt = $pdo->prepare("SELECT id FROM Etudiants WHERE id = ?");
        $stmt->execute([$etudiant_id]);
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Étudiant non trouvé avec ID: ' . $etudiant_id]);
            return;
        }
        
        // Insérer l'absence
        $stmt = $pdo->prepare("
            INSERT INTO Absences (etudiant_id, matiere_id, enseignant_id, date_absence, justification) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([$etudiant_id, $matiere_id, $enseignant_id, $date_absence, $justification]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'id' => (int)$pdo->lastInsertId(),
                'message' => 'Absence créée avec succès'
            ]);
        } else {
            throw new Exception('Échec de l\'insertion');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

function handlePut($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || empty($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID manquant']);
            return;
        }
        
        $id = (int)$input['id'];
        $fields = [];
        $values = [];
        
        if (isset($input['date_absence'])) {
            $fields[] = 'date_absence = ?';
            $values[] = $input['date_absence'];
        }
        
        if (isset($input['justification'])) {
            $fields[] = 'justification = ?';
            $values[] = $input['justification'];
        }
        
        if (isset($input['matiere_id'])) {
            $fields[] = 'matiere_id = ?';
            $values[] = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['error' => 'Aucune donnée à modifier']);
            return;
        }
        
        $values[] = $id;
        $sql = "UPDATE Absences SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Absence modifiée avec succès']);
        } else {
            throw new Exception('Échec de la modification');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

function handleDelete($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || empty($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID manquant']);
            return;
        }
        
        $id = (int)$input['id'];
        
        $stmt = $pdo->prepare("DELETE FROM Absences WHERE id = ?");
        $result = $stmt->execute([$id]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Absence supprimée avec succès']);
        } else {
            throw new Exception('Échec de la suppression');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
