import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { FaChalkboardTeacher, FaUsers, FaClipboardList, FaChartBar, FaCalendarAlt, FaBookOpen } from 'react-icons/fa';
import axios from 'axios';

const EnseignantDashboard = () => {
  const { user } = useContext(AuthContext);
  const [stats, setStats] = useState({
    totalCours: 0,
    totalEtudiants: 0,
    coursAujourdhui: 0,
    devoirsEnAttente: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [coursAujourdhui, setCoursAujourdhui] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simuler des données pour le moment
      setStats({
        totalCours: 8,
        totalEtudiants: 156,
        coursAujourdhui: 3,
        devoirsEnAttente: 12
      });

      setRecentActivities([
        { id: 1, type: 'cours', message: 'Cours de Mathématiques - Classe 3A terminé', time: '10:30' },
        { id: 2, type: 'devoir', message: 'Nouveau devoir assigné - Physique', time: '09:15' },
        { id: 3, type: 'note', message: 'Notes saisies pour le contrôle de Chimie', time: '08:45' }
      ]);

      setCoursAujourdhui([
        { id: 1, matiere: 'Mathématiques', classe: '3A', heure: '08:00-09:30', salle: 'A101' },
        { id: 2, matiere: 'Physique', classe: '2B', heure: '10:00-11:30', salle: 'B205' },
        { id: 3, matiere: 'Chimie', classe: '1C', heure: '14:00-15:30', salle: 'C301' }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  };

  const styles = {
    container: {
      padding: '20px',
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
    },
    header: {
      marginBottom: '30px',
    },
    welcomeText: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    subtitle: {
      color: '#666',
      fontSize: '1.1rem',
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    statCard: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      transition: 'transform 0.3s ease',
    },
    statIcon: {
      fontSize: '2.5rem',
      marginRight: '20px',
      padding: '15px',
      borderRadius: '50%',
      color: 'white',
    },
    statContent: {
      flex: 1,
    },
    statNumber: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '5px',
    },
    statLabel: {
      color: '#666',
      fontSize: '0.9rem',
    },
    contentGrid: {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '30px',
    },
    card: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    },
    cardTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
    },
    cardIcon: {
      marginRight: '10px',
      color: '#007bff',
    },
    courseItem: {
      padding: '15px',
      borderLeft: '4px solid #007bff',
      backgroundColor: '#f8f9ff',
      marginBottom: '15px',
      borderRadius: '5px',
    },
    courseHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '8px',
    },
    courseTitle: {
      fontWeight: 'bold',
      color: '#333',
    },
    courseTime: {
      color: '#007bff',
      fontSize: '0.9rem',
    },
    courseDetails: {
      color: '#666',
      fontSize: '0.9rem',
    },
    activityItem: {
      padding: '12px',
      borderBottom: '1px solid #eee',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    activityMessage: {
      color: '#333',
      fontSize: '0.9rem',
    },
    activityTime: {
      color: '#999',
      fontSize: '0.8rem',
    },
    quickActions: {
      display: 'flex',
      gap: '15px',
      marginTop: '20px',
      flexWrap: 'wrap',
    },
    actionButton: {
      padding: '10px 20px',
      backgroundColor: '#007bff',
      color: 'white',
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
      fontSize: '0.9rem',
      transition: 'background-color 0.3s ease',
    },
  };

  const statCards = [
    { icon: FaBookOpen, number: stats.totalCours, label: 'Cours Total', color: '#007bff' },
    { icon: FaUsers, number: stats.totalEtudiants, label: 'Étudiants', color: '#28a745' },
    { icon: FaCalendarAlt, number: stats.coursAujourdhui, label: "Cours Aujourd'hui", color: '#ffc107' },
    { icon: FaClipboardList, number: stats.devoirsEnAttente, label: 'Devoirs en Attente', color: '#dc3545' },
  ];

  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <h1 style={styles.welcomeText}>
          <FaChalkboardTeacher style={{ marginRight: '15px', color: '#007bff' }} />
          Bienvenue, {user?.email || 'Enseignant'}
        </h1>
        <p style={styles.subtitle}>Tableau de bord enseignant - Gérez vos cours et suivez vos étudiants</p>
      </div>

      {/* Statistiques */}
      <div style={styles.statsGrid}>
        {statCards.map((stat, index) => (
          <div 
            key={index} 
            style={styles.statCard}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{...styles.statIcon, backgroundColor: stat.color}}>
              <stat.icon />
            </div>
            <div style={styles.statContent}>
              <div style={styles.statNumber}>{stat.number}</div>
              <div style={styles.statLabel}>{stat.label}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Contenu principal */}
      <div style={styles.contentGrid}>
        {/* Cours d'aujourd'hui */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaCalendarAlt style={styles.cardIcon} />
            Cours d'Aujourd'hui
          </h2>
          {coursAujourdhui.map(cours => (
            <div key={cours.id} style={styles.courseItem}>
              <div style={styles.courseHeader}>
                <span style={styles.courseTitle}>{cours.matiere} - {cours.classe}</span>
                <span style={styles.courseTime}>{cours.heure}</span>
              </div>
              <div style={styles.courseDetails}>Salle: {cours.salle}</div>
            </div>
          ))}
          
          <div style={styles.quickActions}>
            <button style={styles.actionButton}>Prendre les Présences</button>
            <button style={styles.actionButton}>Créer un Devoir</button>
            <button style={styles.actionButton}>Saisir des Notes</button>
          </div>
        </div>

        {/* Activités récentes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaChartBar style={styles.cardIcon} />
            Activités Récentes
          </h2>
          {recentActivities.map(activity => (
            <div key={activity.id} style={styles.activityItem}>
              <span style={styles.activityMessage}>{activity.message}</span>
              <span style={styles.activityTime}>{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnseignantDashboard;
