/* Styles spécifiques pour QuizCRUD - Extension des styles Factures */

/* Import des styles de base */
@import './Factures.css';

/* Styles spécifiques aux quiz */
.question-preview {
    max-width: 200px;
    word-wrap: break-word;
    line-height: 1.4;
    color: #333;
}

.reponse-correcte {
    max-width: 150px;
    word-wrap: break-word;
    background-color: #e8f5e8;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #2d5a2d;
}

.devoir-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.devoir-info strong {
    color: #333;
    font-weight: 600;
}

.devoir-info small {
    color: #666;
    font-size: 0.8em;
    font-style: italic;
}

/* Badges pour matières et classes */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.badge-primary {
    background-color: #007bff;
    color: white;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

/* Aperçu du devoir dans le modal */
.devoir-preview {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.devoir-preview h4 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 1.1em;
}

.preview-card {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.preview-card p {
    margin: 6px 0;
    font-size: 0.9em;
}

.preview-card strong {
    color: #495057;
    font-weight: 600;
}

/* Champ requis */
.required {
    color: #dc3545;
    font-weight: bold;
}

/* Texte d'aide */
.form-text {
    font-size: 0.875em;
    margin-top: 4px;
}

.text-muted {
    color: #6c757d !important;
}

/* Styles pour les statistiques */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 2.5em;
    margin: 0;
    font-weight: bold;
}

.stat-card p {
    margin: 10px 0 0 0;
    font-size: 1.1em;
    opacity: 0.9;
}

/* Styles pour le tableau responsive */
@media (max-width: 768px) {
    .data-table {
        font-size: 0.8em;
    }
    
    .question-preview {
        max-width: 120px;
    }
    
    .reponse-correcte {
        max-width: 100px;
        font-size: 0.8em;
    }
    
    .devoir-info {
        font-size: 0.9em;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Animation pour le chargement */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles pour les messages d'état vide */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data img {
    width: 80px;
    height: 80px;
    opacity: 0.5;
    margin-bottom: 20px;
}

.no-data p {
    font-size: 1.1em;
    margin: 10px 0;
}

.no-data p:first-of-type {
    font-weight: 600;
    color: #333;
}

/* Amélioration du modal */
.modal-content {
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h3 {
    color: #007bff;
    margin: 0;
}

/* Amélioration des champs de formulaire */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Actions du modal */
.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.modal-actions .btn {
    min-width: 100px;
    padding: 12px 20px;
    font-weight: 600;
}

/* Responsive pour le modal */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .modal-actions .btn {
        width: 100%;
    }
}
