<?php
/**
 * Script de diagnostic pour l'API Cours
 * Utilisation: Accéder via navigateur ou curl
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

echo json_encode([
    'status' => 'API Cours Diagnostic',
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => []
]);

$tests = [];

// Test 1: Vérifier la connexion à la base de données
try {
    require_once('../../config/db.php');
    $tests['database'] = [
        'status' => 'OK',
        'message' => 'Connexion à la base de données réussie'
    ];
} catch (Exception $e) {
    $tests['database'] = [
        'status' => 'ERROR',
        'message' => 'Erreur de connexion BDD: ' . $e->getMessage()
    ];
}

// Test 2: Vérifier la structure de la table Cours
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("DESCRIBE Cours");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $tests['table_structure'] = [
            'status' => 'OK',
            'message' => 'Table Cours trouvée',
            'columns' => array_column($columns, 'Field')
        ];
    } catch (PDOException $e) {
        $tests['table_structure'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Cours: ' . $e->getMessage()
        ];
    }
}

// Test 3: Vérifier les matières
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Matieres");
        $count = $stmt->fetch()['count'];
        $tests['matieres'] = [
            'status' => 'OK',
            'message' => "Table Matieres: $count matières trouvées"
        ];
    } catch (PDOException $e) {
        $tests['matieres'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Matieres: ' . $e->getMessage()
        ];
    }
}

// Test 4: Vérifier les classes
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Classes");
        $count = $stmt->fetch()['count'];
        $tests['classes'] = [
            'status' => 'OK',
            'message' => "Table Classes: $count classes trouvées"
        ];
    } catch (PDOException $e) {
        $tests['classes'] = [
            'status' => 'ERROR',
            'message' => 'Erreur table Classes: ' . $e->getMessage()
        ];
    }
}

// Test 5: Vérifier le dossier uploads
$uploadDir = '../../uploads/cours/';
if (!file_exists($uploadDir)) {
    $tests['uploads'] = [
        'status' => 'WARNING',
        'message' => 'Dossier uploads n\'existe pas',
        'path' => realpath($uploadDir)
    ];
} else {
    $writable = is_writable($uploadDir);
    $tests['uploads'] = [
        'status' => $writable ? 'OK' : 'ERROR',
        'message' => $writable ? 'Dossier uploads accessible' : 'Dossier uploads non accessible en écriture',
        'path' => realpath($uploadDir),
        'permissions' => substr(sprintf('%o', fileperms($uploadDir)), -4)
    ];
}

// Test 6: Vérifier les cours existants
if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Cours");
        $count = $stmt->fetch()['count'];
        $tests['cours_count'] = [
            'status' => 'OK',
            'message' => "Table Cours: $count cours trouvés"
        ];
        
        // Récupérer quelques cours pour test
        $stmt = $pdo->query("SELECT id, titre, fichier_pdf FROM Cours LIMIT 3");
        $cours = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $tests['cours_sample'] = [
            'status' => 'OK',
            'message' => 'Échantillon de cours',
            'data' => $cours
        ];
    } catch (PDOException $e) {
        $tests['cours_count'] = [
            'status' => 'ERROR',
            'message' => 'Erreur lecture cours: ' . $e->getMessage()
        ];
    }
}

// Test 7: Vérifier la configuration PHP
$tests['php_config'] = [
    'status' => 'INFO',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit')
];

// Test 8: Vérifier les headers
$tests['headers'] = [
    'status' => 'INFO',
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'non défini',
    'authorization' => isset($_SERVER['HTTP_AUTHORIZATION']) ? 'présent' : 'absent',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'non défini'
];

// Résultat final
$allOk = true;
foreach ($tests as $test) {
    if (isset($test['status']) && $test['status'] === 'ERROR') {
        $allOk = false;
        break;
    }
}

$result = [
    'status' => $allOk ? 'OK' : 'ERROR',
    'message' => $allOk ? 'Tous les tests sont passés' : 'Certains tests ont échoué',
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => $tests
];

echo json_encode($result, JSON_PRETTY_PRINT);
?>
