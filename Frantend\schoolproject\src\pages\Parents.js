import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const Parents = () => {
    const { user } = useContext(AuthContext);
    const [parents, setParents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingParent, setEditingParent] = useState(null);
    const [utilisateursParents, setUtilisateursParents] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        utilisateur_id: '',
        telephone: '',
        adresse: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';

    useEffect(() => {
        fetchParents();
        if (isAdmin) {
            fetchUtilisateursParents();
        }
    }, [isAdmin]);

    const fetchParents = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des parents...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('✅ Réponse API parents:', response.data);
            setParents(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des parents:', error);
            Swal.fire('Erreur', 'Impossible de charger les parents', 'error');
            setParents([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchUtilisateursParents = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des utilisateurs parents...');

            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=parent', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les parents existants pour les exclure
            const responseParents = await axios.get('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Filtrer pour ne garder que les utilisateurs avec le rôle "parent"
            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const parentsExistants = Array.isArray(responseParents.data) ? responseParents.data : [];
            
            const utilisateursParentsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'parent' || roleNom === 'parents';
            });

            // Exclure les utilisateurs déjà parents
            const parentsExistantsIds = parentsExistants.map(p => p.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursParentsFiltered.filter(user => 
                !parentsExistantsIds.includes(user.id)
            );

            console.log('✅ Utilisateurs parents disponibles:', utilisateursDisponibles.length);
            setUtilisateursParents(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs parents:', error);
            setUtilisateursParents([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des parents', 'error');
            return;
        }

        // Validation des champs requis
        if (!formData.utilisateur_id) {
            Swal.fire('Erreur', 'Veuillez sélectionner un utilisateur', 'error');
            return;
        }

        if (!formData.telephone) {
            Swal.fire('Erreur', 'Le numéro de téléphone est requis', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/parents/parent.php';
            const method = editingParent ? 'PUT' : 'POST';
            const data = editingParent ? { ...formData, id: editingParent.id } : formData;

            console.log('🔄 Envoi requête parent:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.data.success) {
                Swal.fire('Succès', `Parent ${editingParent ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingParent(null);
                resetForm();
                fetchParents();
                fetchUtilisateursParents(); // Recharger pour exclure l'utilisateur ajouté
            } else {
                throw new Error(response.data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('❌ Erreur parent:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = async (parent) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des parents', 'error');
            return;
        }

        // Recharger les utilisateurs parents en incluant l'utilisateur actuel
        await fetchUtilisateursParentsAvecActuel(parent.utilisateur_id);

        setEditingParent(parent);
        setFormData({
            utilisateur_id: parent.utilisateur_id || '',
            telephone: parent.telephone || '',
            adresse: parent.adresse || ''
        });
        setShowModal(true);
    };

    const fetchUtilisateursParentsAvecActuel = async (currentUserId) => {
        try {
            const token = localStorage.getItem('token');
            
            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les parents existants
            const responseParents = await axios.get('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const parentsExistants = Array.isArray(responseParents.data) ? responseParents.data : [];
            
            const utilisateursParentsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'parent' || roleNom === 'parents';
            });

            // Exclure les utilisateurs déjà parents SAUF l'utilisateur actuel
            const parentsExistantsIds = parentsExistants.map(p => p.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursParentsFiltered.filter(user => 
                !parentsExistantsIds.includes(user.id) || user.id === currentUserId
            );

            setUtilisateursParents(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs parents avec actuel:', error);
        }
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des parents', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data.success) {
                    Swal.fire('Supprimé!', 'Le parent a été supprimé.', 'success');
                    fetchParents();
                    fetchUtilisateursParents(); // Recharger pour rendre l'utilisateur disponible
                } else {
                    throw new Error(response.data.error || 'Erreur lors de la suppression');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error || error.message || 'Impossible de supprimer le parent';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            utilisateur_id: '',
            telephone: '',
            adresse: ''
        });
    };

    const getStatutBadge = () => {
        return <span className="badge badge-success">Actif</span>;
    };

    // Styles inline pour les badges et éléments spécifiques
    const styles = {
        idBadge: {
            padding: '4px 8px',
            backgroundColor: '#e3f2fd',
            borderRadius: '4px',
            fontSize: '0.9em',
            fontWeight: 'bold'
        },
        telephoneBadge: {
            padding: '4px 8px',
            backgroundColor: '#fff3cd',
            borderRadius: '4px',
            fontSize: '0.8em',
            color: '#856404'
        },
        infoMessage: {
            padding: '15px',
            backgroundColor: '#e3f2fd',
            borderRadius: '8px',
            marginBottom: '20px',
            border: '1px solid #bbdefb',
            color: '#1976d2'
        }
    };

    // Filtrage des données
    const filteredParents = parents.filter(parent => {
        const searchLower = searchTerm.toLowerCase();
        return (parent.nom && parent.nom.toLowerCase().includes(searchLower)) ||
               (parent.email && parent.email.toLowerCase().includes(searchLower)) ||
               (parent.telephone && parent.telephone.toLowerCase().includes(searchLower)) ||
               (parent.adresse && parent.adresse.toLowerCase().includes(searchLower));
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentParents = filteredParents.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredParents.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des parents...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>👨‍👩‍👧‍👦 Gestion des Parents</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredParents.length} parent(s) trouvé(s)
                    </span>
                    {isAdmin && (
                        <button 
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouveau Parent
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={styles.infoMessage}>
                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les parents en mode lecture seule. Seul l'administrateur peut créer, modifier ou supprimer des parents.</p>
                </div>
            )}

            {/* Barre de recherche */}
            <div className="search-section" style={{ marginBottom: '20px' }}>
                <input
                    type="text"
                    placeholder="🔍 Rechercher un parent (nom, email, téléphone, adresse)..."
                    value={searchTerm}
                    onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setCurrentPage(1);
                    }}
                    className="search-input"
                    style={{
                        width: '100%',
                        padding: '12px',
                        border: '1px solid #ddd',
                        borderRadius: '8px',
                        fontSize: '16px'
                    }}
                />
            </div>

            <div className="factures-grid">
                {filteredParents.length === 0 ? (
                    <div className="no-data">
                        <img src="/parent.png" alt="Aucun parent" />
                        <p>Aucun parent trouvé</p>
                        {searchTerm && (
                            <button
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>👤 Nom du Parent</th>
                                    <th>📧 Email</th>
                                    <th>📞 Téléphone</th>
                                    <th>🏠 Adresse</th>
                                    <th>📊 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentParents.map((parent) => (
                                    <tr key={parent.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{parent.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="parent-info">
                                                <strong>{parent.nom || 'Nom non disponible'}</strong>
                                                <br />
                                                <small style={{ color: '#6c757d' }}>
                                                    ID Utilisateur: {parent.utilisateur_id}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em', color: '#007bff' }}>
                                                {parent.email || 'Email non disponible'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={styles.telephoneBadge}>
                                                {parent.telephone || 'Non renseigné'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {parent.adresse ?
                                                    (parent.adresse.length > 50 ?
                                                        parent.adresse.substring(0, 50) + '...' :
                                                        parent.adresse
                                                    ) : 'Non renseignée'
                                                }
                                            </span>
                                        </td>
                                        <td>{getStatutBadge()}</td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(parent)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(parent.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Modal pour ajouter/modifier un parent */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingParent ? 'Modifier le parent' : 'Nouveau parent'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingParent(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Utilisateur (Parent) *</label>
                                <select
                                    value={formData.utilisateur_id}
                                    onChange={(e) => setFormData({...formData, utilisateur_id: e.target.value})}
                                    required
                                    disabled={editingParent} // Empêcher la modification de l'utilisateur lors de l'édition
                                >
                                    <option value="">Sélectionner un utilisateur parent...</option>
                                    {utilisateursParents.map(user => (
                                        <option key={user.id} value={user.id}>
                                            {user.nom} - {user.email} (ID: {user.id})
                                        </option>
                                    ))}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Seuls les utilisateurs avec le rôle "parent" non encore assignés sont affichés
                                </small>
                            </div>

                            <div className="form-group">
                                <label>Téléphone *</label>
                                <input
                                    type="tel"
                                    value={formData.telephone}
                                    onChange={(e) => setFormData({...formData, telephone: e.target.value})}
                                    placeholder="Ex: 0123456789"
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Adresse (Optionnelle)</label>
                                <textarea
                                    value={formData.adresse}
                                    onChange={(e) => setFormData({...formData, adresse: e.target.value})}
                                    placeholder="Adresse complète du parent"
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingParent(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingParent ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Parents;
