<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔧 DEBUG FORMULAIRE ABSENCE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .test-form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 15px 0; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group select, .form-group input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-success { background: #28a745; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🔧 Debug du Problème 'Données manquantes'</h2>";
    echo "<p>Vérification étape par étape du formulaire d'absence</p>";
    echo "</div>";
    
    // 1. Test de l'API getEtudiants
    echo "<div class='step'>";
    echo "<h3>👨‍🎓 1. Test API getEtudiants</h3>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants_temp.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $etudiants = $data['etudiants'];
                echo "<p class='success'>✅ API accessible - " . count($etudiants) . " étudiant(s) trouvé(s)</p>";
                
                if (!empty($etudiants)) {
                    echo "<h4>📋 Structure du premier étudiant :</h4>";
                    echo "<div class='json-block'>";
                    echo json_encode($etudiants[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    echo "</div>";
                    
                    // Vérifier les champs requis
                    $first = $etudiants[0];
                    $has_id = isset($first['id']);
                    $has_etudiant_id = isset($first['etudiant_id']);
                    $has_nom = isset($first['nom']);
                    $has_prenom = isset($first['prenom']);
                    
                    echo "<h4>🔍 Vérification des champs :</h4>";
                    echo "<ul>";
                    echo "<li>" . ($has_id ? "✅" : "❌") . " Champ 'id': " . ($has_id ? $first['id'] : 'MANQUANT') . "</li>";
                    echo "<li>" . ($has_etudiant_id ? "✅" : "❌") . " Champ 'etudiant_id': " . ($has_etudiant_id ? $first['etudiant_id'] : 'MANQUANT') . "</li>";
                    echo "<li>" . ($has_nom ? "✅" : "❌") . " Champ 'nom': " . ($has_nom ? $first['nom'] : 'MANQUANT') . "</li>";
                    echo "<li>" . ($has_prenom ? "✅" : "❌") . " Champ 'prenom': " . ($has_prenom ? $first['prenom'] : 'MANQUANT') . "</li>";
                    echo "</ul>";
                } else {
                    echo "<p class='error'>❌ Aucun étudiant dans la réponse</p>";
                }
            } else {
                echo "<p class='error'>❌ API retourne une erreur</p>";
                echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test API : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Formulaire de test avec JavaScript
    echo "<div class='step'>";
    echo "<h3>📝 2. Formulaire de Test avec Debug JavaScript</h3>";
    
    // Récupérer les étudiants directement de la DB
    $stmt = $pdo->query("
        SELECT e.id as etudiant_id, e.id, u.nom, u.prenom, c.nom as classe_nom 
        FROM Etudiants e 
        JOIN Utilisateurs u ON e.utilisateur_id = u.id 
        LEFT JOIN Classes c ON e.classe_id = c.id 
        ORDER BY u.nom, u.prenom 
        LIMIT 5
    ");
    $etudiants_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='test-form'>";
    echo "<h4>📝 Formulaire avec Debug JavaScript</h4>";
    
    echo "<form id='testForm'>";
    echo "<div class='form-group'>";
    echo "<label>Étudiant :</label>";
    echo "<select id='etudiant_id' name='etudiant_id' required>";
    echo "<option value=''>Sélectionner un étudiant</option>";
    
    foreach ($etudiants_db as $etudiant) {
        $id = $etudiant['etudiant_id'];
        $nom = $etudiant['nom'] . ' ' . $etudiant['prenom'];
        $classe = $etudiant['classe_nom'] ?: 'Sans classe';
        echo "<option value='$id'>$nom - $classe</option>";
    }
    
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Date d'absence :</label>";
    echo "<input type='date' id='date_absence' name='date_absence' value='" . date('Y-m-d') . "' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Justification :</label>";
    echo "<input type='text' id='justification' name='justification' placeholder='Motif de l\'absence...'>";
    echo "</div>";
    
    echo "<button type='button' onclick='debugForm()' class='btn btn-warning'>🔍 Debug Formulaire</button>";
    echo "<button type='button' onclick='submitTest()' class='btn btn-success'>🧪 Test Soumission</button>";
    echo "</form>";
    
    echo "<div id='debug-output' style='margin-top: 20px;'></div>";
    echo "</div>";
    echo "</div>";
    
    // 3. JavaScript pour le debug
    echo "<script>";
    echo "
    function debugForm() {
        const form = document.getElementById('testForm');
        const formData = new FormData(form);
        
        const data = {
            etudiant_id: formData.get('etudiant_id'),
            date_absence: formData.get('date_absence'),
            justification: formData.get('justification')
        };
        
        console.log('🔍 DEBUG FORM:', data);
        
        let output = '<h4>🔍 Debug du Formulaire :</h4>';
        output += '<div class=\"json-block\">';
        output += 'etudiant_id: \"' + data.etudiant_id + '\" (type: ' + typeof data.etudiant_id + ')\\n';
        output += 'date_absence: \"' + data.date_absence + '\" (type: ' + typeof data.date_absence + ')\\n';
        output += 'justification: \"' + data.justification + '\" (type: ' + typeof data.justification + ')\\n';
        output += '</div>';
        
        // Validation
        if (!data.etudiant_id || data.etudiant_id === '') {
            output += '<p class=\"error\">❌ PROBLÈME: etudiant_id est vide !</p>';
        } else {
            output += '<p class=\"success\">✅ etudiant_id est défini</p>';
        }
        
        if (!data.date_absence || data.date_absence === '') {
            output += '<p class=\"error\">❌ PROBLÈME: date_absence est vide !</p>';
        } else {
            output += '<p class=\"success\">✅ date_absence est défini</p>';
        }
        
        document.getElementById('debug-output').innerHTML = output;
    }
    
    function submitTest() {
        const form = document.getElementById('testForm');
        const formData = new FormData(form);
        
        const data = {
            etudiant_id: parseInt(formData.get('etudiant_id')),
            date_absence: formData.get('date_absence'),
            justification: formData.get('justification') || null
        };
        
        console.log('🧪 TEST SUBMIT:', data);
        
        // Validation
        if (!data.etudiant_id || data.etudiant_id <= 0) {
            alert('❌ Erreur: Veuillez sélectionner un étudiant');
            return;
        }
        
        if (!data.date_absence) {
            alert('❌ Erreur: Veuillez sélectionner une date');
            return;
        }
        
        // Envoi vers l'API
        fetch('http://localhost/Project_PFE/Backend/pages/absences/index_temp.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            console.log('✅ Réponse API:', result);
            
            let output = '<h4>📤 Résultat de la Soumission :</h4>';
            output += '<div class=\"json-block\">';
            output += JSON.stringify(result, null, 2);
            output += '</div>';
            
            if (result.success) {
                output += '<p class=\"success\">✅ Absence créée avec succès ! ID: ' + result.id + '</p>';
            } else {
                output += '<p class=\"error\">❌ Erreur: ' + (result.error || 'Erreur inconnue') + '</p>';
            }
            
            document.getElementById('debug-output').innerHTML = output;
        })
        .catch(error => {
            console.error('❌ Erreur:', error);
            document.getElementById('debug-output').innerHTML = 
                '<p class=\"error\">❌ Erreur de connexion: ' + error.message + '</p>';
        });
    }
    ";
    echo "</script>";
    
    // 4. Instructions
    echo "<div class='step'>";
    echo "<h3>📋 3. Instructions de Test</h3>";
    
    echo "<h4>🔍 Étapes de Debug :</h4>";
    echo "<ol>";
    echo "<li><strong>Sélectionnez un étudiant</strong> dans la liste déroulante</li>";
    echo "<li><strong>Cliquez sur 'Debug Formulaire'</strong> pour voir les données</li>";
    echo "<li><strong>Vérifiez</strong> que etudiant_id n'est pas vide</li>";
    echo "<li><strong>Cliquez sur 'Test Soumission'</strong> pour envoyer à l'API</li>";
    echo "<li><strong>Vérifiez la réponse</strong> dans la section de debug</li>";
    echo "</ol>";
    
    echo "<h4>🎯 Problèmes Possibles :</h4>";
    echo "<ul>";
    echo "<li><strong>Liste vide :</strong> Aucun étudiant en base de données</li>";
    echo "<li><strong>ID incorrect :</strong> Mauvaise structure de données</li>";
    echo "<li><strong>Validation échouée :</strong> Données vides ou invalides</li>";
    echo "<li><strong>API inaccessible :</strong> Problème de serveur</li>";
    echo "</ul>";
    
    echo "<h4>🔧 Solutions :</h4>";
    echo "<ol>";
    echo "<li><strong>Créer des étudiants de test</strong> si la base est vide</li>";
    echo "<li><strong>Vérifier la structure</strong> des données API</li>";
    echo "<li><strong>Utiliser parseInt()</strong> pour convertir les IDs</li>";
    echo "<li><strong>Ajouter des validations</strong> côté frontend</li>";
    echo "</ol>";
    echo "</div>";
    
    // 5. Liens utiles
    echo "<div class='step'>";
    echo "<h3>🔗 4. Liens de Test</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$api_url' target='_blank' class='btn'>🧪 API Étudiants</a>";
    echo "<a href='http://localhost/Project_PFE/Backend/pages/absences/index_temp.php' target='_blank' class='btn'>🎯 API Absences</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='btn'>⚛️ Interface React</a>";
    echo "<a href='fix_data.php' target='_blank' class='btn'>🔧 Créer Données</a>";
    echo "</div>";
    
    echo "<p class='success'>🎯 <strong>Utilisez ce formulaire pour identifier exactement où est le problème !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
