import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './FiliereManager.css';

const FiliereManager = () => {
  const [filieres, setFilieres] = useState([]);
  const [nom, setNom] = useState('');
  const [editId, setEditId] = useState(null);
  const [editNom, setEditNom] = useState('');

  const fetchFilieres = async () => {
    try {
      const res = await axios.get('http://localhost/SchoolProject/pages/filieres/filiere.php');
      setFilieres(res.data);
    } catch (error) {
      console.error('Échec de la récupération des filières', error);
    }
  };

  useEffect(() => {
    fetchFilieres();
  }, []);

  const handleAdd = async (e) => {
    e.preventDefault();
    if (!nom.trim()) {
      alert('Veuillez entrer un nom de filière');
      return;
    }
    try {
      await axios.post('http://localhost/SchoolProject/pages/filieres/filiere.php', { nom });
      setNom('');
      fetchFilieres();
    } catch (error) {
      alert("Erreur lors de l'ajout de la filière");
      console.error(error);
    }
  };

  const startEdit = (filiere) => {
    setEditId(filiere.id);
    setEditNom(filiere.nom);
  };

  const cancelEdit = () => {
    setEditId(null);
    setEditNom('');
  };

  const saveEdit = async (id) => {
    if (!editNom.trim()) {
      alert("Le nom de la filière ne peut pas être vide");
      return;
    }
    try {
      await axios.put('http://localhost/SchoolProject/pages/filieres/filiere.php', { id, nom: editNom });
      setEditId(null);
      setEditNom('');
      fetchFilieres();
    } catch (error) {
      alert("Erreur lors de la mise à jour de la filière");
      console.error(error);
    }
  };

  const deleteFiliere = async (id) => {
    if (!window.confirm("Voulez-vous vraiment supprimer cette filière ?")) return;
    try {
      await axios.delete('http://localhost/SchoolProject/pages/filieres/filiere.php', { data: { id } });
      fetchFilieres();
    } catch (error) {
      alert("Erreur lors de la suppression de la filière");
      console.error(error);
    }
  };

  return (
    <div className="filiere-container">
      <h2 className="filiere-title">🎓 Gestion des Filières</h2>
      <form onSubmit={handleAdd} className="filiere-form">
        <input
          type="text"
          placeholder="Entrer une nouvelle filière"
          value={nom}
          onChange={e => setNom(e.target.value)}
          className="filiere-input glass"
        />
        <button type="submit" className="btn primary">Ajouter</button>
      </form>

      <div className="filiere-card-grid">
        {filieres.length === 0 ? (
          <p className="empty">Aucune filière trouvée</p>
        ) : filieres.map(filiere => (
          <div key={filiere.id} className="filiere-card">
            <div className="card-content">
              <h3>Filière ID: {filiere.id}</h3>
              {editId === filiere.id ? (
                <input
                  type="text"
                  value={editNom}
                  onChange={e => setEditNom(e.target.value)}
                  className="filiere-input glass"
                />
              ) : (
                <p>{filiere.nom}</p>
              )}
            </div>
            <div className="card-actions">
              {editId === filiere.id ? (
                <>
                  <button onClick={() => saveEdit(filiere.id)} className="btn success">Enregistrer</button>
                  <button onClick={cancelEdit} className="btn secondary">Annuler</button>
                </>
              ) : (
                <>
                  <button onClick={() => startEdit(filiere)} className="btn warning">Modifier</button>
                  <button onClick={() => deleteFiliere(filiere.id)} className="btn danger">Supprimer</button>
                </>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FiliereManager;
