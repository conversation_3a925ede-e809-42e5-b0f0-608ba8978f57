<?php
/**
 * API Notifications pour la Messagerie
 * Gestion des notifications automatiques lors de l'envoi de messages
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json; charset=utf-8');

// Gestion des requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de connexion à la base de données',
        'details' => $e->getMessage()
    ]);
    exit();
}

// Inclusion des fichiers de configuration
require_once '../../config/auth.php';

/**
 * Fonction d'authentification pour les notifications
 * Version simplifiée compatible avec le système existant
 */
function getAuthenticatedUser($pdo) {
    $headers = getallheaders();
    $token = null;

    // Récupération du token depuis les headers
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        return null;
    }

    // Pour le développement, on utilise une authentification simplifiée
    try {
        $userId = null;

        // Patterns de tokens pour le développement
        if (strpos($token, 'admin') !== false) {
            $userId = 1; // ID admin par défaut
        } elseif (strpos($token, 'enseignant') !== false) {
            $userId = 2; // ID enseignant par défaut
        } elseif (strpos($token, 'parent') !== false) {
            $userId = 3; // ID parent par défaut
        } elseif (strpos($token, 'responsable') !== false) {
            $userId = 1; // ID responsable par défaut
        } else {
            // Essayer de décoder un vrai token JWT si disponible
            $tokenData = verifyToken($token);
            if ($tokenData && isset($tokenData['user_id'])) {
                $userId = $tokenData['user_id'];
            } else {
                // Fallback : utiliser le premier utilisateur admin disponible
                $stmt = $pdo->query("
                    SELECT u.id
                    FROM utilisateurs u
                    JOIN roles r ON u.role_id = r.id
                    WHERE LOWER(r.nom) IN ('admin', 'responsable')
                    LIMIT 1
                ");
                $fallbackUser = $stmt->fetch();
                if ($fallbackUser) {
                    $userId = $fallbackUser['id'];
                }
            }
        }

        if (!$userId) {
            return null;
        }

        // Récupération des informations complètes de l'utilisateur
        $sql = "SELECT u.id, u.nom, u.email, r.nom as role
                FROM utilisateurs u
                JOIN roles r ON u.role_id = r.id
                WHERE u.id = :user_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();

        $user = $stmt->fetch();

        if ($user) {
            return [
                'id' => $user['id'],
                'nom' => $user['nom'],
                'email' => $user['email'],
                'role' => $user['role']
            ];
        }

        return null;

    } catch (PDOException $e) {
        error_log("Erreur lors de la récupération de l'utilisateur : " . $e->getMessage());
        return null;
    }
}

/**
 * Récupérer les notifications d'un utilisateur
 */
function getUserNotifications($pdo, $userId, $limit = 50, $offset = 0) {
    try {
        $sql = "SELECT n.*, 
                       exp.nom as expediteur_nom,
                       exp_role.nom as expediteur_role
                FROM notifications n
                LEFT JOIN utilisateurs exp ON n.expediteur_id = exp.id
                LEFT JOIN roles exp_role ON exp.role_id = exp_role.id
                WHERE n.utilisateur_id = :user_id
                ORDER BY n.date_envoi DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        throw new Exception("Erreur lors de la récupération des notifications : " . $e->getMessage());
    }
}

/**
 * Compter les notifications non lues
 */
function getUnreadNotificationsCount($pdo, $userId) {
    try {
        $sql = "SELECT COUNT(*) as count 
                FROM notifications 
                WHERE utilisateur_id = :user_id AND lu = FALSE";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result['count'];
    } catch (PDOException $e) {
        throw new Exception("Erreur lors du comptage des notifications : " . $e->getMessage());
    }
}

/**
 * Marquer une notification comme lue
 */
function markNotificationAsRead($pdo, $notificationId, $userId) {
    try {
        $sql = "UPDATE notifications 
                SET lu = TRUE, date_lecture = NOW() 
                WHERE id = :notification_id AND utilisateur_id = :user_id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':notification_id', $notificationId, PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            throw new Exception("Notification non trouvée ou vous n'avez pas l'autorisation");
        }
        
        return [
            'success' => true,
            'message' => 'Notification marquée comme lue'
        ];
    } catch (PDOException $e) {
        throw new Exception("Erreur lors du marquage de la notification : " . $e->getMessage());
    }
}

/**
 * Marquer toutes les notifications comme lues
 */
function markAllNotificationsAsRead($pdo, $userId) {
    try {
        $sql = "UPDATE notifications 
                SET lu = TRUE, date_lecture = NOW() 
                WHERE utilisateur_id = :user_id AND lu = FALSE";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        return [
            'success' => true,
            'message' => 'Toutes les notifications marquées comme lues',
            'updated_count' => $stmt->rowCount()
        ];
    } catch (PDOException $e) {
        throw new Exception("Erreur lors du marquage des notifications : " . $e->getMessage());
    }
}

/**
 * Supprimer une notification
 */
function deleteNotification($pdo, $notificationId, $userId) {
    try {
        $sql = "DELETE FROM notifications 
                WHERE id = :notification_id AND utilisateur_id = :user_id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':notification_id', $notificationId, PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            throw new Exception("Notification non trouvée ou vous n'avez pas l'autorisation");
        }
        
        return [
            'success' => true,
            'message' => 'Notification supprimée'
        ];
    } catch (PDOException $e) {
        throw new Exception("Erreur lors de la suppression de la notification : " . $e->getMessage());
    }
}

/**
 * Créer une notification de message automatique
 */
function createMessageNotification($pdo, $messageId, $expediteurId, $destinataireId, $messageContent) {
    try {
        // Récupérer les informations de l'expéditeur
        $sql = "SELECT u.nom, r.nom as role_name 
                FROM utilisateurs u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = :expediteur_id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':expediteur_id', $expediteurId, PDO::PARAM_INT);
        $stmt->execute();
        $expediteur = $stmt->fetch();
        
        if ($expediteur) {
            $titre = "Nouveau message de " . $expediteur['nom'] . " (" . $expediteur['role_name'] . ")";
            $messagePreview = strlen($messageContent) > 100 ? 
                             substr($messageContent, 0, 100) . "..." : 
                             $messageContent;
            
            // Insérer la notification
            $sql = "INSERT INTO notifications (utilisateur_id, type_notification, titre, message, date_envoi, message_id, expediteur_id) 
                    VALUES (:utilisateur_id, 'message', :titre, :message, NOW(), :message_id, :expediteur_id)";
            
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':utilisateur_id', $destinataireId, PDO::PARAM_INT);
            $stmt->bindParam(':titre', $titre, PDO::PARAM_STR);
            $stmt->bindParam(':message', $messagePreview, PDO::PARAM_STR);
            $stmt->bindParam(':message_id', $messageId, PDO::PARAM_INT);
            $stmt->bindParam(':expediteur_id', $expediteurId, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'notification_id' => $pdo->lastInsertId(),
                'message' => 'Notification créée'
            ];
        }
        
        throw new Exception("Expéditeur non trouvé");
        
    } catch (PDOException $e) {
        throw new Exception("Erreur lors de la création de la notification : " . $e->getMessage());
    }
}

// ============================================================================
// GESTION DES REQUÊTES HTTP
// ============================================================================

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents("php://input"), true);

try {
    // Authentification obligatoire
    $user = getAuthenticatedUser($pdo);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Authentification requise'
        ]);
        exit();
    }
    
    switch ($method) {
        case 'GET':
            handleGetRequest($pdo, $user);
            break;
            
        case 'POST':
            handlePostRequest($pdo, $user, $input);
            break;
            
        case 'PUT':
            handlePutRequest($pdo, $user, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($pdo, $user, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Méthode HTTP non autorisée'
            ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Gestion des requêtes GET
 */
function handleGetRequest($pdo, $user) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
            $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
            
            $notifications = getUserNotifications($pdo, $user['id'], $limit, $offset);
            echo json_encode([
                'success' => true,
                'data' => $notifications
            ]);
            break;
            
        case 'count_unread':
            $count = getUnreadNotificationsCount($pdo, $user['id']);
            echo json_encode([
                'success' => true,
                'unread_count' => $count
            ]);
            break;
            
        default:
            throw new Exception("Action non reconnue : $action");
    }
}

/**
 * Gestion des requêtes POST (création de notification)
 */
function handlePostRequest($pdo, $user, $input) {
    if (!isset($input['message_id']) || !isset($input['destinataire_id']) || !isset($input['message_content'])) {
        throw new Exception("Paramètres requis : message_id, destinataire_id, message_content");
    }
    
    $result = createMessageNotification(
        $pdo, 
        $input['message_id'], 
        $user['id'], 
        $input['destinataire_id'], 
        $input['message_content']
    );
    
    echo json_encode($result);
}

/**
 * Gestion des requêtes PUT (marquage comme lu)
 */
function handlePutRequest($pdo, $user, $input) {
    $action = $input['action'] ?? 'mark_read';
    
    switch ($action) {
        case 'mark_read':
            if (!isset($input['notification_id'])) {
                throw new Exception("ID de notification requis");
            }
            
            $result = markNotificationAsRead($pdo, $input['notification_id'], $user['id']);
            echo json_encode($result);
            break;
            
        case 'mark_all_read':
            $result = markAllNotificationsAsRead($pdo, $user['id']);
            echo json_encode($result);
            break;
            
        default:
            throw new Exception("Action non reconnue : $action");
    }
}

/**
 * Gestion des requêtes DELETE (suppression de notification)
 */
function handleDeleteRequest($pdo, $user, $input) {
    if (!isset($input['notification_id'])) {
        throw new Exception("ID de notification requis");
    }
    
    $result = deleteNotification($pdo, $input['notification_id'], $user['id']);
    echo json_encode($result);
}
