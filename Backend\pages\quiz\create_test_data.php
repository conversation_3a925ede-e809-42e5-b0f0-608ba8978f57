<?php
header('Content-Type: text/html; charset=utf-8');

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Erreur de connexion à la base de données: ' . $e->getMessage());
}

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Création de Données de Test - Quiz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #bee5eb; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 Création de Données de Test - Quiz</h1>";

try {
    // Vérifier si la table Quiz existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'Quiz'");
    if (!$stmt->fetch()) {
        // Créer la table Quiz
        $createTable = "
            CREATE TABLE Quiz (
                id INT AUTO_INCREMENT PRIMARY KEY,
                devoir_id INT,
                question TEXT,
                reponse_correcte TEXT,
                FOREIGN KEY (devoir_id) REFERENCES Devoirs(id)
            )
        ";
        $pdo->exec($createTable);
        echo "<p class='success'>✅ Table Quiz créée avec succès</p>";
    } else {
        echo "<p class='info'>ℹ️ Table Quiz existe déjà</p>";
    }
    
    // Vérifier s'il y a des devoirs disponibles
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM Devoirs");
    $devoirsCount = $stmt->fetch()['count'];
    
    if ($devoirsCount == 0) {
        // Créer quelques devoirs de test
        echo "<h3>📚 Création de devoirs de test</h3>";
        
        // Vérifier/créer les matières
        $matieres = [
            ['nom' => 'Mathématiques', 'description' => 'Matière de mathématiques'],
            ['nom' => 'Français', 'description' => 'Matière de français'],
            ['nom' => 'Sciences', 'description' => 'Matière de sciences']
        ];
        
        foreach ($matieres as $matiere) {
            $stmt = $pdo->prepare("SELECT id FROM Matieres WHERE nom = ?");
            $stmt->execute([$matiere['nom']]);
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Matieres (nom, description) VALUES (?, ?)");
                $stmt->execute([$matiere['nom'], $matiere['description']]);
                echo "<p class='success'>✅ Matière créée: {$matiere['nom']}</p>";
            }
        }
        
        // Vérifier/créer les classes
        $classes = [
            ['nom' => '6ème A', 'niveau' => '6ème'],
            ['nom' => '5ème B', 'niveau' => '5ème'],
            ['nom' => '4ème C', 'niveau' => '4ème']
        ];
        
        foreach ($classes as $classe) {
            $stmt = $pdo->prepare("SELECT id FROM Classes WHERE nom = ?");
            $stmt->execute([$classe['nom']]);
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO Classes (nom, niveau) VALUES (?, ?)");
                $stmt->execute([$classe['nom'], $classe['niveau']]);
                echo "<p class='success'>✅ Classe créée: {$classe['nom']}</p>";
            }
        }
        
        // Créer des devoirs de test
        $devoirs = [
            [
                'titre' => 'Contrôle Algèbre',
                'description' => 'Évaluation sur les équations du premier degré',
                'date_remise' => '2024-02-15',
                'matiere' => 'Mathématiques',
                'classe' => '6ème A'
            ],
            [
                'titre' => 'Dictée et Grammaire',
                'description' => 'Évaluation orthographe et analyse grammaticale',
                'date_remise' => '2024-02-20',
                'matiere' => 'Français',
                'classe' => '5ème B'
            ],
            [
                'titre' => 'Sciences Naturelles',
                'description' => 'Évaluation sur le système digestif',
                'date_remise' => '2024-02-25',
                'matiere' => 'Sciences',
                'classe' => '4ème C'
            ]
        ];
        
        foreach ($devoirs as $devoir) {
            // Récupérer les IDs
            $stmt = $pdo->prepare("SELECT id FROM Matieres WHERE nom = ?");
            $stmt->execute([$devoir['matiere']]);
            $matiere_id = $stmt->fetch()['id'];
            
            $stmt = $pdo->prepare("SELECT id FROM Classes WHERE nom = ?");
            $stmt->execute([$devoir['classe']]);
            $classe_id = $stmt->fetch()['id'];
            
            $stmt = $pdo->prepare("
                INSERT INTO Devoirs (titre, description, date_remise, matiere_id, classe_id) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $devoir['titre'],
                $devoir['description'],
                $devoir['date_remise'],
                $matiere_id,
                $classe_id
            ]);
            echo "<p class='success'>✅ Devoir créé: {$devoir['titre']}</p>";
        }
    }
    
    // Créer des quiz de test
    echo "<h3>🧠 Création de quiz de test</h3>";
    
    // Récupérer les devoirs disponibles
    $stmt = $pdo->query("SELECT id, titre FROM Devoirs LIMIT 3");
    $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $quiz_test = [
        [
            'question' => 'Résolvez l\'équation : 2x + 5 = 13',
            'reponse_correcte' => 'x = 4'
        ],
        [
            'question' => 'Quel est le participe passé du verbe "prendre" ?',
            'reponse_correcte' => 'pris'
        ],
        [
            'question' => 'Combien d\'estomacs possède une vache ?',
            'reponse_correcte' => '4 estomacs (rumen, réseau, feuillet, caillette)'
        ],
        [
            'question' => 'Calculez : 15 × 8 - 20',
            'reponse_correcte' => '100'
        ],
        [
            'question' => 'Conjuguez le verbe "aller" à la première personne du singulier au présent',
            'reponse_correcte' => 'je vais'
        ]
    ];
    
    $quiz_created = 0;
    foreach ($quiz_test as $index => $quiz) {
        if (isset($devoirs[$index % count($devoirs)])) {
            $devoir_id = $devoirs[$index % count($devoirs)]['id'];
            
            // Vérifier si le quiz n'existe pas déjà
            $stmt = $pdo->prepare("SELECT id FROM Quiz WHERE question = ? AND devoir_id = ?");
            $stmt->execute([$quiz['question'], $devoir_id]);
            
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("
                    INSERT INTO Quiz (devoir_id, question, reponse_correcte) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([
                    $devoir_id,
                    $quiz['question'],
                    $quiz['reponse_correcte']
                ]);
                echo "<p class='success'>✅ Quiz créé: " . substr($quiz['question'], 0, 50) . "...</p>";
                $quiz_created++;
            }
        }
    }
    
    // Statistiques finales
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM Quiz");
    $quiz_count = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM Devoirs");
    $devoirs_count = $stmt->fetch()['total'];
    
    echo "<div class='success'>";
    echo "<h3>📊 Statistiques Finales</h3>";
    echo "<p>🧠 <strong>Total quiz :</strong> {$quiz_count}</p>";
    echo "<p>📚 <strong>Total devoirs :</strong> {$devoirs_count}</p>";
    echo "<p>➕ <strong>Quiz créés cette session :</strong> {$quiz_created}</p>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !</h2>";
    echo "<p><strong>✅ Table Quiz configurée</strong></p>";
    echo "<p><strong>✅ Devoirs de test créés</strong></p>";
    echo "<p><strong>✅ Quiz de test ajoutés</strong></p>";
    echo "<p><strong>🚀 L'interface React devrait maintenant afficher des données !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Erreur</h3>";
    echo "<p>Erreur lors de la création des données : " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "    </div>
</body>
</html>";
?>
