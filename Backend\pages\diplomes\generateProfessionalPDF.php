<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

function generateProfessionalDiploma($diplome_id) {
    global $pdo;
    
    try {
        // Récupérer les informations du diplôme
        $stmt = $pdo->prepare("
            SELECT 
                d.id,
                d.titre,
                d.date_obtention,
                d.numero_diplome,
                CONCAT(u.nom, ' ', u.prenom) as etudiant_nom,
                u.email as etudiant_email,
                u.numero_etudiant,
                f.nom as filiere_nom,
                n.nom as niveau_nom,
                c.nom as classe_nom
            FROM diplomes d
            LEFT JOIN etudiants et ON d.etudiant_id = et.id
            LEFT JOIN utilisateurs u ON et.utilisateur_id = u.id
            LEFT JOIN filieres f ON et.filiere_id = f.id
            LEFT JOIN niveaux n ON et.niveau_id = n.id
            LEFT JOIN classes c ON et.classe_id = c.id
            WHERE d.id = :diplome_id
        ");
        
        $stmt->execute(['diplome_id' => $diplome_id]);
        $diplome = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$diplome) {
            throw new Exception("Diplôme non trouvé avec l'ID: $diplome_id");
        }
        
        // Formater la date
        $date_obtention = new DateTime($diplome['date_obtention']);
        $diplome['date_obtention_fr'] = $date_obtention->format('d/m/Y');
        
        // Calculer l'année scolaire
        $annee = $date_obtention->format('Y');
        $mois = $date_obtention->format('n');
        if ($mois >= 9) {
            $annee_scolaire = $annee . '-' . ($annee + 1);
        } else {
            $annee_scolaire = ($annee - 1) . '-' . $annee;
        }
        
    } catch (Exception $e) {
        error_log("Erreur lors de la récupération du diplôme: " . $e->getMessage());
        throw new Exception("Erreur lors de la génération du diplôme: " . $e->getMessage());
    }

    // Générer le HTML avec design ultra-professionnel
    return '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diplôme Professionnel - ' . htmlspecialchars($diplome['etudiant_nom']) . '</title>
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap");
        
        @page {
            size: A4;
            margin: 15mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Inter", sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: #ffffff;
            overflow-x: hidden;
        }
        
        .diploma-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 60px;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: 30px;
            position: relative;
            min-height: 95vh;
            overflow: hidden;
            box-shadow: 
                0 30px 80px rgba(0,0,0,0.12),
                0 0 0 1px rgba(0,0,0,0.05),
                inset 0 1px 0 rgba(255,255,255,0.8);
        }
        
        .diploma-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 3px solid transparent;
            border-radius: 20px;
            background: linear-gradient(45deg, #2563eb, #7c3aed, #dc2626, #ea580c, #2563eb) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
        }
        
        .institution-logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-circle {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                0 15px 35px rgba(30, 64, 175, 0.3),
                0 5px 15px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .logo-circle::before {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #f59e0b, #ef4444, #8b5cf6, #3b82f6, #10b981, #f59e0b);
            border-radius: 50%;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .logo-icon {
            font-size: 48px;
            color: #ffffff;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 2;
        }
        
        .institution-name {
            font-family: "Playfair Display", serif;
            font-size: 42px;
            font-weight: 900;
            color: #1e40af;
            margin-bottom: 15px;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
        }
        
        .diploma-type {
            font-family: "Playfair Display", serif;
            font-size: 28px;
            font-weight: 700;
            color: #dc2626;
            font-style: italic;
            letter-spacing: 1px;
        }
        
        .content {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .certification-text {
            font-size: 20px;
            color: #374151;
            margin-bottom: 40px;
            font-weight: 500;
        }
        
        .student-section {
            margin: 50px 0;
        }
        
        .student-name {
            font-family: "Playfair Display", serif;
            font-size: 48px;
            font-weight: 900;
            color: #1e40af;
            margin-bottom: 40px;
            text-transform: uppercase;
            letter-spacing: 4px;
            position: relative;
            display: inline-block;
        }
        
        .student-name::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f59e0b, #ef4444, #f59e0b);
            border-radius: 2px;
        }
        
        .student-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .detail-item {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .detail-label {
            font-weight: 600;
            color: #1e40af;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }
        
        .detail-value {
            font-size: 16px;
            color: #1f2937;
            font-weight: 500;
        }
        
        .achievement-section {
            margin: 50px 0;
        }
        
        .achievement-title {
            font-size: 24px;
            color: #374151;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .diploma-title-box {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: #ffffff;
            padding: 40px;
            border-radius: 20px;
            font-family: "Playfair Display", serif;
            font-size: 32px;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 3px;
            box-shadow: 
                0 15px 35px rgba(220, 38, 38, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .diploma-title-box::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .date-section {
            margin: 40px 0;
            font-size: 20px;
            color: #374151;
            font-weight: 600;
        }
        
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 80px;
            gap: 60px;
        }
        
        .signature-block {
            flex: 1;
            text-align: center;
        }
        
        .signature-title {
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 50px;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .signature-line {
            border-bottom: 2px solid #374151;
            margin-bottom: 15px;
            height: 50px;
            position: relative;
        }
        
        .signature-note {
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding-top: 30px;
            border-top: 2px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            color: rgba(30, 64, 175, 0.03);
            font-weight: 900;
            pointer-events: none;
            z-index: 1;
            user-select: none;
        }
        
        @media print {
            body {
                background: white !important;
            }
            
            .diploma-container {
                box-shadow: none;
                margin: 0;
                padding: 40px;
                min-height: auto;
            }
            
            .diploma-border {
                border: 2px solid #2563eb;
            }
        }
    </style>
    
    <script>
        var printState = {
            dialogOpened: false,
            completed: false
        };
        
        function closeWindow() {
            try {
                window.close();
            } catch (e) {
                console.log("Cannot close window automatically");
            }
        }
        
        function startPrint() {
            if (printState.dialogOpened) return;
            
            printState.dialogOpened = true;
            console.log("Starting professional diploma print...");
            
            window.print();
            
            window.addEventListener("afterprint", function() {
                console.log("Print completed");
                printState.completed = true;
                setTimeout(closeWindow, 500);
            });
            
            window.addEventListener("focus", function() {
                if (printState.dialogOpened && !printState.completed) {
                    setTimeout(function() {
                        if (!printState.completed) {
                            closeWindow();
                        }
                    }, 1000);
                }
            });
            
            setTimeout(function() {
                if (!printState.completed) {
                    closeWindow();
                }
            }, 30000);
        }
        
        window.addEventListener("load", function() {
            setTimeout(startPrint, 1500);
        });
        
        console.log("Professional diploma print script loaded");
    </script>
</head>
<body>
    <div class="diploma-container">
        <div class="diploma-border"></div>
        <div class="watermark">DIPLÔME</div>
        
        <div class="header">
            <div class="institution-logo">
                <div class="logo-circle">
                    <div class="logo-icon">🎓</div>
                </div>
            </div>
            <h1 class="institution-name">École de Gestion Scolaire</h1>
            <h2 class="diploma-type">Diplôme Officiel</h2>
        </div>
        
        <div class="content">
            <p class="certification-text">
                <strong>Il est certifié par les présentes que</strong>
            </p>
            
            <div class="student-section">
                <div class="student-name">' . htmlspecialchars(strtoupper($diplome['etudiant_nom'])) . '</div>
                
                <div class="student-details">
                    <div class="detail-item">
                        <div class="detail-label">Numéro Étudiant</div>
                        <div class="detail-value">' . htmlspecialchars($diplome['numero_etudiant'] ?? 'N/A') . '</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Email</div>
                        <div class="detail-value">' . htmlspecialchars($diplome['etudiant_email']) . '</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Filière</div>
                        <div class="detail-value">' . htmlspecialchars($diplome['filiere_nom'] ?? 'N/A') . '</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Niveau</div>
                        <div class="detail-value">' . htmlspecialchars($diplome['niveau_nom'] ?? 'N/A') . '</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Classe</div>
                        <div class="detail-value">' . htmlspecialchars($diplome['classe_nom'] ?? 'N/A') . '</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Année Scolaire</div>
                        <div class="detail-value">' . htmlspecialchars($annee_scolaire) . '</div>
                    </div>
                </div>
            </div>
            
            <div class="achievement-section">
                <p class="achievement-title">
                    <strong>a obtenu avec succès le diplôme de :</strong>
                </p>
                
                <div class="diploma-title-box">
                    ' . htmlspecialchars(strtoupper($diplome['titre'])) . '
                </div>
            </div>
            
            <div class="date-section">
                <strong>Délivré le : ' . htmlspecialchars($diplome['date_obtention_fr']) . '</strong>
            </div>
            
            <div class="signatures">
                <div class="signature-block">
                    <div class="signature-title">Le Directeur</div>
                    <div class="signature-line"></div>
                    <div class="signature-note">Signature et cachet officiel</div>
                </div>
                
                <div class="signature-block">
                    <div class="signature-title">Le Responsable Académique</div>
                    <div class="signature-line"></div>
                    <div class="signature-note">Signature et cachet officiel</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>École de Gestion Scolaire</strong></p>
            <p>Document officiel certifié - Toute reproduction non autorisée est strictement interdite</p>
            <p>Généré le ' . date('d/m/Y à H:i') . ' | Numéro de diplôme : ' . htmlspecialchars($diplome['numero_diplome'] ?? 'N/A') . '</p>
        </div>
    </div>
</body>
</html>';
}

// Traitement de la requête
if (isset($_GET['id'])) {
    $diplome_id = intval($_GET['id']);
    
    try {
        echo generateProfessionalDiploma($diplome_id);
    } catch (Exception $e) {
        http_response_code(500);
        echo "<h1>Erreur</h1>";
        echo "<p>Impossible de générer le diplôme : " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><a href='javascript:history.back()'>Retour</a></p>";
    }
} else {
    http_response_code(400);
    echo "<h1>Erreur</h1>";
    echo "<p>ID du diplôme manquant</p>";
    echo "<p><a href='javascript:history.back()'>Retour</a></p>";
}
?>
