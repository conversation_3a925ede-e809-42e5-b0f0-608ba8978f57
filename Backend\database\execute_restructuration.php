<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🏗️ RESTRUCTURATION COMPLÈTE DE LA BASE DE DONNÉES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    // Vérification de confirmation
    $confirm = $_GET['confirm'] ?? '';
    
    if ($confirm !== 'yes') {
        echo "<div class='warning'>";
        echo "<h2>⚠️ ATTENTION - RESTRUCTURATION COMPLÈTE</h2>";
        echo "<p><strong>Cette opération va :</strong></p>";
        echo "<ul>";
        echo "<li>❌ <strong>SUPPRIMER</strong> toutes les tables existantes</li>";
        echo "<li>🗑️ <strong>EFFACER</strong> toutes les données actuelles</li>";
        echo "<li>🏗️ <strong>RECRÉER</strong> une structure propre et cohérente</li>";
        echo "<li>✅ <strong>IMPLÉMENTER</strong> l'architecture rôles → tables spécialisées</li>";
        echo "</ul>";
        echo "<p class='error'><strong>⚠️ CETTE ACTION EST IRRÉVERSIBLE !</strong></p>";
        echo "<p><strong>Assurez-vous d'avoir :</strong></p>";
        echo "<ul>";
        echo "<li>✅ Sauvegardé vos données importantes</li>";
        echo "<li>✅ Confirmé que vous voulez repartir sur une base propre</li>";
        echo "<li>✅ Lu et compris l'architecture qui sera implémentée</li>";
        echo "</ul>";
        echo "<h3>🎯 Architecture qui sera créée :</h3>";
        echo "<pre>";
        echo "UTILISATEURS (table principale)
├── role_id → ROLES
├── Si role = 'admin' → Reste dans utilisateurs uniquement
├── Si role = 'parent' → utilisateurs + PARENTS
├── Si role = 'etudiant' → utilisateurs + ETUDIANTS
└── Si role = 'enseignant' → utilisateurs + ENSEIGNANTS

GARANTIES :
✅ Triggers empêchent insertion utilisateurs avec mauvais rôle
✅ Contraintes de clés étrangères
✅ Index pour performances
✅ Vues pour requêtes facilitées";
        echo "</pre>";
        echo "<p class='info'><strong>Si vous êtes sûr de vouloir procéder :</strong></p>";
        echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 CONFIRMER LA RESTRUCTURATION</a></p>";
        echo "<p><a href='../pages/parents/debug_filtrage.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 Diagnostiquer l'état actuel</a></p>";
        echo "</div>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🚀 DÉBUT DE LA RESTRUCTURATION</h2>";
    echo "<p>Restructuration en cours selon l'architecture cohérente...</p>";
    echo "</div>";
    
    // Lire le fichier SQL
    $sqlFile = __DIR__ . '/restructuration_complete.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("Fichier SQL de restructuration non trouvé : $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Impossible de lire le fichier SQL");
    }
    
    echo "<div class='step'>";
    echo "<h3>📋 Étape 1 : Lecture du script SQL</h3>";
    echo "<p class='success'>✅ Script SQL chargé (" . strlen($sql) . " caractères)</p>";
    echo "</div>";
    
    // Diviser le SQL en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<div class='step'>";
    echo "<h3>⚙️ Étape 2 : Exécution des requêtes</h3>";
    echo "<p class='info'>Nombre de requêtes à exécuter : " . count($queries) . "</p>";
    
    $successCount = 0;
    $errorCount = 0;
    $skipCount = 0;
    
    foreach ($queries as $index => $query) {
        $query = trim($query);
        
        // Ignorer les commentaires et requêtes vides
        if (empty($query) || 
            strpos($query, '--') === 0 || 
            strpos($query, '/*') === 0 ||
            strpos($query, 'DELIMITER') === 0) {
            $skipCount++;
            continue;
        }
        
        try {
            $stmt = $pdo->prepare($query);
            $result = $stmt->execute();
            
            if ($result) {
                $successCount++;
                echo "<p class='success'>✅ Requête " . ($index + 1) . " : " . substr($query, 0, 50) . "...</p>";
            } else {
                $errorCount++;
                echo "<p class='error'>❌ Requête " . ($index + 1) . " échouée : " . substr($query, 0, 50) . "...</p>";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p class='error'>❌ Erreur requête " . ($index + 1) . " : " . $e->getMessage() . "</p>";
            echo "<p style='color: #666; font-size: 0.9em;'>Requête : " . substr($query, 0, 100) . "...</p>";
        }
    }
    
    echo "<h4>📊 Résumé de l'exécution :</h4>";
    echo "<ul>";
    echo "<li class='success'>✅ Requêtes réussies : $successCount</li>";
    echo "<li class='error'>❌ Requêtes échouées : $errorCount</li>";
    echo "<li class='info'>⏭️ Requêtes ignorées : $skipCount</li>";
    echo "</ul>";
    echo "</div>";
    
    // Vérification de la structure créée
    echo "<div class='step'>";
    echo "<h3>🔍 Étape 3 : Vérification de la structure créée</h3>";
    
    // Vérifier les tables principales
    $tables = ['roles', 'utilisateurs', 'parents', 'etudiants', 'enseignants'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✅ Table '$table' créée</p>";
                
                // Compter les enregistrements
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $countStmt->fetch()['count'];
                echo "<p style='margin-left: 20px; color: #666;'>📊 $count enregistrement(s)</p>";
            } else {
                echo "<p class='error'>❌ Table '$table' manquante</p>";
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur vérification table '$table' : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Vérification des rôles
    echo "<div class='step'>";
    echo "<h3>👥 Étape 4 : Vérification des rôles</h3>";
    
    try {
        $stmt = $pdo->query("SELECT id, nom, description FROM roles ORDER BY id");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($roles) > 0) {
            echo "<p class='success'>✅ " . count($roles) . " rôle(s) créé(s)</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th><th>Description</th></tr>";
            foreach ($roles as $role) {
                echo "<tr>";
                echo "<td>{$role['id']}</td>";
                echo "<td><strong>{$role['nom']}</strong></td>";
                echo "<td>{$role['description']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Aucun rôle trouvé</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification rôles : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Vérification des triggers
    echo "<div class='step'>";
    echo "<h3>⚡ Étape 5 : Vérification des triggers</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TRIGGERS");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($triggers) > 0) {
            echo "<p class='success'>✅ " . count($triggers) . " trigger(s) créé(s)</p>";
            echo "<table>";
            echo "<tr><th>Trigger</th><th>Event</th><th>Table</th></tr>";
            foreach ($triggers as $trigger) {
                echo "<tr>";
                echo "<td>{$trigger['Trigger']}</td>";
                echo "<td>{$trigger['Event']}</td>";
                echo "<td>{$trigger['Table']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ Aucun trigger trouvé</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification triggers : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎯 RESTRUCTURATION TERMINÉE</h3>";
    
    if ($errorCount === 0) {
        echo "<p class='success' style='font-size: 18px;'>🎉 SUCCÈS COMPLET !</p>";
        echo "<p>La base de données a été restructurée avec succès selon l'architecture cohérente.</p>";
        
        echo "<h4>🏗️ Architecture implémentée :</h4>";
        echo "<ul>";
        echo "<li>✅ <strong>Table utilisateurs</strong> : Base avec role_id</li>";
        echo "<li>✅ <strong>Table parents</strong> : Uniquement utilisateurs avec rôle 'parent'</li>";
        echo "<li>✅ <strong>Table etudiants</strong> : Uniquement utilisateurs avec rôle 'etudiant'</li>";
        echo "<li>✅ <strong>Table enseignants</strong> : Uniquement utilisateurs avec rôle 'enseignant'</li>";
        echo "<li>✅ <strong>Triggers de sécurité</strong> : Empêchent les insertions incorrectes</li>";
        echo "<li>✅ <strong>Contraintes référentielles</strong> : Intégrité garantie</li>";
        echo "</ul>";
        
        echo "<h4>🚀 Prochaines étapes :</h4>";
        echo "<ol>";
        echo "<li><a href='create_test_data.php'>Créer des données de test</a></li>";
        echo "<li><a href='../pages/parents/debug_filtrage.php'>Tester le filtrage</a></li>";
        echo "<li><a href='http://localhost:3000/parents'>Tester l'interface Parents</a></li>";
        echo "</ol>";
        
    } else {
        echo "<p class='warning' style='font-size: 18px;'>⚠️ RESTRUCTURATION PARTIELLE</p>";
        echo "<p>Certaines erreurs sont survenues. Vérifiez les détails ci-dessus.</p>";
        echo "<p>La structure peut nécessiter des ajustements manuels.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
