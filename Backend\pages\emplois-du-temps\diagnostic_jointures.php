<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔍 DIAGNOSTIC JOINTURES - EMPLOIS DU TEMPS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 10px 0; }
    </style>";
    
    echo "<div class='error'>";
    echo "<h2>🔍 DIAGNOSTIC : Problème de Jointures</h2>";
    echo "<p>Vérification des données et des relations entre tables</p>";
    echo "</div>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    // 1. Vérification des données dans chaque table
    echo "<div class='step'>";
    echo "<h3>📊 1. Vérification des Données dans Chaque Table</h3>";
    
    // EmploisDuTemps
    echo "<h4>📅 Table EmploisDuTemps :</h4>";
    try {
        $stmt = $pdo->query("SELECT * FROM EmploisDuTemps ORDER BY id DESC LIMIT 5");
        $emplois = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($emplois)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Classe ID</th><th>Jour</th><th>Heure Début</th><th>Heure Fin</th><th>Matière ID</th><th>Enseignant ID</th></tr>";
            foreach ($emplois as $emploi) {
                echo "<tr>";
                echo "<td>{$emploi['id']}</td>";
                echo "<td>{$emploi['classe_id']}</td>";
                echo "<td>{$emploi['jour']}</td>";
                echo "<td>{$emploi['heure_debut']}</td>";
                echo "<td>{$emploi['heure_fin']}</td>";
                echo "<td>{$emploi['matiere_id']}</td>";
                echo "<td>{$emploi['enseignant_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p class='success'>✅ " . count($emplois) . " emploi(s) du temps trouvé(s)</p>";
        } else {
            echo "<p class='warning'>⚠️ Aucun emploi du temps trouvé</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur EmploisDuTemps : " . $e->getMessage() . "</p>";
    }
    
    // Classes
    echo "<h4>🏫 Table Classes :</h4>";
    try {
        $stmt = $pdo->query("SELECT * FROM Classes ORDER BY id LIMIT 10");
        $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($classes)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th><th>Niveau</th><th>Description</th></tr>";
            foreach ($classes as $classe) {
                echo "<tr>";
                echo "<td>{$classe['id']}</td>";
                echo "<td>{$classe['nom']}</td>";
                echo "<td>" . ($classe['niveau'] ?? 'N/A') . "</td>";
                echo "<td>" . ($classe['description'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p class='success'>✅ " . count($classes) . " classe(s) trouvée(s)</p>";
        } else {
            echo "<p class='warning'>⚠️ Aucune classe trouvée</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur Classes : " . $e->getMessage() . "</p>";
    }
    
    // Matieres
    echo "<h4>📚 Table Matieres :</h4>";
    try {
        $stmt = $pdo->query("SELECT * FROM Matieres ORDER BY id LIMIT 10");
        $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($matieres)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th><th>Code</th><th>Description</th></tr>";
            foreach ($matieres as $matiere) {
                echo "<tr>";
                echo "<td>{$matiere['id']}</td>";
                echo "<td>{$matiere['nom']}</td>";
                echo "<td>" . ($matiere['code'] ?? 'N/A') . "</td>";
                echo "<td>" . ($matiere['description'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p class='success'>✅ " . count($matieres) . " matière(s) trouvée(s)</p>";
        } else {
            echo "<p class='warning'>⚠️ Aucune matière trouvée</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur Matieres : " . $e->getMessage() . "</p>";
    }
    
    // Enseignants (structure réelle)
    echo "<h4>👨‍🏫 Table Enseignants (structure réelle) :</h4>";
    try {
        $stmt = $pdo->query("
            SELECT e.id, e.utilisateur_id, e.nom_prenom, e.email, e.telephone, e.specialite
            FROM Enseignants e
            ORDER BY e.id LIMIT 10
        ");
        $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($enseignants)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>ID Utilisateur</th><th>Nom Prénom</th><th>Email</th><th>Téléphone</th><th>Spécialité</th></tr>";
            foreach ($enseignants as $enseignant) {
                echo "<tr>";
                echo "<td>{$enseignant['id']}</td>";
                echo "<td>" . ($enseignant['utilisateur_id'] ?? 'NULL') . "</td>";
                echo "<td>" . ($enseignant['nom_prenom'] ?? 'N/A') . "</td>";
                echo "<td>" . ($enseignant['email'] ?? 'N/A') . "</td>";
                echo "<td>" . ($enseignant['telephone'] ?? 'N/A') . "</td>";
                echo "<td>" . ($enseignant['specialite'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p class='success'>✅ " . count($enseignants) . " enseignant(s) trouvé(s)</p>";
        } else {
            echo "<p class='warning'>⚠️ Aucun enseignant trouvé</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur Enseignants : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Test des jointures
    echo "<div class='step'>";
    echo "<h3>🔗 2. Test des Jointures</h3>";
    
    echo "<h4>🧪 Test Jointure Complète (structure corrigée) :</h4>";
    try {
        $stmt = $pdo->query("
            SELECT edt.id, edt.classe_id, edt.jour, edt.heure_debut, edt.heure_fin,
                   edt.matiere_id, edt.enseignant_id,
                   c.nom as classe_nom,
                   m.nom as matiere_nom,
                   e.nom_prenom as enseignant_nom
            FROM EmploisDuTemps edt
            LEFT JOIN Classes c ON edt.classe_id = c.id
            LEFT JOIN Matieres m ON edt.matiere_id = m.id
            LEFT JOIN Enseignants e ON edt.enseignant_id = e.id
            ORDER BY edt.id DESC
            LIMIT 5
        ");
        $jointures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($jointures)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Jour</th><th>Heure</th><th>Classe ID</th><th>Classe Nom</th><th>Matière ID</th><th>Matière Nom</th><th>Enseignant ID</th><th>Enseignant Nom</th></tr>";
            foreach ($jointures as $j) {
                echo "<tr>";
                echo "<td>{$j['id']}</td>";
                echo "<td>{$j['jour']}</td>";
                echo "<td>{$j['heure_debut']}-{$j['heure_fin']}</td>";
                echo "<td>{$j['classe_id']}</td>";
                echo "<td style='background:" . ($j['classe_nom'] ? '#d4edda' : '#f8d7da') . "'>" . ($j['classe_nom'] ?? 'NULL') . "</td>";
                echo "<td>{$j['matiere_id']}</td>";
                echo "<td style='background:" . ($j['matiere_nom'] ? '#d4edda' : '#f8d7da') . "'>" . ($j['matiere_nom'] ?? 'NULL') . "</td>";
                echo "<td>{$j['enseignant_id']}</td>";
                echo "<td style='background:" . ($j['enseignant_nom'] ? '#d4edda' : '#f8d7da') . "'>" . ($j['enseignant_nom'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Analyser les résultats
            $problemes = [];
            foreach ($jointures as $j) {
                if (!$j['classe_nom']) $problemes[] = "Classe ID {$j['classe_id']} non trouvée";
                if (!$j['matiere_nom']) $problemes[] = "Matière ID {$j['matiere_id']} non trouvée";
                if (!$j['enseignant_nom']) $problemes[] = "Enseignant ID {$j['enseignant_id']} non trouvé";
            }
            
            if (empty($problemes)) {
                echo "<p class='success'>✅ Toutes les jointures fonctionnent parfaitement !</p>";
            } else {
                echo "<p class='error'>❌ Problèmes détectés :</p>";
                echo "<ul>";
                foreach (array_unique($problemes) as $probleme) {
                    echo "<li class='error'>$probleme</li>";
                }
                echo "</ul>";
            }
            
        } else {
            echo "<p class='warning'>⚠️ Aucun résultat de jointure</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur jointure : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. Vérification des IDs orphelins
    echo "<div class='step'>";
    echo "<h3>🔍 3. Vérification des IDs Orphelins</h3>";
    
    // Classes orphelines
    try {
        $stmt = $pdo->query("
            SELECT DISTINCT edt.classe_id 
            FROM EmploisDuTemps edt 
            LEFT JOIN Classes c ON edt.classe_id = c.id 
            WHERE c.id IS NULL
        ");
        $classes_orphelines = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($classes_orphelines)) {
            echo "<p class='error'>❌ Classes orphelines (IDs dans EmploisDuTemps mais pas dans Classes) : " . implode(', ', $classes_orphelines) . "</p>";
        } else {
            echo "<p class='success'>✅ Aucune classe orpheline</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur vérification classes : " . $e->getMessage() . "</p>";
    }
    
    // Matières orphelines
    try {
        $stmt = $pdo->query("
            SELECT DISTINCT edt.matiere_id 
            FROM EmploisDuTemps edt 
            LEFT JOIN Matieres m ON edt.matiere_id = m.id 
            WHERE m.id IS NULL
        ");
        $matieres_orphelines = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($matieres_orphelines)) {
            echo "<p class='error'>❌ Matières orphelines (IDs dans EmploisDuTemps mais pas dans Matieres) : " . implode(', ', $matieres_orphelines) . "</p>";
        } else {
            echo "<p class='success'>✅ Aucune matière orpheline</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur vérification matières : " . $e->getMessage() . "</p>";
    }
    
    // Enseignants orphelins
    try {
        $stmt = $pdo->query("
            SELECT DISTINCT edt.enseignant_id 
            FROM EmploisDuTemps edt 
            LEFT JOIN Enseignants e ON edt.enseignant_id = e.id 
            WHERE e.id IS NULL
        ");
        $enseignants_orphelins = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($enseignants_orphelins)) {
            echo "<p class='error'>❌ Enseignants orphelins (IDs dans EmploisDuTemps mais pas dans Enseignants) : " . implode(', ', $enseignants_orphelins) . "</p>";
        } else {
            echo "<p class='success'>✅ Aucun enseignant orphelin</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur vérification enseignants : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 4. Solution recommandée
    echo "<div class='step'>";
    echo "<h3>🔧 4. Solution Recommandée</h3>";
    
    echo "<div class='highlight'>";
    echo "<h4>🎯 Plan de Correction :</h4>";
    echo "<ol>";
    echo "<li><strong>Corriger les IDs orphelins</strong> identifiés ci-dessus</li>";
    echo "<li><strong>Utiliser COALESCE</strong> dans les requêtes pour gérer les valeurs NULL</li>";
    echo "<li><strong>Ajouter des contraintes</strong> de clés étrangères pour éviter les futurs orphelins</li>";
    echo "<li><strong>Créer une API corrigée</strong> avec jointures robustes</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
