<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Vérifier l'authentification
$headers = getallheaders();
if (!isset($headers['Authorization'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Token d\'authentification requis']);
    exit();
}

// Récupérer les paramètres
$filename = $_GET['file'] ?? '';
$devoir_id = $_GET['devoir_id'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    echo json_encode(['error' => 'Nom de fichier requis']);
    exit();
}

// Vérifier que le devoir existe
if (!empty($devoir_id)) {
    try {
        $stmt = $pdo->prepare("SELECT id, titre FROM Devoirs WHERE id = :id AND fichier_pdf = :filename");
        $stmt->execute(['id' => $devoir_id, 'filename' => $filename]);
        $devoir = $stmt->fetch();
        
        if (!$devoir) {
            http_response_code(404);
            echo json_encode(['error' => 'Devoir non trouvé']);
            exit();
        }
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur de base de données']);
        exit();
    }
}

// Chemin du fichier
$uploadDir = '../../uploads/devoirs/';
$filePath = $uploadDir . $filename;

// Vérifier que le fichier existe
if (!file_exists($filePath)) {
    error_log("File not found: " . $filePath);
    http_response_code(404);
    echo json_encode(['error' => 'Fichier non trouvé']);
    exit();
}

// Vérifier que c'est bien un PDF
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $filePath);
finfo_close($finfo);

if ($mimeType !== 'application/pdf') {
    error_log("Invalid file type: " . $mimeType);
    http_response_code(400);
    echo json_encode(['error' => 'Type de fichier invalide']);
    exit();
}

// Préparer le téléchargement
$fileSize = filesize($filePath);
$fileName = basename($filePath);

// Headers pour le téléchargement
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Content-Length: ' . $fileSize);
header('Cache-Control: must-revalidate');
header('Pragma: public');

// Envoyer le fichier
readfile($filePath);

// Log du téléchargement
error_log("File downloaded: " . $filename . " (Size: " . $fileSize . " bytes)");
exit();
?>
