@echo off
echo Installation des polyfills pour résoudre les erreurs webpack...

npm install --save-dev @craco/craco
npm install --save process buffer crypto-browserify stream-browserify assert stream-http https-browserify os-browserify url

echo.
echo Polyfills installés avec succès !
echo.
echo Pour utiliser la nouvelle configuration, modifiez package.json :
echo "start": "craco start"
echo "build": "craco build"
echo "test": "craco test"
echo.
pause
