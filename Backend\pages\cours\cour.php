<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);

    if (
        !isset($data['matiere_id']) || !is_numeric($data['matiere_id']) ||
        !isset($data['classe_id']) || !is_numeric($data['classe_id']) ||
        !isset($data['titre']) || empty(trim($data['titre']))
    ) {
        echo json_encode(['error' => 'Les champs matiere_id, classe_id et titre sont requis.']);
        exit;
    }

    $matiere_id = intval($data['matiere_id']);
    $classe_id = intval($data['classe_id']);
    $titre = trim($data['titre']);
    $description = isset($data['description']) ? trim($data['description']) : null;
    $fichier_url = isset($data['fichier_url']) ? trim($data['fichier_url']) : null;
    $date_publication = isset($data['date_publication']) ? $data['date_publication'] : date('Y-m-d');

    try {
        $stmt = $pdo->prepare("INSERT INTO Cours (matiere_id, classe_id, titre, description, fichier_url, date_publication) VALUES (:matiere_id, :classe_id, :titre, :description, :fichier_url, :date_publication)");
        $stmt->execute([
            'matiere_id' => $matiere_id,
            'classe_id' => $classe_id,
            'titre' => $titre,
            'description' => $description,
            'fichier_url' => $fichier_url,
            'date_publication' => $date_publication
        ]);
        echo json_encode(['success' => true, 'message' => 'Cours ajouté avec succès', 'id' => $pdo->lastInsertId()]);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("
            SELECT Cours.id, Cours.titre, Cours.description, Cours.fichier_url, Cours.date_publication,
                   Cours.matiere_id, Matieres.nom AS matiere_nom,
                   Cours.classe_id, Classes.nom AS classe_nom
            FROM Cours
            LEFT JOIN Matieres ON Cours.matiere_id = Matieres.id
            LEFT JOIN Classes ON Cours.classe_id = Classes.id
            ORDER BY Cours.id DESC
        ");
        $cours = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($cours);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);

    if (
        !isset($data['id']) || !is_numeric($data['id']) ||
        !isset($data['matiere_id']) || !is_numeric($data['matiere_id']) ||
        !isset($data['classe_id']) || !is_numeric($data['classe_id']) ||
        !isset($data['titre']) || empty(trim($data['titre']))
    ) {
        echo json_encode(['error' => 'Tous les champs (id, matiere_id, classe_id, titre) sont requis']);
        exit;
    }

    $id = intval($data['id']);
    $matiere_id = intval($data['matiere_id']);
    $classe_id = intval($data['classe_id']);
    $titre = trim($data['titre']);
    $description = isset($data['description']) ? trim($data['description']) : null;
    $fichier_url = isset($data['fichier_url']) ? trim($data['fichier_url']) : null;
    $date_publication = isset($data['date_publication']) ? $data['date_publication'] : date('Y-m-d');

    try {
        $stmt = $pdo->prepare("UPDATE Cours SET matiere_id = :matiere_id, classe_id = :classe_id, titre = :titre, description = :description, fichier_url = :fichier_url, date_publication = :date_publication WHERE id = :id");
        $stmt->execute([
            'matiere_id' => $matiere_id,
            'classe_id' => $classe_id,
            'titre' => $titre,
            'description' => $description,
            'fichier_url' => $fichier_url,
            'date_publication' => $date_publication,
            'id' => $id
        ]);
        echo json_encode(['success' => true, 'message' => 'Cours mis à jour avec succès']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);

    if (!isset($data['id'])) {
        echo json_encode(['error' => 'ID du cours requis']);
        exit;
    }

    $id = intval($data['id']);

    try {
        $stmt = $pdo->prepare("DELETE FROM Cours WHERE id = :id");
        $stmt->execute(['id' => $id]);
        echo json_encode(['success' => true, 'message' => 'Cours supprimé avec succès']);
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
