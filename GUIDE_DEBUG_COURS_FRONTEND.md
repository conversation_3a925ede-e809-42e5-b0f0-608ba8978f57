# 🔧 Guide de Debug - Problèmes d'Envoi Frontend Cours

## 🎯 **Problème Identifié**
Les champs `classe` et `date_publication` restent vides lors de l'ajout, indiquant un problème de transmission des données entre React et PHP.

## ❌ **Analyse du Problème**

### **Problème Principal**
Vous mentionnez que les champs `classe` et `date_publication` restent vides, mais selon la structure BDD réelle :
- **`classe_id`** : N'existe PAS dans la table `cours`
- **`date_publication`** : N'existe PAS, remplacé par `date_ajout` avec `DEFAULT CURRENT_DATE`

### **Structure BDD Réelle vs Attendue**
```sql
-- Structure RÉELLE
CREATE TABLE cours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    fichier_pdf VARCHAR(255),
    taille_fichier VARCHAR(50),
    date_ajout DATE DEFAULT CURRENT_DATE,  -- ✅ Automatique
    matiere_id INT,                        -- ✅ Seule relation
    FOREIGN KEY (matiere_id) REFERENCES matieres(id)
);

-- Ce qui était SUPPOSÉ (❌)
classe_id INT,           -- N'existe PAS
date_publication DATE,   -- N'existe PAS
```

## ✅ **Corrections Appliquées**

### **1. Frontend Corrigé (Cours.js)**

#### **FormData Simplifié**
```javascript
// Avant (❌)
const [formData, setFormData] = useState({
    titre: '',
    description: '',
    fichier_pdf: null,
    date_publication: '',  // N'existe pas en BDD
    matiere_id: '',
    classe_id: ''          // N'existe pas en BDD
});

// Après (✅)
const [formData, setFormData] = useState({
    titre: '',
    description: '',
    fichier_pdf: null,
    matiere_id: ''         // Seuls champs nécessaires
});
```

#### **Envoi de Données Corrigé**
```javascript
// Données envoyées au backend
const formDataToSend = new FormData();
formDataToSend.append('titre', formData.titre.trim());
formDataToSend.append('description', formData.description.trim());
formDataToSend.append('matiere_id', formData.matiere_id);
// PAS de classe_id ni date_publication

if (formData.fichier_pdf) {
    formDataToSend.append('fichier_pdf', formData.fichier_pdf);
}
```

#### **Validation Côté Client**
```javascript
// Validation avant envoi
if (!formData.titre.trim()) {
    Swal.fire('Erreur', 'Le titre du cours est requis', 'error');
    return;
}

if (!formData.matiere_id) {
    Swal.fire('Erreur', 'Veuillez sélectionner une matière', 'error');
    return;
}

if (!editingCours && !formData.fichier_pdf) {
    Swal.fire('Erreur', 'Veuillez sélectionner un fichier PDF', 'error');
    return;
}
```

### **2. Logs de Debug Ajoutés**

#### **Frontend - Logs Détaillés**
```javascript
console.log('🔄 Envoi requête cours:', { 
    method: editingCours ? 'PUT' : 'POST', 
    url,
    titre: formData.titre,
    matiere_id: formData.matiere_id,
    hasFile: !!formData.fichier_pdf,
    fileSize: formData.fichier_pdf ? formData.fichier_pdf.size : 0
});

console.log('✅ Réponse complète:', {
    status: response.status,
    data: response.data,
    headers: response.headers
});
```

#### **Gestion d'Erreurs Améliorée**
```javascript
console.error('❌ Erreur complète:', {
    message: error.message,
    response: error.response?.data,
    status: error.response?.status,
    config: {
        method: error.config?.method,
        url: error.config?.url,
        data: error.config?.data
    }
});
```

### **3. Validation Fichier en Temps Réel**
```javascript
onChange={(e) => {
    const file = e.target.files[0];
    if (file) {
        // Validation côté client
        if (file.type !== 'application/pdf') {
            Swal.fire('Erreur', 'Seuls les fichiers PDF sont acceptés', 'error');
            e.target.value = '';
            return;
        }
        if (file.size > 10 * 1024 * 1024) {
            Swal.fire('Erreur', 'Le fichier ne doit pas dépasser 10MB', 'error');
            e.target.value = '';
            return;
        }
        console.log('📁 Fichier sélectionné:', {
            name: file.name,
            size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
            type: file.type
        });
    }
    setFormData({...formData, fichier_pdf: file});
}}
```

## 🧪 **Outils de Debug Créés**

### **1. Bouton Test API**
- **Fonction** : `testAPI()` pour vérifier la connexion
- **Interface** : Bouton "🧪 Test API" dans l'en-tête
- **Utilité** : Tester la connexion sans créer de cours

### **2. Script de Debug Backend**
- **Fichier** : `Backend/pages/cours/test_cours_debug.php`
- **Usage** : `php test_cours_debug.php`
- **Tests** :
  - Structure BDD
  - Connexion API
  - Matières disponibles
  - Permissions dossier uploads
  - Logs d'erreur PHP

## 🔍 **Procédure de Debug**

### **Étape 1 : Vérifier la Structure BDD**
```bash
cd Backend/pages/cours
php test_cours_debug.php
```

### **Étape 2 : Tester l'API depuis l'Interface**
1. Ouvrir la page Cours
2. Cliquer sur "🧪 Test API"
3. Vérifier la console pour les logs

### **Étape 3 : Tester l'Ajout avec Logs**
1. Ouvrir la console du navigateur (F12)
2. Cliquer "Nouveau Cours"
3. Remplir le formulaire :
   - **Titre** : "Test Debug"
   - **Description** : "Test"
   - **Fichier PDF** : Sélectionner un PDF
   - **Matière** : Choisir une matière
4. Cliquer "Créer"
5. Analyser les logs dans la console

### **Étape 4 : Vérifier les Logs Backend**
```bash
# Logs PHP (selon configuration)
tail -f /var/log/php_errors.log

# Ou dans le script de debug
php test_cours_debug.php
```

## 🎯 **Points de Vérification**

### **Frontend**
- [ ] FormData contient uniquement : `titre`, `description`, `matiere_id`, `fichier_pdf`
- [ ] Pas de `classe_id` ni `date_publication` envoyés
- [ ] Validation côté client fonctionne
- [ ] Logs console affichent les bonnes données

### **Backend**
- [ ] Table `cours` a la structure correcte
- [ ] Matières existent dans la table `matieres`
- [ ] Dossier `uploads/cours/` existe et est accessible en écriture
- [ ] Logs PHP ne montrent pas d'erreurs

### **Réseau**
- [ ] Requête POST/PUT envoyée avec `Content-Type: multipart/form-data`
- [ ] Headers `Authorization` présents
- [ ] Réponse HTTP 200 avec `{success: true}`

## 🚀 **Solutions Probables**

### **Si l'API ne répond pas**
1. Vérifier l'URL : `http://localhost/Project_PFE/Backend/pages/cours/cours.php`
2. Vérifier que le serveur PHP fonctionne
3. Vérifier les permissions du dossier Backend

### **Si les données ne sont pas sauvées**
1. Vérifier la structure de la table `cours`
2. Vérifier que la matière existe
3. Vérifier les permissions du dossier uploads

### **Si le fichier n'est pas uploadé**
1. Vérifier `upload_max_filesize` et `post_max_size` en PHP
2. Vérifier les permissions du dossier `uploads/cours/`
3. Vérifier que le fichier est bien un PDF

## 📋 **Checklist de Validation**

### **Avant de Tester**
- [ ] Structure BDD vérifiée avec le script de debug
- [ ] Matières créées dans la table `matieres`
- [ ] Dossier `uploads/cours/` créé avec permissions 777
- [ ] Serveur PHP démarré

### **Pendant le Test**
- [ ] Console ouverte pour voir les logs
- [ ] Fichier PDF valide sélectionné (< 10MB)
- [ ] Matière sélectionnée dans le dropdown
- [ ] Titre rempli

### **Après le Test**
- [ ] Vérifier la réponse dans la console
- [ ] Vérifier l'ajout en BDD
- [ ] Vérifier la création du fichier dans uploads/
- [ ] Vérifier les logs d'erreur PHP

**🎯 Avec ces corrections et outils de debug, vous devriez pouvoir identifier et résoudre le problème d'envoi des données !**

### **Rappel Important**
Les champs `classe_id` et `date_publication` ne doivent PAS être envoyés car ils n'existent pas dans la structure BDD réelle. Seuls `titre`, `description`, `matiere_id` et `fichier_pdf` sont nécessaires.
