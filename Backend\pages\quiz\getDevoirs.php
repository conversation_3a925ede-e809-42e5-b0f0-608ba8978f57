<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

try {
    // Récupérer tous les devoirs avec leurs informations complètes
    $sql = "
        SELECT 
            d.id,
            d.titre,
            d.description,
            d.date_remise,
            d.matiere_id,
            d.classe_id,
            m.nom as matiere_nom,
            c.nom as classe_nom,
            CONCAT(d.titre, ' - ', m.nom, ' (', c.nom, ')') as devoir_complet
        FROM Devoirs d
        LEFT JOIN Matieres m ON d.matiere_id = m.id
        LEFT JOIN Classes c ON d.classe_id = c.id
        ORDER BY d.date_remise DESC, d.titre
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'devoirs' => $devoirs,
        'count' => count($devoirs)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur serveur: ' . $e->getMessage()
    ]);
}
?>
