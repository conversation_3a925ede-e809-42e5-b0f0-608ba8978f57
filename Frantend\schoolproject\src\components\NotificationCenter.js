import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { getApiUrl } from '../config/api';
import './NotificationCenter.css';

const NotificationCenter = () => {
    const { user } = useContext(AuthContext);
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [showNotifications, setShowNotifications] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    // Fonction pour faire des requêtes API avec authentification
    const makeAuthenticatedRequest = async (url, options = {}) => {
        let token = localStorage.getItem('token');

        // Si pas de token ou token simple, générer un token basé sur le rôle de l'utilisateur
        if (!token || token.length < 10) {
            const userRole = user && user.role ? user.role.toLowerCase() : 'admin';
            token = `${userRole}_token_${Date.now()}`;
        }

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };

        const finalOptions = { ...defaultOptions, ...options };
        if (options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Erreur HTTP: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('Erreur API:', error);
            throw error;
        }
    };

    // Charger le nombre de notifications non lues
    const loadUnreadCount = async () => {
        try {
            const apiUrl = `${getApiUrl('NOTIFICATIONS')}messages_notifications.php?action=count_unread`;
            const data = await makeAuthenticatedRequest(apiUrl);
            
            if (data.success) {
                setUnreadCount(data.unread_count);
            }
        } catch (error) {
            console.error('Erreur lors du chargement du compteur:', error);
        }
    };

    // Charger les notifications
    const loadNotifications = async () => {
        try {
            setLoading(true);
            const apiUrl = `${getApiUrl('NOTIFICATIONS')}messages_notifications.php?action=list&limit=20`;
            const data = await makeAuthenticatedRequest(apiUrl);
            
            if (data.success) {
                setNotifications(data.data || []);
            } else {
                setError(data.error || 'Erreur lors du chargement des notifications');
            }
        } catch (error) {
            setError('Erreur de connexion: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    // Marquer une notification comme lue
    const markAsRead = async (notificationId) => {
        try {
            const apiUrl = `${getApiUrl('NOTIFICATIONS')}messages_notifications.php`;
            const data = await makeAuthenticatedRequest(apiUrl, {
                method: 'PUT',
                body: JSON.stringify({
                    action: 'mark_read',
                    notification_id: notificationId
                })
            });

            if (data.success) {
                // Mettre à jour l'état local
                setNotifications(prev => 
                    prev.map(notif => 
                        notif.id === notificationId 
                            ? { ...notif, lu: true, date_lecture: new Date().toISOString() }
                            : notif
                    )
                );
                
                // Recharger le compteur
                loadUnreadCount();
            }
        } catch (error) {
            console.error('Erreur lors du marquage:', error);
        }
    };

    // Marquer toutes les notifications comme lues
    const markAllAsRead = async () => {
        try {
            const apiUrl = `${getApiUrl('NOTIFICATIONS')}messages_notifications.php`;
            const data = await makeAuthenticatedRequest(apiUrl, {
                method: 'PUT',
                body: JSON.stringify({
                    action: 'mark_all_read'
                })
            });

            if (data.success) {
                // Mettre à jour l'état local
                setNotifications(prev => 
                    prev.map(notif => ({ 
                        ...notif, 
                        lu: true, 
                        date_lecture: new Date().toISOString() 
                    }))
                );
                
                setUnreadCount(0);
            }
        } catch (error) {
            console.error('Erreur lors du marquage global:', error);
        }
    };

    // Supprimer une notification
    const deleteNotification = async (notificationId) => {
        try {
            const apiUrl = `${getApiUrl('NOTIFICATIONS')}messages_notifications.php`;
            const data = await makeAuthenticatedRequest(apiUrl, {
                method: 'DELETE',
                body: JSON.stringify({
                    notification_id: notificationId
                })
            });

            if (data.success) {
                // Retirer de l'état local
                setNotifications(prev => 
                    prev.filter(notif => notif.id !== notificationId)
                );
                
                // Recharger le compteur
                loadUnreadCount();
            }
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
        }
    };

    // Formater la date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffMinutes = Math.ceil(diffTime / (1000 * 60));
        const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffMinutes < 60) {
            return `Il y a ${diffMinutes} min`;
        } else if (diffHours < 24) {
            return `Il y a ${diffHours}h`;
        } else if (diffDays < 7) {
            return `Il y a ${diffDays}j`;
        } else {
            return date.toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit', 
                year: '2-digit' 
            });
        }
    };

    // Obtenir l'icône selon le type de notification
    const getNotificationIcon = (type) => {
        switch (type) {
            case 'message':
                return '💬';
            case 'system':
                return '⚙️';
            case 'reminder':
                return '⏰';
            case 'absence':
                return '❌';
            case 'retard':
                return '⏰';
            case 'cours':
                return '📚';
            case 'devoir':
                return '📝';
            default:
                return '🔔';
        }
    };

    // Charger les données au montage du composant
    useEffect(() => {
        if (user) {
            loadUnreadCount();
            
            // Recharger le compteur toutes les 30 secondes
            const interval = setInterval(loadUnreadCount, 30000);
            
            return () => clearInterval(interval);
        }
    }, [user]);

    // Charger les notifications quand on ouvre le panneau
    useEffect(() => {
        if (showNotifications && user) {
            loadNotifications();
        }
    }, [showNotifications, user]);

    if (!user) {
        return null;
    }

    return (
        <div className="notification-center">
            {/* Bouton de notification */}
            <button 
                className="notification-button"
                onClick={() => setShowNotifications(!showNotifications)}
                title="Notifications"
            >
                🔔
                {unreadCount > 0 && (
                    <span className="notification-badge">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                )}
            </button>

            {/* Panneau des notifications */}
            {showNotifications && (
                <div className="notification-panel">
                    <div className="notification-header">
                        <h3>🔔 Notifications</h3>
                        <div className="notification-actions">
                            {unreadCount > 0 && (
                                <button 
                                    className="btn-mark-all"
                                    onClick={markAllAsRead}
                                    title="Marquer tout comme lu"
                                >
                                    ✓ Tout lire
                                </button>
                            )}
                            <button 
                                className="btn-close"
                                onClick={() => setShowNotifications(false)}
                            >
                                ✕
                            </button>
                        </div>
                    </div>

                    <div className="notification-content">
                        {loading ? (
                            <div className="notification-loading">
                                <div className="spinner"></div>
                                <p>Chargement...</p>
                            </div>
                        ) : error ? (
                            <div className="notification-error">
                                <p>❌ {error}</p>
                                <button onClick={loadNotifications}>Réessayer</button>
                            </div>
                        ) : notifications.length === 0 ? (
                            <div className="no-notifications">
                                <p>🔕 Aucune notification</p>
                            </div>
                        ) : (
                            <div className="notification-list">
                                {notifications.map(notification => (
                                    <div
                                        key={notification.id}
                                        className={`notification-item ${!notification.lu ? 'unread' : ''}`}
                                    >
                                        <div className="notification-icon">
                                            {getNotificationIcon(notification.type_notification)}
                                        </div>
                                        
                                        <div className="notification-body">
                                            <div className="notification-title">
                                                {notification.titre}
                                            </div>
                                            <div className="notification-message">
                                                {notification.message}
                                            </div>
                                            <div className="notification-meta">
                                                <span className="notification-time">
                                                    {formatDate(notification.date_envoi)}
                                                </span>
                                                {notification.expediteur_nom && (
                                                    <span className="notification-sender">
                                                        de {notification.expediteur_nom}
                                                    </span>
                                                )}
                                            </div>
                                        </div>

                                        <div className="notification-actions-item">
                                            {!notification.lu && (
                                                <button
                                                    className="btn-mark-read"
                                                    onClick={() => markAsRead(notification.id)}
                                                    title="Marquer comme lu"
                                                >
                                                    ✓
                                                </button>
                                            )}
                                            <button
                                                className="btn-delete"
                                                onClick={() => deleteNotification(notification.id)}
                                                title="Supprimer"
                                            >
                                                🗑️
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {notifications.length > 0 && (
                        <div className="notification-footer">
                            <button 
                                className="btn-load-more"
                                onClick={loadNotifications}
                            >
                                🔄 Actualiser
                            </button>
                        </div>
                    )}
                </div>
            )}

            {/* Overlay pour fermer le panneau */}
            {showNotifications && (
                <div 
                    className="notification-overlay"
                    onClick={() => setShowNotifications(false)}
                />
            )}
        </div>
    );
};

export default NotificationCenter;
