<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST API ABSENCES SIMPLIFIÉE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; color: white; text-decoration: none; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🧪 Test de l'API Absences Simplifiée</h2>";
    echo "<p>Vérification que l'API fonctionne correctement pour résoudre l'erreur 'Impossible de charger les absences'</p>";
    echo "</div>";
    
    // 1. Test de connexion à la base de données
    echo "<div class='step'>";
    echo "<h3>🗄️ 1. Test Connexion Base de Données</h3>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
        
        // Vérifier les tables
        $tables = ['Absences', 'Etudiants', 'Utilisateurs', 'Matieres', 'Enseignants'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<p class='info'>📊 Table $table : $count enregistrement(s)</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    echo "</div>";
    
    // 2. Test de l'API GET
    echo "<div class='step'>";
    echo "<h3>📥 2. Test API GET (Récupération des Absences)</h3>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/absences/simple_api.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<h4>🔗 URL testée : <a href='$api_url' target='_blank'>$api_url</a></h4>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API accessible</p>";
            
            $data = json_decode($response, true);
            if ($data !== null) {
                echo "<p class='success'>✅ Réponse JSON valide</p>";
                
                if (is_array($data)) {
                    echo "<p class='success'>✅ Format tableau correct</p>";
                    echo "<p class='info'>📊 Nombre d'absences : " . count($data) . "</p>";
                    
                    if (!empty($data)) {
                        echo "<h5>📋 Première absence (exemple) :</h5>";
                        echo "<div class='json-block'>";
                        echo json_encode($data[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                        echo "</div>";
                        
                        // Vérifier la structure
                        $first = $data[0];
                        $required_fields = ['id', 'etudiant_id', 'date_absence', 'etudiant_nom'];
                        $missing = [];
                        
                        foreach ($required_fields as $field) {
                            if (!isset($first[$field])) {
                                $missing[] = $field;
                            }
                        }
                        
                        if (empty($missing)) {
                            echo "<p class='success'>✅ Tous les champs requis sont présents</p>";
                        } else {
                            echo "<p class='error'>❌ Champs manquants : " . implode(', ', $missing) . "</p>";
                        }
                    } else {
                        echo "<p class='warning'>⚠️ Aucune absence en base de données</p>";
                        echo "<p>Ceci est normal si aucune absence n'a été créée.</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Format de réponse incorrect (pas un tableau)</p>";
                    echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
                }
            } else {
                echo "<p class='error'>❌ Réponse JSON invalide</p>";
                echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
            echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test cURL : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. Test de création d'une absence
    echo "<div class='step'>";
    echo "<h3">📤 3. Test API POST (Création d'Absence)</h3>";
    
    // Récupérer un étudiant pour le test
    $stmt = $pdo->query("SELECT id FROM Etudiants LIMIT 1");
    $etudiant = $stmt->fetch();
    
    if ($etudiant) {
        $test_data = [
            'etudiant_id' => (int)$etudiant['id'],
            'date_absence' => date('Y-m-d'),
            'justification' => 'Test automatique depuis le script de diagnostic'
        ];
        
        echo "<h4>📤 Données de test :</h4>";
        echo "<div class='json-block'>";
        echo json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        echo "</div>";
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<h4>📥 Réponse POST :</h4>";
            echo "<p><strong>Code HTTP :</strong> $http_code</p>";
            echo "<div class='json-block'>" . htmlspecialchars($response) . "</div>";
            
            if ($http_code === 200) {
                $result = json_decode($response, true);
                if ($result && isset($result['success']) && $result['success']) {
                    echo "<p class='success'>✅ Création d'absence réussie ! ID: " . $result['id'] . "</p>";
                } else {
                    echo "<p class='error'>❌ Échec de la création</p>";
                }
            } else {
                echo "<p class='error'>❌ Erreur HTTP lors de la création</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur test POST : " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Aucun étudiant en base pour tester la création</p>";
    }
    echo "</div>";
    
    // 4. Test depuis React
    echo "<div class='step'>";
    echo "<h3">⚛️ 4. Test depuis React</h3>";
    
    echo "<h4>🔧 Code JavaScript pour tester :</h4>";
    echo "<div class='json-block'>";
    echo "// Test dans la console du navigateur (F12)\n";
    echo "fetch('$api_url')\n";
    echo "  .then(response => response.json())\n";
    echo "  .then(data => {\n";
    echo "    console.log('✅ Absences chargées:', data);\n";
    echo "    console.log('📊 Nombre:', data.length);\n";
    echo "  })\n";
    echo "  .catch(error => {\n";
    echo "    console.error('❌ Erreur:', error);\n";
    echo "  });";
    echo "</div>";
    
    echo "<h4>🎯 Instructions pour React :</h4>";
    echo "<ol>";
    echo "<li><strong>Ouvrez l'interface React :</strong> <a href='http://localhost:3000/absences' target='_blank'>http://localhost:3000/absences</a></li>";
    echo "<li><strong>Ouvrez la console :</strong> F12 → Console</li>";
    echo "<li><strong>Vérifiez les logs :</strong> Chercher '🔍 DEBUG ABSENCES API Response'</li>";
    echo "<li><strong>Vérifiez les erreurs :</strong> Messages d'erreur en rouge</li>";
    echo "</ol>";
    echo "</div>";
    
    // 5. Diagnostic des problèmes
    echo "<div class='step'>";
    echo "<h3">🔍 5. Diagnostic des Problèmes</h3>";
    
    echo "<h4>❌ Si l'erreur 'Impossible de charger les absences' persiste :</h4>";
    echo "<ol>";
    echo "<li><strong>Vérifiez le serveur :</strong> Apache/PHP démarré</li>";
    echo "<li><strong>Vérifiez l'URL :</strong> <a href='$api_url' target='_blank'>$api_url</a> accessible</li>";
    echo "<li><strong>Vérifiez la base :</strong> MySQL démarré et base 'GestionScolaire' existe</li>";
    echo "<li><strong>Vérifiez les CORS :</strong> Headers Access-Control-Allow-Origin</li>";
    echo "<li><strong>Vérifiez React :</strong> Console pour erreurs JavaScript</li>";
    echo "</ol>";
    
    echo "<h4>✅ Solutions :</h4>";
    echo "<ul>";
    echo "<li><strong>Redémarrer les services :</strong> Apache, MySQL</li>";
    echo "<li><strong>Vider le cache :</strong> Navigateur (Ctrl+F5)</li>";
    echo "<li><strong>Vérifier les logs :</strong> error.log PHP</li>";
    echo "<li><strong>Tester l'API directement :</strong> Dans le navigateur</li>";
    echo "</ul>";
    
    echo "<h4>🔧 API de Fallback :</h4>";
    echo "<p>Si l'API simple ne fonctionne pas, utilisez cette version minimale :</p>";
    echo "<div class='json-block'>";
    echo "// Dans React, remplacez l'URL par :\n";
    echo "'http://localhost/Project_PFE/Backend/pages/absences/test_api_simple.php?action=get'";
    echo "</div>";
    echo "</div>";
    
    // 6. Liens de test
    echo "<div class='step'>";
    echo "<h3">🔗 6. Liens de Test</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$api_url' target='_blank' class='btn'>🧪 API Simple</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='btn'>⚛️ Interface React</a>";
    echo "<a href='debug_form.php' target='_blank' class='btn'>🔧 Debug Formulaire</a>";
    echo "<a href='fix_data.php' target='_blank' class='btn'>📊 Créer Données</a>";
    echo "</div>";
    
    echo "<h4>🎯 Résultat Attendu :</h4>";
    echo "<p>Après ces corrections, l'interface React devrait :</p>";
    echo "<ul>";
    echo "<li>✅ Charger sans erreur 'Impossible de charger les absences'</li>";
    echo "<li>✅ Afficher la liste des absences (même vide)</li>";
    echo "<li>✅ Permettre d'ajouter de nouvelles absences</li>";
    echo "<li>✅ Afficher les logs de debug dans la console</li>";
    echo "</ul>";
    
    echo "<p class='success'>🎉 <strong>L'API simplifiée devrait résoudre le problème de chargement !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Action GET simple pour fallback
if (isset($_GET['action']) && $_GET['action'] === 'get') {
    header('Content-Type: application/json');
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Absences");
        $count = $stmt->fetch()['count'];
        
        echo json_encode([
            ['id' => 1, 'etudiant_nom' => 'Test Étudiant', 'date_absence' => date('Y-m-d'), 'justification' => 'Test', 'matiere_nom' => 'Test', 'enseignant_nom' => 'Test'],
            'debug' => 'API de fallback - ' . $count . ' absence(s) en base'
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
    exit();
}
?>
