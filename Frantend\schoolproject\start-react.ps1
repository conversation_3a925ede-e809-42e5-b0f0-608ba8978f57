# Script PowerShell pour demarrer React avec Node.js v20 compatibility
Write-Host "Demarrage du serveur React avec compatibilite Node.js v20..." -ForegroundColor Green
Write-Host ""

# Definir la variable d'environnement pour Node.js
$env:NODE_OPTIONS = "--openssl-legacy-provider"
Write-Host "Variable NODE_OPTIONS definie: $env:NODE_OPTIONS" -ForegroundColor Yellow
Write-Host ""

# Afficher les informations de version
Write-Host "Informations systeme:" -ForegroundColor Cyan
Write-Host "   Node.js version: $(node --version)" -ForegroundColor White
Write-Host "   npm version: $(npm --version)" -ForegroundColor White
Write-Host ""

# Demarrer le serveur React
Write-Host "Lancement de npm start..." -ForegroundColor Blue
Write-Host ""

try {
    npm start
} catch {
    Write-Host "Erreur lors du demarrage:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..."
Read-Host
