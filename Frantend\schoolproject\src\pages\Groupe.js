import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const GroupeCRUD = () => {
    const { user } = useContext(AuthContext);
    const [groupes, setGroupes] = useState([]);
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingGroupe, setEditingGroupe] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [classeFilter, setClasseFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        nom: '',
        classe_id: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchGroupes();
        fetchClasses();
    }, []);

    const fetchClasses = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let classesData = response.data;
            if (Array.isArray(classesData)) {
                setClasses(classesData);
            } else {
                setClasses([
                    { id: 1, nom: 'Classe A', filiere_nom: 'Sciences', niveau_nom: 'Première année' },
                    { id: 2, nom: 'Classe B', filiere_nom: 'Sciences', niveau_nom: 'Première année' },
                    { id: 3, nom: 'Classe C', filiere_nom: 'Littéraire', niveau_nom: 'Deuxième année' },
                    { id: 4, nom: 'Classe D', filiere_nom: 'Littéraire', niveau_nom: 'Deuxième année' }
                ]);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des classes:', error);
            setClasses([
                { id: 1, nom: 'Classe A', filiere_nom: 'Sciences', niveau_nom: 'Première année' },
                { id: 2, nom: 'Classe B', filiere_nom: 'Sciences', niveau_nom: 'Première année' },
                { id: 3, nom: 'Classe C', filiere_nom: 'Littéraire', niveau_nom: 'Deuxième année' },
                { id: 4, nom: 'Classe D', filiere_nom: 'Littéraire', niveau_nom: 'Deuxième année' }
            ]);
        }
    };

    const fetchGroupes = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des groupes...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/groupes/groupe.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let groupesData = response.data;
            if (!Array.isArray(groupesData)) {
                groupesData = [];
            }

            setGroupes(groupesData);
            console.log('✅ Groupes chargés:', groupesData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des groupes:', error);

            // Données de test
            const testGroupes = [
                { id: 1, nom: 'Groupe Alpha', classe_id: 1, classe_nom: 'Classe A' },
                { id: 2, nom: 'Groupe Beta', classe_id: 1, classe_nom: 'Classe A' },
                { id: 3, nom: 'Groupe Gamma', classe_id: 2, classe_nom: 'Classe B' },
                { id: 4, nom: 'Groupe Delta', classe_id: 2, classe_nom: 'Classe B' },
                { id: 5, nom: 'Groupe Epsilon', classe_id: 3, classe_nom: 'Classe C' },
                { id: 6, nom: 'Groupe Zeta', classe_id: 3, classe_nom: 'Classe C' },
                { id: 7, nom: 'Groupe Eta', classe_id: 4, classe_nom: 'Classe D' },
                { id: 8, nom: 'Groupe Theta', classe_id: 4, classe_nom: 'Classe D' },
                { id: 9, nom: 'Groupe Iota', classe_id: 1, classe_nom: 'Classe A' },
                { id: 10, nom: 'Groupe Kappa', classe_id: 2, classe_nom: 'Classe B' },
                { id: 11, nom: 'Groupe Lambda', classe_id: 3, classe_nom: 'Classe C' },
                { id: 12, nom: 'Groupe Mu', classe_id: 4, classe_nom: 'Classe D' }
            ];

            setGroupes(testGroupes);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testGroupes.length} groupes de test.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des groupes', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/groupes/groupe.php';
            const method = editingGroupe ? 'PUT' : 'POST';
            const data = editingGroupe ? { ...formData, id: editingGroupe.id } : formData;

            console.log('🔄 Envoi requête:', { method, url, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data && response.data.success === true) {
                Swal.fire('Succès', `Groupe ${editingGroupe ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingGroupe(null);
                resetForm();
                fetchGroupes();
            } else {
                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            const errorMessage = error.response?.data?.error ||
                               error.response?.data?.message ||
                               error.message ||
                               'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (groupe) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des groupes', 'error');
            return;
        }

        setEditingGroupe(groupe);
        setFormData({
            nom: groupe.nom,
            classe_id: groupe.classe_id
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des groupes', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/groupes/groupe.php';

                console.log('🔄 Suppression groupe:', { id, url });

                const response = await axios.delete(url, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data && response.data.success === true) {
                    Swal.fire('Supprimé!', 'Le groupe a été supprimé.', 'success');
                    fetchGroupes();
                } else {
                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error ||
                                   error.response?.data?.message ||
                                   error.message ||
                                   'Impossible de supprimer le groupe';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            nom: '',
            classe_id: ''
        });
    };

    // Filtrage des données
    const filteredGroupes = groupes.filter(groupe => {
        const matchesSearch = groupe.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             groupe.classe_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesClasse = classeFilter === 'all' || groupe.classe_id?.toString() === classeFilter;

        return matchesSearch && matchesClasse;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentGroupes = filteredGroupes.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredGroupes.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, classeFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des groupes...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>👥 Gestion des Groupes</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredGroupes.length} groupe(s) trouvé(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouveau Groupe
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les groupes en mode lecture seule.
                        Seul l'administrateur peut créer, modifier ou supprimer des groupes.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher un groupe..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="classe-filter">
                    <select
                        value={classeFilter}
                        onChange={(e) => {
                            setClasseFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '200px'
                        }}
                    >
                        <option value="all">Toutes les classes</option>
                        {classes.map(classe => (
                            <option key={classe.id} value={classe.id}>
                                {classe.nom}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredGroupes.length === 0 ? (
                    <div className="no-data">
                        <img src="/group.png" alt="Aucun groupe" />
                        <p>Aucun groupe trouvé</p>
                        {(searchTerm || classeFilter !== 'all') && (
                            <button
                                onClick={() => {
                                    setSearchTerm('');
                                    setClasseFilter('all');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>👥 Nom du Groupe</th>
                                    <th>🏫 Classe</th>
                                    <th>📈 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentGroupes.map((groupe) => (
                                    <tr key={groupe.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{groupe.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{groupe.nom}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#d4edda',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#155724'
                                            }}>
                                                {groupe.classe_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="badge badge-success">Actif</span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(groupe)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(groupe.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredGroupes.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minWidth(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredGroupes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des groupes</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {classes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Classes disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {currentGroupes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un groupe */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingGroupe ? 'Modifier le groupe' : 'Nouveau groupe'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingGroupe(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Nom du groupe *</label>
                                <input
                                    type="text"
                                    value={formData.nom}
                                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                                    placeholder="Ex: Groupe Alpha, Groupe Beta..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Classe *</label>
                                <select
                                    value={formData.classe_id}
                                    onChange={(e) => setFormData({...formData, classe_id: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                >
                                    <option value="">Sélectionner une classe</option>
                                    {classes.map((classe) => (
                                        <option key={classe.id} value={classe.id}>
                                            {classe.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingGroupe(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingGroupe ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default GroupeCRUD;
