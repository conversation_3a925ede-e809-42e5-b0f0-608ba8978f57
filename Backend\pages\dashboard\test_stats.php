<?php
// Script de test pour vérifier les statistiques du dashboard
header('Content-Type: application/json');

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connexion à la base de données réussie\n";
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
    exit();
}

// Test de chaque table
$tables = [
    'cours' => 'Cours',
    'matieres' => 'Matières', 
    'groupes' => 'Groupes',
    'etudiants' => 'Étudiants',
    'parents' => 'Parents',
    'filieres' => 'Filières',
    'utilisateurs' => 'Utilisateurs',
    'enseignants' => 'Enseignants',
    'classes' => 'Classes',
    'niveaux' => 'Niveaux',
    'absences' => 'Absences',
    'retards' => 'Retards',
    'devoirs' => 'Devoirs',
    'quiz' => 'Quiz',
    'factures' => 'Factures',
    'diplomes' => 'Diplômes'
];

echo "\n📊 Test des statistiques:\n";
echo "========================\n";

$stats = [];
foreach ($tables as $table => $label) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch()['count'];
        $stats[$table] = $count;
        echo "✅ $label: $count enregistrements\n";
    } catch (Exception $e) {
        echo "❌ Erreur pour $label: " . $e->getMessage() . "\n";
        $stats[$table] = 0;
    }
}

// Tests spécifiques
echo "\n🔍 Tests spécifiques:\n";
echo "====================\n";

// Absences aujourd'hui
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM absences WHERE DATE(date_absence) = CURDATE()");
    $stats['absences_aujourdhui'] = $stmt->fetch()['count'];
    echo "✅ Absences aujourd'hui: " . $stats['absences_aujourdhui'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur absences aujourd'hui: " . $e->getMessage() . "\n";
    $stats['absences_aujourdhui'] = 0;
}

// Retards aujourd'hui
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM retards WHERE DATE(date_retard) = CURDATE()");
    $stats['retards_aujourdhui'] = $stmt->fetch()['count'];
    echo "✅ Retards aujourd'hui: " . $stats['retards_aujourdhui'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur retards aujourd'hui: " . $e->getMessage() . "\n";
    $stats['retards_aujourdhui'] = 0;
}

// Devoirs en cours
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM devoirs WHERE date_limite >= CURDATE()");
    $stats['devoirs_en_cours'] = $stmt->fetch()['count'];
    echo "✅ Devoirs en cours: " . $stats['devoirs_en_cours'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur devoirs en cours: " . $e->getMessage() . "\n";
    $stats['devoirs_en_cours'] = 0;
}

// Quiz actifs
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM quiz WHERE statut = 'actif'");
    $stats['quiz_actifs'] = $stmt->fetch()['count'];
    echo "✅ Quiz actifs: " . $stats['quiz_actifs'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur quiz actifs: " . $e->getMessage() . "\n";
    $stats['quiz_actifs'] = 0;
}

// Factures impayées
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM factures WHERE statut = 'impayé'");
    $stats['factures_impayees'] = $stmt->fetch()['count'];
    echo "✅ Factures impayées: " . $stats['factures_impayees'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur factures impayées: " . $e->getMessage() . "\n";
    $stats['factures_impayees'] = 0;
}

// Diplômes cette année
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM diplomes WHERE YEAR(date_obtention) = YEAR(CURDATE())");
    $stats['diplomes_annee'] = $stmt->fetch()['count'];
    echo "✅ Diplômes cette année: " . $stats['diplomes_annee'] . "\n";
} catch (Exception $e) {
    echo "❌ Erreur diplômes cette année: " . $e->getMessage() . "\n";
    $stats['diplomes_annee'] = 0;
}

echo "\n📋 Résumé JSON:\n";
echo "===============\n";
echo json_encode([
    'success' => true,
    'stats' => $stats,
    'timestamp' => date('Y-m-d H:i:s'),
    'total_tables_tested' => count($tables),
    'message' => 'Test des statistiques terminé avec succès'
], JSON_PRETTY_PRINT);

echo "\n\n🎯 Recommandations:\n";
echo "===================\n";
echo "1. Vérifiez que toutes les tables existent dans votre base de données\n";
echo "2. Assurez-vous que les colonnes date_absence, date_retard, date_limite existent\n";
echo "3. Vérifiez que les colonnes statut dans quiz et factures utilisent les bonnes valeurs\n";
echo "4. Testez l'API stats.php avec ces données\n";
?>
