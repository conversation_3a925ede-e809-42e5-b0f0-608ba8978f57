{"ast": null, "code": "/**\n * Utilitaire de filtrage des données pour les étudiants\n * Garantit que chaque étudiant ne voit que ses propres informations\n */\n\n/**\n * Filtre les données pour s'assurer qu'un étudiant ne voit que ses propres informations\n * @param {Array} data - Les données à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @param {string} userField - Le champ qui identifie l'utilisateur dans les données (ex: 'etudiant_email', 'utilisateur_id')\n * @returns {Array} - Les données filtrées\n */\nexport const filterStudentData = (data, user, userField = 'etudiant_email') => {\n  if (!user || !Array.isArray(data)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner toutes les données\n  if (!isStudent(user)) {\n    return data;\n  }\n\n  // Filtrer les données pour ne garder que celles de l'étudiant connecté\n  return data.filter(item => {\n    // Vérifications multiples pour s'assurer de la correspondance\n    const matchesEmail = item.etudiant_email === user.email;\n    const matchesUserId = item.utilisateur_id === user.id;\n    const matchesEtudiantUserId = item.etudiant_utilisateur_id === user.id;\n    const matchesCustomField = userField && item[userField] === user[userField.replace('etudiant_', '')];\n    const belongs = matchesEmail || matchesUserId || matchesEtudiantUserId || matchesCustomField;\n    if (!belongs) {\n      console.log('🔒 Donnée filtrée pour sécurité:', {\n        itemId: item.id,\n        userEmail: user.email,\n        userId: user.id,\n        itemEmail: item.etudiant_email,\n        itemUserId: item.utilisateur_id\n      });\n    }\n    return belongs;\n  });\n};\n\n/**\n * Vérifie si l'utilisateur est un étudiant\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isStudent = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'etudiant' || role === 'élève' || role === 'student';\n};\n\n/**\n * Vérifie si l'utilisateur est un parent\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isParent = user => {\n  if (!user || !user.role) return false;\n  return user.role.toLowerCase() === 'parent';\n};\n\n/**\n * Vérifie si l'utilisateur est un enseignant\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isTeacher = user => {\n  if (!user || !user.role) return false;\n  return user.role.toLowerCase() === 'enseignant' || user.role.toLowerCase() === 'teacher';\n};\n\n/**\n * Vérifie si l'utilisateur est un administrateur\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isAdmin = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'admin' || role === 'responsable' || role === 'administrator';\n};\n\n/**\n * Vérifie si l'utilisateur peut gérer les données (admin ou enseignant)\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const canManageData = user => {\n  return isAdmin(user) || isTeacher(user);\n};\n\n/**\n * Filtre spécialisé pour les absences\n * @param {Array} absences - Les absences à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterAbsences = (absences, user) => {\n  return filterStudentData(absences, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les notes\n * @param {Array} notes - Les notes à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterNotes = (notes, user) => {\n  return filterStudentData(notes, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les retards\n * @param {Array} retards - Les retards à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterRetards = (retards, user) => {\n  return filterStudentData(retards, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les diplômes\n * @param {Array} diplomes - Les diplômes à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterDiplomes = (diplomes, user) => {\n  return filterStudentData(diplomes, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les cours\n * @param {Array} cours - Les cours à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterCours = (cours, user) => {\n  if (!isStudent(user)) return cours;\n\n  // Pour les cours, filtrer par classe/groupe de l'étudiant\n  return cours.filter(cour => {\n    // Logique de filtrage basée sur la classe/groupe de l'étudiant\n    // Cette logique devra être adaptée selon la structure de vos données\n    return true; // Temporaire - à implémenter selon votre structure\n  });\n};\n\n/**\n * Message d'alerte de sécurité pour tentative d'accès non autorisé\n */\nexport const showSecurityAlert = () => {\n  console.warn('🚨 TENTATIVE D\\'ACCÈS NON AUTORISÉ DÉTECTÉE');\n  alert('Accès refusé : Vous ne pouvez consulter que vos propres informations.');\n};\n\n/**\n * Log de sécurité pour audit\n * @param {string} action - L'action tentée\n * @param {Object} user - L'utilisateur\n * @param {Object} data - Les données concernées\n */\nexport const logSecurityEvent = (action, user, data) => {\n  const logEntry = {\n    timestamp: new Date().toISOString(),\n    action,\n    user: {\n      id: user === null || user === void 0 ? void 0 : user.id,\n      email: user === null || user === void 0 ? void 0 : user.email,\n      role: user === null || user === void 0 ? void 0 : user.role\n    },\n    data: {\n      id: data === null || data === void 0 ? void 0 : data.id,\n      type: (data === null || data === void 0 ? void 0 : data.type) || 'unknown'\n    }\n  };\n  console.log('🔐 AUDIT SÉCURITÉ:', logEntry);\n\n  // Dans un vrai système, envoyer ce log au serveur\n  // sendSecurityLogToServer(logEntry);\n};", "map": {"version": 3, "names": ["filterStudentData", "data", "user", "userField", "Array", "isArray", "isStudent", "filter", "item", "matchesEmail", "etudiant_email", "email", "matchesUserId", "utilisateur_id", "id", "matchesEtudiantUserId", "etudiant_utilisateur_id", "matchesCustomField", "replace", "belongs", "console", "log", "itemId", "userEmail", "userId", "itemEmail", "itemUserId", "role", "toLowerCase", "isParent", "<PERSON><PERSON><PERSON>er", "isAdmin", "canManageData", "filterAbsences", "absences", "filterNotes", "notes", "filterRetards", "retards", "filterDiplomes", "diplomes", "filterCours", "cours", "cour", "showSecurityAlert", "warn", "alert", "logSecurityEvent", "action", "logEntry", "timestamp", "Date", "toISOString", "type"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/utils/studentDataFilter.js"], "sourcesContent": ["/**\n * Utilitaire de filtrage des données pour les étudiants\n * Garantit que chaque étudiant ne voit que ses propres informations\n */\n\n/**\n * Filtre les données pour s'assurer qu'un étudiant ne voit que ses propres informations\n * @param {Array} data - Les données à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @param {string} userField - Le champ qui identifie l'utilisateur dans les données (ex: 'etudiant_email', 'utilisateur_id')\n * @returns {Array} - Les données filtrées\n */\nexport const filterStudentData = (data, user, userField = 'etudiant_email') => {\n    if (!user || !Array.isArray(data)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner toutes les données\n    if (!isStudent(user)) {\n        return data;\n    }\n\n    // Filtrer les données pour ne garder que celles de l'étudiant connecté\n    return data.filter(item => {\n        // Vérifications multiples pour s'assurer de la correspondance\n        const matchesEmail = item.etudiant_email === user.email;\n        const matchesUserId = item.utilisateur_id === user.id;\n        const matchesEtudiantUserId = item.etudiant_utilisateur_id === user.id;\n        const matchesCustomField = userField && item[userField] === user[userField.replace('etudiant_', '')];\n\n        const belongs = matchesEmail || matchesUserId || matchesEtudiantUserId || matchesCustomField;\n        \n        if (!belongs) {\n            console.log('🔒 Donnée filtrée pour sécurité:', {\n                itemId: item.id,\n                userEmail: user.email,\n                userId: user.id,\n                itemEmail: item.etudiant_email,\n                itemUserId: item.utilisateur_id\n            });\n        }\n\n        return belongs;\n    });\n};\n\n/**\n * Vérifie si l'utilisateur est un étudiant\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isStudent = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'etudiant' || role === 'élève' || role === 'student';\n};\n\n/**\n * Vérifie si l'utilisateur est un parent\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isParent = (user) => {\n    if (!user || !user.role) return false;\n    return user.role.toLowerCase() === 'parent';\n};\n\n/**\n * Vérifie si l'utilisateur est un enseignant\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isTeacher = (user) => {\n    if (!user || !user.role) return false;\n    return user.role.toLowerCase() === 'enseignant' || user.role.toLowerCase() === 'teacher';\n};\n\n/**\n * Vérifie si l'utilisateur est un administrateur\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const isAdmin = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'admin' || role === 'responsable' || role === 'administrator';\n};\n\n/**\n * Vérifie si l'utilisateur peut gérer les données (admin ou enseignant)\n * @param {Object} user - L'utilisateur à vérifier\n * @returns {boolean}\n */\nexport const canManageData = (user) => {\n    return isAdmin(user) || isTeacher(user);\n};\n\n/**\n * Filtre spécialisé pour les absences\n * @param {Array} absences - Les absences à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterAbsences = (absences, user) => {\n    return filterStudentData(absences, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les notes\n * @param {Array} notes - Les notes à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterNotes = (notes, user) => {\n    return filterStudentData(notes, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les retards\n * @param {Array} retards - Les retards à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterRetards = (retards, user) => {\n    return filterStudentData(retards, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les diplômes\n * @param {Array} diplomes - Les diplômes à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterDiplomes = (diplomes, user) => {\n    return filterStudentData(diplomes, user, 'etudiant_email');\n};\n\n/**\n * Filtre spécialisé pour les cours\n * @param {Array} cours - Les cours à filtrer\n * @param {Object} user - L'utilisateur connecté\n * @returns {Array}\n */\nexport const filterCours = (cours, user) => {\n    if (!isStudent(user)) return cours;\n    \n    // Pour les cours, filtrer par classe/groupe de l'étudiant\n    return cours.filter(cour => {\n        // Logique de filtrage basée sur la classe/groupe de l'étudiant\n        // Cette logique devra être adaptée selon la structure de vos données\n        return true; // Temporaire - à implémenter selon votre structure\n    });\n};\n\n/**\n * Message d'alerte de sécurité pour tentative d'accès non autorisé\n */\nexport const showSecurityAlert = () => {\n    console.warn('🚨 TENTATIVE D\\'ACCÈS NON AUTORISÉ DÉTECTÉE');\n    alert('Accès refusé : Vous ne pouvez consulter que vos propres informations.');\n};\n\n/**\n * Log de sécurité pour audit\n * @param {string} action - L'action tentée\n * @param {Object} user - L'utilisateur\n * @param {Object} data - Les données concernées\n */\nexport const logSecurityEvent = (action, user, data) => {\n    const logEntry = {\n        timestamp: new Date().toISOString(),\n        action,\n        user: {\n            id: user?.id,\n            email: user?.email,\n            role: user?.role\n        },\n        data: {\n            id: data?.id,\n            type: data?.type || 'unknown'\n        }\n    };\n    \n    console.log('🔐 AUDIT SÉCURITÉ:', logEntry);\n    \n    // Dans un vrai système, envoyer ce log au serveur\n    // sendSecurityLogToServer(logEntry);\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,SAAS,GAAG,gBAAgB,KAAK;EAC3E,IAAI,CAACD,IAAI,IAAI,CAACE,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IAC/B,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAACK,SAAS,CAACJ,IAAI,CAAC,EAAE;IAClB,OAAOD,IAAI;EACf;;EAEA;EACA,OAAOA,IAAI,CAACM,MAAM,CAACC,IAAI,IAAI;IACvB;IACA,MAAMC,YAAY,GAAGD,IAAI,CAACE,cAAc,KAAKR,IAAI,CAACS,KAAK;IACvD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,cAAc,KAAKX,IAAI,CAACY,EAAE;IACrD,MAAMC,qBAAqB,GAAGP,IAAI,CAACQ,uBAAuB,KAAKd,IAAI,CAACY,EAAE;IACtE,MAAMG,kBAAkB,GAAGd,SAAS,IAAIK,IAAI,CAACL,SAAS,CAAC,KAAKD,IAAI,CAACC,SAAS,CAACe,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAEpG,MAAMC,OAAO,GAAGV,YAAY,IAAIG,aAAa,IAAIG,qBAAqB,IAAIE,kBAAkB;IAE5F,IAAI,CAACE,OAAO,EAAE;MACVC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC5CC,MAAM,EAAEd,IAAI,CAACM,EAAE;QACfS,SAAS,EAAErB,IAAI,CAACS,KAAK;QACrBa,MAAM,EAAEtB,IAAI,CAACY,EAAE;QACfW,SAAS,EAAEjB,IAAI,CAACE,cAAc;QAC9BgB,UAAU,EAAElB,IAAI,CAACK;MACrB,CAAC,CAAC;IACN;IAEA,OAAOM,OAAO;EAClB,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMb,SAAS,GAAIJ,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyB,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGzB,IAAI,CAACyB,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS;AACxE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,QAAQ,GAAI3B,IAAI,IAAK;EAC9B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyB,IAAI,EAAE,OAAO,KAAK;EACrC,OAAOzB,IAAI,CAACyB,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,SAAS,GAAI5B,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyB,IAAI,EAAE,OAAO,KAAK;EACrC,OAAOzB,IAAI,CAACyB,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,YAAY,IAAI1B,IAAI,CAACyB,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS;AAC5F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,OAAO,GAAI7B,IAAI,IAAK;EAC7B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyB,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGzB,IAAI,CAACyB,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,eAAe;AACjF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,aAAa,GAAI9B,IAAI,IAAK;EACnC,OAAO6B,OAAO,CAAC7B,IAAI,CAAC,IAAI4B,SAAS,CAAC5B,IAAI,CAAC;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,cAAc,GAAGA,CAACC,QAAQ,EAAEhC,IAAI,KAAK;EAC9C,OAAOF,iBAAiB,CAACkC,QAAQ,EAAEhC,IAAI,EAAE,gBAAgB,CAAC;AAC9D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiC,WAAW,GAAGA,CAACC,KAAK,EAAElC,IAAI,KAAK;EACxC,OAAOF,iBAAiB,CAACoC,KAAK,EAAElC,IAAI,EAAE,gBAAgB,CAAC;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmC,aAAa,GAAGA,CAACC,OAAO,EAAEpC,IAAI,KAAK;EAC5C,OAAOF,iBAAiB,CAACsC,OAAO,EAAEpC,IAAI,EAAE,gBAAgB,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqC,cAAc,GAAGA,CAACC,QAAQ,EAAEtC,IAAI,KAAK;EAC9C,OAAOF,iBAAiB,CAACwC,QAAQ,EAAEtC,IAAI,EAAE,gBAAgB,CAAC;AAC9D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuC,WAAW,GAAGA,CAACC,KAAK,EAAExC,IAAI,KAAK;EACxC,IAAI,CAACI,SAAS,CAACJ,IAAI,CAAC,EAAE,OAAOwC,KAAK;;EAElC;EACA,OAAOA,KAAK,CAACnC,MAAM,CAACoC,IAAI,IAAI;IACxB;IACA;IACA,OAAO,IAAI,CAAC,CAAC;EACjB,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EACnCxB,OAAO,CAACyB,IAAI,CAAC,6CAA6C,CAAC;EAC3DC,KAAK,CAAC,uEAAuE,CAAC;AAClF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAE9C,IAAI,EAAED,IAAI,KAAK;EACpD,MAAMgD,QAAQ,GAAG;IACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCJ,MAAM;IACN9C,IAAI,EAAE;MACFY,EAAE,EAAEZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAE;MACZH,KAAK,EAAET,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,KAAK;MAClBgB,IAAI,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB;IAChB,CAAC;IACD1B,IAAI,EAAE;MACFa,EAAE,EAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,EAAE;MACZuC,IAAI,EAAE,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoD,IAAI,KAAI;IACxB;EACJ,CAAC;EAEDjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4B,QAAQ,CAAC;;EAE3C;EACA;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}