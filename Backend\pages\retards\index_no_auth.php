<?php
/**
 * API CRUD pour la gestion des Retards - VERSION SANS AUTHENTIFICATION
 * Pour tests et développement uniquement
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Simulation utilisateur Admin pour les tests
$user_info = [
    'id' => 1,
    'role' => 'Admin'
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * GET - Récupérer tous les retards
 */
function handleGet($pdo, $user_info) {
    try {
        $sql = "
            SELECT r.id, r.etudiant_id, r.matiere_id, r.enseignant_id, 
                   r.date_retard, r.duree_retard, r.justification,
                   CONCAT(u.nom, ' ', u.prenom) as etudiant_nom,
                   u.email as etudiant_email,
                   m.nom as matiere_nom,
                   CONCAT(ue.nom, ' ', ue.prenom) as enseignant_nom
            FROM Retards r 
            JOIN Etudiants e ON r.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id
            LEFT JOIN Enseignants ens ON r.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            ORDER BY r.date_retard DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $retards = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formater les données pour React
        $result = [];
        foreach ($retards as $retard) {
            $result[] = [
                'id' => (int)$retard['id'],
                'etudiant_id' => (int)$retard['etudiant_id'],
                'matiere_id' => $retard['matiere_id'] ? (int)$retard['matiere_id'] : null,
                'enseignant_id' => $retard['enseignant_id'] ? (int)$retard['enseignant_id'] : null,
                'date_retard' => $retard['date_retard'],
                'duree_retard' => $retard['duree_retard'],
                'justification' => $retard['justification'],
                'etudiant_nom' => $retard['etudiant_nom'],
                'etudiant_email' => $retard['etudiant_email'],
                'matiere_nom' => $retard['matiere_nom'] ?: 'Non définie',
                'enseignant_nom' => $retard['enseignant_nom'] ?: 'Non défini'
            ];
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération: ' . $e->getMessage()]);
    }
}

/**
 * POST - Créer un nouveau retard
 */
function handlePost($pdo, $user_info, $input) {
    try {
        // Validation des données requises
        if (!isset($input['etudiant_id'], $input['date_retard'], $input['duree_retard'])) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Données manquantes: etudiant_id, date_retard et duree_retard requis',
                'received' => $input
            ]);
            return;
        }
        
        // Nettoyer et valider les données
        $etudiant_id = (int)$input['etudiant_id'];
        $matiere_id = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
        $enseignant_id = !empty($input['enseignant_id']) ? (int)$input['enseignant_id'] : null;
        $date_retard = $input['date_retard'];
        $duree_retard = $input['duree_retard'];
        $justification = !empty($input['justification']) ? trim($input['justification']) : null;
        
        // Validation de l'étudiant
        if ($etudiant_id <= 0) {
            http_response_code(400);
            echo json_encode(['error' => 'ID étudiant invalide: ' . $etudiant_id]);
            return;
        }
        
        // Vérifier que l'étudiant existe
        $stmt = $pdo->prepare("SELECT id FROM Etudiants WHERE id = ?");
        $stmt->execute([$etudiant_id]);
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Étudiant non trouvé avec ID: ' . $etudiant_id]);
            return;
        }
        
        // Validation de la date
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_retard)) {
            http_response_code(400);
            echo json_encode(['error' => 'Format de date invalide (YYYY-MM-DD requis): ' . $date_retard]);
            return;
        }
        
        // Validation de la durée (format HH:MM:SS ou HH:MM)
        if (!preg_match('/^\d{1,2}:\d{2}(:\d{2})?$/', $duree_retard)) {
            http_response_code(400);
            echo json_encode(['error' => 'Format de durée invalide (HH:MM requis): ' . $duree_retard]);
            return;
        }
        
        // Ajouter les secondes si manquantes
        if (substr_count($duree_retard, ':') === 1) {
            $duree_retard .= ':00';
        }
        
        // Insérer le retard
        $stmt = $pdo->prepare("
            INSERT INTO Retards (etudiant_id, matiere_id, enseignant_id, date_retard, duree_retard, justification) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([$etudiant_id, $matiere_id, $enseignant_id, $date_retard, $duree_retard, $justification]);
        
        if ($result) {
            $new_id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'id' => (int)$new_id,
                'message' => 'Retard créé avec succès (version sans auth)',
                'debug' => [
                    'etudiant_id' => $etudiant_id,
                    'matiere_id' => $matiere_id,
                    'enseignant_id' => $enseignant_id,
                    'date_retard' => $date_retard,
                    'duree_retard' => $duree_retard,
                    'justification' => $justification
                ]
            ]);
        } else {
            throw new Exception('Échec de l\'insertion en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * PUT - Modifier un retard existant
 */
function handlePut($pdo, $user_info, $input) {
    try {
        if (!isset($input['id']) || !is_numeric($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID retard manquant ou invalide']);
            return;
        }
        
        $retard_id = (int)$input['id'];
        
        // Vérifier que le retard existe
        $stmt = $pdo->prepare("SELECT * FROM Retards WHERE id = ?");
        $stmt->execute([$retard_id]);
        $existing_retard = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existing_retard) {
            http_response_code(404);
            echo json_encode(['error' => 'Retard non trouvé']);
            return;
        }
        
        // Préparer les champs à modifier
        $fields = [];
        $values = [];
        
        if (isset($input['date_retard'])) {
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $input['date_retard'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Format de date invalide (YYYY-MM-DD requis)']);
                return;
            }
            $fields[] = 'date_retard = ?';
            $values[] = $input['date_retard'];
        }
        
        if (isset($input['duree_retard'])) {
            $duree = $input['duree_retard'];
            if (!preg_match('/^\d{1,2}:\d{2}(:\d{2})?$/', $duree)) {
                http_response_code(400);
                echo json_encode(['error' => 'Format de durée invalide (HH:MM requis)']);
                return;
            }
            // Ajouter les secondes si manquantes
            if (substr_count($duree, ':') === 1) {
                $duree .= ':00';
            }
            $fields[] = 'duree_retard = ?';
            $values[] = $duree;
        }
        
        if (isset($input['justification'])) {
            $fields[] = 'justification = ?';
            $values[] = !empty($input['justification']) ? trim($input['justification']) : null;
        }
        
        if (isset($input['matiere_id'])) {
            $fields[] = 'matiere_id = ?';
            $values[] = !empty($input['matiere_id']) ? (int)$input['matiere_id'] : null;
        }
        
        if (isset($input['enseignant_id'])) {
            $fields[] = 'enseignant_id = ?';
            $values[] = !empty($input['enseignant_id']) ? (int)$input['enseignant_id'] : null;
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['error' => 'Aucune donnée à modifier']);
            return;
        }
        
        $values[] = $retard_id;
        $sql = "UPDATE Retards SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Retard modifié avec succès (version sans auth)'
            ]);
        } else {
            throw new Exception('Échec de la modification en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

/**
 * DELETE - Supprimer un retard
 */
function handleDelete($pdo, $user_info, $input) {
    try {
        if (!isset($input['id']) || !is_numeric($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID retard manquant ou invalide']);
            return;
        }
        
        $retard_id = (int)$input['id'];
        
        // Vérifier que le retard existe
        $stmt = $pdo->prepare("SELECT id FROM Retards WHERE id = ?");
        $stmt->execute([$retard_id]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Retard non trouvé']);
            return;
        }
        
        // Supprimer le retard
        $stmt = $pdo->prepare("DELETE FROM Retards WHERE id = ?");
        $result = $stmt->execute([$retard_id]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Retard supprimé avec succès (version sans auth)'
            ]);
        } else {
            throw new Exception('Échec de la suppression en base de données');
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
