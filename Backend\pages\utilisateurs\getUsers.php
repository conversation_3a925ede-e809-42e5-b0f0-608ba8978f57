<?php

// Autoriser les requêtes CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Vérifier si on veut la liste simple ou détaillée
    $detailed = isset($_GET['detailed']) && $_GET['detailed'] === 'true';

    try {
        if ($detailed) {
            // Récupérer tous les utilisateurs avec détails complets
            $stmt = $pdo->prepare("
                SELECT
                    u.id,
                    u.nom,
                    u.email,
                    u.role_id,
                    r.nom as role_nom,
                    -- Informations additionnelles selon le rôle
                    p.telephone as parent_telephone,
                    p.adresse as parent_adresse,
                    g.nom as groupe_nom,
                    c.nom as classe_nom,
                    f.nom as filiere_nom,
                    n.nom as niveau_nom,
                    -- Indicateurs de profil
                    CASE WHEN e.id IS NOT NULL THEN 1 ELSE 0 END as is_enseignant,
                    CASE WHEN et.id IS NOT NULL THEN 1 ELSE 0 END as is_etudiant,
                    CASE WHEN p.id IS NOT NULL THEN 1 ELSE 0 END as is_parent
                FROM Utilisateurs u
                LEFT JOIN Roles r ON u.role_id = r.id
                LEFT JOIN Enseignants e ON u.id = e.utilisateur_id
                LEFT JOIN Etudiants et ON u.id = et.utilisateur_id
                LEFT JOIN Parents p ON u.id = p.utilisateur_id
                LEFT JOIN Groupes g ON et.groupe_id = g.id
                LEFT JOIN Classes c ON g.classe_id = c.id
                LEFT JOIN Filieres f ON c.filiere_id = f.id
                LEFT JOIN Niveaux n ON c.niveau_id = n.id
                ORDER BY u.nom ASC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Ajouter une date par défaut pour chaque utilisateur
            foreach ($users as &$user) {
                $user['created_at_formatted'] = 'Non disponible';
            }

        } else {
            // Récupérer la liste simple (pour les dropdowns)
            $stmt = $pdo->prepare("SELECT id, nom, email FROM Utilisateurs ORDER BY nom");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        echo json_encode([
            'success' => true,
            'users' => $users,
            'total' => count($users)
        ]);

    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}

?>
