import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const ProtectedRoute = ({ children, requiredRole, requiredRoles }) => {
  const { isAuthenticated, user, isLoading } = useContext(AuthContext);

  // Afficher un loader pendant la vérification de l'authentification
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem',
        color: '#666'
      }}>
        Chargement...
      </div>
    );
  }

  // Rediriger vers la page de connexion si non authentifié
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Vérifier le rôle si spécifié
  if (requiredRole) {
    const userRole = user?.role?.toLowerCase();
    const required = requiredRole.toLowerCase();
    
    if (userRole !== required) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          textAlign: 'center',
          padding: '20px'
        }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>
            Accès non autorisé
          </h2>
          <p style={{ color: '#666', marginBottom: '20px' }}>
            Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          </p>
          <p style={{ color: '#666' }}>
            Rôle requis: <strong>{requiredRole}</strong><br />
            Votre rôle: <strong>{user?.role || 'Non défini'}</strong>
          </p>
        </div>
      );
    }
  }

  // Vérifier les rôles multiples si spécifiés
  if (requiredRoles && Array.isArray(requiredRoles)) {
    const userRole = user?.role?.toLowerCase();
    const hasRequiredRole = requiredRoles.some(role => 
      role.toLowerCase() === userRole
    );
    
    if (!hasRequiredRole) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          textAlign: 'center',
          padding: '20px'
        }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>
            Accès non autorisé
          </h2>
          <p style={{ color: '#666', marginBottom: '20px' }}>
            Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          </p>
          <p style={{ color: '#666' }}>
            Rôles autorisés: <strong>{requiredRoles.join(', ')}</strong><br />
            Votre rôle: <strong>{user?.role || 'Non défini'}</strong>
          </p>
        </div>
      );
    }
  }

  // Afficher le contenu si toutes les vérifications passent
  return children;
};

export default ProtectedRoute;
