<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Fonction d'authentification
function getAuthenticatedUser($pdo) {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        return null;
    }

    // Simulation d'authentification
    if (strpos($token, 'etudiant') !== false) {
        // Récupérer un vrai étudiant de la base de données
        $stmt = $pdo->query("SELECT id FROM etudiants LIMIT 1");
        $etudiant = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$etudiant) {
            return null;
        }

        return [
            'id' => 1,
            'role' => 'etudiant',
            'etudiant_id' => $etudiant['id'],
            'email' => '<EMAIL>'
        ];
    }

    return null;
}

try {
    $user = getAuthenticatedUser($pdo);

    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentification requise ou aucun étudiant trouvé']);
        exit();
    }
    
    // Seuls les étudiants peuvent accéder à cette API
    if ($user['role'] !== 'etudiant') {
        http_response_code(403);
        echo json_encode(['error' => 'Accès réservé aux étudiants']);
        exit();
    }
    
    // Récupérer tous les quiz avec leurs informations complètes
    // Marquer ceux auxquels l'étudiant a déjà répondu
    $sql = "
        SELECT 
            q.id,
            q.question,
            q.devoir_id,
            d.titre as devoir_titre,
            d.description as devoir_description,
            d.date_remise,
            m.nom as matiere_nom,
            c.nom as classe_nom,
            CONCAT(
                LEFT(q.question, 60), 
                IF(LENGTH(q.question) > 60, '...', ''),
                ' (', d.titre, ' - ', m.nom, ')'
            ) as quiz_display,
            CASE 
                WHEN rq.id IS NOT NULL THEN 1 
                ELSE 0 
            END as deja_repondu,
            rq.reponse as ma_reponse,
            rq.id as ma_reponse_id
        FROM quiz q
        LEFT JOIN devoirs d ON q.devoir_id = d.id
        LEFT JOIN matieres m ON d.matiere_id = m.id
        LEFT JOIN classes c ON d.classe_id = c.id
        LEFT JOIN reponsesquiz rq ON q.id = rq.quiz_id AND rq.etudiant_id = ?
        WHERE q.question IS NOT NULL AND q.question != ''
        ORDER BY 
            rq.id IS NULL DESC,  -- Quiz non répondus en premier
            d.date_remise DESC, 
            q.id DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user['etudiant_id']]);
    $quiz = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistiques
    $total_quiz = count($quiz);
    $quiz_repondus = count(array_filter($quiz, function($q) { return $q['deja_repondu']; }));
    $quiz_non_repondus = $total_quiz - $quiz_repondus;
    
    $response = [
        'success' => true,
        'data' => $quiz,
        'statistics' => [
            'total_quiz' => $total_quiz,
            'quiz_repondus' => $quiz_repondus,
            'quiz_non_repondus' => $quiz_non_repondus,
            'pourcentage_completion' => $total_quiz > 0 ? round(($quiz_repondus / $total_quiz) * 100, 1) : 0
        ],
        'etudiant_id' => $user['etudiant_id']
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur serveur: ' . $e->getMessage()
    ]);
}
?>
