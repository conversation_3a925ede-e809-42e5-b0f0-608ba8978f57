<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🎨 DÉMONSTRATION - INTERFACE PARENTS SIMILAIRE AUX FACTURES</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .feature { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .demo-link { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .demo-link:hover { background: #0056b3; color: white; text-decoration: none; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Interface Parents - Design Identique aux Factures</h2>";
    echo "<p>L'interface Parents a été construite pour être <strong>identique</strong> à l'interface des factures :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Même CSS</strong> : Utilise Factures.css</li>";
    echo "<li>✅ <strong>Même structure</strong> : Header, tableau, modal</li>";
    echo "<li>✅ <strong>Même design</strong> : Couleurs, boutons, animations</li>";
    echo "<li>✅ <strong>Même fonctionnalités</strong> : CRUD complet</li>";
    echo "</ul>";
    echo "</div>";
    
    // Comparaison des fonctionnalités
    echo "<div class='step'>";
    echo "<h3>📊 Comparaison Factures vs Parents</h3>";
    
    echo "<div class='comparison'>";
    
    echo "<div>";
    echo "<h4>🧾 Interface Factures</h4>";
    echo "<div class='feature'>📋 Tableau avec étudiants</div>";
    echo "<div class='feature'>💰 Montants et statuts</div>";
    echo "<div class='feature'>📅 Dates de paiement</div>";
    echo "<div class='feature'>⚙️ Actions CRUD</div>";
    echo "<div class='feature'>🎨 Design professionnel</div>";
    echo "<div class='feature'>📱 Responsive</div>";
    echo "</div>";
    
    echo "<div>";
    echo "<h4>👨‍👩‍👧‍👦 Interface Parents</h4>";
    echo "<div class='feature'>📋 Tableau avec parents</div>";
    echo "<div class='feature'>📞 Téléphones et adresses</div>";
    echo "<div class='feature'>👤 Informations utilisateur</div>";
    echo "<div class='feature'>⚙️ Actions CRUD identiques</div>";
    echo "<div class='feature'>🎨 Design identique</div>";
    echo "<div class='feature'>📱 Responsive identique</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Fonctionnalités spécifiques
    echo "<div class='step'>";
    echo "<h3>🔧 Fonctionnalités Spécifiques Parents</h3>";
    
    echo "<h4>✅ Sécurité Renforcée</h4>";
    echo "<ul>";
    echo "<li>🔒 <strong>Filtrage strict</strong> : Seuls les utilisateurs avec rôle 'parent'</li>";
    echo "<li>🚫 <strong>Prévention doublons</strong> : Utilisateurs déjà parents exclus</li>";
    echo "<li>✅ <strong>Validation multicouche</strong> : Frontend + Backend + BDD</li>";
    echo "<li>🛡️ <strong>Triggers de sécurité</strong> : Protection base de données</li>";
    echo "</ul>";
    
    echo "<h4>✅ Interface Adaptée</h4>";
    echo "<ul>";
    echo "<li>👤 <strong>Dropdown intelligent</strong> : Utilisateurs parents disponibles uniquement</li>";
    echo "<li>📞 <strong>Champs spécialisés</strong> : Téléphone et adresse</li>";
    echo "<li>🔄 <strong>Mise à jour dynamique</strong> : Liste se met à jour après actions</li>";
    echo "<li>💬 <strong>Messages informatifs</strong> : Explications claires</li>";
    echo "</ul>";
    echo "</div>";
    
    // Vérification des données
    echo "<div class='step'>";
    echo "<h3>📊 État Actuel des Données</h3>";
    
    // Compter les parents
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM parents");
    $totalParents = $stmt->fetch()['total'];
    
    // Compter les utilisateurs parents
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM utilisateurs u 
        INNER JOIN roles r ON u.role_id = r.id 
        WHERE r.nom = 'parent'
    ");
    $totalUtilisateursParents = $stmt->fetch()['total'];
    
    // Compter les utilisateurs parents disponibles
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM utilisateurs u 
        INNER JOIN roles r ON u.role_id = r.id 
        LEFT JOIN parents p ON u.id = p.utilisateur_id
        WHERE r.nom = 'parent' AND p.id IS NULL
    ");
    $parentsDisponibles = $stmt->fetch()['total'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
    echo "<h4>📈 Statistiques</h4>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>Parents dans la table :</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>$totalParents</td></tr>";
    echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>Utilisateurs avec rôle 'parent' :</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>$totalUtilisateursParents</td></tr>";
    echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>Parents disponibles pour ajout :</strong></td><td style='padding: 8px; border: 1px solid #ddd; color: green; font-weight: bold;'>$parentsDisponibles</td></tr>";
    echo "</table>";
    echo "</div>";
    
    if ($parentsDisponibles > 0) {
        echo "<p class='success'>✅ $parentsDisponibles utilisateur(s) parent(s) disponible(s) pour ajout</p>";
    } else {
        echo "<p class='warning'>⚠️ Aucun utilisateur parent disponible. Tous sont déjà associés ou aucun utilisateur avec le rôle 'parent' n'existe.</p>";
    }
    echo "</div>";
    
    // Exemples de données
    echo "<div class='step'>";
    echo "<h3>👥 Aperçu des Données Parents</h3>";
    
    $stmt = $pdo->query("
        SELECT 
            p.id,
            p.telephone,
            p.adresse,
            CONCAT(u.nom, ' ', u.prenom) as nom_complet,
            u.email,
            r.nom as role_nom
        FROM parents p
        INNER JOIN utilisateurs u ON p.utilisateur_id = u.id
        INNER JOIN roles r ON u.role_id = r.id
        LIMIT 5
    ");
    $exempleParents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($exempleParents) > 0) {
        echo "<p class='info'>📋 Aperçu des " . count($exempleParents) . " premiers parents :</p>";
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>ID</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Nom Complet</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Email</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Téléphone</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Adresse</th>";
        echo "</tr>";
        
        foreach ($exempleParents as $parent) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>#{$parent['id']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>{$parent['nom_complet']}</strong></td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$parent['email']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$parent['telephone']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . (strlen($parent['adresse']) > 30 ? substr($parent['adresse'], 0, 30) . '...' : $parent['adresse']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Aucun parent dans la base de données</p>";
    }
    echo "</div>";
    
    // Liens de démonstration
    echo "<div class='step'>";
    echo "<h3>🚀 Liens de Démonstration</h3>";
    
    echo "<h4>🔧 Tests et Configuration</h4>";
    echo "<a href='add_security_triggers_parents.php' class='demo-link'>🔒 Ajouter Triggers Sécurité</a>";
    echo "<a href='test_security_complete.php' class='demo-link'>🧪 Test Sécurité Complet</a>";
    echo "<a href='../pages/parents/test_filtrage_dropdown.php' class='demo-link'>🔍 Test Filtrage Dropdown</a>";
    
    echo "<h4>🎨 Interfaces</h4>";
    echo "<a href='http://localhost:3000/parents' class='demo-link'>👨‍👩‍👧‍👦 Interface Parents</a>";
    echo "<a href='http://localhost:3000/factures' class='demo-link'>🧾 Interface Factures (Comparaison)</a>";
    
    echo "<h4>📊 APIs Backend</h4>";
    echo "<a href='../pages/parents/parent.php' class='demo-link'>🌐 API Parents</a>";
    echo "<a href='../pages/utilisateurs/utilisateur.php?role=parent' class='demo-link'>👤 API Utilisateurs Parents</a>";
    echo "</div>";
    
    // Instructions d'utilisation
    echo "<div class='step'>";
    echo "<h3>📖 Instructions d'Utilisation</h3>";
    
    echo "<h4>🎯 Pour Tester l'Interface Parents</h4>";
    echo "<ol>";
    echo "<li>🔐 <strong>Connectez-vous</strong> avec un compte Admin</li>";
    echo "<li>🌐 <strong>Accédez</strong> à <a href='http://localhost:3000/parents'>http://localhost:3000/parents</a></li>";
    echo "<li>👀 <strong>Observez</strong> le design identique aux factures</li>";
    echo "<li>➕ <strong>Cliquez</strong> sur 'Nouveau Parent' pour tester l'ajout</li>";
    echo "<li>📋 <strong>Vérifiez</strong> que seuls les utilisateurs parents apparaissent</li>";
    echo "<li>✏️ <strong>Testez</strong> la modification et suppression</li>";
    echo "</ol>";
    
    echo "<h4>🔍 Points à Vérifier</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Design identique</strong> : Même apparence que les factures</li>";
    echo "<li>✅ <strong>Dropdown filtré</strong> : Seuls les parents disponibles</li>";
    echo "<li>✅ <strong>Sécurité active</strong> : Validation des rôles</li>";
    echo "<li>✅ <strong>Messages clairs</strong> : Informations utilisateur</li>";
    echo "<li>✅ <strong>Responsive</strong> : Fonctionne sur mobile</li>";
    echo "</ul>";
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎉 INTERFACE PARENTS PRÊTE</h3>";
    echo "<p class='success' style='font-size: 18px;'>✅ L'interface Parents est maintenant identique aux Factures !</p>";
    
    echo "<h4>🏆 Fonctionnalités Implémentées</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0;'>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px;'>";
    echo "<h5>🎨 Design</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>CSS identique aux factures</li>";
    echo "<li>Couleurs et animations</li>";
    echo "<li>Layout responsive</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 8px;'>";
    echo "<h5>⚙️ Fonctionnalités</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>CRUD complet</li>";
    echo "<li>Modal d'ajout/modification</li>";
    echo "<li>Validation des données</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
    echo "<h5>🔒 Sécurité</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>Filtrage par rôle</li>";
    echo "<li>Prévention doublons</li>";
    echo "<li>Triggers base de données</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px;'>";
    echo "<h5>👤 Expérience</h5>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    echo "<li>Interface intuitive</li>";
    echo "<li>Messages informatifs</li>";
    echo "<li>Cohérence visuelle</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<p class='info'><strong>🚀 Votre interface Parents est maintenant prête et parfaitement alignée sur le design des factures !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
