<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST SOUMISSION ABSENCE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .test-form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 15px 0; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group select, .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    echo "<div class='info'>";
    echo "<h2>🧪 Test de Soumission d'Absence</h2>";
    echo "<p>Simulation de l'envoi de données depuis React vers l'API PHP</p>";
    echo "</div>";
    
    // 1. Récupérer les données nécessaires
    echo "<div class='step'>";
    echo "<h3>📋 1. Données Disponibles</h3>";
    
    // Étudiants
    $stmt = $pdo->query("
        SELECT e.id, u.nom, u.prenom, c.nom as classe_nom 
        FROM Etudiants e 
        JOIN Utilisateurs u ON e.utilisateur_id = u.id 
        LEFT JOIN Classes c ON e.classe_id = c.id 
        ORDER BY u.nom, u.prenom 
        LIMIT 5
    ");
    $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Matières
    $stmt = $pdo->query("SELECT id, nom FROM Matieres ORDER BY nom LIMIT 5");
    $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Enseignants
    $stmt = $pdo->query("
        SELECT e.id, u.nom, u.prenom 
        FROM Enseignants e 
        JOIN Utilisateurs u ON e.utilisateur_id = u.id 
        ORDER BY u.nom, u.prenom 
        LIMIT 5
    ");
    $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>👨‍🎓 Étudiants disponibles :</h4>";
    if (!empty($etudiants)) {
        echo "<ul>";
        foreach ($etudiants as $etudiant) {
            echo "<li><strong>ID: {$etudiant['id']}</strong> - {$etudiant['nom']} {$etudiant['prenom']} - {$etudiant['classe_nom']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='warning'>⚠️ Aucun étudiant trouvé</p>";
    }
    
    echo "<h4>📚 Matières disponibles :</h4>";
    if (!empty($matieres)) {
        echo "<ul>";
        foreach ($matieres as $matiere) {
            echo "<li><strong>ID: {$matiere['id']}</strong> - {$matiere['nom']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='warning'>⚠️ Aucune matière trouvée</p>";
    }
    
    echo "<h4>👨‍🏫 Enseignants disponibles :</h4>";
    if (!empty($enseignants)) {
        echo "<ul>";
        foreach ($enseignants as $enseignant) {
            echo "<li><strong>ID: {$enseignant['id']}</strong> - {$enseignant['nom']} {$enseignant['prenom']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='warning'>⚠️ Aucun enseignant trouvé</p>";
    }
    echo "</div>";
    
    // 2. Formulaire de test
    echo "<div class='step'>";
    echo "<h3>📝 2. Formulaire de Test</h3>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_submit'])) {
        echo "<h4>🔍 Données reçues du formulaire :</h4>";
        echo "<div class='json-block'>";
        echo "POST Data:\n";
        print_r($_POST);
        echo "</div>";
        
        // Simulation de l'insertion
        try {
            $etudiant_id = (int)$_POST['etudiant_id'];
            $matiere_id = !empty($_POST['matiere_id']) ? (int)$_POST['matiere_id'] : null;
            $enseignant_id = !empty($_POST['enseignant_id']) ? (int)$_POST['enseignant_id'] : null;
            $date_absence = $_POST['date_absence'];
            $justification = !empty($_POST['justification']) ? $_POST['justification'] : null;
            
            echo "<h4>🔧 Données nettoyées :</h4>";
            echo "<div class='json-block'>";
            echo "etudiant_id: $etudiant_id (type: " . gettype($etudiant_id) . ")\n";
            echo "matiere_id: " . ($matiere_id ?: 'NULL') . " (type: " . gettype($matiere_id) . ")\n";
            echo "enseignant_id: " . ($enseignant_id ?: 'NULL') . " (type: " . gettype($enseignant_id) . ")\n";
            echo "date_absence: $date_absence\n";
            echo "justification: " . ($justification ?: 'NULL') . "\n";
            echo "</div>";
            
            // Test d'insertion
            $stmt = $pdo->prepare("
                INSERT INTO Absences (etudiant_id, matiere_id, enseignant_id, date_absence, justification) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([$etudiant_id, $matiere_id, $enseignant_id, $date_absence, $justification]);
            
            if ($result) {
                $new_id = $pdo->lastInsertId();
                echo "<p class='success'>✅ Absence créée avec succès ! ID: $new_id</p>";
                
                // Vérifier l'insertion
                $stmt = $pdo->prepare("
                    SELECT a.*, u.nom as etudiant_nom, u.prenom as etudiant_prenom,
                           m.nom as matiere_nom, ue.nom as enseignant_nom
                    FROM Absences a 
                    JOIN Etudiants e ON a.etudiant_id = e.id 
                    JOIN Utilisateurs u ON e.utilisateur_id = u.id 
                    LEFT JOIN Matieres m ON a.matiere_id = m.id
                    LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
                    LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
                    WHERE a.id = ?
                ");
                $stmt->execute([$new_id]);
                $absence = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo "<h4>📋 Absence créée :</h4>";
                echo "<div class='json-block'>";
                echo json_encode($absence, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo "</div>";
            } else {
                echo "<p class='error'>❌ Échec de l'insertion</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur : " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div class='test-form'>";
    echo "<h4>📝 Formulaire de Test d'Absence</h4>";
    echo "<form method='POST'>";
    
    echo "<div class='form-group'>";
    echo "<label>Étudiant :</label>";
    echo "<select name='etudiant_id' required>";
    echo "<option value=''>Sélectionner un étudiant</option>";
    foreach ($etudiants as $etudiant) {
        echo "<option value='{$etudiant['id']}'>{$etudiant['nom']} {$etudiant['prenom']} - {$etudiant['classe_nom']}</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Matière (optionnel) :</label>";
    echo "<select name='matiere_id'>";
    echo "<option value=''>Sélectionner une matière</option>";
    foreach ($matieres as $matiere) {
        echo "<option value='{$matiere['id']}'>{$matiere['nom']}</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Enseignant (optionnel) :</label>";
    echo "<select name='enseignant_id'>";
    echo "<option value=''>Sélectionner un enseignant</option>";
    foreach ($enseignants as $enseignant) {
        echo "<option value='{$enseignant['id']}'>{$enseignant['nom']} {$enseignant['prenom']}</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Date d'absence :</label>";
    echo "<input type='date' name='date_absence' value='" . date('Y-m-d') . "' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Justification (optionnel) :</label>";
    echo "<textarea name='justification' rows='3' placeholder='Motif de l\'absence...'></textarea>";
    echo "</div>";
    
    echo "<button type='submit' name='test_submit' class='btn'>🧪 Tester l'Insertion</button>";
    echo "</form>";
    echo "</div>";
    echo "</div>";
    
    // 3. Test avec cURL (simulation React)
    echo "<div class='step'>";
    echo "<h3>🌐 3. Test avec cURL (Simulation React)</h3>";
    
    if (isset($_GET['test_curl']) && !empty($etudiants)) {
        echo "<h4>🔧 Test d'envoi JSON via cURL :</h4>";
        
        $test_data = [
            'etudiant_id' => $etudiants[0]['id'],
            'matiere_id' => !empty($matieres) ? $matieres[0]['id'] : null,
            'enseignant_id' => !empty($enseignants) ? $enseignants[0]['id'] : null,
            'date_absence' => date('Y-m-d'),
            'justification' => 'Test automatique depuis cURL'
        ];
        
        echo "<h5>📤 Données envoyées :</h5>";
        echo "<div class='json-block'>";
        echo json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        echo "</div>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/Project_PFE/Backend/pages/absences/');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer fake_token_for_test'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<h5>📥 Réponse de l'API :</h5>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        echo "<div class='json-block'>";
        echo htmlspecialchars($response);
        echo "</div>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ Test cURL réussi</p>";
        } else {
            echo "<p class='error'>❌ Test cURL échoué</p>";
        }
    }
    
    echo "<p><a href='?test_curl=1' class='btn'>🧪 Lancer Test cURL</a></p>";
    echo "</div>";
    
    // 4. Instructions de débogage
    echo "<div class='step'>";
    echo "<h3>🐛 4. Instructions de Débogage</h3>";
    
    echo "<h4>🔍 Problèmes possibles :</h4>";
    echo "<ol>";
    echo "<li><strong>Type de données :</strong> etudiant_id doit être un entier, pas une chaîne</li>";
    echo "<li><strong>Valeur du select :</strong> Vérifier que value='{etudiant.id}' et non le texte affiché</li>";
    echo "<li><strong>Conversion :</strong> parseInt() côté React ou (int) côté PHP</li>";
    echo "<li><strong>Données vides :</strong> Vérifier que les étudiants sont bien chargés</li>";
    echo "</ol>";
    
    echo "<h4>✅ Solutions :</h4>";
    echo "<ol>";
    echo "<li><strong>Côté React :</strong> parseInt(formData.etudiant_id) avant envoi</li>";
    echo "<li><strong>Côté PHP :</strong> (int)\$input['etudiant_id'] avant insertion</li>";
    echo "<li><strong>Validation :</strong> Vérifier que l'ID existe en base</li>";
    echo "<li><strong>Debug :</strong> console.log() des données avant envoi</li>";
    echo "</ol>";
    
    echo "<h4>🔧 Code React corrigé :</h4>";
    echo "<div class='json-block'>";
    echo "const cleanData = {\n";
    echo "    etudiant_id: parseInt(formData.etudiant_id),\n";
    echo "    matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,\n";
    echo "    enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,\n";
    echo "    date_absence: formData.date_absence,\n";
    echo "    justification: formData.justification || null\n";
    echo "};\n";
    echo "</div>";
    
    echo "<h4>🔧 Code PHP corrigé :</h4>";
    echo "<div class='json-block'>";
    echo "\$etudiant_id = (int)\$input['etudiant_id'];\n";
    echo "\$matiere_id = !empty(\$input['matiere_id']) ? (int)\$input['matiere_id'] : null;\n";
    echo "\$enseignant_id = !empty(\$input['enseignant_id']) ? (int)\$input['enseignant_id'] : null;\n";
    echo "</div>";
    echo "</div>";
    
    // 5. Liens de test
    echo "<div class='step'>";
    echo "<h3>🔗 5. Liens de Test</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost/Project_PFE/Backend/pages/absences/' target='_blank' class='btn'>🧪 API Absences</a>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='btn'>🎯 Interface React</a>";
    echo "<a href='test_etudiants_api.php' target='_blank' class='btn'>👨‍🎓 Test Étudiants</a>";
    echo "</div>";
    
    echo "<p class='success'>🎯 <strong>Avec les corrections appliquées, l'erreur SQL devrait être résolue !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
