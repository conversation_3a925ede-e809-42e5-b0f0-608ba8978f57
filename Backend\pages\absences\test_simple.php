<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Connexion simple à la base de données
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Requête simple pour récupérer les absences avec les noms
    $stmt = $pdo->prepare("
        SELECT a.id, a.date_absence, a.justification,
               u.nom as etudiant_nom, u.prenom as etudiant_prenom,
               m.nom as matiere_nom,
               ue.nom as enseignant_nom, ue.prenom as enseignant_prenom
        FROM Absences a 
        JOIN Etudiants e ON a.etudiant_id = e.id 
        JOIN Utilisateurs u ON e.utilisateur_id = u.id 
        LEFT JOIN Matieres m ON a.matiere_id = m.id
        LEFT JOIN Enseignants ens ON a.enseignant_id = ens.id
        LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
        ORDER BY a.date_absence DESC
    ");
    
    $stmt->execute();
    $absences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formater les données pour React
    $formatted_absences = [];
    foreach ($absences as $absence) {
        $formatted_absences[] = [
            'id' => $absence['id'],
            'date_absence' => $absence['date_absence'],
            'justification' => $absence['justification'],
            'etudiant_nom' => $absence['etudiant_nom'] . ' ' . $absence['etudiant_prenom'],
            'matiere_nom' => $absence['matiere_nom'] ?: 'Non définie',
            'enseignant_nom' => $absence['enseignant_nom'] ? 
                $absence['enseignant_nom'] . ' ' . $absence['enseignant_prenom'] : 
                'Non défini'
        ];
    }
    
    echo json_encode($formatted_absences, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => __FILE__,
        'line' => __LINE__
    ]);
}
?>
