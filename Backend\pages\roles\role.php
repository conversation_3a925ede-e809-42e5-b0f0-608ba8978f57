
<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Log de debug
error_log("Role API - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Role API - Headers: " . json_encode(getallheaders()));
error_log("Role API - Input: " . file_get_contents("php://input"));

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("POST Data: " . json_encode($data));

    if (!isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("POST Error: Role name required");
        echo json_encode(['success' => false, 'error' => 'Role name required']);
        exit;
    }

    $nom = trim($data['nom']);

    // Vérifier si le rôle existe déjà
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Roles WHERE nom = :nom");
        $checkStmt->execute(['nom' => $nom]);
        if ($checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Role name already exists']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check role error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO Roles (nom) VALUES (:nom)");
        $stmt->execute(['nom' => $nom]);
        $newId = $pdo->lastInsertId();
        error_log("Role created successfully with ID: " . $newId);
        echo json_encode(['success' => true, 'message' => 'Role added successfully', 'id' => $newId]);
    } catch (PDOException $e) {
        error_log("POST Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'GET') {
    try {
        $stmt = $pdo->query("SELECT * FROM Roles ORDER BY id ASC");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("GET Roles: Found " . count($roles) . " roles");
        echo json_encode($roles);
    } catch (PDOException $e) {
        error_log("GET Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'PUT') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("PUT Data: " . json_encode($data));

    if (!isset($data['id']) || !isset($data['nom']) || empty(trim($data['nom']))) {
        error_log("PUT Error: Role id and name required");
        echo json_encode(['success' => false, 'error' => 'Role id and name required']);
        exit;
    }

    $id = intval($data['id']);
    $nom = trim($data['nom']);

    // Vérifier si le rôle existe
    try {
        $checkStmt = $pdo->prepare("SELECT id FROM Roles WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Role not found']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check role existence error: " . $e->getMessage());
    }

    // Vérifier si le nouveau nom existe déjà (sauf pour le rôle actuel)
    try {
        $checkNameStmt = $pdo->prepare("SELECT id FROM Roles WHERE nom = :nom AND id != :id");
        $checkNameStmt->execute(['nom' => $nom, 'id' => $id]);
        if ($checkNameStmt->fetch()) {
            echo json_encode(['success' => false, 'error' => 'Role name already exists']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("Check role name error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("UPDATE Roles SET nom = :nom WHERE id = :id");
        $result = $stmt->execute(['nom' => $nom, 'id' => $id]);

        if ($result && $stmt->rowCount() > 0) {
            error_log("Role updated successfully: ID $id");
            echo json_encode(['success' => true, 'message' => 'Role updated successfully']);
        } else {
            echo json_encode(['success' => false, 'error' => 'No changes made or role not found']);
        }
    } catch (PDOException $e) {
        error_log("PUT Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
} elseif ($method === 'DELETE') {
    $data = json_decode(file_get_contents("php://input"), true);
    error_log("DELETE Data: " . json_encode($data));

    if (!isset($data['id'])) {
        error_log("DELETE Error: Role id required");
        echo json_encode(['success' => false, 'error' => 'Role id required']);
        exit;
    }

    $id = intval($data['id']);

    // Vérifier si le rôle existe
    try {
        $checkStmt = $pdo->prepare("SELECT nom FROM Roles WHERE id = :id");
        $checkStmt->execute(['id' => $id]);
        $role = $checkStmt->fetch();

        if (!$role) {
            echo json_encode(['success' => false, 'error' => 'Role not found']);
            exit;
        }

        // Empêcher la suppression de rôles critiques
        $criticalRoles = ['Admin', 'admin', 'Administrateur'];
        if (in_array($role['nom'], $criticalRoles)) {
            echo json_encode(['success' => false, 'error' => 'Cannot delete critical system role']);
            exit;
        }

    } catch (PDOException $e) {
        error_log("Check role for deletion error: " . $e->getMessage());
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM Roles WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);

        if ($result && $stmt->rowCount() > 0) {
            error_log("Role deleted successfully: ID $id");
            echo json_encode(['success' => true, 'message' => 'Role deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Role not found or already deleted']);
        }
    } catch (PDOException $e) {
        error_log("DELETE Database error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
