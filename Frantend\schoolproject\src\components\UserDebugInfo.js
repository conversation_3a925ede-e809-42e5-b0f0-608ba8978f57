import React, { useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

/**
 * Composant de debug pour afficher les informations utilisateur
 * À utiliser temporairement pour diagnostiquer les problèmes de rôles
 */
const UserDebugInfo = () => {
    const { user, isAuthenticated } = useContext(AuthContext);
    
    const debugStyle = {
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        padding: '10px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        maxWidth: '300px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    };
    
    return (
        <div style={debugStyle}>
            <h4 style={{margin: '0 0 10px 0', fontSize: '14px', color: '#495057'}}>
                🔍 DEBUG USER INFO
            </h4>
            
            <div style={{marginBottom: '8px'}}>
                <strong>isAuthenticated:</strong> {String(isAuthenticated)}
            </div>
            
            <div style={{marginBottom: '8px'}}>
                <strong>user object:</strong> {user ? 'EXISTS' : 'NULL'}
            </div>
            
            {user && (
                <>
                    <div style={{marginBottom: '8px'}}>
                        <strong>user.role:</strong> "{user.role || 'undefined'}"
                    </div>
                    
                    <div style={{marginBottom: '8px'}}>
                        <strong>user.nom:</strong> "{user.nom || 'undefined'}"
                    </div>
                    
                    <div style={{marginBottom: '8px'}}>
                        <strong>user.email:</strong> "{user.email || 'undefined'}"
                    </div>
                    
                    <div style={{marginBottom: '8px'}}>
                        <strong>user.id:</strong> "{user.id || 'undefined'}"
                    </div>
                </>
            )}
            
            <div style={{marginBottom: '8px'}}>
                <strong>Can Add Quiz:</strong> {
                    (user?.role === 'admin' || 
                     user?.role === 'enseignant' || 
                     user?.role === 'Admin' || 
                     user?.role === 'Enseignant' ||
                     !user) ? '✅ YES' : '❌ NO'
                }
            </div>
            
            <div style={{marginBottom: '8px'}}>
                <strong>localStorage token:</strong> {
                    localStorage.getItem('token') ? 'EXISTS' : 'NULL'
                }
            </div>
            
            <div style={{marginBottom: '8px'}}>
                <strong>Full user object:</strong>
                <pre style={{
                    background: '#e9ecef',
                    padding: '5px',
                    borderRadius: '2px',
                    fontSize: '10px',
                    margin: '5px 0 0 0',
                    overflow: 'auto',
                    maxHeight: '100px'
                }}>
                    {JSON.stringify(user, null, 2)}
                </pre>
            </div>
            
            <button 
                onClick={() => {
                    console.log('🔍 FULL DEBUG INFO:');
                    console.log('isAuthenticated:', isAuthenticated);
                    console.log('user:', user);
                    console.log('localStorage token:', localStorage.getItem('token'));
                    console.log('localStorage user:', localStorage.getItem('user'));
                }}
                style={{
                    background: '#007bff',
                    color: 'white',
                    border: 'none',
                    padding: '4px 8px',
                    borderRadius: '2px',
                    fontSize: '10px',
                    cursor: 'pointer'
                }}
            >
                Log to Console
            </button>
        </div>
    );
};

export default UserDebugInfo;
