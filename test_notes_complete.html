<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complet - Notes avec Matières</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #218838; }
        select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 2px solid #ced4da;
            border-radius: 4px;
        }
        .form-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .filter-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #ffffff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Complet - Notes avec Matières</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Ce test simule complètement le comportement de NotesUnified.js pour diagnostiquer le problème des matières.</p>
        <button onclick="runCompleteTest()">🚀 Lancer Test Complet</button>
        <button onclick="clearAll()">🧹 Tout Effacer</button>
    </div>

    <div id="test-results"></div>

    <div class="form-section">
        <h3>🎯 Simulation du Formulaire de Notes</h3>
        <div class="status" id="form-status">Status: En attente de test</div>
        
        <label for="etudiant-select">Étudiant :</label>
        <select id="etudiant-select">
            <option value="">Chargement des étudiants...</option>
        </select>
        
        <label for="matiere-select">Matière :</label>
        <select id="matiere-select">
            <option value="">Chargement des matières...</option>
        </select>
        
        <label for="note-input">Note :</label>
        <input type="number" id="note-input" min="0" max="20" step="0.5" placeholder="Note sur 20">
    </div>

    <div class="filter-section">
        <h3>🔍 Simulation des Filtres</h3>
        <div class="status" id="filter-status">Status: En attente de test</div>
        
        <label for="filter-matiere">Filtrer par matière :</label>
        <select id="filter-matiere">
            <option value="all">📖 Toutes les matières</option>
        </select>
    </div>

    <script>
        let matieres = [];
        let etudiants = [];

        function clearAll() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('form-status').textContent = 'Status: En attente de test';
            document.getElementById('filter-status').textContent = 'Status: En attente de test';
            
            // Reset selects
            document.getElementById('etudiant-select').innerHTML = '<option value="">Chargement des étudiants...</option>';
            document.getElementById('matiere-select').innerHTML = '<option value="">Chargement des matières...</option>';
            document.getElementById('filter-matiere').innerHTML = '<option value="all">📖 Toutes les matières</option>';
            document.getElementById('note-input').value = '';
            
            matieres = [];
            etudiants = [];
        }

        async function runCompleteTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Test complet en cours...</h3></div>';
            
            try {
                // Étape 1: Charger les matières
                console.log('🔄 Étape 1: Chargement des matières...');
                await fetchMatieres();
                
                // Étape 2: Charger les étudiants
                console.log('🔄 Étape 2: Chargement des étudiants...');
                await fetchEtudiants();
                
                // Étape 3: Peupler les selects
                console.log('🔄 Étape 3: Population des selects...');
                populateSelects();
                
                // Étape 4: Afficher les résultats
                displayResults();
                
            } catch (error) {
                console.error('❌ Erreur dans le test complet:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Erreur dans le Test Complet</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function fetchMatieres() {
            try {
                console.log('🔄 Chargement des matières...');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/getMatieres_no_auth.php');
                const data = await response.json();
                
                console.log('🔍 DEBUG MATIERES API Response:', data);
                
                if (data.success && data.matieres) {
                    matieres = data.matieres;
                    console.log('✅ Matières chargées:', data.matieres.length);
                } else if (Array.isArray(data)) {
                    matieres = data;
                    console.log('✅ Matières chargées (format tableau):', data.length);
                } else {
                    throw new Error('Format de réponse inattendu pour les matières');
                }
                
            } catch (error) {
                console.error('❌ Erreur lors du chargement des matières:', error);
                throw error;
            }
        }

        async function fetchEtudiants() {
            try {
                console.log('🔄 Chargement des étudiants...');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    headers: { 'Authorization': 'Bearer test-token' }
                });
                const data = await response.json();
                
                console.log('🔍 DEBUG ETUDIANTS API Response:', data);
                
                if (data.success) {
                    etudiants = data.etudiants;
                    console.log('✅ Étudiants chargés:', data.etudiants.length);
                } else {
                    // Simuler des étudiants pour le test
                    etudiants = [
                        { id: 1, nom: 'Dupont Jean', email: '<EMAIL>' },
                        { id: 2, nom: 'Martin Marie', email: '<EMAIL>' }
                    ];
                    console.log('⚠️ Utilisation d\'étudiants simulés');
                }
                
            } catch (error) {
                console.error('❌ Erreur lors du chargement des étudiants:', error);
                // Simuler des étudiants pour le test
                etudiants = [
                    { id: 1, nom: 'Dupont Jean', email: '<EMAIL>' },
                    { id: 2, nom: 'Martin Marie', email: '<EMAIL>' }
                ];
                console.log('⚠️ Utilisation d\'étudiants simulés après erreur');
            }
        }

        function populateSelects() {
            // Peupler le select des étudiants
            const etudiantSelect = document.getElementById('etudiant-select');
            etudiantSelect.innerHTML = '<option value="">Sélectionner un étudiant...</option>';
            
            etudiants.forEach(etudiant => {
                const option = document.createElement('option');
                option.value = etudiant.id;
                option.textContent = `📚 ${etudiant.nom} | Email: ${etudiant.email}`;
                etudiantSelect.appendChild(option);
            });
            
            // Peupler le select des matières (formulaire)
            const matiereSelect = document.getElementById('matiere-select');
            matiereSelect.innerHTML = '<option value="">Sélectionner une matière...</option>';
            
            matieres.forEach(matiere => {
                const option = document.createElement('option');
                option.value = matiere.id;
                option.textContent = `📖 ${matiere.nom} | Filière: ${matiere.filiere_nom || 'N/A'}`;
                matiereSelect.appendChild(option);
            });
            
            // Peupler le select de filtrage
            const filterSelect = document.getElementById('filter-matiere');
            filterSelect.innerHTML = '<option value="all">📖 Toutes les matières</option>';
            
            matieres.forEach(matiere => {
                const option = document.createElement('option');
                option.value = matiere.nom;
                option.textContent = matiere.nom;
                filterSelect.appendChild(option);
            });
            
            // Mettre à jour les status
            document.getElementById('form-status').textContent = `Status: ✅ ${etudiants.length} étudiants et ${matieres.length} matières chargés`;
            document.getElementById('form-status').className = 'status success';
            
            document.getElementById('filter-status').textContent = `Status: ✅ ${matieres.length} matières disponibles pour filtrage`;
            document.getElementById('filter-status').className = 'status success';
        }

        function displayResults() {
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = `
                <div class="test-section success">
                    <h3>✅ Test Complet Réussi</h3>
                    <p><strong>Matières chargées:</strong> ${matieres.length}</p>
                    <p><strong>Étudiants chargés:</strong> ${etudiants.length}</p>
                    <h4>Première matière (exemple):</h4>
                    <pre>${matieres.length > 0 ? JSON.stringify(matieres[0], null, 2) : 'Aucune matière'}</pre>
                    <h4>Premier étudiant (exemple):</h4>
                    <pre>${etudiants.length > 0 ? JSON.stringify(etudiants[0], null, 2) : 'Aucun étudiant'}</pre>
                </div>
            `;
        }
    </script>
</body>
</html>
