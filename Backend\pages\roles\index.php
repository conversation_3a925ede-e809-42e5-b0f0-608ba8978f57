<?php
// API alternative pour les rôles - compatible avec UserEditModal
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    try {
        // Vérifier si la table roles existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        if ($stmt->rowCount() == 0) {
            // Essayer avec Roles (majuscule)
            $stmt = $pdo->query("SHOW TABLES LIKE 'Roles'");
            if ($stmt->rowCount() == 0) {
                echo json_encode(['error' => 'Table roles non trouvée']);
                exit();
            }
            $table_name = 'Roles';
        } else {
            $table_name = 'roles';
        }
        
        // Récupérer tous les rôles
        $stmt = $pdo->query("SELECT * FROM $table_name ORDER BY id ASC");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Retourner dans le format attendu par UserEditModal
        echo json_encode([
            'success' => true,
            'roles' => $roles,
            'count' => count($roles)
        ]);
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
