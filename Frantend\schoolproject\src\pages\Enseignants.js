import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import { AuthContext } from '../context/AuthContext';
import '../css/Factures.css';

const Enseignants = () => {
    const { user } = useContext(AuthContext);
    const [enseignants, setEnseignants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingEnseignant, setEditingEnseignant] = useState(null);
    const [utilisateursEnseignants, setUtilisateursEnseignants] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        utilisateur_id: '',
        nom_prenom: '',
        email: '',
        telephone: '',
        specialite: '',
        date_embauche: '',
        salaire: '',
        statut: 'actif'
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';

    useEffect(() => {
        fetchEnseignants();
        if (isAdmin) {
            fetchUtilisateursEnseignants();
        }
    }, [isAdmin]);

    const fetchEnseignants = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des enseignants...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('✅ Réponse API enseignants:', response.data);

            if (response.data.success) {
                setEnseignants(Array.isArray(response.data.enseignants) ? response.data.enseignants : []);
            } else {
                setEnseignants(Array.isArray(response.data) ? response.data : []);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des enseignants:', error);
            Swal.fire('Erreur', 'Impossible de charger les enseignants', 'error');
            setEnseignants([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchUtilisateursEnseignants = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des utilisateurs enseignants...');

            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les enseignants existants pour les exclure
            const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Filtrer pour ne garder que les utilisateurs avec le rôle "enseignant"
            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];

            const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'enseignant' || roleNom === 'enseignants';
            });

            // Exclure les utilisateurs déjà enseignants
            const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user =>
                !enseignantsExistantsIds.includes(user.id)
            );

            console.log('✅ Utilisateurs enseignants disponibles:', utilisateursDisponibles.length);
            setUtilisateursEnseignants(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs enseignants:', error);
            setUtilisateursEnseignants([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des enseignants', 'error');
            return;
        }

        if (!formData.utilisateur_id || !formData.nom_prenom || !formData.email) {
            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php';
            const method = editingEnseignant ? 'PUT' : 'POST';
            const data = editingEnseignant ? { ...formData, id: editingEnseignant.id } : formData;

            console.log('🔄 Envoi requête enseignant:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data.success) {
                Swal.fire('Succès', response.data.message, 'success');
                setShowModal(false);
                setEditingEnseignant(null);
                resetForm();
                fetchEnseignants();
                if (isAdmin) fetchUtilisateursEnseignants();
            } else {
                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = async (enseignant) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des enseignants', 'error');
            return;
        }

        // Recharger les utilisateurs enseignants en incluant l'utilisateur actuel
        await fetchUtilisateursEnseignantsAvecActuel(enseignant.utilisateur_id);

        setEditingEnseignant(enseignant);
        setFormData({
            utilisateur_id: enseignant.utilisateur_id || '',
            nom_prenom: enseignant.nom_prenom || '',
            email: enseignant.email || '',
            telephone: enseignant.telephone || '',
            specialite: enseignant.specialite || '',
            date_embauche: enseignant.date_embauche || '',
            salaire: enseignant.salaire || '',
            statut: enseignant.statut || 'actif'
        });
        setShowModal(true);
    };

    const fetchUtilisateursEnseignantsAvecActuel = async (currentUserId) => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des utilisateurs enseignants avec actuel...');

            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les enseignants existants pour les exclure
            const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];

            const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'enseignant' || roleNom === 'enseignants';
            });

            // Exclure les utilisateurs déjà enseignants SAUF l'utilisateur actuel
            const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user =>
                !enseignantsExistantsIds.includes(user.id) || user.id === currentUserId
            );

            console.log('✅ Utilisateurs enseignants avec actuel:', utilisateursDisponibles.length);
            setUtilisateursEnseignants(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs enseignants avec actuel:', error);
            setUtilisateursEnseignants([]);
        }
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des enseignants', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: 'Cette action est irréversible !',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer !',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                console.log('🗑️ Suppression enseignant ID:', id);

                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                console.log('✅ Réponse suppression:', response.data);

                if (response.data.success) {
                    Swal.fire('Supprimé!', response.data.message, 'success');
                    fetchEnseignants();
                    if (isAdmin) fetchUtilisateursEnseignants();
                } else {
                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer l\'enseignant', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer l\'enseignant', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            utilisateur_id: '',
            nom_prenom: '',
            email: '',
            telephone: '',
            specialite: '',
            date_embauche: '',
            salaire: '',
            statut: 'actif'
        });
    };



    // Filtrage des enseignants
    const filteredEnseignants = enseignants.filter(enseignant => {
        const searchLower = searchTerm.toLowerCase();
        return (
            (enseignant.nom_prenom || '').toLowerCase().includes(searchLower) ||
            (enseignant.email || '').toLowerCase().includes(searchLower) ||
            (enseignant.specialite || '').toLowerCase().includes(searchLower) ||
            (enseignant.telephone || '').toLowerCase().includes(searchLower)
        );
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentEnseignants = filteredEnseignants.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredEnseignants.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when search changes
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm]);

    const getStatutBadge = (statut) => {
        const style = {
            padding: '4px 12px',
            borderRadius: '20px',
            fontSize: '0.8em',
            fontWeight: 'bold',
            textTransform: 'uppercase'
        };

        if (statut === 'actif') {
            return <span style={{...style, backgroundColor: '#d4edda', color: '#155724'}}>Actif</span>;
        } else {
            return <span style={{...style, backgroundColor: '#f8d7da', color: '#721c24'}}>Inactif</span>;
        }
    };

    const formatSalaire = (salaire) => {
        if (!salaire) return 'Non spécifié';
        return new Intl.NumberFormat('fr-MA', {
            style: 'currency',
            currency: 'MAD'
        }).format(salaire);
    };

    const styles = {
        infoMessage: {
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '8px',
            padding: '15px',
            margin: '20px 0',
            color: '#1565c0'
        },
        idBadge: {
            backgroundColor: '#007bff',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.8em',
            fontWeight: 'bold'
        }
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des enseignants...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>👨‍🏫 Gestion des Enseignants</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredEnseignants.length} enseignant(s) trouvé(s)
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvel Enseignant
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={styles.infoMessage}>
                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les enseignants en mode lecture seule. Seul l'administrateur peut créer, modifier ou supprimer des enseignants.</p>
                </div>
            )}

            {/* Barre de recherche */}
            <div className="search-section">
                <div className="search-bar">
                   
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par nom, email, spécialité, téléphone..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>

            {/* Tableau des enseignants */}
            <div className="table-container">
                {filteredEnseignants.length === 0 ? (
                    <div className="no-data">
                        <img src="/empty.png" alt="Aucune donnée" />
                        <p>Aucun enseignant trouvé</p>
                        <p>Essayez de modifier vos critères de recherche</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>👤 Nom Complet</th>
                                    <th>📧 Email</th>
                                    <th>📞 Téléphone</th>
                                    <th>🎓 Spécialité</th>
                                    <th>💰 Salaire</th>
                                    <th>📊 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentEnseignants.map((enseignant) => (
                                    <tr key={enseignant.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{enseignant.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="user-info">
                                                <strong>{enseignant.nom_prenom || 'Nom non disponible'}</strong>
                                                <br />
                                                <small style={{ color: '#6c757d' }}>
                                                    {enseignant.utilisateur_id ? `Utilisateur ID: ${enseignant.utilisateur_id}` : 'Pas d\'utilisateur lié'}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {enseignant.email || 'Email non disponible'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {enseignant.telephone || 'Non renseigné'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {enseignant.specialite || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50', fontSize: '1.1em' }}>
                                                {formatSalaire(enseignant.salaire)}
                                            </strong>
                                        </td>
                                        <td>{getStatutBadge(enseignant.statut)}</td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(enseignant)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(enseignant.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="btn btn-outline-primary"
                    >
                        Précédent
                    </button>

                    <div className="page-info">
                        Page {currentPage} sur {totalPages}
                    </div>

                    <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="btn btn-outline-primary"
                    >
                        Suivant
                    </button>
                </div>
            )}

            {/* Modal pour ajouter/modifier un enseignant */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingEnseignant ? 'Modifier l\'enseignant' : 'Nouvel enseignant'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingEnseignant(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Utilisateur (Enseignant) *</label>
                                <select
                                    value={formData.utilisateur_id}
                                    onChange={(e) => {
                                        const selectedUser = utilisateursEnseignants.find(u => u.id == e.target.value);
                                        setFormData({
                                            ...formData,
                                            utilisateur_id: e.target.value,
                                            nom_prenom: selectedUser ? selectedUser.nom : formData.nom_prenom,
                                            email: selectedUser ? selectedUser.email : formData.email
                                        });
                                    }}
                                    required
                                    disabled={editingEnseignant} // Empêcher la modification de l'utilisateur lors de l'édition
                                >
                                    <option value="">Sélectionner un utilisateur enseignant...</option>
                                    {utilisateursEnseignants.map(user => (
                                        <option key={user.id} value={user.id}>
                                            {user.nom} - {user.email} (ID: {user.id})
                                        </option>
                                    ))}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Seuls les utilisateurs avec le rôle "enseignant" non encore assignés sont affichés
                                </small>
                            </div>

                            <div className="form-group">
                                <label>Nom Complet *</label>
                                <input
                                    type="text"
                                    value={formData.nom_prenom}
                                    onChange={(e) => setFormData({...formData, nom_prenom: e.target.value})}
                                    placeholder="Nom complet de l'enseignant"
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label>Email *</label>
                                <input
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                                    placeholder="Email de l'enseignant"
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label>Téléphone</label>
                                <input
                                    type="tel"
                                    value={formData.telephone}
                                    onChange={(e) => setFormData({...formData, telephone: e.target.value})}
                                    placeholder="Numéro de téléphone"
                                />
                            </div>

                            <div className="form-group">
                                <label>Spécialité</label>
                                <input
                                    type="text"
                                    value={formData.specialite}
                                    onChange={(e) => setFormData({...formData, specialite: e.target.value})}
                                    placeholder="Spécialité de l'enseignant"
                                />
                            </div>

                            <div className="form-group">
                                <label>Date d'embauche</label>
                                <input
                                    type="date"
                                    value={formData.date_embauche}
                                    onChange={(e) => setFormData({...formData, date_embauche: e.target.value})}
                                />
                            </div>

                            <div className="form-group">
                                <label>Salaire (MAD)</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={formData.salaire}
                                    onChange={(e) => setFormData({...formData, salaire: e.target.value})}
                                    placeholder="Salaire en dirhams"
                                />
                            </div>

                            <div className="form-group">
                                <label>Statut *</label>
                                <select
                                    value={formData.statut}
                                    onChange={(e) => setFormData({...formData, statut: e.target.value})}
                                    required
                                >
                                    <option value="actif">Actif</option>
                                    <option value="inactif">Inactif</option>
                                </select>
                            </div>

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingEnseignant ? '💾 Modifier' : '➕ Créer'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingEnseignant(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Enseignants;
