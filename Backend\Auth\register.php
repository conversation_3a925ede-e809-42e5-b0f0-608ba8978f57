<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type");

$host = 'localhost';
$dbname = 'GestionScolaire';
$username = 'root';
$password = '';

$conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);

$data = json_decode(file_get_contents("php://input"));

if (
    isset($data->nom) &&
    isset($data->email) &&
    isset($data->mot_de_passe) &&
    isset($data->role_id)
) {
    $nom = $data->nom;
    $email = $data->email;
    $mot_de_passe = password_hash($data->mot_de_passe, PASSWORD_BCRYPT);
    $role_id = $data->role_id;

    $stmt = $conn->prepare("INSERT INTO Utilisateurs (nom, email, mot_de_passe, role_id) VALUES (?, ?, ?, ?)");
    if ($stmt->execute([$nom, $email, $mot_de_passe, $role_id])) {
        echo json_encode(["message" => "Inscription réussie"]);
    } else {
        echo json_encode(["message" => "Erreur lors de l'inscription"]);
    }
} else {
    echo json_encode(["message" => "Champs manquants"]);
}
?>
