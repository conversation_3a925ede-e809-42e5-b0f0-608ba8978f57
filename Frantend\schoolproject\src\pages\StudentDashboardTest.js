import React, { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../context/AuthContext';
import { StudentReadOnlyAlert, useStudentDataFilter } from '../components/StudentDataFilter';
import '../css/Factures.css';

const StudentDashboardTest = () => {
  const { user } = useContext(AuthContext);
  const { isStudentReadOnly, currentUserId, userRole } = useStudentDataFilter();
  const [testData, setTestData] = useState([]);

  useEffect(() => {
    // Simuler des données de test
    const mockData = [
      { id: 1, utilisateur_id: 1, nom: 'Test Étudiant 1', note: 15, matiere: 'Mathématiques' },
      { id: 2, utilisateur_id: 2, nom: 'Test Étudiant 2', note: 12, matiere: 'Français' },
      { id: 3, utilisateur_id: 1, nom: 'Test Étudiant 1', note: 18, matiere: 'Sciences' },
      { id: 4, utilisateur_id: 3, nom: 'Test Étudiant 3', note: 14, matiere: 'Histoire' }
    ];
    setTestData(mockData);
  }, []);

  // Filtrer les données pour l'étudiant connecté
  const filteredData = testData.filter(item => 
    !isStudentReadOnly || item.utilisateur_id.toString() === currentUserId?.toString()
  );

  return (
    <div className="factures-container">
      <div className="page-header">
        <h1>🧪 Test Interface Étudiant</h1>
        <div className="header-info">
          <span className="total-count">
            Mode: {isStudentReadOnly ? 'Étudiant (Lecture seule)' : 'Administrateur'}
          </span>
        </div>
      </div>

      <StudentReadOnlyAlert />

      {/* Informations utilisateur */}
      <div style={{
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3>👤 Informations Utilisateur</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div>
            <strong>ID:</strong> {currentUserId || 'Non défini'}
          </div>
          <div>
            <strong>Email:</strong> {user?.email || 'Non défini'}
          </div>
          <div>
            <strong>Rôle:</strong> {userRole || 'Non défini'}
          </div>
          <div>
            <strong>Mode lecture seule:</strong> {isStudentReadOnly ? '✅ Oui' : '❌ Non'}
          </div>
        </div>
      </div>

      {/* Test de filtrage des données */}
      <div style={{
        backgroundColor: '#fff',
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3>📊 Test de Filtrage des Données</h3>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>Données totales:</strong> {testData.length} éléments
          <br />
          <strong>Données visibles:</strong> {filteredData.length} éléments
          <br />
          <strong>Filtrage actif:</strong> {isStudentReadOnly ? '✅ Oui (données personnelles uniquement)' : '❌ Non (toutes les données)'}
        </div>

        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th>🆔 ID</th>
                <th>👤 Nom</th>
                <th>📊 Note</th>
                <th>📚 Matière</th>
                <th>🔑 User ID</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map((item) => (
                <tr key={item.id}>
                  <td>#{item.id}</td>
                  <td>{item.nom}</td>
                  <td>
                    <span style={{
                      padding: '4px 8px',
                      backgroundColor: item.note >= 15 ? '#d4edda' : item.note >= 10 ? '#fff3cd' : '#f8d7da',
                      borderRadius: '4px',
                      color: item.note >= 15 ? '#155724' : item.note >= 10 ? '#856404' : '#721c24'
                    }}>
                      {item.note}/20
                    </span>
                  </td>
                  <td>{item.matiere}</td>
                  <td>
                    <span style={{
                      padding: '2px 6px',
                      backgroundColor: item.utilisateur_id.toString() === currentUserId?.toString() ? '#d4edda' : '#f8d7da',
                      borderRadius: '4px',
                      fontSize: '0.8em',
                      color: item.utilisateur_id.toString() === currentUserId?.toString() ? '#155724' : '#721c24'
                    }}>
                      {item.utilisateur_id} {item.utilisateur_id.toString() === currentUserId?.toString() ? '(Vous)' : ''}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Navigation disponible */}
      <div style={{
        backgroundColor: '#e3f2fd',
        border: '1px solid #2196f3',
        borderRadius: '8px',
        padding: '20px'
      }}>
        <h3>🧭 Navigation Disponible pour les Étudiants</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          {[
            { name: 'Matières', icon: '📚', access: 'Lecture seule' },
            { name: 'Filières', icon: '🎓', access: 'Lecture seule' },
            { name: 'Classes', icon: '🏫', access: 'Lecture seule' },
            { name: 'Groupes', icon: '👥', access: 'Lecture seule' },
            { name: 'Niveaux', icon: '📊', access: 'Lecture seule' },
            { name: 'Cours', icon: '📖', access: 'Lecture + Téléchargement PDF' },
            { name: 'Devoirs', icon: '📝', access: 'Lecture seule' },
            { name: 'Notes', icon: '📊', access: 'Mes notes uniquement' },
            { name: 'Diplômes', icon: '🎓', access: 'Mes diplômes uniquement' },
            { name: 'Retards', icon: '⏰', access: 'Mes retards uniquement' },
            { name: 'Absences', icon: '❌', access: 'Mes absences uniquement' }
          ].map((item, index) => (
            <div key={index} style={{
              padding: '10px',
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #e0e0e0',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '1.5em', marginBottom: '5px' }}>{item.icon}</div>
              <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{item.name}</div>
              <div style={{ fontSize: '0.8em', color: '#666' }}>{item.access}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StudentDashboardTest;
