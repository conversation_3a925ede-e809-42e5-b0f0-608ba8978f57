import React from 'react';

const UserSelectField = ({ 
    value, 
    onChange, 
    utilisateurs, 
    editingEnseignant, 
    required = false,
    label = "Utilisateur Enseignant"
}) => {
    
    if (editingEnseignant) {
        // Mode modification : affichage en lecture seule
        return (
            <div className="form-group">
                <label>{label} *</label>
                <div
                    style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #ced4da',
                        borderRadius: '4px',
                        fontSize: '14px',
                        backgroundColor: '#f8f9fa',
                        color: '#6c757d',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px'
                    }}
                >
                    <span>👤</span>
                    <span>{editingEnseignant.nom_prenom} - {editingEnseignant.email}</span>
                </div>
                
                {/* Champ caché pour conserver la valeur utilisateur_id */}
                <input
                    type="hidden"
                    name="utilisateur_id"
                    value={value}
                />
                
                <small style={{ color: '#6c757d', fontSize: '12px', display: 'block', marginTop: '5px' }}>
                    ℹ️ L'utilisateur ne peut pas être modifié en mode édition pour préserver l'intégrité des données
                </small>
            </div>
        );
    }
    
    // Mode création : select normal
    return (
        <div className="form-group">
            <label>{label} {required && '*'}</label>
            <select
                value={value}
                onChange={onChange}
                required={required}
                style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ced4da',
                    borderRadius: '4px',
                    fontSize: '14px'
                }}
            >
                <option value="">Sélectionner un utilisateur enseignant</option>
                {utilisateurs.map((user) => (
                    <option key={user.id} value={user.id}>
                        {user.nom} {user.prenom} - {user.email}
                    </option>
                ))}
            </select>
            
            <small style={{ color: '#6c757d', fontSize: '12px', display: 'block', marginTop: '5px' }}>
                ✅ Seuls les utilisateurs avec le rôle "enseignant" non encore associés sont affichés
                <br />
                📊 {utilisateurs.length} utilisateur(s) disponible(s)
            </small>
        </div>
    );
};

export default UserSelectField;
