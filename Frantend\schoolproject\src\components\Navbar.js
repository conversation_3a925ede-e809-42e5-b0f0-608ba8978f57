import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  FaUsers,
  FaBook,
  FaGraduationCap,
  FaLayerGroup,
  FaChalkboardTeacher,
  FaUserGraduate,
  FaClipboardList,
  FaTasks,
  FaSignInAlt,
  FaUserPlus,
  FaUserFriends,
  FaBars,
  FaTimes,
  FaMoneyBillWave,
  FaCertificate,
  FaCalendarTimes,
  FaClock,
  FaQuestionCircle,
  FaCalendarAlt,
  FaChartBar,
  FaEnvelope,
  FaHeart
} from 'react-icons/fa';

const Navbar = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const location = useLocation();

  const menuItems = [
    { path: '/roles', label: 'Rôles', icon: <FaUsers /> },
    { path: '/utilisateurs', label: 'Utilisateurs', icon: <FaUsers /> },
    { path: '/matieres', label: 'Matières', icon: <FaBook /> },
    { path: '/filieres', label: 'Filières', icon: <FaGraduationCap /> },
    { path: '/niveaux', label: 'Niveaux', icon: <FaLayerGroup /> },
    { path: '/classes', label: 'Classes', icon: <FaChalkboardTeacher /> },
    { path: '/groupes', label: 'Groupes', icon: <FaUserFriends /> },
    { path: '/cours', label: 'Cours', icon: <FaClipboardList /> },
    { path: '/devoirs', label: 'Devoirs', icon: <FaTasks /> },
    { path: '/emplois-du-temps', label: 'Emplois du Temps', icon: <FaCalendarAlt /> },
    { path: '/notes', label: 'Notes', icon: <FaChartBar /> },
    { path: '/messagerie', label: 'Messagerie', icon: <FaEnvelope /> },
    { path: '/parent-etudiant', label: 'Relations Familiales', icon: <FaHeart /> },
    { path: '/quiz', label: 'Quiz', icon: <FaQuestionCircle /> },
    { path: '/reponses-quiz', label: 'Reponses Quiz', icon: <FaQuestionCircle /> },
    { path: '/factures', label: 'Factures', icon: <FaMoneyBillWave /> },
    { path: '/diplomes', label: 'Diplômes', icon: <FaCertificate /> },
    { path: '/absences', label: 'Absences', icon: <FaCalendarTimes /> },
    { path: '/retards', label: 'Retards', icon: <FaClock /> }, 
    { path: '/parents', label: 'Parents', icon: <FaUserFriends /> },
    { path: '/etudiants', label: 'Étudiants', icon: <FaUserGraduate /> },
    { path: '/enseignants', label: 'Enseignants', icon: <FaChalkboardTeacher /> },
     { path: '/registers', label: 'Inscription', icon: <FaUserPlus /> },
     { path: '/login', label: 'Connexion', icon: <FaSignInAlt /> }
  ];

  const navStyles = {
    nav: {
      backgroundColor: 'var(--cerulean)',
      padding: '20px 10px',
      width: isExpanded ? '250px' : '70px',
      height: '100vh',
      position: 'fixed',
      top: 0,
      left: 0,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      transition: 'width 0.3s ease-in-out',
      boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
      zIndex: 1000,
      overflow: 'hidden'
    },
    toggleButton: {
      background: 'none',
      border: 'none',
      color: 'var(--antiflash-white)',
      fontSize: '1.5rem',
      cursor: 'pointer',
      marginBottom: '30px',
      padding: '10px',
      borderRadius: '8px',
      transition: 'all 0.3s ease',
      alignSelf: isExpanded ? 'flex-end' : 'center'
    },
    menuItem: {
      color: 'var(--antiflash-white)',
      textDecoration: 'none',
      display: 'flex',
      alignItems: 'center',
      padding: '12px 15px',
      marginBottom: '8px',
      borderRadius: '10px',
      transition: 'all 0.3s ease',
      width: '100%',
      position: 'relative',
      overflow: 'hidden'
    },
    menuIcon: {
      fontSize: '1.2rem',
      minWidth: '20px',
      textAlign: 'center'
    },
    menuLabel: {
      marginLeft: '15px',
      opacity: isExpanded ? 1 : 0,
      transform: isExpanded ? 'translateX(0)' : 'translateX(-20px)',
      transition: 'all 0.3s ease',
      whiteSpace: 'nowrap',
      fontSize: '0.9rem',
      fontWeight: '500'
    }
  };

  const isActive = (path) => location.pathname === path;

  return (
    <nav style={navStyles.nav}>
      <button
        style={navStyles.toggleButton}
        onClick={() => setIsExpanded(!isExpanded)}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = 'transparent';
          e.target.style.transform = 'scale(1)';
        }}
      >
        {isExpanded ? <FaTimes /> : <FaBars />}
      </button>

      {menuItems.map((item, index) => (
        <Link
          key={item.path}
          to={item.path}
          style={{
            ...navStyles.menuItem,
            backgroundColor: isActive(item.path) ? 'rgba(255,255,255,0.2)' : 'transparent',
            borderLeft: isActive(item.path) ? '4px solid rgb(240, 247, 252)' : '4px solid transparent',
            animationDelay: `${index * 0.1}s`
          }}
          onMouseEnter={(e) => {
            if (!isActive(item.path)) {
              e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
            }
            e.target.style.transform = 'translateX(5px)';
            e.target.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
          }}
          onMouseLeave={(e) => {
            if (!isActive(item.path)) {
              e.target.style.backgroundColor = 'transparent';
            }
            e.target.style.transform = 'translateX(0)';
            e.target.style.boxShadow = 'none';
          }}
        >
          <span style={navStyles.menuIcon}>
            {item.icon}
          </span>
          <span style={navStyles.menuLabel}>
            {item.label}
          </span>
        </Link>
      ))}
    </nav>
  );
};

export default Navbar;
