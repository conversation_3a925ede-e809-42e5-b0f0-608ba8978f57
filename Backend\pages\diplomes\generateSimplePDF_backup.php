<?php
// Version de sauvegarde avec script d'impression simplifié
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

function generateSimplePDFBackup($diplome_id) {
    global $pdo;
    
    try {
        // Récupérer les informations du diplôme avec jointures
        $stmt = $pdo->prepare("
            SELECT 
                d.id,
                d.titre,
                d.date_obtention,
                d.numero_diplome,
                CONCAT(u.nom, ' ', u.prenom) as etudiant_nom,
                u.email as etudiant_email,
                u.numero_etudiant,
                f.nom as filiere_nom,
                n.nom as niveau_nom,
                c.nom as classe_nom
            FROM diplomes d
            LEFT JOIN etudiants et ON d.etudiant_id = et.id
            LEFT JOIN utilisateurs u ON et.utilisateur_id = u.id
            LEFT JOIN filieres f ON et.filiere_id = f.id
            LEFT JOIN niveaux n ON et.niveau_id = n.id
            LEFT JOIN classes c ON et.classe_id = c.id
            WHERE d.id = :diplome_id
        ");
        
        $stmt->execute(['diplome_id' => $diplome_id]);
        $diplome = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$diplome) {
            throw new Exception("Diplôme non trouvé avec l'ID: $diplome_id");
        }
        
        // Formater la date
        $date_obtention = new DateTime($diplome['date_obtention']);
        $diplome['date_obtention_fr'] = $date_obtention->format('d/m/Y');
        
        // Calculer l'année scolaire
        $annee = $date_obtention->format('Y');
        $mois = $date_obtention->format('n');
        if ($mois >= 9) {
            $annee_scolaire = $annee . '-' . ($annee + 1);
        } else {
            $annee_scolaire = ($annee - 1) . '-' . $annee;
        }
        
    } catch (Exception $e) {
        error_log("Erreur lors de la récupération du diplôme: " . $e->getMessage());
        throw new Exception("Erreur lors de la génération du diplôme: " . $e->getMessage());
    }

    // Générer le HTML avec script d'impression simplifié
    return '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diplôme - ' . htmlspecialchars($diplome['etudiant_nom']) . '</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Times New Roman", serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .diploma-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            border: 8px solid #2c5aa0;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            position: relative;
            min-height: 90vh;
        }
        
        .decorative-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px solid #gold;
            border-radius: 10px;
            pointer-events: none;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 1;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #2c5aa0;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            margin: 0 auto 20px;
        }
        
        .school-name {
            font-size: 28px;
            color: #2c5aa0;
            margin-bottom: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .diploma-title {
            font-size: 24px;
            color: #d4af37;
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .content {
            text-align: center;
            position: relative;
            z-index: 1;
        }
        
        .certification-text {
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        
        .student-info {
            margin: 40px 0;
        }
        
        .student-name {
            font-size: 36px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 3px;
            border-bottom: 3px solid #d4af37;
            padding-bottom: 10px;
            display: inline-block;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
            text-align: left;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .info-item {
            background: rgba(44, 90, 160, 0.05);
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #2c5aa0;
        }
        
        .info-label {
            font-weight: bold;
            color: #2c5aa0;
            display: block;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 16px;
        }
        
        .diploma-subject {
            font-size: 28px;
            font-weight: bold;
            color: #d4af37;
            margin: 40px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
            border: 3px solid #d4af37;
            padding: 20px;
            border-radius: 10px;
            background: rgba(212, 175, 55, 0.1);
        }
        
        .date-section {
            font-size: 18px;
            margin: 30px 0;
            color: #555;
        }
        
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 60px;
            gap: 40px;
        }
        
        .signature-box {
            flex: 1;
            text-align: center;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 40px;
            color: #2c5aa0;
            font-size: 16px;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            margin-bottom: 10px;
            height: 40px;
        }
        
        .signature-note {
            font-size: 12px;
            color: #666;
            font-style: italic;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        @media print {
            body {
                background: white !important;
            }
            
            .diploma-container {
                box-shadow: none;
                border: 8px solid #2c5aa0;
                margin: 0;
                padding: 30px;
                min-height: auto;
            }
        }
    </style>
    <script>
        function autoPrint() {
            setTimeout(function() {
                window.print();
                
                setTimeout(function() {
                    window.close();
                }, 1000);
            }, 2000);
        }
        
        window.onload = autoPrint;
        
        window.onafterprint = function() {
            window.close();
        };
        
        window.onbeforeunload = function() {
            return null;
        };
    </script>
</head>
<body>
    <div class="diploma-container">
        <div class="decorative-border"></div>
        
        <div class="header">
            <div class="logo">LOGO</div>
            <h1 class="school-name">École de Gestion Scolaire</h1>
            <h2 class="diploma-title">Diplôme Officiel</h2>
        </div>
        
        <div class="content">
            <p class="certification-text">
                <strong>Il est certifié par les présentes que</strong>
            </p>
            
            <div class="student-info">
                <div class="student-name">' . htmlspecialchars(strtoupper($diplome['etudiant_nom'])) . '</div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Numéro d\'étudiant :</span>
                        <span class="info-value">' . htmlspecialchars($diplome['numero_etudiant'] ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email :</span>
                        <span class="info-value">' . htmlspecialchars($diplome['etudiant_email']) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Filière :</span>
                        <span class="info-value">' . htmlspecialchars($diplome['filiere_nom'] ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Niveau :</span>
                        <span class="info-value">' . htmlspecialchars($diplome['niveau_nom'] ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Classe :</span>
                        <span class="info-value">' . htmlspecialchars($diplome['classe_nom'] ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Année scolaire :</span>
                        <span class="info-value">' . htmlspecialchars($annee_scolaire) . '</span>
                    </div>
                </div>
            </div>
            
            <p class="certification-text">
                <strong>a obtenu avec succès le diplôme de :</strong>
            </p>
            
            <div class="diploma-subject">
                ' . htmlspecialchars(strtoupper($diplome['titre'])) . '
            </div>
            
            <div class="date-section">
                <strong>Délivré le : ' . htmlspecialchars($diplome['date_obtention_fr']) . '</strong>
            </div>
            
            <div class="signatures">
                <div class="signature-box">
                    <div class="signature-title">Le Directeur</div>
                    <div class="signature-line"></div>
                    <div class="signature-note">Signature et cachet</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-title">Le Responsable Académique</div>
                    <div class="signature-line"></div>
                    <div class="signature-note">Signature et cachet</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>École de Gestion Scolaire</strong></p>
            <p>Document officiel - Toute reproduction non autorisée est interdite</p>
            <p>Généré le ' . date('d/m/Y à H:i') . '</p>
        </div>
    </div>
    
</body>
</html>';
}

// Traitement de la requête
if (isset($_GET['id'])) {
    $diplome_id = intval($_GET['id']);
    
    try {
        echo generateSimplePDFBackup($diplome_id);
    } catch (Exception $e) {
        http_response_code(500);
        echo "<h1>Erreur</h1>";
        echo "<p>Impossible de générer le diplôme : " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><a href='javascript:history.back()'>Retour</a></p>";
    }
} else {
    http_response_code(400);
    echo "<h1>Erreur</h1>";
    echo "<p>ID du diplôme manquant</p>";
    echo "<p><a href='javascript:history.back()'>Retour</a></p>";
}
?>
