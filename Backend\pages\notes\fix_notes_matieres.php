<?php
/**
 * Script de correction pour le problème des matières dans les notes
 * Ce script vérifie et corrige la requête SQL si nécessaire
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Connexion à la base de données
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $response = [
        'success' => true,
        'message' => 'Diagnostic et correction des notes',
        'diagnostics' => [],
        'corrections' => [],
        'test_data' => []
    ];
    
    // 1. Vérifier la structure des tables
    $response['diagnostics'][] = "🔍 Vérification de la structure des tables...";
    
    // Vérifier la table notes
    $stmt = $pdo->query("DESCRIBE notes");
    $notes_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['diagnostics'][] = "📋 Colonnes table notes: " . implode(', ', $notes_columns);
    
    // Vérifier la table matieres
    $stmt = $pdo->query("DESCRIBE matieres");
    $matieres_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['diagnostics'][] = "📋 Colonnes table matieres: " . implode(', ', $matieres_columns);
    
    // 2. Tester la requête actuelle
    $response['diagnostics'][] = "🧪 Test de la requête actuelle...";
    
    $current_query = "
        SELECT 
            n.id,
            n.etudiant_id,
            n.devoir_id,
            n.matiere_id,
            n.note,
            n.date_enregistrement,
            u.nom as etudiant_nom,
            u.email as etudiant_email,
            d.titre as devoir_titre,
            d.description as devoir_description,
            d.date_remise,
            m.nom as matiere_nom,
            c.nom as classe_nom,
            DATE_FORMAT(n.date_enregistrement, '%d/%m/%Y') as date_formatted
        FROM notes n
        LEFT JOIN etudiants e ON n.etudiant_id = e.id
        LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
        LEFT JOIN devoirs d ON n.devoir_id = d.id
        LEFT JOIN matieres m ON n.matiere_id = m.id
        LEFT JOIN classes c ON d.classe_id = c.id
        LIMIT 5
    ";
    
    try {
        $stmt = $pdo->prepare($current_query);
        $stmt->execute();
        $current_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['diagnostics'][] = "✅ Requête actuelle fonctionne - " . count($current_results) . " résultats";
        
        if (count($current_results) > 0) {
            $first_result = $current_results[0];
            $has_matiere_nom = !empty($first_result['matiere_nom']);
            $response['diagnostics'][] = $has_matiere_nom ? 
                "✅ matiere_nom présent: " . $first_result['matiere_nom'] : 
                "❌ matiere_nom manquant dans le premier résultat";
            
            // Compter les résultats avec/sans matiere_nom
            $with_matiere = array_filter($current_results, function($r) { return !empty($r['matiere_nom']); });
            $without_matiere = array_filter($current_results, function($r) { return empty($r['matiere_nom']); });
            
            $response['diagnostics'][] = "📊 Résultats avec matière: " . count($with_matiere) . "/" . count($current_results);
            $response['diagnostics'][] = "📊 Résultats sans matière: " . count($without_matiere) . "/" . count($current_results);
            
            $response['test_data'] = $current_results;
        }
        
    } catch (Exception $e) {
        $response['diagnostics'][] = "❌ Erreur requête actuelle: " . $e->getMessage();
    }
    
    // 3. Vérifier les relations
    $response['diagnostics'][] = "🔗 Vérification des relations...";
    
    // Notes sans matière
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM notes n 
        LEFT JOIN matieres m ON n.matiere_id = m.id 
        WHERE m.id IS NULL
    ");
    $notes_sans_matiere = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    $response['diagnostics'][] = $notes_sans_matiere > 0 ? 
        "⚠️ $notes_sans_matiere notes sans matière associée" : 
        "✅ Toutes les notes ont une matière associée";
    
    // 4. Proposer une requête alternative si nécessaire
    if ($notes_sans_matiere > 0) {
        $response['corrections'][] = "🔧 Tentative de correction des relations...";
        
        // Requête alternative avec INNER JOIN pour forcer la présence de matière
        $alternative_query = "
            SELECT 
                n.id,
                n.etudiant_id,
                n.devoir_id,
                n.matiere_id,
                n.note,
                n.date_enregistrement,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                d.titre as devoir_titre,
                d.description as devoir_description,
                d.date_remise,
                m.nom as matiere_nom,
                c.nom as classe_nom,
                DATE_FORMAT(n.date_enregistrement, '%d/%m/%Y') as date_formatted
            FROM notes n
            LEFT JOIN etudiants e ON n.etudiant_id = e.id
            LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN devoirs d ON n.devoir_id = d.id
            INNER JOIN matieres m ON n.matiere_id = m.id
            LEFT JOIN classes c ON d.classe_id = c.id
            LIMIT 5
        ";
        
        try {
            $stmt = $pdo->prepare($alternative_query);
            $stmt->execute();
            $alternative_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response['corrections'][] = "✅ Requête alternative (INNER JOIN) fonctionne - " . count($alternative_results) . " résultats";
            
            if (count($alternative_results) > 0) {
                $all_have_matiere = array_filter($alternative_results, function($r) { return !empty($r['matiere_nom']); });
                $response['corrections'][] = "✅ Tous les résultats ont matiere_nom: " . count($all_have_matiere) . "/" . count($alternative_results);
            }
            
        } catch (Exception $e) {
            $response['corrections'][] = "❌ Erreur requête alternative: " . $e->getMessage();
        }
    }
    
    // 5. Vérifier les données de test
    $response['diagnostics'][] = "📊 Statistiques générales...";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM notes");
    $total_notes = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM matieres");
    $total_matieres = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM etudiants");
    $total_etudiants = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $response['diagnostics'][] = "📊 Total notes: $total_notes";
    $response['diagnostics'][] = "📊 Total matières: $total_matieres";
    $response['diagnostics'][] = "📊 Total étudiants: $total_etudiants";
    
    // 6. Recommandations
    $response['recommendations'] = [];
    
    if ($notes_sans_matiere > 0) {
        $response['recommendations'][] = "🔧 Utiliser INNER JOIN au lieu de LEFT JOIN pour la table matieres";
        $response['recommendations'][] = "🔧 Vérifier et corriger les matiere_id manquants dans la table notes";
        $response['recommendations'][] = "🔧 Ajouter une contrainte de clé étrangère pour éviter les matiere_id invalides";
    } else {
        $response['recommendations'][] = "✅ Les relations sont correctes, le problème peut être dans le frontend";
        $response['recommendations'][] = "🔍 Vérifier les logs de la console dans NotesUnified.js";
        $response['recommendations'][] = "🔍 Vérifier que les données arrivent correctement dans le composant React";
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur de connexion à la base de données',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur générale',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
