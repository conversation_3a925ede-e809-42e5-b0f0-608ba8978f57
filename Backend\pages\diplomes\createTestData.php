<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

try {
    // Vérifier s'il y a des étudiants
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM Etudiants");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($count['count'] == 0) {
        echo json_encode([
            'error' => 'Aucun étudiant trouvé',
            'solution' => 'Créez d\'abord des étudiants dans le système'
        ]);
        exit;
    }
    
    // Récupérer quelques étudiants
    $stmt = $pdo->prepare("SELECT id FROM Etudiants LIMIT 3");
    $stmt->execute();
    $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $diplomes_created = 0;
    $titres_diplomes = [
        'Licence en Informatique',
        'Master en Gestion des Entreprises',
        'Diplôme Technicien Supérieur',
        'Licence en Mathématiques Appliquées',
        'Master en Finance et Comptabilité',
        'Certificat en Marketing Digital',
        'Licence en Sciences Économiques',
        'Master en Ressources Humaines'
    ];
    
    foreach ($etudiants as $etudiant) {
        // Créer 1-2 diplômes par étudiant
        $nb_diplomes = rand(1, 2);
        
        for ($i = 0; $i < $nb_diplomes; $i++) {
            $titre = $titres_diplomes[array_rand($titres_diplomes)];
            $date_obtention = date('Y-m-d', strtotime('-' . rand(30, 730) . ' days')); // Entre 1 mois et 2 ans
            
            // Vérifier si ce diplôme n'existe pas déjà pour cet étudiant
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM Diplomes WHERE etudiant_id = ? AND titre = ?");
            $stmt->execute([$etudiant['id'], $titre]);
            $exists = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($exists['count'] == 0) {
                $stmt = $pdo->prepare("
                    INSERT INTO Diplomes (etudiant_id, titre, date_obtention) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([
                    $etudiant['id'],
                    $titre,
                    $date_obtention
                ]);
                $diplomes_created++;
            }
        }
    }
    
    // Récupérer un diplôme pour test PDF
    $stmt = $pdo->prepare("SELECT id FROM Diplomes LIMIT 1");
    $stmt->execute();
    $test_diplome = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => "Données de test créées avec succès",
        'data' => [
            'diplomes_created' => $diplomes_created,
            'total_etudiants' => $count['count'],
            'test_pdf_url' => $test_diplome ? 
                "http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=" . $test_diplome['id'] : 
                null
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => 'Erreur lors de la création des données: ' . $e->getMessage()
    ]);
}
?>
