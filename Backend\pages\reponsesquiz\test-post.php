<?php
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test POST - ReponsesQuiz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #bee5eb; }
        h1, h2, h3 { color: #333; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 Test POST - Ajout de Réponse</h1>";

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "<div class='error'>Erreur de connexion à la base de données: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit();
}

// Récupérer les quiz disponibles
$stmt = $pdo->query("SELECT id, question, reponse_correcte FROM quiz LIMIT 10");
$quiz_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Récupérer les étudiants
$stmt = $pdo->query("SELECT id FROM etudiants LIMIT 5");
$etudiants_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_post'])) {
    echo "<h2>📤 Résultat du Test POST</h2>";
    
    $quiz_id = $_POST['quiz_id'];
    $reponse = $_POST['reponse'];
    
    // Préparer les données pour l'API
    $postData = json_encode([
        'quiz_id' => $quiz_id,
        'reponse' => $reponse
    ]);
    
    // Appel à l'API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer etudiant-token'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div class='info'>";
    echo "<h3>📋 Détails de la Requête</h3>";
    echo "<p><strong>URL:</strong> http://localhost/Project_PFE/Backend/pages/reponsesquiz/api.php</p>";
    echo "<p><strong>Méthode:</strong> POST</p>";
    echo "<p><strong>Headers:</strong> Content-Type: application/json, Authorization: Bearer etudiant-token</p>";
    echo "<p><strong>Données:</strong> " . htmlspecialchars($postData) . "</p>";
    echo "</div>";
    
    if ($error) {
        echo "<div class='error'>";
        echo "<h3>❌ Erreur cURL</h3>";
        echo "<p>" . htmlspecialchars($error) . "</p>";
        echo "</div>";
    } else {
        echo "<div class='result " . ($httpCode === 200 ? 'success' : 'error') . "'>";
        echo "<h3>📨 Réponse de l'API</h3>";
        echo "<p><strong>Code HTTP:</strong> $httpCode</p>";
        echo "<p><strong>Réponse:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        echo "</div>";
        
        if ($httpCode === 200) {
            $responseData = json_decode($response, true);
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                echo "<div class='success'>";
                echo "<h3>✅ Succès !</h3>";
                echo "<p>La réponse a été ajoutée avec succès.</p>";
                if (isset($responseData['id'])) {
                    echo "<p><strong>ID de la réponse:</strong> " . $responseData['id'] . "</p>";
                    
                    // Vérifier dans la base de données
                    $stmt = $pdo->prepare("SELECT * FROM reponsesquiz WHERE id = ?");
                    $stmt->execute([$responseData['id']]);
                    $inserted = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($inserted) {
                        echo "<h4>🔍 Vérification en Base de Données</h4>";
                        echo "<table style='width: 100%; border-collapse: collapse;'>";
                        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px; border: 1px solid #ddd;'>Champ</th><th style='padding: 8px; border: 1px solid #ddd;'>Valeur</th></tr>";
                        foreach ($inserted as $key => $value) {
                            $displayValue = $value;
                            if ($key === 'est_correct') {
                                if ($value === '1') $displayValue = '1 (✅ Correct)';
                                elseif ($value === '0') $displayValue = '0 (❌ Incorrect)';
                                elseif ($value === null) $displayValue = 'NULL (⏳ Non évalué)';
                            }
                            echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>$key</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($displayValue) . "</td></tr>";
                        }
                        echo "</table>";
                        
                        // Récupérer la réponse correcte pour comparaison
                        $stmt = $pdo->prepare("SELECT reponse_correcte FROM quiz WHERE id = ?");
                        $stmt->execute([$inserted['quiz_id']]);
                        $quiz_info = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($quiz_info) {
                            echo "<h4>🎯 Analyse de l'Évaluation</h4>";
                            echo "<p><strong>Réponse de l'étudiant:</strong> '" . htmlspecialchars($inserted['reponse']) . "'</p>";
                            echo "<p><strong>Réponse correcte:</strong> '" . htmlspecialchars($quiz_info['reponse_correcte']) . "'</p>";
                            
                            $reponse_norm = trim(strtolower($inserted['reponse']));
                            $correcte_norm = trim(strtolower($quiz_info['reponse_correcte']));
                            $should_be_correct = ($reponse_norm === $correcte_norm);
                            
                            echo "<p><strong>Évaluation attendue:</strong> " . ($should_be_correct ? '1 (Correct)' : '0 (Incorrect)') . "</p>";
                            echo "<p><strong>Évaluation obtenue:</strong> " . ($inserted['est_correct'] === null ? 'NULL' : $inserted['est_correct']) . "</p>";
                            
                            if (($should_be_correct && $inserted['est_correct'] == 1) || (!$should_be_correct && $inserted['est_correct'] == 0)) {
                                echo "<p style='color: #28a745; font-weight: bold;'>✅ Évaluation automatique correcte !</p>";
                            } else {
                                echo "<p style='color: #dc3545; font-weight: bold;'>❌ Problème d'évaluation automatique</p>";
                            }
                        }
                    }
                }
                echo "</div>";
            }
        }
    }
}

echo "<h2>📝 Formulaire de Test</h2>";

if (count($quiz_list) === 0) {
    echo "<div class='error'>❌ Aucun quiz trouvé. Veuillez d'abord créer des quiz.</div>";
} elseif (count($etudiants_list) === 0) {
    echo "<div class='error'>❌ Aucun étudiant trouvé. Veuillez d'abord créer des étudiants.</div>";
} else {
    echo "<form method='POST'>";
    echo "<div class='form-group'>";
    echo "<label for='quiz_id'>Sélectionner un Quiz:</label>";
    echo "<select name='quiz_id' id='quiz_id' required>";
    echo "<option value=''>-- Choisir un quiz --</option>";
    foreach ($quiz_list as $quiz) {
        echo "<option value='{$quiz['id']}'>";
        echo "ID {$quiz['id']}: " . htmlspecialchars(substr($quiz['question'], 0, 50));
        if (strlen($quiz['question']) > 50) echo "...";
        echo " (Réponse: " . htmlspecialchars($quiz['reponse_correcte']) . ")";
        echo "</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='reponse'>Votre Réponse:</label>";
    echo "<textarea name='reponse' id='reponse' rows='3' placeholder='Saisissez votre réponse...' required></textarea>";
    echo "</div>";
    
    echo "<button type='submit' name='test_post'>🚀 Tester l'Ajout de Réponse</button>";
    echo "</form>";
    
    echo "<div class='info'>";
    echo "<h3>💡 Conseils pour le Test</h3>";
    echo "<ul>";
    echo "<li>Essayez d'abord avec la réponse exacte pour tester l'évaluation 'correct'</li>";
    echo "<li>Puis testez avec une réponse différente pour l'évaluation 'incorrect'</li>";
    echo "<li>Testez avec des variations de casse (majuscules/minuscules)</li>";
    echo "<li>Testez avec des espaces en début/fin</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='api.php' target='_blank' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px;'>Test API GET</a>";
echo "<a href='test-correction.php' target='_blank' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 5px;'>Test Correction</a>";
echo "<a href='setup-et-test.php' target='_blank' style='display: inline-block; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 5px;'>Setup Complet</a>";
echo "</div>";

echo "    </div>
</body>
</html>";
?>
