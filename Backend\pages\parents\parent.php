<?php
// API Parents - CRUD complet avec interface similaire aux factures
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// Fonction de logging
function logDebug($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $log = "[$timestamp] PARENTS: $message";
    if ($data !== null) {
        $log .= " | Data: " . json_encode($data);
    }
    error_log($log);
}

// Fonction de réponse sécurisée
function jsonResponse($data, $code = 200) {
    logDebug("Sending response", ['code' => $code, 'data' => $data]);
    http_response_code($code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS request received");
    jsonResponse(['success' => true]);
}

try {
    // Connexion DB
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logDebug("Database connected");

    $method = $_SERVER['REQUEST_METHOD'];
    logDebug("Request method: $method");

    if ($method === 'POST') {
        // CREATE - Ajouter un nouveau parent
        logDebug("Processing POST request");
        
        $input = file_get_contents("php://input");
        logDebug("Raw input received", ['input' => $input]);
        
        if (empty($input)) {
            jsonResponse(['error' => 'Aucune donnée reçue'], 400);
        }
        
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        logDebug("Data decoded successfully", $data);
        
        // Validation des champs requis
        if (!isset($data['utilisateur_id']) || empty($data['utilisateur_id'])) {
            jsonResponse(['error' => 'L\'ID utilisateur est requis'], 400);
        }
        
        if (!isset($data['telephone']) || empty($data['telephone'])) {
            jsonResponse(['error' => 'Le numéro de téléphone est requis'], 400);
        }
        
        $utilisateur_id = intval($data['utilisateur_id']);
        $telephone = trim($data['telephone']);
        $adresse = isset($data['adresse']) ? trim($data['adresse']) : null;
        
        logDebug("Processing creation", ['utilisateur_id' => $utilisateur_id, 'telephone' => $telephone, 'adresse' => $adresse]);
        
        // Vérifier que l'utilisateur existe et a le rôle parent
        $checkUserStmt = $pdo->prepare("
            SELECT u.id, u.nom, u.email, r.nom as role_nom 
            FROM utilisateurs u 
            LEFT JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ?
        ");
        $checkUserStmt->execute([$utilisateur_id]);
        $user = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            jsonResponse(['error' => 'Utilisateur non trouvé'], 404);
        }
        
        if (strtolower($user['role_nom']) !== 'parent') {
            jsonResponse(['error' => 'L\'utilisateur doit avoir le rôle "parent"'], 400);
        }
        
        // Vérifier que l'utilisateur n'est pas déjà parent
        $checkExistingStmt = $pdo->prepare("SELECT id FROM parents WHERE utilisateur_id = ?");
        $checkExistingStmt->execute([$utilisateur_id]);
        if ($checkExistingStmt->fetch()) {
            jsonResponse(['error' => 'Cet utilisateur est déjà enregistré comme parent'], 400);
        }
        
        // Insérer le parent
        try {
            $stmt = $pdo->prepare("
                INSERT INTO parents (utilisateur_id, telephone, adresse)
                VALUES (:utilisateur_id, :telephone, :adresse)
            ");
            
            $result = $stmt->execute([
                'utilisateur_id' => $utilisateur_id,
                'telephone' => $telephone,
                'adresse' => $adresse
            ]);
            
            if ($result) {
                $parentId = $pdo->lastInsertId();
                logDebug("Parent created successfully", ['id' => $parentId]);
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Parent créé avec succès',
                    'id' => $parentId
                ]);
            } else {
                jsonResponse(['error' => 'Échec de la création du parent'], 500);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during creation", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'GET') {
        // READ - Récupérer tous les parents avec informations complètes
        logDebug("Processing GET request");
        
        try {
            $stmt = $pdo->query("
                SELECT 
                    p.id,
                    p.utilisateur_id,
                    p.telephone,
                    p.adresse,
                    u.nom,
                    u.email
                FROM parents p
                LEFT JOIN utilisateurs u ON p.utilisateur_id = u.id
                ORDER BY p.id DESC
            ");
            
            $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            logDebug("Parents retrieved", [
                'total' => count($parents)
            ]);
            
            jsonResponse($parents);
            
        } catch (PDOException $e) {
            logDebug("Database error during read", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'PUT') {
        // UPDATE - Modifier un parent
        logDebug("Processing PUT request");
        
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            jsonResponse(['error' => 'ID du parent requis'], 400);
        }
        
        $id = intval($data['id']);
        $utilisateur_id = !empty($data['utilisateur_id']) ? intval($data['utilisateur_id']) : null;
        $telephone = isset($data['telephone']) ? trim($data['telephone']) : null;
        $adresse = isset($data['adresse']) ? trim($data['adresse']) : null;
        
        logDebug("Processing update", ['id' => $id, 'utilisateur_id' => $utilisateur_id, 'telephone' => $telephone, 'adresse' => $adresse]);
        
        // Vérifier que le parent existe
        $checkStmt = $pdo->prepare("SELECT id, utilisateur_id FROM parents WHERE id = ?");
        $checkStmt->execute([$id]);
        $existingParent = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existingParent) {
            jsonResponse(['error' => 'Parent non trouvé'], 404);
        }
        
        // Si l'utilisateur_id est modifié, faire les vérifications
        if ($utilisateur_id !== null && $utilisateur_id !== $existingParent['utilisateur_id']) {
            // Vérifier que le nouvel utilisateur existe et a le rôle parent
            $checkUserStmt = $pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role_nom 
                FROM utilisateurs u 
                LEFT JOIN roles r ON u.role_id = r.id 
                WHERE u.id = ?
            ");
            $checkUserStmt->execute([$utilisateur_id]);
            $user = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                jsonResponse(['error' => 'Utilisateur non trouvé'], 404);
            }
            
            if (strtolower($user['role_nom']) !== 'parent') {
                jsonResponse(['error' => 'L\'utilisateur doit avoir le rôle "parent"'], 400);
            }
            
            // Vérifier que le nouvel utilisateur n'est pas déjà parent
            $checkExistingStmt = $pdo->prepare("SELECT id FROM parents WHERE utilisateur_id = ? AND id != ?");
            $checkExistingStmt->execute([$utilisateur_id, $id]);
            if ($checkExistingStmt->fetch()) {
                jsonResponse(['error' => 'Cet utilisateur est déjà enregistré comme parent'], 400);
            }
        } else {
            // Si utilisateur_id n'est pas fourni, garder l'ancien
            $utilisateur_id = $existingParent['utilisateur_id'];
        }
        
        // Validation du téléphone
        if ($telephone === null || empty($telephone)) {
            jsonResponse(['error' => 'Le numéro de téléphone est requis'], 400);
        }
        
        // Mettre à jour le parent
        try {
            $stmt = $pdo->prepare("
                UPDATE parents 
                SET utilisateur_id = :utilisateur_id, telephone = :telephone, adresse = :adresse
                WHERE id = :id
            ");
            
            $result = $stmt->execute([
                'utilisateur_id' => $utilisateur_id,
                'telephone' => $telephone,
                'adresse' => $adresse,
                'id' => $id
            ]);
            
            if ($result) {
                logDebug("Parent updated successfully", ['id' => $id]);
                jsonResponse([
                    'success' => true,
                    'message' => 'Parent modifié avec succès'
                ]);
            } else {
                jsonResponse(['error' => 'Aucune modification effectuée'], 400);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during update", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'DELETE') {
        // DELETE - Supprimer un parent
        logDebug("Processing DELETE request");
        
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            jsonResponse(['error' => 'ID du parent requis'], 400);
        }
        
        $id = intval($data['id']);
        logDebug("Processing deletion", ['id' => $id]);
        
        // Vérifier que le parent existe
        $checkStmt = $pdo->prepare("SELECT id FROM parents WHERE id = ?");
        $checkStmt->execute([$id]);
        if (!$checkStmt->fetch()) {
            jsonResponse(['error' => 'Parent non trouvé'], 404);
        }
        
        // Supprimer le parent
        try {
            $stmt = $pdo->prepare("DELETE FROM parents WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result && $stmt->rowCount() > 0) {
                logDebug("Parent deleted successfully", ['id' => $id]);
                jsonResponse([
                    'success' => true,
                    'message' => 'Parent supprimé avec succès'
                ]);
            } else {
                jsonResponse(['error' => 'Aucune suppression effectuée'], 400);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during deletion", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } else {
        jsonResponse(['error' => 'Méthode non autorisée'], 405);
    }
    
} catch (Exception $e) {
    logDebug("Fatal exception", [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    jsonResponse(['error' => 'Erreur fatale : ' . $e->getMessage()], 500);
}
?>
