<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test CRUD Complet Parents - Interface Factures</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .log-entry { margin: 5px 0; padding: 5px; background: white; border-radius: 2px; }
        .log-success { border-left: 4px solid #28a745; }
        .log-error { border-left: 4px solid #dc3545; }
        .log-info { border-left: 4px solid #007bff; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test CRUD Complet Parents - Interface Factures</h1>
        
        <div class="section success">
            <h2>🎯 Objectifs du Test</h2>
            <ul>
                <li>✅ Interface similaire aux factures (design et organisation)</li>
                <li>✅ CRUD complet : Créer, Lire, Modifier, Supprimer</li>
                <li>✅ Filtrage utilisateurs : seuls les rôles "parent"</li>
                <li>✅ Restrictions d'accès : seuls les admins peuvent modifier</li>
                <li>✅ Connexion dynamique Frontend ↔ Backend via API</li>
                <li>✅ Champs spécifiques : téléphone (requis), adresse (optionnelle)</li>
            </ul>
        </div>

        <!-- Schéma de base de données -->
        <div class="section info">
            <h2>🗄️ Schéma de Base de Données</h2>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
CREATE TABLE `parents` (
    `id` INT(10) NOT NULL AUTO_INCREMENT,
    `utilisateur_id` INT(10) NULL DEFAULT NULL,
    `telephone` VARCHAR(20) NULL DEFAULT NULL,
    `adresse` TEXT NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `utilisateur_id` (`utilisateur_id`),
    CONSTRAINT `parents_ibfk_1` FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs` (`id`)
);
            </pre>
        </div>

        <!-- Test des utilisateurs parents -->
        <div class="section">
            <h2>👥 Test Filtrage Utilisateurs Parents</h2>
            <button class="btn btn-primary" onclick="testFiltragUtilisateurs()">🔍 Tester Filtrage</button>
            <div id="filtrageResult"></div>
        </div>

        <!-- Test CRUD complet -->
        <div class="section">
            <h2>🔧 Test CRUD Complet</h2>
            <div class="form-group">
                <label for="test_utilisateur_id">Utilisateur Parent:</label>
                <select id="test_utilisateur_id">
                    <option value="">Chargement...</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="test_telephone">Téléphone (requis):</label>
                <input type="tel" id="test_telephone" placeholder="0123456789" value="0123456789">
            </div>
            
            <div class="form-group">
                <label for="test_adresse">Adresse (optionnelle):</label>
                <textarea id="test_adresse" rows="3" placeholder="Adresse complète du parent">123 Rue de la Paix, 75001 Paris</textarea>
            </div>
            
            <div style="margin: 20px 0;">
                <button class="btn btn-success" onclick="testCreate()">➕ CREATE</button>
                <button class="btn btn-primary" onclick="testRead()">📋 READ</button>
                <button class="btn btn-warning" onclick="testUpdate()">✏️ UPDATE</button>
                <button class="btn btn-danger" onclick="testDelete()">🗑️ DELETE</button>
            </div>
            
            <button class="btn btn-info" onclick="testWorkflowComplet()">🚀 Test Workflow Complet</button>
        </div>

        <!-- Liste des parents -->
        <div class="section">
            <h2>📋 Liste des Parents (Interface Factures)</h2>
            <button class="btn btn-primary" onclick="loadParents()">🔄 Recharger</button>
            <div id="parentsList"></div>
        </div>

        <!-- Comparaison avec Factures -->
        <div class="section">
            <h2>📊 Comparaison Interface Factures vs Parents</h2>
            <div class="comparison">
                <div>
                    <h4>💰 Interface Factures (Modèle)</h4>
                    <ul>
                        <li>Tableau : ID, Étudiant, Mois, Montant, Statut, Date, Actions</li>
                        <li>Modal avec formulaire structuré</li>
                        <li>Restrictions admin pour CRUD</li>
                        <li>Messages d'information pour non-admins</li>
                        <li>Boutons d'action avec icônes</li>
                        <li>Recherche et pagination</li>
                    </ul>
                </div>
                <div>
                    <h4>👨‍👩‍👧‍👦 Interface Parents (Nouvelle)</h4>
                    <ul>
                        <li>Tableau : ID, Nom, Email, Téléphone, Adresse, Statut, Actions</li>
                        <li>Modal avec formulaire similaire</li>
                        <li>Restrictions admin identiques</li>
                        <li>Messages d'information similaires</li>
                        <li>Boutons d'action avec icônes</li>
                        <li>Recherche et pagination</li>
                        <li>+ Filtrage automatique utilisateurs parents</li>
                        <li>+ Validation téléphone requis</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Log détaillé -->
        <div class="section">
            <h2>🔍 Log Détaillé</h2>
            <button class="btn btn-danger" onclick="clearLog()">🧹 Effacer Log</button>
            <div id="logContainer" class="log">
                <div class="log-entry log-info">
                    <strong>[Prêt]</strong> Interface de test chargée.
                </div>
            </div>
        </div>

        <!-- Instructions React -->
        <div class="section warning">
            <h2>📋 Instructions pour Tester dans React</h2>
            <ol>
                <li><strong>Accédez à la page Parents</strong> dans votre interface React</li>
                <li><strong>Vérifiez l'interface</strong> : doit ressembler aux factures</li>
                <li><strong>Testez les restrictions</strong> : seuls les admins voient les boutons CRUD</li>
                <li><strong>Testez le filtrage</strong> : seuls les utilisateurs "parent" dans la liste</li>
                <li><strong>Testez le CRUD complet</strong> : Créer, Modifier, Supprimer</li>
                <li><strong>Vérifiez la validation</strong> : téléphone requis, adresse optionnelle</li>
                <li><strong>Testez la recherche</strong> : nom, email, téléphone, adresse</li>
            </ol>
        </div>
    </div>

    <script>
        let testParentId = null;

        function addLog(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            
            let html = `<strong>[${timestamp}]</strong> ${message}`;
            if (data) {
                html += `<br><pre style="margin: 5px 0; font-size: 11px;">${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            entry.innerHTML = html;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info"><strong>[Effacé]</strong> Log effacé.</div>';
        }

        async function testFiltragUtilisateurs() {
            try {
                addLog('🔍 Test filtrage utilisateurs parents', 'info');
                
                // Récupérer tous les utilisateurs
                const responseUtilisateurs = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php');
                const utilisateurs = await responseUtilisateurs.json();
                
                // Filtrer les parents
                const utilisateursParents = utilisateurs.filter(user => {
                    const roleNom = (user.role_nom || user.role || '').toLowerCase();
                    return roleNom === 'parent' || roleNom === 'parents';
                });
                
                // Récupérer les parents existants
                const responseParents = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php');
                const parents = await responseParents.json();
                
                const parentsExistants = parents.map(p => p.utilisateur_id).filter(id => id !== null);
                const utilisateursDisponibles = utilisateursParents.filter(user => 
                    !parentsExistants.includes(user.id)
                );
                
                addLog('✅ Résultats filtrage', 'success', {
                    total_utilisateurs: utilisateurs.length,
                    utilisateurs_parents: utilisateursParents.length,
                    parents_existants: parentsExistants.length,
                    utilisateurs_disponibles: utilisateursDisponibles.length
                });
                
                // Remplir les selects
                const selectUtilisateur = document.getElementById('test_utilisateur_id');
                selectUtilisateur.innerHTML = '<option value="">Sélectionner un utilisateur parent...</option>';
                
                utilisateursDisponibles.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.nom} - ${user.email} (ID: ${user.id})`;
                    selectUtilisateur.appendChild(option);
                });
                
                // Afficher les résultats
                document.getElementById('filtrageResult').innerHTML = `
                    <div style="margin-top: 15px;">
                        <h4>📊 Résultats du Filtrage :</h4>
                        <table>
                            <tr><th>Catégorie</th><th>Nombre</th><th>Statut</th></tr>
                            <tr><td>Total utilisateurs</td><td>${utilisateurs.length}</td><td>ℹ️</td></tr>
                            <tr><td>Utilisateurs parents</td><td>${utilisateursParents.length}</td><td>${utilisateursParents.length > 0 ? '✅' : '❌'}</td></tr>
                            <tr><td>Parents existants</td><td>${parentsExistants.length}</td><td>ℹ️</td></tr>
                            <tr><td>Utilisateurs disponibles</td><td>${utilisateursDisponibles.length}</td><td>${utilisateursDisponibles.length > 0 ? '✅' : '⚠️'}</td></tr>
                        </table>
                        <p><strong>Conclusion :</strong> ${utilisateursDisponibles.length > 0 ? 
                            '✅ Le filtrage fonctionne parfaitement' : 
                            '⚠️ Aucun utilisateur parent disponible'}</p>
                    </div>
                `;
                
            } catch (error) {
                addLog('❌ Erreur test filtrage', 'error', { error: error.message });
            }
        }

        async function testCreate() {
            try {
                const utilisateur_id = document.getElementById('test_utilisateur_id').value;
                const telephone = document.getElementById('test_telephone').value;
                const adresse = document.getElementById('test_adresse').value;
                
                if (!utilisateur_id) {
                    addLog('❌ Veuillez sélectionner un utilisateur', 'error');
                    return;
                }
                
                if (!telephone) {
                    addLog('❌ Le téléphone est requis', 'error');
                    return;
                }
                
                const data = {
                    utilisateur_id: parseInt(utilisateur_id),
                    telephone: telephone,
                    adresse: adresse || null
                };
                
                addLog('➕ Test CREATE', 'info', data);
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testParentId = result.id;
                    addLog('✅ CREATE réussi', 'success', result);
                    await loadParents();
                    await testFiltragUtilisateurs();
                } else {
                    addLog('❌ CREATE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur CREATE', 'error', { error: error.message });
            }
        }

        async function testRead() {
            try {
                addLog('📋 Test READ', 'info');
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php');
                const parents = await response.json();
                
                addLog('✅ READ réussi', 'success', { count: parents.length, data: parents });
                
            } catch (error) {
                addLog('❌ Erreur READ', 'error', { error: error.message });
            }
        }

        async function testUpdate() {
            if (!testParentId) {
                addLog('❌ Aucun parent de test. Créez d\'abord un parent.', 'error');
                return;
            }
            
            try {
                const data = {
                    id: testParentId,
                    utilisateur_id: document.getElementById('test_utilisateur_id').value,
                    telephone: document.getElementById('test_telephone').value,
                    adresse: document.getElementById('test_adresse').value || null
                };
                
                addLog('✏️ Test UPDATE', 'info', data);
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('✅ UPDATE réussi', 'success', result);
                    await loadParents();
                } else {
                    addLog('❌ UPDATE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur UPDATE', 'error', { error: error.message });
            }
        }

        async function testDelete() {
            if (!testParentId) {
                addLog('❌ Aucun parent de test. Créez d\'abord un parent.', 'error');
                return;
            }
            
            try {
                addLog('🗑️ Test DELETE', 'info', { id: testParentId });
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: testParentId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('✅ DELETE réussi', 'success', result);
                    testParentId = null;
                    await loadParents();
                    await testFiltragUtilisateurs();
                } else {
                    addLog('❌ DELETE échoué', 'error', result);
                }
                
            } catch (error) {
                addLog('❌ Erreur DELETE', 'error', { error: error.message });
            }
        }

        async function testWorkflowComplet() {
            addLog('🚀 Début test workflow complet', 'info');
            
            await testFiltragUtilisateurs();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCreate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testRead();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testUpdate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDelete();
            
            addLog('🎉 Test workflow complet terminé', 'success');
        }

        async function loadParents() {
            try {
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php');
                const parents = await response.json();
                
                const parentsList = document.getElementById('parentsList');
                
                if (!Array.isArray(parents) || parents.length === 0) {
                    parentsList.innerHTML = '<p>Aucun parent trouvé.</p>';
                    return;
                }
                
                let html = `<h4>Total: ${parents.length} parent(s)</h4>`;
                html += '<table><tr><th>ID</th><th>Nom</th><th>Email</th><th>Téléphone</th><th>Adresse</th><th>Actions</th></tr>';
                
                parents.forEach(parent => {
                    html += `<tr>
                        <td>#${parent.id}</td>
                        <td>${parent.nom || 'N/A'}</td>
                        <td>${parent.email || 'N/A'}</td>
                        <td>${parent.telephone || 'N/A'}</td>
                        <td>${parent.adresse ? (parent.adresse.length > 30 ? parent.adresse.substring(0, 30) + '...' : parent.adresse) : 'N/A'}</td>
                        <td>
                            <button class="btn btn-danger" onclick="deleteParent(${parent.id})" style="font-size: 12px; padding: 5px 10px;">
                                🗑️ Supprimer
                            </button>
                        </td>
                    </tr>`;
                });
                
                html += '</table>';
                parentsList.innerHTML = html;
                
            } catch (error) {
                addLog('❌ Erreur chargement parents', 'error', { error: error.message });
            }
        }

        async function deleteParent(id) {
            try {
                if (!confirm(`Supprimer le parent ID ${id} ?`)) return;
                
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: id })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ Parent ${id} supprimé`, 'success');
                    await loadParents();
                    await testFiltragUtilisateurs();
                } else {
                    addLog(`❌ Échec suppression ${id}`, 'error', result);
                }
                
            } catch (error) {
                addLog(`❌ Erreur suppression ${id}`, 'error', { error: error.message });
            }
        }

        // Initialisation
        window.addEventListener('load', () => {
            addLog('🌐 Page chargée', 'info');
            testFiltragUtilisateurs();
            loadParents();
        });
    </script>
</body>
</html>
