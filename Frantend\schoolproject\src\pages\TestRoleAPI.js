import React, { useState } from 'react';
import axios from 'axios';

const TestRoleAPI = () => {
    const [results, setResults] = useState([]);
    const [loading, setLoading] = useState(false);

    const addResult = (test, success, data) => {
        setResults(prev => [...prev, {
            test,
            success,
            data,
            timestamp: new Date().toLocaleTimeString()
        }]);
    };

    const testGet = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/roles/role.php', {
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            addResult('GET /roles', true, response.data);
        } catch (error) {
            addResult('GET /roles', false, error.response?.data || error.message);
        }
        setLoading(false);
    };

    const testPost = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await axios.post('http://localhost/Project_PFE/Backend/pages/roles/role.php', 
                { nom: 'Test Role ' + Date.now() },
                {
                    headers: { 
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            addResult('POST /roles', true, response.data);
        } catch (error) {
            addResult('POST /roles', false, error.response?.data || error.message);
        }
        setLoading(false);
    };

    const testPut = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await axios.put('http://localhost/Project_PFE/Backend/pages/roles/role.php', 
                { id: 1, nom: 'Admin Updated ' + Date.now() },
                {
                    headers: { 
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            addResult('PUT /roles', true, response.data);
        } catch (error) {
            addResult('PUT /roles', false, error.response?.data || error.message);
        }
        setLoading(false);
    };

    const testDelete = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/roles/role.php', {
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { id: 999 } // ID qui n'existe probablement pas
            });
            addResult('DELETE /roles', true, response.data);
        } catch (error) {
            addResult('DELETE /roles', false, error.response?.data || error.message);
        }
        setLoading(false);
    };

    const clearResults = () => {
        setResults([]);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>🧪 Test API Rôles</h1>
            
            <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                <button 
                    onClick={testGet} 
                    disabled={loading}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test GET
                </button>
                
                <button 
                    onClick={testPost} 
                    disabled={loading}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test POST
                </button>
                
                <button 
                    onClick={testPut} 
                    disabled={loading}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#ffc107',
                        color: 'black',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test PUT
                </button>
                
                <button 
                    onClick={testDelete} 
                    disabled={loading}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test DELETE
                </button>
                
                <button 
                    onClick={clearResults}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#6c757d',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                >
                    Effacer
                </button>
            </div>

            {loading && (
                <div style={{ 
                    padding: '10px', 
                    backgroundColor: '#f8f9fa', 
                    borderRadius: '4px',
                    marginBottom: '20px'
                }}>
                    ⏳ Test en cours...
                </div>
            )}

            <div>
                <h2>Résultats des Tests</h2>
                {results.length === 0 ? (
                    <p style={{ color: '#6c757d' }}>Aucun test effectué</p>
                ) : (
                    <div>
                        {results.map((result, index) => (
                            <div 
                                key={index}
                                style={{
                                    padding: '15px',
                                    marginBottom: '10px',
                                    backgroundColor: result.success ? '#d4edda' : '#f8d7da',
                                    border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
                                    borderRadius: '4px'
                                }}
                            >
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: '10px'
                                }}>
                                    <strong style={{ 
                                        color: result.success ? '#155724' : '#721c24' 
                                    }}>
                                        {result.success ? '✅' : '❌'} {result.test}
                                    </strong>
                                    <small style={{ color: '#6c757d' }}>
                                        {result.timestamp}
                                    </small>
                                </div>
                                <pre style={{
                                    backgroundColor: '#f8f9fa',
                                    padding: '10px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    overflow: 'auto',
                                    margin: 0
                                }}>
                                    {JSON.stringify(result.data, null, 2)}
                                </pre>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            <div style={{ 
                marginTop: '30px', 
                padding: '15px', 
                backgroundColor: '#e3f2fd',
                borderRadius: '4px'
            }}>
                <h3>ℹ️ Instructions</h3>
                <ul>
                    <li><strong>GET</strong> : Récupère tous les rôles</li>
                    <li><strong>POST</strong> : Crée un nouveau rôle avec un nom unique</li>
                    <li><strong>PUT</strong> : Modifie le rôle avec ID=1 (Admin)</li>
                    <li><strong>DELETE</strong> : Tente de supprimer un rôle avec ID=999</li>
                </ul>
                <p><strong>Note :</strong> Assurez-vous d'être connecté avec un compte Admin pour tester les opérations CRUD.</p>
            </div>
        </div>
    );
};

export default TestRoleAPI;
