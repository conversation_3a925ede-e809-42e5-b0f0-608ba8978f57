import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import { AuthContext } from '../context/AuthContext';
import '../css/Factures.css';

const Quiz = () => {
    const { user } = useContext(AuthContext);
    const [quiz, setQuiz] = useState([]);
    const [devoirs, setDevoirs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingQuiz, setEditingQuiz] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        devoir_id: '',
        question: '',
        reponse_correcte: ''
    });

    // Vérifier les permissions
    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';
    const canManage = isEnseignant; // Seuls les enseignants peuvent gérer
    const canView = isEnseignant || isAdmin; // Enseignants et admins peuvent voir

    useEffect(() => {
        if (canView) {
            fetchQuiz();
            fetchDevoirs();
        }
    }, [canView]);

    const fetchQuiz = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des quiz...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/quiz/quiz.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('✅ Réponse API quiz:', response.data);
            setQuiz(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des quiz:', error);
            if (error.response?.status === 403) {
                Swal.fire('Accès refusé', 'Vous n\'avez pas les permissions pour consulter les quiz', 'error');
            } else {
                Swal.fire('Erreur', 'Impossible de charger les quiz', 'error');
            }
            setQuiz([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchDevoirs = async () => {
        try {
            console.log('🔄 Chargement des devoirs...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/quiz/getDevoirs.php');

            console.log('✅ Réponse API devoirs:', response.data);
            if (response.data.success) {
                setDevoirs(response.data.devoirs || []);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des devoirs:', error);
            setDevoirs([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des quiz', 'error');
            return;
        }

        if (!formData.devoir_id || !formData.question || !formData.reponse_correcte) {
            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/quiz/quiz.php';
            const method = editingQuiz ? 'PUT' : 'POST';
            const data = editingQuiz ? { ...formData, id: editingQuiz.id } : formData;

            console.log('🔄 Envoi requête quiz:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data.success) {
                Swal.fire('Succès', response.data.message, 'success');
                setShowModal(false);
                setEditingQuiz(null);
                resetForm();
                fetchQuiz();
            } else {
                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (quizItem) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier des quiz', 'error');
            return;
        }

        setEditingQuiz(quizItem);
        setFormData({
            devoir_id: quizItem.devoir_id || '',
            question: quizItem.question || '',
            reponse_correcte: quizItem.reponse_correcte || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer des quiz', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: 'Cette action est irréversible !',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer !',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                console.log('🗑️ Suppression quiz ID:', id);
                
                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/quiz/quiz.php', {
                    headers: { 
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                console.log('✅ Réponse suppression:', response.data);

                if (response.data.success) {
                    Swal.fire('Supprimé!', response.data.message, 'success');
                    fetchQuiz();
                } else {
                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer le quiz', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer le quiz', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            devoir_id: '',
            question: '',
            reponse_correcte: ''
        });
    };

    // Filtrage des quiz
    const filteredQuiz = quiz.filter(quizItem => {
        const searchLower = searchTerm.toLowerCase();
        return (
            (quizItem.question || '').toLowerCase().includes(searchLower) ||
            (quizItem.reponse_correcte || '').toLowerCase().includes(searchLower) ||
            (quizItem.devoir_titre || '').toLowerCase().includes(searchLower) ||
            (quizItem.matiere_nom || '').toLowerCase().includes(searchLower) ||
            (quizItem.classe_nom || '').toLowerCase().includes(searchLower)
        );
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentQuiz = filteredQuiz.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredQuiz.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when search changes
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm]);

    const styles = {
        accessDenied: {
            textAlign: 'center',
            padding: '50px 20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            margin: '20px 0'
        },
        infoMessage: {
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '8px',
            padding: '15px',
            margin: '20px 0',
            color: '#1565c0'
        },
        idBadge: {
            backgroundColor: '#007bff',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.8em',
            fontWeight: 'bold'
        },
        questionPreview: {
            maxWidth: '300px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
        }
    };

    // Vérification d'accès
    if (!canView) {
        return (
            <div className="factures-container">
                <div style={styles.accessDenied}>
                    <h2>🚫 Accès Refusé</h2>
                    <p>Cette interface est réservée aux enseignants et administrateurs.</p>
                    <p>Les étudiants n'ont pas accès à la gestion des quiz.</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des quiz...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>🧠 Gestion des Quiz</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredQuiz.length} quiz trouvé(s)
                    </span>
                    {canManage && (
                        <button 
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouveau Quiz
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les admins */}
            {isAdmin && !isEnseignant && (
                <div style={styles.infoMessage}>
                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les quiz en mode lecture seule. Seuls les enseignants peuvent créer, modifier ou supprimer des quiz.</p>
                </div>
            )}

            {/* Barre de recherche */}
            <div className="search-section">
                <div className="search-bar">
                    <img src="/search.png" alt="Rechercher" />
                    <input
                        type="text"
                        placeholder="Rechercher par question, réponse, devoir, matière..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>

            {/* Tableau des quiz */}
            <div className="table-container">
                {filteredQuiz.length === 0 ? (
                    <div className="no-data">
                        <img src="/empty.png" alt="Aucune donnée" />
                        <p>Aucun quiz trouvé</p>
                        <p>Essayez de modifier vos critères de recherche</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📚 Devoir</th>
                                    <th>❓ Question</th>
                                    <th>✅ Réponse Correcte</th>
                                    <th>📖 Matière</th>
                                    <th>🏫 Classe</th>
                                    {canManage && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentQuiz.map((quizItem) => (
                                    <tr key={quizItem.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{quizItem.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="devoir-info">
                                                <strong>{quizItem.devoir_titre || 'Devoir non défini'}</strong>
                                                <br />
                                                <small style={{ color: '#6c757d' }}>
                                                    ID: {quizItem.devoir_id}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div style={styles.questionPreview} title={quizItem.question}>
                                                {quizItem.question || 'Question non définie'}
                                            </div>
                                        </td>
                                        <td>
                                            <div style={styles.questionPreview} title={quizItem.reponse_correcte}>
                                                <span style={{
                                                    padding: '4px 8px',
                                                    backgroundColor: '#d4edda',
                                                    borderRadius: '4px',
                                                    fontSize: '0.9em',
                                                    color: '#155724'
                                                }}>
                                                    {quizItem.reponse_correcte || 'Réponse non définie'}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {quizItem.matiere_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3e0',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {quizItem.classe_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        {canManage && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(quizItem)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(quizItem.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="btn btn-outline-primary"
                    >
                        Précédent
                    </button>

                    <div className="page-info">
                        Page {currentPage} sur {totalPages}
                    </div>

                    <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="btn btn-outline-primary"
                    >
                        Suivant
                    </button>
                </div>
            )}

            {/* Modal pour ajouter/modifier un quiz */}
            {showModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingQuiz ? 'Modifier le quiz' : 'Nouveau quiz'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingQuiz(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Devoir *</label>
                                <select
                                    value={formData.devoir_id}
                                    onChange={(e) => setFormData({...formData, devoir_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner un devoir...</option>
                                    {devoirs.map(devoir => (
                                        <option key={devoir.id} value={devoir.id}>
                                            {devoir.devoir_complet || `${devoir.titre} - ${devoir.matiere_nom}`}
                                        </option>
                                    ))}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Sélectionnez le devoir auquel ce quiz est associé
                                </small>
                            </div>

                            <div className="form-group">
                                <label>Question *</label>
                                <textarea
                                    value={formData.question}
                                    onChange={(e) => setFormData({...formData, question: e.target.value})}
                                    placeholder="Saisissez la question du quiz..."
                                    required
                                    rows="4"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Réponse Correcte *</label>
                                <textarea
                                    value={formData.reponse_correcte}
                                    onChange={(e) => setFormData({...formData, reponse_correcte: e.target.value})}
                                    placeholder="Saisissez la réponse correcte..."
                                    required
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Cette réponse sera utilisée pour évaluer les réponses des étudiants
                                </small>
                            </div>

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingQuiz ? '💾 Modifier' : '➕ Créer'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingQuiz(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Quiz;
