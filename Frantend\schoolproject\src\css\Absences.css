/* Styles pour les Absences - Basé sur le modèle des Factures */

/* Import des styles de base des factures */
@import './Factures.css';

/* Styles spécifiques aux absences */
.absences-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    background: #f8f9fa;
    min-height: 100vh;
}

/* En-tête spécifique aux absences */
.page-header h1 {
    color: #dc3545;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Filtres et recherche */
.filters-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    border-left: 4px solid #dc3545;
}

.search-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.search-box {
    flex: 1;
    min-width: 300px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.filter-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #dc3545;
}

.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.total-count {
    padding: 6px 12px;
    background-color: #fff5f5;
    color: #dc3545;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
    border: 1px solid #fecaca;
}

/* Badges spécifiques aux absences */
.badge-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.badge-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Styles pour les cellules du tableau */
.table th {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 15px 12px;
    border: none;
}

.table td {
    vertical-align: middle;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #fff5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

/* Styles pour les informations étudiant */
.student-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.student-info strong {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.student-info small {
    color: #6c757d;
    font-size: 12px;
}

/* Styles pour la justification */
.justification-text {
    cursor: help;
    transition: all 0.3s ease;
}

.justification-text:hover {
    background-color: #e9ecef !important;
    transform: scale(1.02);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pagination-info {
    color: #6c757d;
    font-size: 14px;
}

.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    font-weight: 600;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 8px 4px;
    color: #6c757d;
}

/* Modal spécifique aux absences */
.modal-header h3 {
    color: #dc3545;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header h3::before {
    content: "📋";
    font-size: 1.2em;
}

/* En-tête de page amélioré */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 5px solid #dc3545;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-info .total-count {
    padding: 8px 16px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
    color: #dc3545;
    border-radius: 25px;
    font-size: 0.9em;
    font-weight: 600;
    border: 2px solid #fecaca;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

/* Bouton d'ajout principal */
.add-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.add-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.add-button:hover::before {
    left: 100%;
}

.add-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
}

.add-button img {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
}

/* Informations des boutons */
.button-info {
    margin-top: 4px;
    opacity: 0.9;
}

.button-info small {
    font-size: 10px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.3px;
    line-height: 1.2;
}

.add-button .button-info small {
    color: rgba(255, 255, 255, 0.9);
}

/* Boutons d'action dans le tableau */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.edit-button, .delete-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 90px;
    justify-content: center;
}

.edit-button {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.edit-button:hover {
    background: linear-gradient(135deg, #ffcd39 0%, #ffc107 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.delete-button {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-button:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.edit-button img, .delete-button img {
    width: 14px;
    height: 14px;
    filter: brightness(0) invert(1);
}

.edit-button img {
    filter: brightness(0) saturate(100%) invert(13%) sepia(94%) saturate(7151%) hue-rotate(0deg) brightness(90%) contrast(119%);
}

.btn-text {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Informations des boutons d'action */
.btn-info {
    margin-top: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-info small {
    font-size: 9px;
    font-weight: 400;
    text-transform: none;
    letter-spacing: 0.2px;
    line-height: 1;
}

.edit-button .btn-info small {
    color: rgba(33, 37, 41, 0.7);
}

.delete-button .btn-info small {
    color: rgba(255, 255, 255, 0.8);
}

/* Afficher les infos au survol */
.edit-button:hover .btn-info,
.delete-button:hover .btn-info {
    opacity: 1;
}

/* Ajustement de la taille des boutons pour les infos */
.edit-button, .delete-button {
    min-width: 100px;
    min-height: 45px;
    flex-direction: column;
    padding: 6px 10px;
}

/* Effet de brillance sur les boutons */
.edit-button::before, .delete-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.edit-button:hover::before, .delete-button:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .absences-container {
        padding: 15px;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 20px;
    }

    .header-info {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .add-button {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
        font-size: 16px;
    }

    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-group {
        justify-content: center;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .table th,
    .table td {
        padding: 8px 6px;
        font-size: 13px;
    }

    .student-info strong {
        font-size: 13px;
    }

    .student-info small {
        font-size: 11px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 6px;
        align-items: center;
    }

    .edit-button, .delete-button {
        width: 100%;
        min-width: 80px;
        padding: 10px 8px;
    }

    .btn-text {
        display: block;
    }
}

@media (max-width: 480px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
        justify-content: center;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .table {
        min-width: 700px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-sm {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* Animation pour les cartes */
.table tbody tr {
    transition: all 0.3s ease;
}

/* Styles pour les états d'absence */
.absence-justified {
    border-left: 4px solid #28a745;
}

.absence-unjustified {
    border-left: 4px solid #dc3545;
}

/* Indicateurs visuels */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.justified {
    background-color: #28a745;
}

.status-indicator.unjustified {
    background-color: #dc3545;
}

/* Amélioration des tooltips */
[title] {
    position: relative;
}

/* Styles pour les formulaires */
.form-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.form-group textarea:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Loading states */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #dc3545;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #dc3545;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No data state */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-data img {
    width: 120px;
    height: 120px;
    opacity: 0.6;
    margin-bottom: 20px;
}

.no-data p {
    font-size: 18px;
    font-weight: 500;
}
