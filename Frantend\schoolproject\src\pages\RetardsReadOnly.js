import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const RetardsReadOnly = () => {
    const { user } = useContext(AuthContext);
    const [retards, setRetards] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [justificationFilter, setJustificationFilter] = useState('all');
    const [dateFilter, setDateFilter] = useState('');

    useEffect(() => {
        fetchRetards();
    }, []);

    const fetchRetards = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/retards/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setRetards(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des retards:', error);
            Swal.fire('Erreur', 'Impossible de charger les retards', 'error');
        } finally {
            setLoading(false);
        }
    };

    const formatDuree = (duree) => {
        if (!duree) return 'Non spécifiée';
        
        const [hours, minutes] = duree.split(':');
        const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);
        
        if (totalMinutes < 60) {
            return `${totalMinutes} min`;
        } else {
            const h = Math.floor(totalMinutes / 60);
            const m = totalMinutes % 60;
            return m > 0 ? `${h}h ${m}min` : `${h}h`;
        }
    };

    const getDureeSeverity = (duree) => {
        if (!duree) return 'info';
        
        const [hours, minutes] = duree.split(':');
        const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);
        
        if (totalMinutes <= 15) return 'warning';
        if (totalMinutes <= 30) return 'danger';
        return 'dark';
    };

    // Filtrage des données
    const filteredRetards = retards.filter(retard => {
        const matchesSearch = retard.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             retard.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             retard.enseignant_nom?.toLowerCase().includes(searchTerm.toLowerCase());
        
        const hasJustification = retard.justification && retard.justification.trim() !== '';
        const matchesJustification = justificationFilter === 'all' || 
                                   (justificationFilter === 'justified' && hasJustification) ||
                                   (justificationFilter === 'not_justified' && !hasJustification);
        
        const matchesDate = !dateFilter || retard.date_retard.includes(dateFilter);
        
        return matchesSearch && matchesJustification && matchesDate;
    });

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des retards...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>⏰ Consultation des Retards</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredRetards.length} retard(s) trouvé(s)
                    </span>
                </div>
            </div>

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box">
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par étudiant, matière ou enseignant..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="justification-filter">
                    <select
                        value={justificationFilter}
                        onChange={(e) => setJustificationFilter(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    >
                        <option value="all">Tous les retards</option>
                        <option value="justified">Justifiés</option>
                        <option value="not_justified">Non justifiés</option>
                    </select>
                </div>
                <div className="date-filter">
                    <input
                        type="month"
                        value={dateFilter}
                        onChange={(e) => setDateFilter(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
            </div>

            <div className="factures-grid">
                {filteredRetards.length === 0 ? (
                    <div className="no-data">
                        <img src="/attendance.png" alt="Aucun retard" />
                        <p>Aucun retard trouvé</p>
                        {(searchTerm || justificationFilter !== 'all' || dateFilter) && (
                            <button 
                                onClick={() => {
                                    setSearchTerm('');
                                    setJustificationFilter('all');
                                    setDateFilter('');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer tous les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📚 Matière</th>
                                    <th>👨‍🏫 Enseignant</th>
                                    <th>📅 Date</th>
                                    <th>⏱️ Durée</th>
                                    <th>📝 Justification</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredRetards.map((retard) => (
                                    <tr key={retard.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{retard.etudiant_nom}</strong>
                                                <small>{retard.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {retard.matiere_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ color: '#6c757d' }}>
                                                {retard.enseignant_nom || 'Non spécifié'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: '500'
                                            }}>
                                                {new Date(retard.date_retard).toLocaleDateString('fr-FR')}
                                            </span>
                                        </td>
                                        <td>
                                            <span className={`badge badge-${getDureeSeverity(retard.duree_retard)}`} style={{
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                ⏰ {formatDuree(retard.duree_retard)}
                                            </span>
                                        </td>
                                        <td>
                                            {retard.justification && retard.justification.trim() !== '' ? (
                                                <div>
                                                    <span className="badge badge-success">✅ Justifié</span>
                                                    <div style={{
                                                        marginTop: '5px',
                                                        fontSize: '0.9em',
                                                        color: '#6c757d',
                                                        fontStyle: 'italic',
                                                        maxWidth: '200px'
                                                    }}>
                                                        "{retard.justification}"
                                                    </div>
                                                </div>
                                            ) : (
                                                <span className="badge badge-danger">❌ Non justifié</span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Statistiques */}
            {filteredRetards.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#dc3545', margin: '0' }}>
                            {filteredRetards.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total retards</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredRetards.filter(r => r.justification && r.justification.trim() !== '').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Justifiés</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {Math.round(
                                filteredRetards.reduce((sum, r) => {
                                    if (!r.duree_retard) return sum;
                                    const [hours, minutes] = r.duree_retard.split(':');
                                    return sum + (parseInt(hours) * 60 + parseInt(minutes));
                                }, 0) / filteredRetards.length
                            )} min
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Durée moyenne</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {new Set(filteredRetards.map(r => r.etudiant_id)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants concernés</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default RetardsReadOnly;
