import React, { useState, useEffect, useRef } from 'react';
import './NotificationBell.css';

const NotificationBell = () => {
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [showDropdown, setShowDropdown] = useState(false);
    const [loading, setLoading] = useState(false);
    const dropdownRef = useRef(null);

    useEffect(() => {
        fetchNotificationStats();
        fetchRecentNotifications();
        
        // Actualiser toutes les 30 secondes
        const interval = setInterval(() => {
            fetchNotificationStats();
            fetchRecentNotifications();
        }, 30000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        // Fermer le dropdown si on clique ailleurs
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const fetchNotificationStats = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/notifications/api.php?action=stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                setUnreadCount(data.unread || 0);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
        }
    };

    const fetchRecentNotifications = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/notifications/api.php?action=recent&limit=5', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                setNotifications(data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des notifications:', error);
        } finally {
            setLoading(false);
        }
    };

    const markAsRead = async (notificationId) => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/notifications/api.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ id: notificationId })
            });

            if (response.ok) {
                // Mettre à jour localement
                setNotifications(prev =>
                    prev.map(notif =>
                        notif.id === notificationId
                            ? { ...notif, lu: true, date_lecture: new Date().toISOString() }
                            : notif
                    )
                );

                // Actualiser le compteur
                fetchNotificationStats();
            }
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    };

    const handleNotificationClick = (notification) => {
        // Marquer comme lu si pas encore lu
        if (!notification.lu) {
            markAsRead(notification.id);
        }

        // Si c'est une notification de message, rediriger vers la messagerie
        if (notification.type_notification === 'message' && notification.message_id) {
            setShowDropdown(false);
            window.location.href = '/messages';
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'À l\'instant';
        if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
        if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)} h`;
        return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;
    };

    const getNotificationIcon = (type) => {
        switch (type) {
            case 'message': return '💬';
            case 'system': return '⚙️';
            case 'reminder': return '⏰';
            default: return '🔔';
        }
    };

    const getNotificationColor = (type) => {
        switch (type) {
            case 'message': return '#007bff';
            case 'system': return '#ffc107';
            case 'reminder': return '#28a745';
            default: return '#6c757d';
        }
    };

    return (
        <div className="notification-bell" ref={dropdownRef}>
            <button 
                className="bell-button"
                onClick={() => setShowDropdown(!showDropdown)}
                title={`${unreadCount} notification(s) non lue(s)`}
            >
                <span className="bell-icon">🔔</span>
                {unreadCount > 0 && (
                    <span className="notification-badge">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                )}
            </button>

            {showDropdown && (
                <div className="notification-dropdown">
                    <div className="dropdown-header">
                        <h4>Notifications</h4>
                        {unreadCount > 0 && (
                            <span className="unread-count">
                                {unreadCount} non lue{unreadCount > 1 ? 's' : ''}
                            </span>
                        )}
                    </div>

                    <div className="notifications-list">
                        {loading ? (
                            <div className="loading-notifications">
                                <div className="loading-spinner-small"></div>
                                <span>Chargement...</span>
                            </div>
                        ) : notifications.length === 0 ? (
                            <div className="no-notifications">
                                <span className="no-notif-icon">🔔</span>
                                <p>Aucune notification</p>
                            </div>
                        ) : (
                            notifications.map((notification) => (
                                <div 
                                    key={notification.id}
                                    className={`notification-item ${!notification.lu ? 'unread' : ''}`}
                                    onClick={() => handleNotificationClick(notification)}
                                >
                                    <div className="notification-content">
                                        <div className="notification-header">
                                            <span 
                                                className="notification-type-icon"
                                                style={{ color: getNotificationColor(notification.type_notification) }}
                                            >
                                                {getNotificationIcon(notification.type_notification)}
                                            </span>
                                            <span className="notification-title">
                                                {notification.titre}
                                            </span>
                                            {!notification.lu && (
                                                <span className="unread-dot"></span>
                                            )}
                                        </div>
                                        <p className="notification-message">
                                            {notification.message.length > 80 
                                                ? notification.message.substring(0, 80) + '...'
                                                : notification.message
                                            }
                                        </p>
                                        <div className="notification-meta">
                                            <span className="notification-time">
                                                {formatDate(notification.date_envoi)}
                                            </span>
                                            {notification.expediteur_nom && (
                                                <span className="notification-sender">
                                                    de {notification.expediteur_prenom} {notification.expediteur_nom}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>

                    {notifications.length > 0 && (
                        <div className="dropdown-footer">
                            <a href="/notifications" className="view-all-link">
                                Voir toutes les notifications
                            </a>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default NotificationBell;
