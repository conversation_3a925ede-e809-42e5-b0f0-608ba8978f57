<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🔒 TEST COMPLET DE SÉCURITÉ - TABLE PARENTS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-pass { background-color: #d4edda; }
        .test-fail { background-color: #f8d7da; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Objectif</h2>";
    echo "<p>Tester toutes les sécurités implémentées pour garantir que :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>AUCUN</strong> utilisateur sans le rôle 'parent' ne peut être inséré</li>";
    echo "<li>✅ <strong>Triggers</strong> de base de données fonctionnent</li>";
    echo "<li>✅ <strong>API Backend</strong> valide correctement</li>";
    echo "<li>✅ <strong>Filtrage Frontend</strong> est efficace</li>";
    echo "</ul>";
    echo "</div>";
    
    $testResults = [];
    
    // Étape 1 : Vérifier les triggers
    echo "<div class='step'>";
    echo "<h3>1. ⚡ Test des Triggers de Base de Données</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TRIGGERS LIKE 'parents'");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($triggers) >= 2) {
            echo "<p class='success'>✅ " . count($triggers) . " trigger(s) trouvé(s)</p>";
            $testResults['triggers_exist'] = true;
        } else {
            echo "<p class='error'>❌ Triggers manquants</p>";
            $testResults['triggers_exist'] = false;
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur vérification triggers : " . $e->getMessage() . "</p>";
        $testResults['triggers_exist'] = false;
    }
    echo "</div>";
    
    // Étape 2 : Créer des utilisateurs de test
    echo "<div class='step'>";
    echo "<h3>2. 👤 Création d'Utilisateurs de Test</h3>";
    
    // Vérifier les rôles
    $stmt = $pdo->query("SELECT id, nom FROM roles ORDER BY id");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $roleIds = [];
    foreach ($roles as $role) {
        $roleIds[$role['nom']] = $role['id'];
    }
    
    if (!isset($roleIds['parent']) || !isset($roleIds['admin'])) {
        echo "<p class='error'>❌ Rôles 'parent' ou 'admin' manquants</p>";
        $testResults['roles_exist'] = false;
    } else {
        echo "<p class='success'>✅ Rôles trouvés : parent (ID: {$roleIds['parent']}), admin (ID: {$roleIds['admin']})</p>";
        $testResults['roles_exist'] = true;
        
        // Créer un utilisateur parent de test
        try {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES ('Test', 'Parent', '<EMAIL>', 'test123', :role_id)
            ");
            $stmt->execute(['role_id' => $roleIds['parent']]);
            
            $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = '<EMAIL>'");
            $stmt->execute();
            $testParentUser = $stmt->fetch();
            
            if ($testParentUser) {
                echo "<p class='success'>✅ Utilisateur parent de test créé (ID: {$testParentUser['id']})</p>";
                $testResults['test_parent_created'] = $testParentUser['id'];
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur création utilisateur parent : " . $e->getMessage() . "</p>";
        }
        
        // Créer un utilisateur admin de test
        try {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO utilisateurs (nom, prenom, email, mot_de_passe, role_id) 
                VALUES ('Test', 'Admin', '<EMAIL>', 'test123', :role_id)
            ");
            $stmt->execute(['role_id' => $roleIds['admin']]);
            
            $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = '<EMAIL>'");
            $stmt->execute();
            $testAdminUser = $stmt->fetch();
            
            if ($testAdminUser) {
                echo "<p class='success'>✅ Utilisateur admin de test créé (ID: {$testAdminUser['id']})</p>";
                $testResults['test_admin_created'] = $testAdminUser['id'];
            }
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur création utilisateur admin : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Étape 3 : Test des triggers avec insertion directe SQL
    echo "<div class='step'>";
    echo "<h3>3. 🧪 Test des Triggers - Insertion Directe SQL</h3>";
    
    if (isset($testResults['test_admin_created'])) {
        $adminId = $testResults['test_admin_created'];
        
        echo "<h4>3.1 Test : Tentative d'insertion d'un utilisateur admin (doit échouer)</h4>";
        try {
            $stmt = $pdo->prepare("
                INSERT INTO parents (utilisateur_id, telephone, adresse) 
                VALUES (:utilisateur_id, '0123456789', 'Test Address Admin')
            ");
            $stmt->execute(['utilisateur_id' => $adminId]);
            
            echo "<p class='test-fail error'>❌ ÉCHEC : L'utilisateur admin a été inséré (ne devrait pas arriver !)</p>";
            $testResults['trigger_blocks_admin'] = false;
            
            // Nettoyer l'insertion incorrecte
            $deleteStmt = $pdo->prepare("DELETE FROM parents WHERE utilisateur_id = :user_id");
            $deleteStmt->execute(['user_id' => $adminId]);
            
        } catch (PDOException $e) {
            echo "<p class='test-pass success'>✅ SUCCÈS : Trigger a correctement bloqué l'insertion</p>";
            echo "<p style='color: #666; margin-left: 20px;'>Message : " . $e->getMessage() . "</p>";
            $testResults['trigger_blocks_admin'] = true;
        }
    }
    
    if (isset($testResults['test_parent_created'])) {
        $parentId = $testResults['test_parent_created'];
        
        echo "<h4>3.2 Test : Insertion d'un utilisateur parent (doit réussir)</h4>";
        try {
            $stmt = $pdo->prepare("
                INSERT INTO parents (utilisateur_id, telephone, adresse) 
                VALUES (:utilisateur_id, '0123456789', 'Test Address Parent')
            ");
            $stmt->execute(['utilisateur_id' => $parentId]);
            
            echo "<p class='test-pass success'>✅ SUCCÈS : Utilisateur parent inséré correctement</p>";
            $testResults['trigger_allows_parent'] = true;
            
            // Garder cette insertion pour les tests suivants
            $testResults['test_parent_inserted'] = true;
            
        } catch (PDOException $e) {
            echo "<p class='test-fail error'>❌ ÉCHEC : L'utilisateur parent n'a pas pu être inséré</p>";
            echo "<p style='color: #666; margin-left: 20px;'>Message : " . $e->getMessage() . "</p>";
            $testResults['trigger_allows_parent'] = false;
        }
    }
    echo "</div>";
    
    // Étape 4 : Test de l'API Backend
    echo "<div class='step'>";
    echo "<h3>4. 🌐 Test de l'API Backend</h3>";
    
    if (isset($testResults['test_admin_created'])) {
        $adminId = $testResults['test_admin_created'];
        
        echo "<h4>4.1 Test API : Tentative d'ajout d'un utilisateur admin via API</h4>";
        
        $apiUrl = 'http://localhost/Project_PFE/Backend/pages/parents/parents_api.php';
        $postData = json_encode([
            'utilisateur_id' => $adminId,
            'telephone' => '0123456789',
            'adresse' => 'Test Address API'
        ]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response && $httpCode === 200) {
            $data = json_decode($response, true);
            
            if (isset($data['error']) && isset($data['code']) && $data['code'] === 'INVALID_ROLE') {
                echo "<p class='test-pass success'>✅ SUCCÈS : API a correctement rejeté l'utilisateur admin</p>";
                echo "<p style='color: #666; margin-left: 20px;'>Message : " . $data['error'] . "</p>";
                $testResults['api_blocks_admin'] = true;
            } else if (isset($data['success']) && $data['success']) {
                echo "<p class='test-fail error'>❌ ÉCHEC : API a accepté l'utilisateur admin</p>";
                $testResults['api_blocks_admin'] = false;
            } else {
                echo "<p class='warning'>⚠️ Réponse API inattendue : " . json_encode($data) . "</p>";
                $testResults['api_blocks_admin'] = false;
            }
        } else {
            echo "<p class='error'>❌ Erreur API - Code: $httpCode</p>";
            $testResults['api_blocks_admin'] = false;
        }
    }
    echo "</div>";
    
    // Étape 5 : Test du filtrage frontend
    echo "<div class='step'>";
    echo "<h3>5. 🎨 Test du Filtrage Frontend</h3>";
    
    // Test de l'API utilisateurs avec filtrage
    $apiUrl = 'http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=parent';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response && $httpCode === 200) {
        $data = json_decode($response, true);
        if (is_array($data)) {
            $onlyParents = true;
            foreach ($data as $user) {
                if (isset($user['role_nom']) && $user['role_nom'] !== 'parent') {
                    $onlyParents = false;
                    break;
                }
            }
            
            if ($onlyParents) {
                echo "<p class='test-pass success'>✅ SUCCÈS : API utilisateurs retourne uniquement des parents</p>";
                echo "<p style='color: #666; margin-left: 20px;'>" . count($data) . " utilisateur(s) parent(s) trouvé(s)</p>";
                $testResults['frontend_filtering'] = true;
            } else {
                echo "<p class='test-fail error'>❌ ÉCHEC : API utilisateurs retourne des non-parents</p>";
                $testResults['frontend_filtering'] = false;
            }
        } else {
            echo "<p class='error'>❌ Format de réponse invalide</p>";
            $testResults['frontend_filtering'] = false;
        }
    } else {
        echo "<p class='error'>❌ Erreur API filtrage - Code: $httpCode</p>";
        $testResults['frontend_filtering'] = false;
    }
    echo "</div>";
    
    // Étape 6 : Nettoyage
    echo "<div class='step'>";
    echo "<h3>6. 🧹 Nettoyage des Données de Test</h3>";
    
    // Supprimer les parents de test
    if (isset($testResults['test_parent_inserted'])) {
        try {
            $stmt = $pdo->prepare("DELETE FROM parents WHERE utilisateur_id = :user_id");
            $stmt->execute(['user_id' => $testResults['test_parent_created']]);
            echo "<p class='success'>✅ Parent de test supprimé</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur suppression parent : " . $e->getMessage() . "</p>";
        }
    }
    
    // Supprimer les utilisateurs de test
    if (isset($testResults['test_parent_created'])) {
        try {
            $stmt = $pdo->prepare("DELETE FROM utilisateurs WHERE id = :user_id");
            $stmt->execute(['user_id' => $testResults['test_parent_created']]);
            echo "<p class='success'>✅ Utilisateur parent de test supprimé</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur suppression utilisateur parent : " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($testResults['test_admin_created'])) {
        try {
            $stmt = $pdo->prepare("DELETE FROM utilisateurs WHERE id = :user_id");
            $stmt->execute(['user_id' => $testResults['test_admin_created']]);
            echo "<p class='success'>✅ Utilisateur admin de test supprimé</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Erreur suppression utilisateur admin : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎯 RÉSUMÉ DES TESTS DE SÉCURITÉ</h3>";
    
    $totalTests = 0;
    $passedTests = 0;
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Résultat</th><th>Description</th></tr>";
    
    $tests = [
        'triggers_exist' => 'Triggers de sécurité existent',
        'roles_exist' => 'Rôles parent et admin existent',
        'trigger_blocks_admin' => 'Trigger bloque insertion admin',
        'trigger_allows_parent' => 'Trigger autorise insertion parent',
        'api_blocks_admin' => 'API rejette utilisateur admin',
        'frontend_filtering' => 'Filtrage frontend fonctionne'
    ];
    
    foreach ($tests as $testKey => $testName) {
        $totalTests++;
        $result = $testResults[$testKey] ?? false;
        if ($result) $passedTests++;
        
        $resultClass = $result ? 'test-pass' : 'test-fail';
        $resultText = $result ? '✅ SUCCÈS' : '❌ ÉCHEC';
        
        echo "<tr class='$resultClass'>";
        echo "<td><strong>$testName</strong></td>";
        echo "<td>$resultText</td>";
        echo "<td>" . ($result ? 'Fonctionne correctement' : 'Nécessite une correction') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $successRate = round(($passedTests / $totalTests) * 100, 1);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>📊 Score de Sécurité</h4>";
    echo "<p style='font-size: 24px; margin: 10px 0;'>";
    
    if ($successRate >= 100) {
        echo "<span class='success'>🔒 $successRate% - SÉCURITÉ PARFAITE</span>";
    } else if ($successRate >= 80) {
        echo "<span class='warning'>⚠️ $successRate% - SÉCURITÉ BONNE</span>";
    } else {
        echo "<span class='error'>❌ $successRate% - SÉCURITÉ INSUFFISANTE</span>";
    }
    
    echo "</p>";
    echo "<p><strong>Tests réussis :</strong> $passedTests / $totalTests</p>";
    echo "</div>";
    
    if ($successRate >= 100) {
        echo "<p class='success' style='font-size: 18px;'>🎉 TOUTES LES SÉCURITÉS FONCTIONNENT PARFAITEMENT !</p>";
        echo "<p>✅ La table Parents est maintenant complètement sécurisée.</p>";
        echo "<p>✅ Aucun utilisateur sans le rôle 'parent' ne peut être inséré.</p>";
    } else {
        echo "<p class='error' style='font-size: 18px;'>⚠️ CERTAINES SÉCURITÉS NÉCESSITENT DES CORRECTIONS</p>";
        echo "<p>Vérifiez les tests échoués ci-dessus et corrigez les problèmes identifiés.</p>";
    }
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><a href='../pages/parents/test_filtrage_dropdown.php'>Tester le filtrage dropdown</a></li>";
    echo "<li><a href='http://localhost:3000/parents'>Tester l'interface Parents</a></li>";
    echo "<li>Vérifier que seuls les utilisateurs parents apparaissent dans le dropdown</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Fichier :</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Ligne :</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
