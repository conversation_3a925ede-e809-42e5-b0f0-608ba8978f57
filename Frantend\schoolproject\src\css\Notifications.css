/* Import des styles de base depuis Messages.css */
@import './Messages.css';

/* Styles spécifiques aux notifications */

/* En-tête avec actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.mark-all-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.mark-all-btn:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Section des filtres */
.filters-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.type-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.type-stat {
    background: #f8f9fa;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    border: 1px solid #e9ecef;
}

/* Statistiques dans l'en-tête */
.stat-item.total {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    border: 2px solid #90caf9;
}

.stat-item.unread {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 2px solid #ffc107;
}

.stat-item.read {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
}

/* Items de notification */
.notification-item {
    padding: 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: white;
    margin-bottom: 10px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border-left: 4px solid transparent;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.notification-item.unread {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.notification-item.unread:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffe082 100%);
}

/* Contenu de la notification */
.notification-content {
    flex: 1;
    margin-right: 15px;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.notification-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.type-icon {
    font-size: 18px;
}

.type-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-date {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.unread-badge {
    background: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.notification-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.notification-message {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
    font-size: 14px;
}

.notification-sender {
    font-size: 13px;
    color: #007bff;
    margin-bottom: 5px;
}

.sender-role {
    color: #6c757d;
    font-style: italic;
    margin-left: 5px;
}

.read-info {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.read-info small {
    color: #6c757d;
    font-style: italic;
}

/* Actions des notifications */
.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.notification-actions .btn {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-actions .btn-primary {
    background: #007bff;
    color: white;
}

.notification-actions .btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.notification-actions .btn-danger {
    background: #dc3545;
    color: white;
}

.notification-actions .btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Layout des items */
.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

/* Aucune notification */
.no-notifications {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.no-notif-icon {
    font-size: 64px;
    opacity: 0.3;
    display: block;
    margin-bottom: 20px;
}

.no-notifications h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: #495057;
}

.no-notifications p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .type-stats {
        justify-content: center;
    }
    
    .header-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .mark-all-btn {
        width: 100%;
        text-align: center;
    }
    
    .notification-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .notification-actions {
        flex-direction: row;
        justify-content: center;
        width: 100%;
    }
    
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .notification-meta {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .notifications-container {
        padding: 15px;
    }
    
    .page-header {
        padding: 20px;
    }
    
    .filters-section {
        padding: 15px;
    }
    
    .notification-item {
        padding: 15px;
    }
    
    .notification-title {
        font-size: 15px;
    }
    
    .notification-message {
        font-size: 13px;
    }
    
    .type-stats {
        gap: 8px;
    }
    
    .type-stat {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Animations */
.notification-item {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Effet de nouvelle notification */
.notification-item.new-notification {
    animation: newNotificationPulse 1s ease-out;
}

@keyframes newNotificationPulse {
    0% {
        background-color: #d4edda;
        transform: scale(1.02);
    }
    50% {
        background-color: #c3e6cb;
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* Styles pour les différents types de notifications */
.notification-item[data-type="message"] {
    border-left-color: #007bff;
}

.notification-item[data-type="system"] {
    border-left-color: #ffc107;
}

.notification-item[data-type="reminder"] {
    border-left-color: #28a745;
}

/* Amélioration de l'accessibilité */
.notification-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Indicateur de chargement spécifique */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #ffc107;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
