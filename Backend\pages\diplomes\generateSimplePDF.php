
<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['diplome_id'])) {
    try {
        $diplome_id = $_GET['diplome_id'];

        $stmt = $pdo->prepare("
            SELECT
                d.*,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                u.id as numero_etudiant,
                g.nom as groupe_nom,
                c.nom as classe_nom,
                f.nom as filiere_nom,
                n.nom as niveau_nom,
                DATE_FORMAT(d.date_obtention, '%d/%m/%Y') as date_obtention_fr
            FROM Diplomes d
            JOIN Etudiants e ON d.etudiant_id = e.id
            JOIN Utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN Groupes g ON e.groupe_id = g.id
            LEFT JOIN Classes c ON g.classe_id = c.id
            LEFT JOIN Filieres f ON c.filiere_id = f.id
            LEFT JOIN Niveaux n ON c.niveau_id = n.id
            WHERE d.id = ?
        ");
        $stmt->execute([$diplome_id]);
        $diplome = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$diplome) {
            echo json_encode(['error' => 'Diplôme non trouvé']);
            exit;
        }

        $annee_scolaire = date('Y', strtotime($diplome['date_obtention'])) . '-' . (date('Y', strtotime($diplome['date_obtention'])) + 1);

        echo '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diplôme - ' . htmlspecialchars($diplome['etudiant_nom']) . '</title>
    <style>
        body {
            font-family: "Georgia", serif;
            color: #2c3e50;
            margin: 0;
            padding: 20px;
        }
        .diploma-container {
            max-width: 850px;
            margin: 50px auto;
            padding: 50px;
            background: linear-gradient(135deg, #ffffff, #fbfbfb);
            border: 12px solid #2c3e50;
            border-image: linear-gradient(to right, #2c3e50, #3498db, #2c3e50) 1;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 20px;
        }
        .header, .content, .footer {
            text-align: center;
            margin-bottom: 20px;
        }
        .student-info, .info-grid {
            text-align: left;
            margin-top: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .diploma-subject {
            margin: 30px 0;
            font-size: 20px;
            font-weight: bold;
        }
        .signatures {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
        }
        @media print {
            * {
                animation: none !important;
                box-shadow: none !important;
                background: none !important;
            }
            .diploma-container {
                border: none;
            }
        }
    </style>
    <script>
        window.onload = function () {
            setTimeout(function () {
                window.print();
            }, 1000);
        };
        window.onafterprint = function () {
            window.close();
        };
    </script>
</head>
<body>
    <div class="diploma-container">
        <div class="header">
            <h1>École de Gestion Scolaire</h1>
            <h2>Diplôme Officiel</h2>
        </div>
        <div class="content">
            <p><strong>Il est certifié par les présentes que</strong></p>
            <div class="student-info">
                <div class="info-item"><strong>Nom :</strong> ' . htmlspecialchars(strtoupper($diplome['etudiant_nom'])) . '</div>
                <div class="info-item"><strong>Numéro :</strong> ' . htmlspecialchars($diplome['numero_etudiant']) . '</div>
                <div class="info-item"><strong>Email :</strong> ' . htmlspecialchars($diplome['etudiant_email']) . '</div>
                <div class="info-item"><strong>Filière :</strong> ' . htmlspecialchars($diplome['filiere_nom']) . '</div>
                <div class="info-item"><strong>Niveau :</strong> ' . htmlspecialchars($diplome['niveau_nom']) . '</div>
                <div class="info-item"><strong>Classe :</strong> ' . htmlspecialchars($diplome['classe_nom']) . '</div>
                <div class="info-item"><strong>Année scolaire :</strong> ' . htmlspecialchars($annee_scolaire) . '</div>
            </div>
            <p><strong>a obtenu avec succès le diplôme de :</strong></p>
            <div class="diploma-subject">' . htmlspecialchars(strtoupper($diplome['titre'])) . '</div>
            <div><strong>Délivré le :</strong> ' . htmlspecialchars($diplome['date_obtention_fr']) . '</div>
            <div class="signatures">
                <div><strong>Le Directeur</strong><hr><em>Signature</em></div>
                <div><strong>Responsable Académique</strong><hr><em>Signature</em></div>
            </div>
        </div>
        <div class="footer">
            <p><strong>École de Gestion Scolaire</strong></p>
            <p>Document officiel - Toute reproduction non autorisée est interdite</p>
            <p>Généré le ' . date('d/m/Y à H:i') . '</p>
        </div>
    </div>
</body>
</html>';
    } catch (Exception $e) {
        echo json_encode(['error' => 'Erreur lors de la génération: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['error' => 'ID du diplôme manquant']);
}
?>
