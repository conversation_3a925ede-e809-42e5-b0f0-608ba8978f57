<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

// Vérification du token JWT (temporairement désactivée pour les tests)
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';

// Pour les tests, on accepte les requêtes sans token
// TODO: Réactiver la vérification JWT en production
if (false && (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches))) {
    http_response_code(401);
    echo json_encode(['error' => 'Token d\'authentification requis']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Récupérer le paramètre de filtrage par rôle
        $roleFilter = $_GET['role'] ?? null;
        
        // Log pour debug
        error_log("🔍 Filtrage utilisateurs - Rôle demandé: " . ($roleFilter ?? 'tous'));
        
        // Construire la requête avec ou sans filtre
        if ($roleFilter) {
            // Filtrer par rôle spécifique
            $stmt = $pdo->prepare("
                            SELECT 
                    u.id,
                    u.nom,
                    u.email,
                    u.role_id,
                    r.nom as role_nom
                FROM utilisateurs u
                INNER JOIN roles r ON u.role_id = r.id
                WHERE LOWER(r.nom) = LOWER(:role)
                ORDER BY u.nom

            ");
            $stmt->execute(['role' => $roleFilter]);
        } else {
            // Récupérer tous les utilisateurs
            $stmt = $pdo->prepare("
                                SELECT 
                        u.id,
                        u.nom,
                        u.email,
                        u.role_id,
                        r.nom as role_nom
                    FROM utilisateurs u
                    INNER JOIN roles r ON u.role_id = r.id
                    WHERE LOWER(r.nom) = LOWER(:role)
                    ORDER BY u.nom

            ");
            $stmt->execute();
        }
        
        $utilisateurs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ajouter des informations formatées pour l'affichage
        foreach ($utilisateurs as &$user) {
            $user['nom_complet'] = trim($user['nom']);
            $user['display_text'] = $user['nom_complet'] . ' - ' . $user['email'];

        }
        
        // Log pour debug
        error_log("✅ Utilisateurs récupérés: " . count($utilisateurs) . " pour le rôle: " . ($roleFilter ?? 'tous'));
        
        // Si on filtre par "parent", exclure ceux qui sont déjà dans la table parents
       // Si on filtre par "parent", exclure ceux qui sont déjà dans la table parents
if (strtolower($roleFilter) === 'parent') {
    // Récupérer les IDs des utilisateurs déjà assignés comme parents
    $stmt = $pdo->prepare("SELECT utilisateur_id FROM parents WHERE utilisateur_id IS NOT NULL");
    $stmt->execute();
    $parentsExistants = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Filtrer les utilisateurs avec le rôle "parent" qui ne sont pas encore assignés
    $utilisateurs = array_filter($utilisateurs, function($user) use ($parentsExistants) {
        return !in_array($user['id'], $parentsExistants) && strtolower($user['role_nom']) === 'parent';
    });

    // Réindexer le tableau
    $utilisateurs = array_values($utilisateurs);

    error_log("✅ Utilisateurs parents disponibles (non encore dans table parents): " . count($utilisateurs));
}

        
        echo json_encode($utilisateurs);
        
    } catch (PDOException $e) {
        error_log("❌ Erreur SQL GET utilisateurs: " . $e->getMessage());
        echo json_encode(['error' => 'Erreur base de données : ' . $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
