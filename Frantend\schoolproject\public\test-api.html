<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Messagerie</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; color: #721c24; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Messagerie depuis React</h1>
        <p>Ce test vérifie la connectivité entre React et l'API PHP</p>
        
        <button onclick="testAPI()">🚀 Tester l'API</button>
        <button onclick="testConversations()">💬 Tester Conversations</button>
        <button onclick="testUsers()">👥 Tester Users</button>
        <button onclick="clearResults()">🗑️ Effacer</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function makeAPIRequest(endpoint) {
            try {
                const token = 'test_user_1';
                const config = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };
                
                const url = `${API_BASE_URL}?action=${endpoint}`;
                addResult(`🔗 Requête vers: ${endpoint}`, `URL: ${url}\nHeaders: ${JSON.stringify(config.headers, null, 2)}`, 'info');
                
                const response = await fetch(url, config);
                const result = await response.json();
                
                if (result.success) {
                    addResult(`✅ Succès: ${endpoint}`, JSON.stringify(result, null, 2), 'success');
                } else {
                    addResult(`❌ Erreur: ${endpoint}`, JSON.stringify(result, null, 2), 'error');
                }
                
                return result;
            } catch (error) {
                addResult(`💥 Exception: ${endpoint}`, `Erreur: ${error.message}\nStack: ${error.stack}`, 'error');
                throw error;
            }
        }
        
        async function testAPI() {
            addResult('🚀 Test API', 'Démarrage du test de l\'endpoint test...', 'info');
            await makeAPIRequest('test');
        }
        
        async function testConversations() {
            addResult('💬 Test Conversations', 'Démarrage du test de l\'endpoint conversations...', 'info');
            await makeAPIRequest('conversations');
        }
        
        async function testUsers() {
            addResult('👥 Test Users', 'Démarrage du test de l\'endpoint users...', 'info');
            await makeAPIRequest('users');
        }
        
        // Test automatique au chargement
        window.onload = function() {
            addResult('🌐 Page chargée', 'Test de connectivité API depuis React (port 3000)', 'info');
        };
    </script>
</body>
</html>
