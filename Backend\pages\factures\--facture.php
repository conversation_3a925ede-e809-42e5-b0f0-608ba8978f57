<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('Database.php'); // تأكد من المسار المناسب

$db = new Database();
$method = $_SERVER['REQUEST_METHOD'];

// ---------------- GET ALL or SINGLE ----------------
if ($method === 'GET') {
    if (isset($_GET['id'])) {
        $facture = $db->getSingleBill((int)$_GET['id']);
        echo json_encode($facture ?: ['error' => 'Facture non trouvée']);
    } else {
        $factures = $db->read();
        echo json_encode($factures);
    }

// ---------------- CREATE ----------------
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents("php://input"), true);
    $etudiant_id = $input['etudiant_id'] ?? null;
    $mois = $input['mois'] ?? null;
    $montant = $input['montant'] ?? null;
    $date_paiement = $input['date_paiement'] ?? null;
    $statut = $input['statut'] ?? 'Non payé';

    if (!$etudiant_id || !$mois || !$montant || !$date_paiement) {
        http_response_code(400);
        echo json_encode(['error' => 'Données incomplètes']);
        exit();
    }

    $success = $db->create((int)$etudiant_id, $mois, (float)$montant, $date_paiement, $statut);
    echo json_encode($success ? ['message' => 'Facture créée avec succès'] : ['error' => 'Échec de la création']);

// ---------------- UPDATE ----------------
} elseif ($method === 'PUT') {
    $input = json_decode(file_get_contents("php://input"), true);

    $id = $input['id'] ?? null;
    $etudiant_id = $input['etudiant_id'] ?? null;
    $mois = $input['mois'] ?? null;
    $montant = $input['montant'] ?? null;
    $date_paiement = $input['date_paiement'] ?? null;
    $statut = $input['statut'] ?? null;

    if (!$id || !$etudiant_id || !$mois || !$montant || !$date_paiement || !$statut) {
        http_response_code(400);
        echo json_encode(['error' => 'Données incomplètes pour la mise à jour']);
        exit();
    }

    $success = $db->update((int)$id, (int)$etudiant_id, $mois, (float)$montant, $date_paiement, $statut);
    echo json_encode($success ? ['message' => 'Facture mise à jour'] : ['error' => 'Échec de la mise à jour']);

// ---------------- DELETE ----------------
} elseif ($method === 'DELETE') {
    $input = json_decode(file_get_contents("php://input"), true);
    $id = $input['id'] ?? null;

    if (!$id) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant pour la suppression']);
        exit();
    }

    $success = $db->delete((int)$id);
    echo json_encode($success ? ['message' => 'Facture supprimée'] : ['error' => 'Échec de la suppression']);
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
