<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Fonction d'authentification
function getAuthenticatedUser($pdo) {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        return null;
    }

    // Simulation d'authentification
    if (strpos($token, 'enseignant') !== false) {
        return [
            'id' => 2,
            'role' => 'enseignant',
            'email' => '<EMAIL>'
        ];
    }
    
    return null;
}

try {
    $user = getAuthenticatedUser($pdo);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentification requise']);
        exit();
    }
    
    // Seuls les enseignants peuvent accéder à cette API
    if ($user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Accès réservé aux enseignants']);
        exit();
    }
    
    // Récupérer tous les devoirs avec leurs informations complètes
    // Inclure le nombre de quiz et d'étudiants ayant répondu
    $sql = "
        SELECT 
            d.id,
            d.titre,
            d.description,
            d.date_remise,
            d.matiere_id,
            d.classe_id,
            m.nom as matiere_nom,
            c.nom as classe_nom,
            CONCAT(d.titre, ' (', m.nom, ' - ', c.nom, ')') as devoir_display,
            (SELECT COUNT(*) FROM quiz q WHERE q.devoir_id = d.id) as nombre_quiz,
            (SELECT COUNT(DISTINCT rq.etudiant_id) 
             FROM reponsesquiz rq 
             JOIN quiz q ON rq.quiz_id = q.id 
             WHERE q.devoir_id = d.id) as nombre_etudiants_repondus,
            (SELECT COUNT(*) 
             FROM notes n 
             WHERE n.devoir_id = d.id AND n.matiere_id = d.matiere_id) as notes_existantes,
            DATE_FORMAT(d.date_remise, '%d/%m/%Y') as date_remise_formatted
        FROM devoirs d
        LEFT JOIN matieres m ON d.matiere_id = m.id
        LEFT JOIN classes c ON d.classe_id = c.id
        WHERE d.titre IS NOT NULL AND d.titre != ''
        ORDER BY 
            d.date_remise DESC, 
            d.id DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $devoirs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Enrichir les données avec des informations supplémentaires
    foreach ($devoirs as &$devoir) {
        $devoir['peut_generer_notes'] = ($devoir['nombre_quiz'] > 0 && $devoir['nombre_etudiants_repondus'] > 0);
        $devoir['statut'] = '';
        
        if ($devoir['nombre_quiz'] == 0) {
            $devoir['statut'] = 'Aucun quiz';
        } elseif ($devoir['nombre_etudiants_repondus'] == 0) {
            $devoir['statut'] = 'Aucune réponse';
        } elseif ($devoir['notes_existantes'] > 0) {
            $devoir['statut'] = 'Notes existantes (' . $devoir['notes_existantes'] . ')';
        } else {
            $devoir['statut'] = 'Prêt pour génération';
        }
    }
    
    // Statistiques globales
    $total_devoirs = count($devoirs);
    $devoirs_avec_quiz = count(array_filter($devoirs, function($d) { return $d['nombre_quiz'] > 0; }));
    $devoirs_avec_reponses = count(array_filter($devoirs, function($d) { return $d['nombre_etudiants_repondus'] > 0; }));
    $devoirs_avec_notes = count(array_filter($devoirs, function($d) { return $d['notes_existantes'] > 0; }));
    $devoirs_prets = count(array_filter($devoirs, function($d) { return $d['peut_generer_notes']; }));
    
    $response = [
        'success' => true,
        'data' => $devoirs,
        'statistics' => [
            'total_devoirs' => $total_devoirs,
            'devoirs_avec_quiz' => $devoirs_avec_quiz,
            'devoirs_avec_reponses' => $devoirs_avec_reponses,
            'devoirs_avec_notes' => $devoirs_avec_notes,
            'devoirs_prets_generation' => $devoirs_prets
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur serveur: ' . $e->getMessage()
    ]);
}
?>
