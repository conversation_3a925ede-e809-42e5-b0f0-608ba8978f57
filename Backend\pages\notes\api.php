<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Fonction d'authentification
function getAuthenticatedUser($pdo) {
    $headers = getallheaders();
    $token = null;

    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        return null;
    }

    // Simulation d'authentification basée sur le token
    if (strpos($token, 'etudiant') !== false) {
        $stmt = $pdo->query("SELECT id FROM etudiants LIMIT 1");
        $etudiant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$etudiant) {
            return null;
        }
        
        return [
            'id' => 1,
            'role' => 'etudiant',
            'etudiant_id' => $etudiant['id'],
            'email' => '<EMAIL>'
        ];
    } elseif (strpos($token, 'enseignant') !== false) {
        return [
            'id' => 2,
            'role' => 'enseignant',
            'email' => '<EMAIL>'
        ];
    } elseif (strpos($token, 'admin') !== false) {
        return [
            'id' => 3,
            'role' => 'admin',
            'email' => '<EMAIL>'
        ];
    }
    
    return null;
}

// Fonction de calcul automatique de la note
function calculateNote($pdo, $etudiant_id, $devoir_id) {
    try {
        // Récupérer tous les quiz liés à ce devoir
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_questions
            FROM quiz q
            WHERE q.devoir_id = ?
        ");
        $stmt->execute([$devoir_id]);
        $total_questions = $stmt->fetch(PDO::FETCH_ASSOC)['total_questions'];
        
        if ($total_questions == 0) {
            return null; // Pas de quiz pour ce devoir
        }
        
        // Récupérer le nombre de bonnes réponses de l'étudiant pour ce devoir
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as bonnes_reponses
            FROM reponsesquiz rq
            JOIN quiz q ON rq.quiz_id = q.id
            WHERE rq.etudiant_id = ? 
            AND q.devoir_id = ? 
            AND rq.est_correct = 1
        ");
        $stmt->execute([$etudiant_id, $devoir_id]);
        $bonnes_reponses = $stmt->fetch(PDO::FETCH_ASSOC)['bonnes_reponses'];
        
        // Calcul de la note : (Bonnes réponses ÷ Total questions) × 20
        $note = ($bonnes_reponses / $total_questions) * 20;
        
        return round($note, 2);
        
    } catch (Exception $e) {
        error_log("Erreur calcul note: " . $e->getMessage());
        return null;
    }
}

// Fonction pour générer automatiquement les notes d'un devoir
function generateNotesForDevoir($pdo, $devoir_id) {
    try {
        // Récupérer la matière du devoir
        $stmt = $pdo->prepare("SELECT matiere_id FROM devoirs WHERE id = ?");
        $stmt->execute([$devoir_id]);
        $devoir = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$devoir) {
            return ['error' => 'Devoir non trouvé'];
        }
        
        // Récupérer tous les étudiants qui ont répondu à au moins un quiz de ce devoir
        $stmt = $pdo->prepare("
            SELECT DISTINCT rq.etudiant_id
            FROM reponsesquiz rq
            JOIN quiz q ON rq.quiz_id = q.id
            WHERE q.devoir_id = ?
        ");
        $stmt->execute([$devoir_id]);
        $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $notes_generees = 0;
        $notes_mises_a_jour = 0;
        
        foreach ($etudiants as $etudiant) {
            $etudiant_id = $etudiant['etudiant_id'];
            
            // Calculer la note
            $note = calculateNote($pdo, $etudiant_id, $devoir_id);
            
            if ($note !== null) {
                // Vérifier si une note existe déjà
                $stmt = $pdo->prepare("
                    SELECT id FROM notes 
                    WHERE etudiant_id = ? AND devoir_id = ? AND matiere_id = ?
                ");
                $stmt->execute([$etudiant_id, $devoir_id, $devoir['matiere_id']]);
                $existing = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($existing) {
                    // Mettre à jour la note existante
                    $stmt = $pdo->prepare("
                        UPDATE notes 
                        SET note = ?, date_enregistrement = CURDATE()
                        WHERE id = ?
                    ");
                    $stmt->execute([$note, $existing['id']]);
                    $notes_mises_a_jour++;
                } else {
                    // Créer une nouvelle note
                    $stmt = $pdo->prepare("
                        INSERT INTO notes (etudiant_id, devoir_id, matiere_id, note, date_enregistrement)
                        VALUES (?, ?, ?, ?, CURDATE())
                    ");
                    $stmt->execute([$etudiant_id, $devoir_id, $devoir['matiere_id'], $note]);
                    $notes_generees++;
                }
            }
        }
        
        return [
            'success' => true,
            'notes_generees' => $notes_generees,
            'notes_mises_a_jour' => $notes_mises_a_jour,
            'total_etudiants' => count($etudiants)
        ];
        
    } catch (Exception $e) {
        return ['error' => 'Erreur lors de la génération des notes: ' . $e->getMessage()];
    }
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $user = getAuthenticatedUser($pdo);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentification requise']);
        exit();
    }
    
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user);
            break;
        case 'POST':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePost($pdo, $user, $input);
            break;
        case 'PUT':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePut($pdo, $user, $input);
            break;
        case 'DELETE':
            $input = json_decode(file_get_contents("php://input"), true);
            handleDelete($pdo, $user, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

function handleGet($pdo, $user) {
    try {
        $role = $user['role'];
        
        // Construction de la requête selon le rôle
        $baseQuery = "
            SELECT 
                n.id,
                n.etudiant_id,
                n.devoir_id,
                n.matiere_id,
                n.note,
                n.date_enregistrement,
                u.nom as etudiant_nom,
                u.email as etudiant_email,
                d.titre as devoir_titre,
                d.description as devoir_description,
                d.date_remise,
                m.nom as matiere_nom,
                c.nom as classe_nom,
                DATE_FORMAT(n.date_enregistrement, '%d/%m/%Y') as date_formatted
            FROM notes n
            LEFT JOIN etudiants e ON n.etudiant_id = e.id
            LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN devoirs d ON n.devoir_id = d.id
            LEFT JOIN matieres m ON n.matiere_id = m.id
            LEFT JOIN classes c ON d.classe_id = c.id
        ";
        
        // Filtrage selon le rôle
        if ($role === 'etudiant') {
            // L'étudiant ne voit que ses propres notes
            $query = $baseQuery . " WHERE n.etudiant_id = ? ORDER BY n.date_enregistrement DESC, n.id DESC";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$user['etudiant_id']]);
        } else {
            // Enseignants et admins voient toutes les notes
            $query = $baseQuery . " ORDER BY n.date_enregistrement DESC, n.id DESC";
            $stmt = $pdo->prepare($query);
            $stmt->execute();
        }
        
        $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ajouter des métadonnées selon le rôle
        $response = [
            'success' => true,
            'data' => $notes,
            'count' => count($notes),
            'role' => $role,
            'permissions' => [
                'can_create' => $role === 'enseignant',
                'can_update' => $role === 'enseignant',
                'can_delete' => $role === 'enseignant',
                'can_generate' => $role === 'enseignant',
                'read_only' => $role !== 'enseignant'
            ]
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des notes: ' . $e->getMessage()]);
    }
}

function handlePost($pdo, $user, $input) {
    // Seuls les enseignants peuvent créer des notes
    if ($user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent créer des notes']);
        return;
    }
    
    // Vérifier le type d'action
    if (isset($input['action']) && $input['action'] === 'generate') {
        // Génération automatique des notes pour un devoir
        if (!isset($input['devoir_id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'devoir_id requis pour la génération automatique']);
            return;
        }
        
        $result = generateNotesForDevoir($pdo, $input['devoir_id']);
        
        if (isset($result['error'])) {
            http_response_code(400);
            echo json_encode(['error' => $result['error']]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'Notes générées automatiquement',
                'details' => $result
            ]);
        }
        return;
    }
    
    // Création manuelle d'une note
    if (!isset($input['etudiant_id']) || !isset($input['devoir_id']) || !isset($input['matiere_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (etudiant_id, devoir_id, matiere_id requis)']);
        return;
    }
    
    try {
        // Vérifier que l'étudiant, le devoir et la matière existent
        $stmt = $pdo->prepare("SELECT id FROM etudiants WHERE id = ?");
        $stmt->execute([$input['etudiant_id']]);
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Étudiant non trouvé']);
            return;
        }
        
        $stmt = $pdo->prepare("SELECT id, matiere_id FROM devoirs WHERE id = ?");
        $stmt->execute([$input['devoir_id']]);
        $devoir = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$devoir) {
            http_response_code(400);
            echo json_encode(['error' => 'Devoir non trouvé']);
            return;
        }
        
        $stmt = $pdo->prepare("SELECT id FROM matieres WHERE id = ?");
        $stmt->execute([$input['matiere_id']]);
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Matière non trouvée']);
            return;
        }
        
        // Vérifier qu'une note n'existe pas déjà
        $stmt = $pdo->prepare("SELECT id FROM notes WHERE etudiant_id = ? AND devoir_id = ? AND matiere_id = ?");
        $stmt->execute([$input['etudiant_id'], $input['devoir_id'], $input['matiere_id']]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Une note existe déjà pour cet étudiant, ce devoir et cette matière']);
            return;
        }
        
        // Calculer automatiquement la note ou utiliser la note fournie
        $note = null;
        if (isset($input['note']) && is_numeric($input['note'])) {
            $note = floatval($input['note']);
            if ($note < 0 || $note > 20) {
                http_response_code(400);
                echo json_encode(['error' => 'La note doit être entre 0 et 20']);
                return;
            }
        } else {
            // Calcul automatique
            $note = calculateNote($pdo, $input['etudiant_id'], $input['devoir_id']);
            if ($note === null) {
                http_response_code(400);
                echo json_encode(['error' => 'Impossible de calculer la note automatiquement. Aucune réponse trouvée ou aucun quiz pour ce devoir.']);
                return;
            }
        }
        
        // Insérer la note
        $stmt = $pdo->prepare("
            INSERT INTO notes (etudiant_id, devoir_id, matiere_id, note, date_enregistrement) 
            VALUES (?, ?, ?, ?, CURDATE())
        ");
        
        $stmt->execute([
            $input['etudiant_id'],
            $input['devoir_id'],
            $input['matiere_id'],
            $note
        ]);
        
        $response = [
            'success' => true,
            'message' => 'Note enregistrée avec succès',
            'id' => $pdo->lastInsertId(),
            'note_calculee' => $note
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

function handlePut($pdo, $user, $input) {
    // Seuls les enseignants peuvent modifier les notes
    if ($user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent modifier les notes']);
        return;
    }

    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de la note manquant']);
        return;
    }

    try {
        // Vérifier que la note existe
        $stmt = $pdo->prepare("SELECT * FROM notes WHERE id = ?");
        $stmt->execute([$input['id']]);
        $note = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$note) {
            http_response_code(404);
            echo json_encode(['error' => 'Note non trouvée']);
            return;
        }

        // Préparer les champs à mettre à jour
        $updates = [];
        $params = [];

        if (isset($input['note']) && is_numeric($input['note'])) {
            $new_note = floatval($input['note']);
            if ($new_note < 0 || $new_note > 20) {
                http_response_code(400);
                echo json_encode(['error' => 'La note doit être entre 0 et 20']);
                return;
            }
            $updates[] = "note = ?";
            $params[] = $new_note;
        }

        if (isset($input['recalculate']) && $input['recalculate'] === true) {
            // Recalculer automatiquement la note
            $calculated_note = calculateNote($pdo, $note['etudiant_id'], $note['devoir_id']);
            if ($calculated_note !== null) {
                $updates[] = "note = ?";
                $params[] = $calculated_note;
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Impossible de recalculer la note automatiquement']);
                return;
            }
        }

        if (empty($updates)) {
            echo json_encode([
                'success' => true,
                'message' => 'Aucune modification nécessaire'
            ]);
            return;
        }

        // Ajouter la date de mise à jour
        $updates[] = "date_enregistrement = CURDATE()";
        $params[] = $input['id'];

        // Mettre à jour la note
        $sql = "UPDATE notes SET " . implode(", ", $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Note modifiée avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'Aucune modification nécessaire'
            ]);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

function handleDelete($pdo, $user, $input) {
    // Seuls les enseignants peuvent supprimer les notes
    if ($user['role'] !== 'enseignant') {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls les enseignants peuvent supprimer les notes']);
        return;
    }

    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de la note manquant']);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM notes WHERE id = ?");
        $stmt->execute([$input['id']]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Note supprimée avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Note non trouvée'
            ]);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
