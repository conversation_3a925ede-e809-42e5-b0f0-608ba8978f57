.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  /* نعوض الخلفية بتدرج ألوان cerulean و moonstone */
  background: linear-gradient(to right, #006989, #01a7c2); /* cerulean to moonstone */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.register-form {
  /* نخليها خلفية فاتحة قريب من antiflash white */
  background-color: #f5f7f9;
  padding: 30px 40px;
  border-radius: 12px;
  /* ظل خفيف مع لمسة ألوان cerulean */
  box-shadow: 0 10px 25px rgba(1, 167, 194, 0.3);
  width: 100%;
  max-width: 400px;
  color: #006989; /* نص cerulean */
}

.register-form h2 {
  text-align: center;
  margin-bottom: 25px;
  /* لون العنوان cerulean-2 */
  color: #007090;
}

.register-form input,
.register-form select {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  /* نعوض لون الحدود بتدرج cerulean */
  border: 1px solid #007090;
  border-radius: 8px;
  font-size: 14px;
  color: #274c77; /* لون نص داكن مناسب */
  background-color: #ffffffdd; /* خلفية شفافة قليلاً */
  transition: border-color 0.3s ease;
}

.register-form input:focus,
.register-form select:focus {
  outline: none;
  border-color: #01a7c2; /* لون التركيز moonstone */
  box-shadow: 0 0 8px #01a7c2aa;
}

.register-form button {
  width: 100%;
  padding: 12px;
  /* نبدل لون الخلفية إلى moonstone */
  background-color: #01a7c2;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.register-form button:hover {
  /* لون أغمق قليلاً من moonstone */
  background-color: #007090;
}
