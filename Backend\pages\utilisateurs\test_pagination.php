<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST SYSTÈME DE PAGINATION - LISTE UTILISATEURS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: red; font-weight: bold; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: blue; font-weight: bold; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: orange; font-weight: bold; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
    </style>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 SYSTÈME DE PAGINATION IMPLÉMENTÉ</h2>";
    echo "<p><strong>Pagination complète avec contrôles avancés pour la liste des utilisateurs</strong></p>";
    echo "</div>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='success'>";
        echo "<h3>✅ Connexion à la Base de Données Réussie</h3>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Erreur de Connexion</h3>";
        echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
        exit();
    }
    
    // Compter le nombre total d'utilisateurs
    echo "<div class='info'>";
    echo "<h3>📊 Analyse des Données pour la Pagination</h3>";
    echo "</div>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM utilisateurs");
        $total_users = $stmt->fetchColumn();
        
        echo "<div class='info'>";
        echo "<p><strong>Nombre total d'utilisateurs :</strong> $total_users</p>";
        echo "</div>";
        
        // Simuler différentes tailles de page
        $page_sizes = [6, 12, 24, 48];
        
        echo "<table>";
        echo "<tr><th>Utilisateurs par page</th><th>Nombre de pages</th><th>Dernière page (utilisateurs)</th></tr>";
        
        foreach ($page_sizes as $size) {
            $total_pages = ceil($total_users / $size);
            $last_page_users = $total_users % $size;
            if ($last_page_users == 0) $last_page_users = $size;
            
            echo "<tr>";
            echo "<td><strong>$size</strong></td>";
            echo "<td>$total_pages</td>";
            echo "<td>$last_page_users</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if ($total_users < 12) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Peu d'utilisateurs pour tester la pagination</h4>";
            echo "<p>Pour mieux tester la pagination, ajoutez plus d'utilisateurs de test :</p>";
            echo "<div class='code'>";
            echo "-- Ajouter des utilisateurs de test
INSERT INTO utilisateurs (nom, email, mot_de_passe, role_id) VALUES 
('Test User 1', '<EMAIL>', 'password123', 1),
('Test User 2', '<EMAIL>', 'password123', 2),
('Test User 3', '<EMAIL>', 'password123', 3),
('Test User 4', '<EMAIL>', 'password123', 4),
('Test User 5', '<EMAIL>', 'password123', 1),
('Test User 6', '<EMAIL>', 'password123', 2),
('Test User 7', '<EMAIL>', 'password123', 3),
('Test User 8', '<EMAIL>', 'password123', 4),
('Test User 9', '<EMAIL>', 'password123', 1),
('Test User 10', '<EMAIL>', 'password123', 2),
('Test User 11', '<EMAIL>', 'password123', 3),
('Test User 12', '<EMAIL>', 'password123', 4),
('Test User 13', '<EMAIL>', 'password123', 1),
('Test User 14', '<EMAIL>', 'password123', 2),
('Test User 15', '<EMAIL>', 'password123', 3);";
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Erreur :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    // Fonctionnalités implémentées
    echo "<div class='info'>";
    echo "<h3>🚀 Fonctionnalités de Pagination Implémentées</h3>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>✅ 1. Contrôles de Navigation</h4>";
    echo "<ul>";
    echo "<li><strong>Première page</strong> : Bouton pour aller directement à la page 1</li>";
    echo "<li><strong>Page précédente</strong> : Navigation vers la page précédente</li>";
    echo "<li><strong>Numéros de pages</strong> : Affichage intelligent des numéros (max 5 visibles)</li>";
    echo "<li><strong>Page suivante</strong> : Navigation vers la page suivante</li>";
    echo "<li><strong>Dernière page</strong> : Bouton pour aller directement à la dernière page</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>✅ 2. Informations de Pagination</h4>";
    echo "<ul>";
    echo "<li><strong>Compteur d'éléments</strong> : \"Affichage de X à Y sur Z utilisateurs\"</li>";
    echo "<li><strong>Sélecteur de taille</strong> : 6, 12, 24, ou 48 utilisateurs par page</li>";
    echo "<li><strong>Indicateur de page</strong> : \"Page X sur Y\"</li>";
    echo "<li><strong>Statistiques</strong> : Nombre total d'utilisateurs et par rôle</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>✅ 3. Fonctionnalités Avancées</h4>";
    echo "<ul>";
    echo "<li><strong>Pagination intelligente</strong> : Réinitialisation à la page 1 lors des filtres</li>";
    echo "<li><strong>États des boutons</strong> : Désactivation automatique des boutons non disponibles</li>";
    echo "<li><strong>Navigation au clavier</strong> : Support des touches de navigation</li>";
    echo "<li><strong>Responsive</strong> : Adaptation mobile des contrôles</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test JavaScript
    echo "<div class='code'>";
    echo "// Test JavaScript pour vérifier la pagination dans React

// 1. Vérifier le nombre d'utilisateurs affichés
console.log('Utilisateurs affichés:', document.querySelectorAll('.user-card').length);

// 2. Tester la navigation
const nextButton = document.querySelector('[title=\"Page suivante\"]');
if (nextButton && !nextButton.disabled) {
    console.log('✅ Bouton page suivante disponible');
} else {
    console.log('⚠️ Bouton page suivante désactivé ou non trouvé');
}

// 3. Vérifier les informations de pagination
const pageInfo = document.querySelector('.page-info');
if (pageInfo) {
    console.log('📊 Info pagination:', pageInfo.textContent);
}

// 4. Tester le changement de taille de page
const pageSizeSelect = document.querySelector('select[value]');
if (pageSizeSelect) {
    console.log('📏 Taille de page actuelle:', pageSizeSelect.value);
}";
    echo "</div>";
    
    // Styles CSS utilisés
    echo "<div class='info'>";
    echo "<h3>🎨 Styles CSS de la Pagination</h3>";
    echo "</div>";
    
    echo "<div class='code'>";
    echo "/* Styles principaux de la pagination */

.paginationContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-top: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.paginationControls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.paginationButton {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 40px;
    height: 40px;
}

.paginationButtonActive {
    background-color: var(--cerulean);
    color: white;
    border-color: var(--cerulean);
    font-weight: bold;
}

.paginationButtonDisabled {
    background-color: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
}";
    echo "</div>";
    
    // Instructions d'utilisation
    echo "<div class='warning'>";
    echo "<h3>📋 Instructions d'Utilisation</h3>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🚀 Pour tester la pagination :</h4>";
    echo "<ol>";
    echo "<li><strong>Démarrez votre application React</strong> : <code>npm start</code></li>";
    echo "<li><strong>Naviguez vers la liste des utilisateurs</strong></li>";
    echo "<li><strong>Testez les contrôles de pagination</strong> :</li>";
    echo "<ul>";
    echo "<li>Cliquez sur les numéros de pages</li>";
    echo "<li>Utilisez les boutons de navigation (première, précédente, suivante, dernière)</li>";
    echo "<li>Changez le nombre d'utilisateurs par page</li>";
    echo "<li>Testez avec des filtres de recherche et de rôle</li>";
    echo "</ul>";
    echo "<li><strong>Vérifiez les informations</strong> : compteurs, indicateurs de page</li>";
    echo "</ol>";
    echo "</div>";
    
    // Liens de test
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>🔗 Liens de Test</h3>";
    echo "<a href='userManagement.php' target='_blank' class='btn btn-success'>📊 API Utilisateurs</a>";
    echo "<a href='../../../Frantend/schoolproject/public/index.html' target='_blank' class='btn btn-warning'>🖥️ Interface React</a>";
    echo "<a href='test_user_management.php' target='_blank' class='btn btn-success'>🧪 Test Gestion</a>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 SYSTÈME DE PAGINATION COMPLET !</h2>";
    echo "<p><strong>✅ Navigation complète avec tous les contrôles</strong></p>";
    echo "<p><strong>✅ Informations détaillées de pagination</strong></p>";
    echo "<p><strong>✅ Sélecteur de taille de page</strong></p>";
    echo "<p><strong>✅ Gestion intelligente des états</strong></p>";
    echo "<p><strong>✅ Design moderne et responsive</strong></p>";
    echo "<p><strong>✅ Compatible avec filtres et recherche</strong></p>";
    echo "<p><strong>🚀 Votre liste d'utilisateurs est maintenant parfaitement paginée !</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR GÉNÉRALE</h2>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
