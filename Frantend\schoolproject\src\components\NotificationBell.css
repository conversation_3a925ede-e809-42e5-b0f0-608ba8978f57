/* Container principal */
.notification-bell {
    position: relative;
    display: inline-block;
}

/* Bouton de la cloche */
.bell-button {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bell-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.bell-icon {
    font-size: 20px;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Badge de notification */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Dropdown des notifications */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    max-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    border: 1px solid #e9ecef;
    z-index: 1000;
    overflow: hidden;
    margin-top: 8px;
}

.notification-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

/* En-tête du dropdown */
.dropdown-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.unread-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Liste des notifications */
.notifications-list {
    max-height: 350px;
    overflow-y: auto;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Item de notification */
.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
}

.notification-item.unread:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffe082 100%);
}

/* Contenu de la notification */
.notification-content {
    width: 100%;
}

.notification-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.notification-type-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.notification-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    flex: 1;
    line-height: 1.3;
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-message {
    color: #555;
    font-size: 13px;
    line-height: 1.4;
    margin: 5px 0;
    word-wrap: break-word;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.notification-time {
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
}

.notification-sender {
    font-size: 11px;
    color: #007bff;
    font-style: italic;
}

/* États vides et de chargement */
.no-notifications {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.no-notif-icon {
    font-size: 32px;
    opacity: 0.5;
    display: block;
    margin-bottom: 10px;
}

.no-notifications p {
    margin: 0;
    font-size: 14px;
}

.loading-notifications {
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #6c757d;
}

.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pied du dropdown */
.dropdown-footer {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.view-all-link {
    color: #007bff;
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    transition: color 0.3s ease;
}

.view-all-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 300px;
        right: -50px;
    }
    
    .notification-dropdown::before {
        right: 70px;
    }
    
    .notification-item {
        padding: 12px 15px;
    }
    
    .dropdown-header {
        padding: 12px 15px;
    }
    
    .dropdown-footer {
        padding: 10px 15px;
    }
}

@media (max-width: 480px) {
    .notification-dropdown {
        width: 280px;
        right: -80px;
    }
    
    .notification-dropdown::before {
        right: 100px;
    }
    
    .notification-title {
        font-size: 13px;
    }
    
    .notification-message {
        font-size: 12px;
    }
}

/* Animation d'entrée */
.notification-dropdown {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Effet de survol pour les notifications de message */
.notification-item[data-type="message"]:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.notification-item[data-type="message"].unread:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffe082 100%);
}

/* Styles pour les différents types de notifications */
.notification-item[data-type="system"] {
    border-left: 3px solid #ffc107;
}

.notification-item[data-type="reminder"] {
    border-left: 3px solid #28a745;
}

.notification-item[data-type="message"] {
    border-left: 3px solid #007bff;
}

/* Animation pour les nouvelles notifications */
.notification-item.new-notification {
    animation: newNotification 0.5s ease-out;
}

@keyframes newNotification {
    0% {
        background-color: #d4edda;
        transform: translateX(-10px);
    }
    100% {
        background-color: transparent;
        transform: translateX(0);
    }
}
