/* ============================================================================
   CENTRE DE NOTIFICATIONS
   ============================================================================ */

.notification-center {
    position: relative;
    display: inline-block;
}

/* ============================================================================
   BOUTON DE NOTIFICATION
   ============================================================================ */

.notification-button {
    position: relative;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    color: #4a5568;
}

.notification-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #e53e3e;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(229, 62, 62, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(229, 62, 62, 0);
    }
}

/* ============================================================================
   OVERLAY
   ============================================================================ */

.notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 998;
    background-color: transparent;
}

/* ============================================================================
   PANNEAU DE NOTIFICATIONS
   ============================================================================ */

.notification-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 380px;
    max-height: 500px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 999;
    overflow: hidden;
    animation: slideDown 0.3s ease-out;
    border: 1px solid #e2e8f0;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ============================================================================
   EN-TÊTE DU PANNEAU
   ============================================================================ */

.notification-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.notification-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-mark-all {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-mark-all:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.btn-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.btn-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* ============================================================================
   CONTENU DU PANNEAU
   ============================================================================ */

.notification-content {
    max-height: 400px;
    overflow-y: auto;
}

.notification-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #718096;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notification-error {
    padding: 20px;
    text-align: center;
    color: #e53e3e;
}

.notification-error button {
    background-color: #e53e3e;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

.no-notifications {
    padding: 40px 20px;
    text-align: center;
    color: #718096;
}

/* ============================================================================
   LISTE DES NOTIFICATIONS
   ============================================================================ */

.notification-list {
    padding: 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
    position: relative;
}

.notification-item:hover {
    background-color: #f8fafc;
}

.notification-item.unread {
    background-color: #e6f3ff;
    border-left: 3px solid #667eea;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 20px;
    width: 8px;
    height: 8px;
    background-color: #667eea;
    border-radius: 50%;
}

.notification-icon {
    font-size: 1.2rem;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.notification-body {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    font-size: 0.9rem;
    line-height: 1.3;
}

.notification-message {
    color: #4a5568;
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 6px;
    word-wrap: break-word;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #718096;
}

.notification-time {
    font-weight: 500;
}

.notification-sender {
    font-style: italic;
}

.notification-actions-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: 10px;
}

.btn-mark-read,
.btn-delete {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-mark-read {
    color: #48bb78;
}

.btn-mark-read:hover {
    background-color: rgba(72, 187, 120, 0.1);
    transform: scale(1.1);
}

.btn-delete {
    color: #e53e3e;
}

.btn-delete:hover {
    background-color: rgba(229, 62, 62, 0.1);
    transform: scale(1.1);
}

/* ============================================================================
   PIED DU PANNEAU
   ============================================================================ */

.notification-footer {
    background-color: #f8fafc;
    padding: 10px 20px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.btn-load-more {
    background-color: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-load-more:hover {
    background-color: #5a67d8;
    transform: translateY(-1px);
}

/* ============================================================================
   RESPONSIVE
   ============================================================================ */

@media (max-width: 768px) {
    .notification-panel {
        width: 320px;
        right: -20px;
    }
    
    .notification-item {
        padding: 12px 15px;
    }
    
    .notification-title {
        font-size: 0.85rem;
    }
    
    .notification-message {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .notification-panel {
        width: 280px;
        right: -40px;
    }
    
    .notification-header {
        padding: 12px 15px;
    }
    
    .notification-header h3 {
        font-size: 1rem;
    }
    
    .notification-item {
        padding: 10px 12px;
    }
    
    .notification-actions-item {
        margin-left: 5px;
    }
    
    .btn-mark-read,
    .btn-delete {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }
}

/* ============================================================================
   SCROLLBAR PERSONNALISÉE
   ============================================================================ */

.notification-content::-webkit-scrollbar {
    width: 6px;
}

.notification-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.notification-content::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.notification-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
