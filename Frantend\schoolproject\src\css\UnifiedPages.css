/* Styles Unifiés pour toutes les pages de gestion */

/* Container principal */
.unified-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* En-tête de page */
.unified-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.unified-title {
    margin: 0;
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.unified-title-icon {
    font-size: 2rem;
}

.unified-header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.unified-count {
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Section de filtres */
.unified-filters {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.unified-filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.unified-search-box {
    position: relative;
}

.unified-search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white !important;
  /*  color: #495057 !important;*/
  color: red !important;
caret-color: #495057;
    font-family: inherit;
    line-height: 1.5;
    box-sizing: border-box;
    outline: none;
    cursor: text;
	

}

.unified-search-input::placeholder {
    color: #6c757d !important;
    opacity: 1;
}

.unified-search-input:focus {
    border-color: #007bff !important;
    background: white !important;
    color: #495057 !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.unified-search-input:hover {
    border-color: #adb5bd;
}

.unified-search-input:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.unified-search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 16px;
}

.unified-filter-select {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.unified-filter-select:focus {
    outline: none;
    border-color: #007bff;
    background: white;
}

.unified-clear-btn {
    padding: 12px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.unified-clear-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Contenu principal */
.unified-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Tableau */
.unified-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.unified-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.unified-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.unified-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.unified-table tbody tr {
    transition: all 0.2s ease;
}

.unified-table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Boutons d'action */
.unified-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.unified-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    text-decoration: none;
}

.unified-btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.unified-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.unified-btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.unified-btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.unified-btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.unified-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.unified-btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.unified-btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
}

/* État vide */
.unified-empty {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.unified-empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.unified-empty-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #495057;
}

.unified-empty-text {
    font-size: 1rem;
    margin-bottom: 20px;
}

/* Loading */
.unified-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6c757d;
}

.unified-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Badges */
.unified-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.unified-badge-primary {
    background: #e3f2fd;
    color: #1976d2;
}

.unified-badge-success {
    background: #e8f5e8;
    color: #2e7d32;
}

.unified-badge-warning {
    background: #fff3e0;
    color: #f57c00;
}

.unified-badge-danger {
    background: #ffebee;
    color: #d32f2f;
}

.unified-badge-info {
    background: #e0f2f1;
    color: #00796b;
}

/* Responsive */
@media (max-width: 768px) {
    .unified-container {
        padding: 10px;
    }
    
    .unified-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .unified-filters-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .unified-table {
        font-size: 12px;
    }
    
    .unified-table th,
    .unified-table td {
        padding: 10px 8px;
    }
    
    .unified-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .unified-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .unified-title {
        font-size: 1.4rem;
    }
    
    .unified-table {
        font-size: 11px;
    }
    
    .unified-table th,
    .unified-table td {
        padding: 8px 6px;
    }
}
