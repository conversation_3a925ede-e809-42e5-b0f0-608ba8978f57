<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔍 DIAGNOSTIC ERREUR 500 - EMPLOIS DU TEMPS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .test-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
    </style>";
    
    echo "<div class='error'>";
    echo "<h2>🔍 DIAGNOSTIC COMPLET : Erreur 500 Emplois du Temps</h2>";
    echo "<p>Test de toutes les APIs pour identifier le problème exact</p>";
    echo "</div>";
    
    // 1. Test connexion base de données
    echo "<div class='step'>";
    echo "<h3>🗄️ 1. Test Base de Données</h3>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
        
        // Test des tables principales
        $tables_test = [
            'EmploisDuTemps' => 'Emplois du temps',
            'Classes' => 'Classes',
            'Matieres' => 'Matières', 
            'Enseignants' => 'Enseignants',
            'Utilisateurs' => 'Utilisateurs'
        ];
        
        foreach ($tables_test as $table => $description) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
                $count = $stmt->fetch()['count'];
                echo "<p class='success'>✅ $description ($table) : $count enregistrement(s)</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ $description ($table) : " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur base de données : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Test de toutes les APIs
    echo "<div class='step'>";
    echo "<h3>🔧 2. Test de Toutes les APIs</h3>";
    
    echo "<div class='test-grid'>";
    
    // Test API Emplois du Temps
    echo "<div class='test-card'>";
    echo "<h4>📅 API Emplois du Temps</h4>";
    
    $emplois_url = "http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $emplois_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$emplois_url' target='_blank'>index_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Emplois accessible</p>";
            $data = json_decode($response, true);
            if (is_array($data)) {
                echo "<p class='success'>✅ Format JSON valide</p>";
                echo "<p class='info'>📊 " . count($data) . " emploi(s)</p>";
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ ERREUR $http_code</p>";
            echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Test API Classes
    echo "<div class='test-card'>";
    echo "<h4>🏫 API Classes</h4>";
    
    $classes_url = "http://localhost/Project_PFE/Backend/pages/classes/getClasses_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $classes_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$classes_url' target='_blank'>getClasses_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Classes accessible</p>";
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "<p class='success'>✅ Format JSON valide</p>";
                echo "<p class='info'>🏫 " . count($data['classes']) . " classe(s)</p>";
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ ERREUR $http_code</p>";
            echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Test API Matières
    echo "<div class='test-card'>";
    echo "<h4>📚 API Matières</h4>";
    
    $matieres_url = "http://localhost/Project_PFE/Backend/pages/matieres/getMatieres_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $matieres_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$matieres_url' target='_blank'>getMatieres_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Matières accessible</p>";
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "<p class='success'>✅ Format JSON valide</p>";
                echo "<p class='info'>📚 " . count($data['matieres']) . " matière(s)</p>";
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ ERREUR $http_code</p>";
            echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Test API Enseignants
    echo "<div class='test-card'>";
    echo "<h4>👨‍🏫 API Enseignants</h4>";
    
    $enseignants_url = "http://localhost/Project_PFE/Backend/pages/enseignants/getEnseignants_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $enseignants_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$enseignants_url' target='_blank'>getEnseignants_no_auth.php</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API Enseignants accessible</p>";
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "<p class='success'>✅ Format JSON valide</p>";
                echo "<p class='info'>👨‍🏫 " . count($data['enseignants']) . " enseignant(s)</p>";
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
            }
        } else {
            echo "<p class='error'>❌ ERREUR $http_code</p>";
            echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "</div>"; // fin test-grid
    echo "</div>";
    
    // 3. Test direct de l'API Emplois du Temps avec gestion d'erreurs
    echo "<div class='step'>";
    echo "<h3>🔍 3. Test Direct API Emplois du Temps</h3>";
    
    try {
        // Inclure directement le fichier pour voir l'erreur exacte
        echo "<h4>Test d'inclusion directe :</h4>";
        
        // Capturer la sortie
        ob_start();
        $error_occurred = false;
        
        try {
            // Simuler une requête GET
            $_SERVER['REQUEST_METHOD'] = 'GET';
            include 'index_no_auth.php';
        } catch (Exception $e) {
            $error_occurred = true;
            echo "<p class='error'>❌ Erreur d'inclusion : " . $e->getMessage() . "</p>";
        } catch (Error $e) {
            $error_occurred = true;
            echo "<p class='error'>❌ Erreur fatale : " . $e->getMessage() . "</p>";
        }
        
        $output = ob_get_clean();
        
        if (!$error_occurred) {
            echo "<p class='success'>✅ Inclusion réussie</p>";
            echo "<h5>Sortie de l'API :</h5>";
            echo "<div class='json-block'>" . htmlspecialchars($output) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test direct : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 4. Instructions de correction
    echo "<div class='step'>";
    echo "<h3>🔧 4. Plan de Correction</h3>";
    
    echo "<h4>🎯 Actions à effectuer selon les résultats :</h4>";
    echo "<ul>";
    echo "<li><strong>Si API Emplois = 500 :</strong> Problème dans index_no_auth.php</li>";
    echo "<li><strong>Si API Classes = 500 :</strong> Problème dans getClasses_no_auth.php</li>";
    echo "<li><strong>Si API Matières = 500 :</strong> Problème dans getMatieres_no_auth.php</li>";
    echo "<li><strong>Si API Enseignants = 500 :</strong> Problème dans getEnseignants_no_auth.php</li>";
    echo "<li><strong>Si toutes APIs = 200 :</strong> Problème côté React</li>";
    echo "</ul>";
    
    echo "<h4>🚨 Actions IMMÉDIATES :</h4>";
    echo "<ol>";
    echo "<li><strong>Identifier l'API défaillante</strong> dans les tests ci-dessus</li>";
    echo "<li><strong>Corriger l'API problématique</strong></li>";
    echo "<li><strong>Vider le cache React :</strong> Ctrl+F5</li>";
    echo "<li><strong>Redémarrer React :</strong> npm start</li>";
    echo "<li><strong>Tester l'interface :</strong> <a href='http://localhost:3000/emplois-du-temps' target='_blank'>http://localhost:3000/emplois-du-temps</a></li>";
    echo "</ol>";
    echo "</div>";
    
    // 5. Liens de test
    echo "<div class='step'>";
    echo "<h3>🔗 5. Liens de Test Directs</h3>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$emplois_url' target='_blank' class='btn btn-success'>📅 API Emplois</a>";
    echo "<a href='$classes_url' target='_blank' class='btn btn-success'>🏫 API Classes</a>";
    echo "<a href='$matieres_url' target='_blank' class='btn btn-success'>📚 API Matières</a>";
    echo "<a href='$enseignants_url' target='_blank' class='btn btn-success'>👨‍🏫 API Enseignants</a>";
    echo "<a href='http://localhost:3000/emplois-du-temps' target='_blank' class='btn btn-danger'>⚛️ Interface React</a>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔍 DIAGNOSTIC TERMINÉ</h4>";
    echo "<p><strong>Consultez les résultats ci-dessus pour identifier l'API défaillante.</strong></p>";
    echo "<p><strong>L'API qui retourne une erreur 500 est celle qui cause le problème.</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
