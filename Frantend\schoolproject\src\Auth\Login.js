import React, { useState, useContext } from "react";
import { useNavigate, Link } from "react-router-dom";
import { AuthContext } from "../context/AuthContext";
import { FaGraduationCap, FaUser, FaLock, <PERSON>a<PERSON>ye, FaEyeSlash } from "react-icons/fa";
import "../css/Login.css"; 

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const { login } = useContext(AuthContext);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("http://localhost/SchoolProject/Auth/login.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage("✅ Connexion réussie !");

        // Stocker les informations utilisateur
        const userInfo = {
          token: data.token,
          role: data.role,
          email: email
        };

        // Utiliser le contexte d'authentification
        login(userInfo);

        // Redirection selon le rôle
        setTimeout(() => {
          switch(data.role.toLowerCase()) {
            case 'enseignant':
              navigate('/dashboard/enseignant');
              break;
            case 'etudiant':
            case 'élève':
              navigate('/dashboard/etudiant');
              break;
            case 'parent':
              navigate('/dashboard/parent');
              break;
            case 'responsable':
            case 'admin':
              navigate('/dashboard/responsable');
              break;
            default:
              navigate('/dashboard');
          }
        }, 1500);

      } else {
        setMessage("❌ Email ou mot de passe incorrect");
      }
    } catch (error) {
      setMessage("⚠️ Problème de connexion au serveur");
      console.error("Erreur :", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      
    <div className="container">
      <form onSubmit={handleSubmit}>
        <h2>Connexion</h2>
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          disabled={isLoading}
        />
        <input
          type="password"
          placeholder="Mot de passe"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          {isLoading ? "Connexion en cours..." : "Se connecter"}
        </button>
        <p className="message">{message}</p>
      </form>
    </div>
    </div>
  );
}

export default Login;
