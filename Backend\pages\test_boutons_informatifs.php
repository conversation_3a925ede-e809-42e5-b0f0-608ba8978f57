<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../config/db.php');

try {
    echo "<h1>🧪 TEST - BOUTONS INFORMATIFS ABSENCES & RETARDS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .test-result { background: white; border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .test-result.success { border-color: #28a745; background: #d4edda; }
        .test-result.error { border-color: #dc3545; background: #f8d7da; }
        .test-button { color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; font-weight: bold; }
        .test-button:hover { color: white; text-decoration: none; transform: translateY(-2px); }
        .test-button.absences { background: #dc3545; }
        .test-button.absences:hover { background: #c82333; }
        .test-button.retards { background: #fd7e14; }
        .test-button.retards:hover { background: #e8590c; }
        .checklist { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .checklist h4 { margin-top: 0; color: #333; }
        .checklist ul { margin: 0; padding-left: 20px; }
        .checklist li { margin: 5px 0; }
        .info-demo { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>🎯 Test des Boutons Informatifs</h2>";
    echo "<p>Validation complète des informations affichées par les boutons d'action :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Informations d'ajout</strong> : Champs à remplir clairement indiqués</li>";
    echo "<li>✅ <strong>Tooltips contextuels</strong> : Détails spécifiques à chaque élément</li>";
    echo "<li>✅ <strong>Animations fluides</strong> : Apparition progressive des informations</li>";
    echo "<li>✅ <strong>Accessibilité</strong> : Informations lisibles et contrastées</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test des données disponibles
    echo "<div class='step'>";
    echo "<h3>🗄️ Test des Données pour Validation</h3>";
    
    try {
        // Récupérer quelques exemples d'absences et retards
        $stmt = $pdo->query("
            SELECT a.*, e.nom as etudiant_nom, m.nom as matiere_nom 
            FROM Absences a 
            LEFT JOIN Etudiants et ON a.etudiant_id = et.id 
            LEFT JOIN Utilisateurs e ON et.utilisateur_id = e.id 
            LEFT JOIN Matieres m ON a.matiere_id = m.id 
            LIMIT 3
        ");
        $absences = $stmt->fetchAll();
        
        $stmt = $pdo->query("
            SELECT r.*, e.nom as etudiant_nom, m.nom as matiere_nom 
            FROM Retards r 
            LEFT JOIN Etudiants et ON r.etudiant_id = et.id 
            LEFT JOIN Utilisateurs e ON et.utilisateur_id = e.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id 
            LIMIT 3
        ");
        $retards = $stmt->fetchAll();
        
        echo "<div class='test-result success'>";
        echo "<p class='success'>✅ Données disponibles pour tester les tooltips contextuels :</p>";
        
        if (count($absences) > 0) {
            echo "<h5>📋 Exemples d'Absences :</h5>";
            echo "<ul>";
            foreach ($absences as $absence) {
                $date = date('d/m/Y', strtotime($absence['date_absence']));
                $matiere = $absence['matiere_nom'] ? " en {$absence['matiere_nom']}" : "";
                echo "<li><strong>{$absence['etudiant_nom']}</strong> - {$date}{$matiere}</li>";
            }
            echo "</ul>";
        }
        
        if (count($retards) > 0) {
            echo "<h5>⏰ Exemples de Retards :</h5>";
            echo "<ul>";
            foreach ($retards as $retard) {
                $date = date('d/m/Y', strtotime($retard['date_retard']));
                $duree = $retard['duree_retard'];
                echo "<li><strong>{$retard['etudiant_nom']}</strong> - {$date} ({$duree})</li>";
            }
            echo "</ul>";
        }
        
        if (count($absences) == 0 && count($retards) == 0) {
            echo "<p class='warning'>⚠️ Aucune donnée - Créez des absences/retards pour tester les tooltips contextuels</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-result error'>";
        echo "<p class='error'>❌ Erreur lors de la récupération des données : " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Checklist de test pour les absences
    echo "<div class='step'>";
    echo "<h3>📋 Checklist de Test - ABSENCES</h3>";
    
    echo "<div class='checklist'>";
    echo "<h4>📋 Interface des Absences</h4>";
    
    echo "<h5>➕ Bouton d'Ajout Principal</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal visible : 'Nouvelle Absence'</li>";
    echo "<li>☐ Informations sous le texte : '📅 Date • 👤 Étudiant • 📚 Matière • 💬 Justification'</li>";
    echo "<li>☐ Tooltip détaillé au survol</li>";
    echo "<li>☐ Style dégradé rouge avec ombre</li>";
    echo "</ul>";
    
    echo "<h5>✏️ Boutons de Modification</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal : 'MODIFIER'</li>";
    echo "<li>☐ Information au survol : '✏️ Éditer les détails'</li>";
    echo "<li>☐ Tooltip contextuel avec nom étudiant, date et matière</li>";
    echo "<li>☐ Animation d'apparition fluide (opacity 0 → 1)</li>";
    echo "</ul>";
    
    echo "<h5>🗑️ Boutons de Suppression</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal : 'SUPPRIMER'</li>";
    echo "<li>☐ Information au survol : '🗑️ Suppression définitive'</li>";
    echo "<li>☐ Tooltip d'alerte avec contexte complet</li>";
    echo "<li>☐ Couleur rouge d'avertissement</li>";
    echo "</ul>";
    
    echo "<a href='http://localhost:3000/absences' target='_blank' class='test-button absences'>📋 Tester Absences</a>";
    echo "</div>";
    echo "</div>";
    
    // Checklist de test pour les retards
    echo "<div class='step'>";
    echo "<h3>⏰ Checklist de Test - RETARDS</h3>";
    
    echo "<div class='checklist'>";
    echo "<h4>⏰ Interface des Retards</h4>";
    
    echo "<h5>➕ Bouton d'Ajout Principal</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal visible : 'Nouveau Retard'</li>";
    echo "<li>☐ Informations sous le texte : '📅 Date • ⏱️ Durée • 👤 Étudiant • 💬 Justification'</li>";
    echo "<li>☐ Tooltip détaillé au survol</li>";
    echo "<li>☐ Style dégradé orange avec ombre</li>";
    echo "</ul>";
    
    echo "<h5>✏️ Boutons de Modification</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal : 'MODIFIER'</li>";
    echo "<li>☐ Information au survol : '✏️ Éditer durée/justification'</li>";
    echo "<li>☐ Tooltip contextuel avec nom étudiant, date et durée</li>";
    echo "<li>☐ Animation d'apparition fluide</li>";
    echo "</ul>";
    
    echo "<h5>🗑️ Boutons de Suppression</h5>";
    echo "<ul>";
    echo "<li>☐ Texte principal : 'SUPPRIMER'</li>";
    echo "<li>☐ Information au survol : '🗑️ Suppression définitive'</li>";
    echo "<li>☐ Tooltip d'alerte avec contexte (nom, date, durée)</li>";
    echo "<li>☐ Couleur rouge d'avertissement</li>";
    echo "</ul>";
    
    echo "<a href='http://localhost:3000/retards' target='_blank' class='test-button retards'>⏰ Tester Retards</a>";
    echo "</div>";
    echo "</div>";
    
    // Test des informations contextuelles
    echo "<div class='step'>";
    echo "<h3>🔍 Test des Informations Contextuelles</h3>";
    
    echo "<h4>📋 Exemples de Tooltips Attendus</h4>";
    
    echo "<div class='info-demo'>";
    echo "<h5>📋 Absences - Tooltips de Modification</h5>";
    echo "<p><strong>Format attendu :</strong></p>";
    echo "<code>\"Modifier l'absence de [NOM_ETUDIANT] du [DATE] en [MATIERE]\"</code>";
    echo "<p><strong>Exemples :</strong></p>";
    echo "<ul>";
    echo "<li>\"Modifier l'absence de Jean Dupont du 15/01/2024 en Mathématiques\"</li>";
    echo "<li>\"Modifier l'absence de Marie Martin du 16/01/2024\"</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info-demo'>";
    echo "<h5>⏰ Retards - Tooltips de Modification</h5>";
    echo "<p><strong>Format attendu :</strong></p>";
    echo "<code>\"Modifier le retard de [NOM_ETUDIANT] du [DATE] ([DUREE])\"</code>";
    echo "<p><strong>Exemples :</strong></p>";
    echo "<ul>";
    echo "<li>\"Modifier le retard de Pierre Durand du 15/01/2024 (00:15)\"</li>";
    echo "<li>\"Modifier le retard de Sophie Leroy du 16/01/2024 (00:30)\"</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info-demo'>";
    echo "<h5>🗑️ Tooltips de Suppression</h5>";
    echo "<p><strong>Format attendu :</strong></p>";
    echo "<code>\"Supprimer définitivement [TYPE] de [NOM_ETUDIANT] du [DATE]\"</code>";
    echo "<p><strong>Exemples :</strong></p>";
    echo "<ul>";
    echo "<li>\"Supprimer définitivement l'absence de Jean Dupont du 15/01/2024 en Mathématiques\"</li>";
    echo "<li>\"Supprimer définitivement le retard de Pierre Durand du 15/01/2024 (00:15)\"</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    // Test des animations et styles
    echo "<div class='step'>";
    echo "<h3>🎨 Test des Animations et Styles</h3>";
    
    echo "<h4>✨ Éléments à Vérifier</h4>";
    echo "<table style='width: 100%; border-collapse: collapse; margin: 15px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Élément</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>État Initial</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Au Survol</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Transition</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>Infos bouton d'ajout</strong></td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Toujours visibles</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Aucun changement</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>N/A</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>Infos boutons d'action</strong></td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Opacity: 0 (invisibles)</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Opacity: 1 (visibles)</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>0.3s ease</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>Taille des boutons</strong></td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>min-width: 100px, min-height: 45px</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Aucun changement</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>N/A</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>Disposition</strong></td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>flex-direction: column</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>Aucun changement</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>N/A</td>";
    echo "</tr>";
    echo "</table>";
    
    echo "<h4>🎯 Points de Contrôle</h4>";
    echo "<ol>";
    echo "<li><strong>Lisibilité :</strong> Les informations sont-elles lisibles sur tous les fonds ?</li>";
    echo "<li><strong>Hiérarchie :</strong> Le texte principal est-il plus visible que les infos ?</li>";
    echo "<li><strong>Animation :</strong> L'apparition des infos est-elle fluide ?</li>";
    echo "<li><strong>Responsive :</strong> Les boutons s'adaptent-ils correctement ?</li>";
    echo "<li><strong>Accessibilité :</strong> Les tooltips sont-ils complets et informatifs ?</li>";
    echo "</ol>";
    echo "</div>";
    
    // Procédure de test détaillée
    echo "<div class='step'>";
    echo "<h3>🧪 Procédure de Test Détaillée</h3>";
    
    echo "<h4>📝 Étapes de Test</h4>";
    echo "<ol>";
    echo "<li><strong>Préparation :</strong>";
    echo "<ul>";
    echo "<li>Connectez-vous avec un compte Admin ou Enseignant</li>";
    echo "<li>Assurez-vous d'avoir des données d'absences et retards</li>";
    echo "<li>Ouvrez les outils de développement (F12)</li>";
    echo "</ul>";
    echo "</li>";
    
    echo "<li><strong>Test du bouton d'ajout :</strong>";
    echo "<ul>";
    echo "<li>Vérifiez la présence des informations sous le texte principal</li>";
    echo "<li>Survolez pour voir le tooltip complet</li>";
    echo "<li>Cliquez et vérifiez que les champs correspondent aux infos</li>";
    echo "</ul>";
    echo "</li>";
    
    echo "<li><strong>Test des boutons de modification :</strong>";
    echo "<ul>";
    echo "<li>Survolez un bouton 'Modifier' et observez l'apparition des infos</li>";
    echo "<li>Vérifiez que le tooltip contient le nom, la date et la matière/durée</li>";
    echo "<li>Testez l'animation (transition fluide)</li>";
    echo "</ul>";
    echo "</li>";
    
    echo "<li><strong>Test des boutons de suppression :</strong>";
    echo "<ul>";
    echo "<li>Survolez un bouton 'Supprimer' et observez les infos d'alerte</li>";
    echo "<li>Vérifiez que le tooltip contient le contexte complet</li>";
    echo "<li>Testez la couleur rouge d'avertissement</li>";
    echo "</ul>";
    echo "</li>";
    
    echo "<li><strong>Test responsive :</strong>";
    echo "<ul>";
    echo "<li>Réduisez la taille de la fenêtre</li>";
    echo "<li>Vérifiez l'adaptation des boutons sur mobile</li>";
    echo "<li>Testez la lisibilité des informations</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    
    echo "<h4>🐛 Problèmes Potentiels</h4>";
    echo "<ul>";
    echo "<li><strong>Infos non visibles :</strong> Vérifiez les styles CSS et l'opacity</li>";
    echo "<li><strong>Animation saccadée :</strong> Contrôlez la transition CSS</li>";
    echo "<li><strong>Tooltip incorrect :</strong> Vérifiez les données en base</li>";
    echo "<li><strong>Texte illisible :</strong> Ajustez le contraste des couleurs</li>";
    echo "</ul>";
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>";
    echo "<h3>🎉 RÉSUMÉ DU TEST DES BOUTONS INFORMATIFS</h3>";
    echo "<p class='success' style='font-size: 18px;'>✅ Guide de test complet pour valider les informations des boutons !</p>";
    
    echo "<h4>🎯 Actions de Test</h4>";
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='http://localhost:3000/absences' target='_blank' class='test-button absences'>📋 Tester Absences</a>";
    echo "<a href='http://localhost:3000/retards' target='_blank' class='test-button retards'>⏰ Tester Retards</a>";
    echo "</div>";
    
    echo "<h4>📋 Validation Complète</h4>";
    echo "<p>Après avoir testé les deux interfaces, vous devriez avoir validé :</p>";
    echo "<ul>";
    echo "<li>✅ <strong>Informations d'ajout</strong> : Champs clairement indiqués</li>";
    echo "<li>✅ <strong>Tooltips contextuels</strong> : Détails spécifiques à chaque élément</li>";
    echo "<li>✅ <strong>Animations fluides</strong> : Apparition progressive des informations</li>";
    echo "<li>✅ <strong>Hiérarchie visuelle</strong> : Texte principal et secondaire bien différenciés</li>";
    echo "<li>✅ <strong>Accessibilité</strong> : Informations lisibles et contrastées</li>";
    echo "<li>✅ <strong>Responsive design</strong> : Adaptation correcte sur tous les écrans</li>";
    echo "</ul>";
    
    echo "<p class='info'><strong>🚀 Vos boutons d'action sont maintenant informatifs, contextuels et parfaitement accessibles !</strong></p>";
    
    echo "<h4>🔗 Liens Utiles</h4>";
    echo "<ul>";
    echo "<li><a href='demo_boutons_informatifs.php'>📋 Démonstration des boutons informatifs</a></li>";
    echo "<li><a href='http://localhost:3000/absences' target='_blank'>📋 Interface Absences</a></li>";
    echo "<li><a href='http://localhost:3000/retards' target='_blank'>⏰ Interface Retards</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
