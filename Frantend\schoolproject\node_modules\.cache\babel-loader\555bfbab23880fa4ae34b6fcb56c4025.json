{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\components\\\\NavigationWrapper.js\";\nimport React, { useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport NavbarStudent from './NavbarStudent';\nimport NavbarTeacher from './NavbarTeacher';\nimport NavbarTop from './NavbarTop';\nconst NavigationWrapper = ({\n  children\n}) => {\n  const {\n    user,\n    isAuthenticated\n  } = useContext(AuthContext);\n\n  // Si l'utilisateur n'est pas connecté, pas de navigation\n  if (!isAuthenticated) {\n    return children;\n  }\n\n  // Déterminer quelle navbar utiliser selon le rôle\n  const renderNavbar = () => {\n    var _user$role;\n    const userRole = user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.toLowerCase();\n\n    // Pour les étudiants, utiliser la navbar spécialisée\n    if (userRole === 'etudiant' || userRole === 'élève') {\n      return /*#__PURE__*/React.createElement(NavbarStudent, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 14\n        }\n      });\n    }\n\n    // Pour les enseignants, utiliser la navbar spécialisée\n    if (userRole === 'enseignant') {\n      return /*#__PURE__*/React.createElement(NavbarTeacher, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 14\n        }\n      });\n    }\n\n    // Pour tous les autres rôles (admin, responsable, parent), utiliser la navbar standard\n    return /*#__PURE__*/React.createElement(Navbar, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 12\n      }\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 5\n    }\n  }, renderNavbar(), /*#__PURE__*/React.createElement(NavbarTop, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginLeft: '110px',\n      marginTop: '60px',\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }\n  }, children));\n};\nexport default NavigationWrapper;", "map": {"version": 3, "names": ["React", "useContext", "AuthContext", "<PERSON><PERSON><PERSON>", "NavbarStudent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NavbarTop", "NavigationWrapper", "children", "user", "isAuthenticated", "renderNavbar", "_user$role", "userRole", "role", "toLowerCase", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "marginTop", "padding"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/components/NavigationWrapper.js"], "sourcesContent": ["import React, { useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport NavbarStudent from './NavbarStudent';\nimport NavbarTeacher from './NavbarTeacher';\nimport NavbarTop from './NavbarTop';\n\nconst NavigationWrapper = ({ children }) => {\n  const { user, isAuthenticated } = useContext(AuthContext);\n\n  // Si l'utilisateur n'est pas connecté, pas de navigation\n  if (!isAuthenticated) {\n    return children;\n  }\n\n  // Déterminer quelle navbar utiliser selon le rôle\n  const renderNavbar = () => {\n    const userRole = user?.role?.toLowerCase();\n\n    // Pour les étudiants, utiliser la navbar spécialisée\n    if (userRole === 'etudiant' || userRole === 'élève') {\n      return <NavbarStudent />;\n    }\n\n    // Pour les enseignants, utiliser la navbar spécialisée\n    if (userRole === 'enseignant') {\n      return <NavbarTeacher />;\n    }\n\n    // Pour tous les autres rôles (admin, responsable, parent), utiliser la navbar standard\n    return <Navbar />;\n  };\n\n  return (\n    <div>\n      {renderNavbar()}\n      <NavbarTop />\n      <div style={{\n        marginLeft: '110px',\n        marginTop: '60px',\n        padding: '20px'\n      }}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default NavigationWrapper;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,SAAS,MAAM,aAAa;AAEnC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC1C,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,UAAU,CAACC,WAAW,CAAC;;EAEzD;EACA,IAAI,CAACQ,eAAe,EAAE;IACpB,OAAOF,QAAQ;EACjB;;EAEA;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,UAAA;IACzB,MAAMC,QAAQ,GAAGJ,IAAI,aAAJA,IAAI,wBAAAG,UAAA,GAAJH,IAAI,CAAEK,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYG,WAAW,CAAC,CAAC;;IAE1C;IACA,IAAIF,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACnD,oBAAOb,KAAA,CAAAgB,aAAA,CAACZ,aAAa;QAAAa,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC1B;;IAEA;IACA,IAAIT,QAAQ,KAAK,YAAY,EAAE;MAC7B,oBAAOb,KAAA,CAAAgB,aAAA,CAACX,aAAa;QAAAY,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAC1B;;IAEA;IACA,oBAAOtB,KAAA,CAAAgB,aAAA,CAACb,MAAM;MAAAc,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;EACnB,CAAC;EAED,oBACEtB,KAAA,CAAAgB,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGX,YAAY,CAAC,CAAC,eACfX,KAAA,CAAAgB,aAAA,CAACV,SAAS;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACbtB,KAAA,CAAAgB,aAAA;IAAKO,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE;IACX,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCd,QACE,CACF,CAAC;AAEV,CAAC;AAED,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}