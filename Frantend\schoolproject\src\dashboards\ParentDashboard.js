import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { FaUsers, FaChartLine, FaCalendarAlt, FaComments, FaExclamationTriangle, FaBell } from 'react-icons/fa';

const ParentDashboard = () => {
  const { user } = useContext(AuthContext);
  const [enfants, setEnfants] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [rendezVous, setRendezVous] = useState([]);
  const [selectedEnfant, setSelectedEnfant] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simuler des données pour le moment
      const enfantsData = [
        {
          id: 1,
          nom: '<PERSON>',
          classe: '3ème A',
          moyenneGenerale: 15.2,
          absences: 1,
          retards: 0,
          prochainCours: 'Mathématiques - 08:00',
          dernieresNotes: [
            { matiere: 'Mathématiques', note: 16, date: '2024-01-10' },
            { matiere: 'Français', note: 14, date: '2024-01-08' },
            { matiere: 'Histoire', note: 17, date: '2024-01-05' }
          ]
        },
        {
          id: 2,
          nom: 'Pierre Dupont',
          classe: '1ère S',
          moyenneGenerale: 13.8,
          absences: 3,
          retards: 2,
          prochainCours: 'Physique - 10:00',
          dernieresNotes: [
            { matiere: 'Physique', note: 15, date: '2024-01-09' },
            { matiere: 'Chimie', note: 12, date: '2024-01-07' },
            { matiere: 'Mathématiques', note: 14, date: '2024-01-04' }
          ]
        }
      ];

      setEnfants(enfantsData);
      setSelectedEnfant(enfantsData[0]);

      setNotifications([
        { id: 1, type: 'note', message: 'Nouvelle note en Mathématiques pour Marie', date: '2024-01-10', enfant: 'Marie' },
        { id: 2, type: 'absence', message: 'Absence signalée pour Pierre en Physique', date: '2024-01-09', enfant: 'Pierre' },
        { id: 3, type: 'info', message: 'Réunion parents-professeurs le 15 janvier', date: '2024-01-08', enfant: 'Général' }
      ]);

      setRendezVous([
        { id: 1, enseignant: 'M. Dupont - Mathématiques', date: '2024-01-15', heure: '14:00', enfant: 'Marie' },
        { id: 2, enseignant: 'Mme Martin - Français', date: '2024-01-18', heure: '16:30', enfant: 'Pierre' }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  };

  const getNotificationIcon = (type) => {
    switch(type) {
      case 'note': return '📝';
      case 'absence': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📢';
    }
  };

  const styles = {
    container: {
      padding: '20px',
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
    },
    header: {
      marginBottom: '30px',
    },
    welcomeText: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    subtitle: {
      color: '#666',
      fontSize: '1.1rem',
    },
    enfantSelector: {
      marginBottom: '30px',
    },
    selectorLabel: {
      fontSize: '1.1rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    select: {
      padding: '10px',
      fontSize: '1rem',
      borderRadius: '5px',
      border: '1px solid #ddd',
      backgroundColor: 'white',
      minWidth: '200px',
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    statCard: {
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      textAlign: 'center',
      transition: 'transform 0.3s ease',
    },
    statIcon: {
      fontSize: '2rem',
      marginBottom: '10px',
    },
    statNumber: {
      fontSize: '1.8rem',
      fontWeight: 'bold',
      marginBottom: '5px',
    },
    statLabel: {
      color: '#666',
      fontSize: '0.9rem',
    },
    contentGrid: {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '30px',
    },
    card: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    },
    cardTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
    },
    cardIcon: {
      marginRight: '10px',
      color: '#007bff',
    },
    gradeItem: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '12px',
      borderBottom: '1px solid #eee',
    },
    gradeSubject: {
      fontWeight: 'bold',
      color: '#333',
    },
    gradeValue: {
      fontSize: '1.2rem',
      fontWeight: 'bold',
      color: '#007bff',
    },
    notificationItem: {
      padding: '15px',
      borderLeft: '4px solid #007bff',
      backgroundColor: '#f8f9ff',
      marginBottom: '15px',
      borderRadius: '5px',
    },
    notificationHeader: {
      display: 'flex',
      alignItems: 'center',
      marginBottom: '8px',
    },
    notificationIcon: {
      marginRight: '10px',
      fontSize: '1.2rem',
    },
    notificationMessage: {
      fontWeight: 'bold',
      color: '#333',
    },
    notificationDetails: {
      color: '#666',
      fontSize: '0.9rem',
    },
    appointmentItem: {
      padding: '15px',
      border: '1px solid #eee',
      borderRadius: '8px',
      marginBottom: '15px',
    },
    appointmentHeader: {
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '8px',
    },
    appointmentDetails: {
      color: '#666',
      fontSize: '0.9rem',
    },
    fullWidthCard: {
      gridColumn: '1 / -1',
    },
  };

  if (!selectedEnfant) {
    return <div>Chargement...</div>;
  }

  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <h1 style={styles.welcomeText}>
          <FaUsers style={{ marginRight: '15px', color: '#007bff' }} />
          Bienvenue, {user?.email || 'Parent'}
        </h1>
        <p style={styles.subtitle}>Tableau de bord parent - Suivez la scolarité de vos enfants</p>
      </div>

      {/* Sélecteur d'enfant */}
      <div style={styles.enfantSelector}>
        <div style={styles.selectorLabel}>Sélectionner un enfant :</div>
        <select 
          style={styles.select}
          value={selectedEnfant.id}
          onChange={(e) => {
            const enfant = enfants.find(e => e.id === parseInt(e.target.value));
            setSelectedEnfant(enfant);
          }}
        >
          {enfants.map(enfant => (
            <option key={enfant.id} value={enfant.id}>{enfant.nom} - {enfant.classe}</option>
          ))}
        </select>
      </div>

      {/* Statistiques de l'enfant sélectionné */}
      <div style={styles.statsGrid}>
        <div style={styles.statCard}>
          <div style={{...styles.statIcon, color: '#28a745'}}>
            <FaChartLine />
          </div>
          <div style={{...styles.statNumber, color: '#28a745'}}>{selectedEnfant.moyenneGenerale}/20</div>
          <div style={styles.statLabel}>Moyenne Générale</div>
        </div>
        <div style={styles.statCard}>
          <div style={{...styles.statIcon, color: '#dc3545'}}>
            <FaExclamationTriangle />
          </div>
          <div style={{...styles.statNumber, color: '#dc3545'}}>{selectedEnfant.absences}</div>
          <div style={styles.statLabel}>Absences</div>
        </div>
        <div style={styles.statCard}>
          <div style={{...styles.statIcon, color: '#ffc107'}}>
            <FaCalendarAlt />
          </div>
          <div style={{...styles.statNumber, color: '#ffc107'}}>{selectedEnfant.retards}</div>
          <div style={styles.statLabel}>Retards</div>
        </div>
        <div style={styles.statCard}>
          <div style={{...styles.statIcon, color: '#007bff'}}>
            <FaCalendarAlt />
          </div>
          <div style={{...styles.statNumber, color: '#007bff', fontSize: '1rem'}}>{selectedEnfant.prochainCours}</div>
          <div style={styles.statLabel}>Prochain Cours</div>
        </div>
      </div>

      {/* Contenu principal */}
      <div style={styles.contentGrid}>
        {/* Notes récentes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaChartLine style={styles.cardIcon} />
            Notes Récentes - {selectedEnfant.nom}
          </h2>
          {selectedEnfant.dernieresNotes.map((note, index) => (
            <div key={index} style={styles.gradeItem}>
              <div>
                <span style={styles.gradeSubject}>{note.matiere}</span>
                <div style={{ color: '#666', fontSize: '0.9rem' }}>
                  Date: {note.date}
                </div>
              </div>
              <span style={styles.gradeValue}>{note.note}/20</span>
            </div>
          ))}
        </div>

        {/* Notifications */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaBell style={styles.cardIcon} />
            Notifications
          </h2>
          {notifications.map(notification => (
            <div key={notification.id} style={styles.notificationItem}>
              <div style={styles.notificationHeader}>
                <span style={styles.notificationIcon}>
                  {getNotificationIcon(notification.type)}
                </span>
                <span style={styles.notificationMessage}>{notification.message}</span>
              </div>
              <div style={styles.notificationDetails}>
                {notification.enfant} | {notification.date}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Rendez-vous */}
      <div style={{...styles.card, ...styles.fullWidthCard}}>
        <h2 style={styles.cardTitle}>
          <FaComments style={styles.cardIcon} />
          Rendez-vous Programmés
        </h2>
        {rendezVous.map(rdv => (
          <div key={rdv.id} style={styles.appointmentItem}>
            <div style={styles.appointmentHeader}>{rdv.enseignant}</div>
            <div style={styles.appointmentDetails}>
              Date: {rdv.date} à {rdv.heure} | Enfant: {rdv.enfant}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ParentDashboard;
