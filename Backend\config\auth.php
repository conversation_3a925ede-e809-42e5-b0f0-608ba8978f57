<?php
/**
 * Configuration et fonctions d'authentification
 * Gestion des tokens JWT et vérification des utilisateurs
 */

// Clé secrète pour signer les tokens JWT (à changer en production)
define('JWT_SECRET_KEY', 'votre_cle_secrete_super_forte_2024_school_management');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRATION_TIME', 3600 * 24); // 24 heures

/**
 * Génère un token JWT pour un utilisateur
 */
function generateToken($user_data) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    
    $payload = json_encode([
        'user_id' => $user_data['id'],
        'email' => $user_data['email'],
        'role' => $user_data['role'],
        'nom' => $user_data['nom'],
        'prenom' => $user_data['prenom'],
        'iat' => time(),
        'exp' => time() + JWT_EXPIRATION_TIME
    ]);
    
    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET_KEY, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

/**
 * Vérifie et décode un token JWT
 */
function verifyToken($token) {
    try {
        if (empty($token)) {
            return false;
        }
        
        $tokenParts = explode('.', $token);
        if (count($tokenParts) !== 3) {
            return false;
        }
        
        $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[0]));
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[1]));
        $signatureProvided = $tokenParts[2];
        
        // Vérifier la signature
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET_KEY, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        if (!hash_equals($base64Signature, $signatureProvided)) {
            return false;
        }
        
        $payloadData = json_decode($payload, true);
        
        // Vérifier l'expiration
        if (isset($payloadData['exp']) && $payloadData['exp'] < time()) {
            return false;
        }
        
        return $payloadData;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Vérifie si l'utilisateur a le rôle requis
 */
function hasRole($user_info, $required_roles) {
    if (!$user_info || !isset($user_info['role'])) {
        return false;
    }
    
    if (is_string($required_roles)) {
        $required_roles = [$required_roles];
    }
    
    return in_array($user_info['role'], $required_roles);
}

/**
 * Authentifie un utilisateur avec email/mot de passe
 */
function authenticateUser($pdo, $email, $password) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.id, u.nom, u.prenom, u.email, u.mot_de_passe, r.nom as role
            FROM Utilisateurs u
            JOIN Roles r ON u.role_id = r.id
            WHERE u.email = ? AND u.actif = TRUE
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return false;
        }
        
        // Vérifier le mot de passe
        if (!password_verify($password, $user['mot_de_passe'])) {
            return false;
        }
        
        // Retourner les données utilisateur (sans le mot de passe)
        unset($user['mot_de_passe']);
        return $user;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Récupère les informations d'un utilisateur par son ID
 */
function getUserById($pdo, $user_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.id, u.nom, u.prenom, u.email, r.nom as role
            FROM Utilisateurs u
            JOIN Roles r ON u.role_id = r.id
            WHERE u.id = ? AND u.actif = TRUE
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Middleware d'authentification pour les APIs
 */
function requireAuth($required_roles = []) {
    $headers = getallheaders();
    $token = null;

    // Récupérer le token depuis l'en-tête Authorization
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    } elseif (isset($headers['authorization'])) {
        $token = str_replace('Bearer ', '', $headers['authorization']);
    }

    if (!$token) {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification manquant']);
        exit();
    }

    $user_info = verifyToken($token);
    if (!$user_info) {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification invalide']);
        exit();
    }

    // Vérifier les rôles si spécifiés
    if (!empty($required_roles) && !hasRole($user_info, $required_roles)) {
        http_response_code(403);
        echo json_encode([
            'error' => 'Accès non autorisé',
            'user_role' => $user_info['role'],
            'required_roles' => $required_roles
        ]);
        exit();
    }

    return $user_info;
}

/**
 * Génère un mot de passe sécurisé
 */
function generateSecurePassword($length = 12) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[random_int(0, strlen($characters) - 1)];
    }
    
    return $password;
}

/**
 * Hash un mot de passe de manière sécurisée
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Vérifie un mot de passe contre son hash
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Nettoie et valide un email
 */
function validateEmail($email) {
    $email = filter_var($email, FILTER_SANITIZE_EMAIL);
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Génère un token de réinitialisation de mot de passe
 */
function generateResetToken() {
    return bin2hex(random_bytes(32));
}

/**
 * Vérifie si un token de réinitialisation est valide
 */
function verifyResetToken($pdo, $token) {
    try {
        $stmt = $pdo->prepare("
            SELECT user_id, expires_at 
            FROM password_resets 
            WHERE token = ? AND used = FALSE
        ");
        $stmt->execute([$token]);
        $reset = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$reset) {
            return false;
        }
        
        // Vérifier l'expiration
        if (strtotime($reset['expires_at']) < time()) {
            return false;
        }
        
        return $reset['user_id'];
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Log les tentatives de connexion
 */
function logLoginAttempt($pdo, $email, $success, $ip_address = null) {
    try {
        if (!$ip_address) {
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO login_attempts (email, success, ip_address, attempted_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$email, $success ? 1 : 0, $ip_address]);
        
    } catch (Exception $e) {
        // Log silencieusement, ne pas interrompre le processus
        error_log("Erreur log tentative connexion: " . $e->getMessage());
    }
}

/**
 * Vérifie si une IP est bloquée (protection brute force)
 */
function isIpBlocked($pdo, $ip_address, $max_attempts = 5, $block_duration = 900) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE ip_address = ? 
            AND success = FALSE 
            AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$ip_address, $block_duration]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['attempts'] >= $max_attempts;
        
    } catch (Exception $e) {
        return false; // En cas d'erreur, ne pas bloquer
    }
}

/**
 * Nettoie les anciennes tentatives de connexion
 */
function cleanupLoginAttempts($pdo, $older_than_days = 30) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM login_attempts 
            WHERE attempted_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$older_than_days]);
        
    } catch (Exception $e) {
        error_log("Erreur nettoyage tentatives connexion: " . $e->getMessage());
    }
}

// Configuration des sessions (si utilisées)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
}

// Headers de sécurité
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
}

// Appeler automatiquement les headers de sécurité
setSecurityHeaders();

?>
