import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const FacturesReadOnly = () => {
    const { user } = useContext(AuthContext);
    const [factures, setFactures] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');

    useEffect(() => {
        fetchFactures();
    }, []);

    const fetchFactures = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/factures/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setFactures(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des factures:', error);
            Swal.fire('Erreur', 'Impossible de charger les factures', 'error');
        } finally {
            setLoading(false);
        }
    };

    const formatMontant = (montant) => {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'MAD'
        }).format(montant);
    };

    const getStatutBadge = (statut) => {
        const badgeClass = statut === 'Payé' ? 'badge-success' : 'badge-danger';
        return <span className={`badge ${badgeClass}`}>{statut}</span>;
    };

    // Filtrage des données
    const filteredFactures = factures.filter(facture => {
        const matchesSearch = facture.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             facture.etudiant_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             facture.mois?.includes(searchTerm);
        
        const matchesStatus = statusFilter === 'all' || facture.statut === statusFilter;
        
        return matchesSearch && matchesStatus;
    });

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des factures...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>📋 Consultation des Factures</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredFactures.length} facture(s) trouvée(s)
                    </span>
                </div>
            </div>

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par nom, email ou mois..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="status-filter">
                    <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Tous les statuts</option>
                        <option value="Payé">Payé</option>
                        <option value="Non payé">Non payé</option>
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredFactures.length === 0 ? (
                    <div className="no-data">
                        <img src="/finance.png" alt="Aucune facture" />
                        <p>Aucune facture trouvée</p>
                        {searchTerm && (
                            <button 
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📅 Mois</th>
                                    <th>💰 Montant</th>
                                    <th>📊 Statut</th>
                                    <th>💳 Date de paiement</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredFactures.map((facture) => (
                                    <tr key={facture.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{facture.etudiant_nom}</strong>
                                                <small>{facture.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{ 
                                                padding: '4px 8px', 
                                                backgroundColor: '#e3f2fd', 
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {facture.mois}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50', fontSize: '1.1em' }}>
                                                {formatMontant(facture.montant)}
                                            </strong>
                                        </td>
                                        <td>{getStatutBadge(facture.statut)}</td>
                                        <td>
                                            {facture.date_paiement ? (
                                                <span style={{ color: '#28a745' }}>
                                                    ✅ {new Date(facture.date_paiement).toLocaleDateString('fr-FR')}
                                                </span>
                                            ) : (
                                                <span style={{ color: '#6c757d' }}>
                                                    ⏳ En attente
                                                </span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Statistiques */}
            {filteredFactures.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredFactures.filter(f => f.statut === 'Payé').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures payées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#dc3545', margin: '0' }}>
                            {filteredFactures.filter(f => f.statut === 'Non payé').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures impayées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {formatMontant(
                                filteredFactures
                                    .filter(f => f.statut === 'Payé')
                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)
                            )}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total encaissé</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {formatMontant(
                                filteredFactures
                                    .filter(f => f.statut === 'Non payé')
                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)
                            )}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>En attente</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FacturesReadOnly;
