import React, { useState, useContext } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  FaBars, 
  FaTimes, 
  FaBook, 
  FaGraduationCap, 
  FaLayerGroup, 
  FaChalkboardTeacher, 
  FaUserFriends, 
  FaClipboardList, 
  FaTasks, 
  FaChartBar, 
  FaCertificate, 
  FaCalendarTimes, 
  FaClock,
  FaCalendarAlt,
  FaQuestionCircle,
  FaSignInAlt,
  FaHome
} from 'react-icons/fa';
import { AuthContext } from '../context/AuthContext';

const NavbarStudent = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const location = useLocation();
  const { user } = useContext(AuthContext);

  const isActive = (path) => {
    return location.pathname === path;
  };

  // Menu items spécifiques aux étudiants - accès en lecture seule uniquement
  const studentMenuItems = [
    { path: '/dashboard/etudiant', label: 'Tableau de Bord', icon: <FaHome /> },
    { path: '/matieres', label: 'Matières', icon: <FaBook /> },
    { path: '/filieres', label: 'Filières', icon: <FaGraduationCap /> },
    { path: '/classes', label: 'Classes', icon: <FaChalkboardTeacher /> },
    { path: '/groupes', label: 'Groupes', icon: <FaUserFriends /> },
    { path: '/niveaux', label: 'Niveaux', icon: <FaLayerGroup /> },
    { path: '/emplois-du-temps', label: 'Emplois du Temps', icon: <FaCalendarAlt /> },
    { path: '/cours', label: 'Cours', icon: <FaClipboardList /> },
    { path: '/devoirs', label: 'Devoirs', icon: <FaTasks /> },
    { path: '/reponses-quiz', label: 'Reponses Quiz', icon: <FaQuestionCircle /> },
    { path: '/notes', label: 'Notes', icon: <FaChartBar /> },
    { path: '/diplomes', label: 'Diplômes', icon: <FaCertificate /> },
    { path: '/retards', label: 'Retards', icon: <FaClock /> },
    { path: '/absences', label: 'Absences', icon: <FaCalendarTimes /> },
    { path: '/login', label: 'Connexion', icon: <FaSignInAlt /> }
    
  ];

  const navStyles = {
    nav: {
      backgroundColor: 'var(--cerulean)',
      padding: '20px 10px',
      width: isExpanded ? '250px' : '70px',
      height: '100vh',
      position: 'fixed',
      top: 0,
      left: 0,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      transition: 'width 0.3s ease-in-out',
      boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
      zIndex: 1000,
      overflow: 'hidden'
    },
    toggleButton: {
      background: 'none',
      border: 'none',
      color: 'var(--antiflash-white)',
      fontSize: '1.5rem',
      cursor: 'pointer',
      marginBottom: '30px',
      padding: '10px',
      borderRadius: '8px',
      transition: 'all 0.3s ease',
      alignSelf: isExpanded ? 'flex-end' : 'center'
    },
    menuItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '15px',
      padding: '15px 20px',
      color: 'var(--antiflash-white)',
      textDecoration: 'none',
      fontSize: '16px',
      fontWeight: '500',
      width: '100%',
      borderRadius: '12px',
      margin: '5px 0',
      transition: 'all 0.3s ease',
      position: 'relative',
      overflow: 'hidden',
      whiteSpace: 'nowrap'
    },
    menuIcon: {
      fontSize: '1.2rem',
      minWidth: '20px',
      textAlign: 'center'
    },
    menuText: {
      opacity: isExpanded ? 1 : 0,
      transition: 'opacity 0.3s ease',
      fontSize: '14px'
    },
    studentBadge: {
      position: 'absolute',
      top: '10px',
      right: '10px',
      backgroundColor: 'rgba(255,255,255,0.2)',
      color: 'white',
      padding: '4px 8px',
      borderRadius: '12px',
      fontSize: '0.7rem',
      fontWeight: 'bold',
      opacity: isExpanded ? 1 : 0,
      transition: 'opacity 0.3s ease'
    },
    readOnlyIndicator: {
      fontSize: '0.7rem',
      opacity: 0.7,
      marginLeft: 'auto',
      opacity: isExpanded ? 0.7 : 0,
      transition: 'opacity 0.3s ease'
    }
  };

  return (
    <nav style={navStyles.nav}>
     

      {/* Bouton toggle */}
      <button
        style={navStyles.toggleButton}
        onClick={() => setIsExpanded(!isExpanded)}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = 'transparent';
          e.target.style.transform = 'scale(1)';
        }}
      >
        {isExpanded ? <FaTimes /> : <FaBars />}
      </button>

      {/* Menu items pour étudiants */}
      {studentMenuItems.map((item, index) => (
        <Link
          key={item.path}
          to={item.path}
          style={{
            ...navStyles.menuItem,
            backgroundColor: isActive(item.path) ? 'rgba(255,255,255,0.2)' : 'transparent',
            borderLeft: isActive(item.path) ? '4px solid rgb(240, 247, 252)' : '4px solid transparent',
            animationDelay: `${index * 0.1}s`
          }}
          onMouseEnter={(e) => {
            if (!isActive(item.path)) {
              e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
            }
            e.target.style.transform = 'translateX(5px)';
            e.target.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
          }}
          onMouseLeave={(e) => {
            if (!isActive(item.path)) {
              e.target.style.backgroundColor = 'transparent';
            }
            e.target.style.transform = 'translateX(0)';
            e.target.style.boxShadow = 'none';
          }}
        >
          <span style={navStyles.menuIcon}>
            {item.icon}
          </span>
          <span style={navStyles.menuText}>
            {item.label}
          </span>
       
        </Link>
      ))}

      {/* Information en bas */}
      {isExpanded && (
        <div style={{
          marginTop: 'auto',
          padding: '15px',
          color: 'rgba(255,255,255,0.7)',
          fontSize: '0.8rem',
          textAlign: 'center',
          borderTop: '1px solid rgba(255,255,255,0.1)'
        }}>
          <div style={{ marginBottom: '5px' }}>
            🔒 Accès lecture seule
          </div>
          <div style={{ fontSize: '0.7rem', opacity: 0.8 }}>
            Vos données personnelles uniquement
          </div>
        </div>
      )}
    </nav>
  );
};

export default NavbarStudent;
