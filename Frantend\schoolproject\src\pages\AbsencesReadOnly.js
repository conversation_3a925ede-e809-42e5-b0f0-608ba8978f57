import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const AbsencesReadOnly = () => {
    const { user } = useContext(AuthContext);
    const [absences, setAbsences] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [justificationFilter, setJustificationFilter] = useState('all');
    const [dateFilter, setDateFilter] = useState('');

    useEffect(() => {
        fetchAbsences();
    }, []);

    const fetchAbsences = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/absences/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setAbsences(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des absences:', error);
            Swal.fire('Erreur', 'Impossible de charger les absences', 'error');
        } finally {
            setLoading(false);
        }
    };

    // Filtrage des données
    const filteredAbsences = absences.filter(absence => {
        const matchesSearch = absence.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             absence.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             absence.enseignant_nom?.toLowerCase().includes(searchTerm.toLowerCase());
        
        const hasJustification = absence.justification && absence.justification.trim() !== '';
        const matchesJustification = justificationFilter === 'all' || 
                                   (justificationFilter === 'justified' && hasJustification) ||
                                   (justificationFilter === 'not_justified' && !hasJustification);
        
        const matchesDate = !dateFilter || absence.date_absence.includes(dateFilter);
        
        return matchesSearch && matchesJustification && matchesDate;
    });

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des absences...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>❌ Consultation des Absences</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredAbsences.length} absence(s) trouvée(s)
                    </span>
                </div>
            </div>

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box">
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par étudiant, matière ou enseignant..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="justification-filter">
                    <select
                        value={justificationFilter}
                        onChange={(e) => setJustificationFilter(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    >
                        <option value="all">Toutes les absences</option>
                        <option value="justified">Justifiées</option>
                        <option value="not_justified">Non justifiées</option>
                    </select>
                </div>
                <div className="date-filter">
                    <input
                        type="month"
                        value={dateFilter}
                        onChange={(e) => setDateFilter(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
            </div>

            <div className="factures-grid">
                {filteredAbsences.length === 0 ? (
                    <div className="no-data">
                        <img src="/attendance.png" alt="Aucune absence" />
                        <p>Aucune absence trouvée</p>
                        {(searchTerm || justificationFilter !== 'all' || dateFilter) && (
                            <button 
                                onClick={() => {
                                    setSearchTerm('');
                                    setJustificationFilter('all');
                                    setDateFilter('');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer tous les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📚 Matière</th>
                                    <th>👨‍🏫 Enseignant</th>
                                    <th>📅 Date</th>
                                    <th>📝 Justification</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredAbsences.map((absence) => (
                                    <tr key={absence.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{absence.etudiant_nom}</strong>
                                                <small>{absence.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {absence.matiere_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ color: '#6c757d' }}>
                                                {absence.enseignant_nom || 'Non spécifié'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: '500'
                                            }}>
                                                {new Date(absence.date_absence).toLocaleDateString('fr-FR')}
                                            </span>
                                        </td>
                                        <td>
                                            {absence.justification && absence.justification.trim() !== '' ? (
                                                <div>
                                                    <span className="badge badge-success">✅ Justifiée</span>
                                                    <div style={{
                                                        marginTop: '5px',
                                                        fontSize: '0.9em',
                                                        color: '#6c757d',
                                                        fontStyle: 'italic',
                                                        maxWidth: '200px'
                                                    }}>
                                                        "{absence.justification}"
                                                    </div>
                                                </div>
                                            ) : (
                                                <span className="badge badge-danger">❌ Non justifiée</span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Statistiques */}
            {filteredAbsences.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#dc3545', margin: '0' }}>
                            {filteredAbsences.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total absences</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredAbsences.filter(a => a.justification && a.justification.trim() !== '').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Justifiées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {filteredAbsences.filter(a => !a.justification || a.justification.trim() === '').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Non justifiées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {new Set(filteredAbsences.map(a => a.etudiant_id)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants concernés</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AbsencesReadOnly;
