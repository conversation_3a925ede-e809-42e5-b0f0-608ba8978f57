<?php
/**
 * API pour récupérer les matières avec authentification et filtrage par filière
 * Les étudiants ne voient que les matières de leur filière
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Inclure le système d'authentification
require_once '../../config/auth.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Authentification de l'utilisateur
$user_info = authenticateRequest($pdo);
if (!$user_info) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentification requise']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $role = $user_info['role'];
        $user_id = $user_info['id'];
        
        if ($role === 'Etudiant') {
            // Pour les étudiants : récupérer seulement les matières de leur filière
            $stmt = $pdo->prepare("
                SELECT DISTINCT m.id, m.nom, m.code, m.description, m.coefficient,
                       f.id as filiere_id, f.nom as filiere_nom,
                       COUNT(DISTINCT edt.id) as nombre_cours_planifies,
                       COUNT(DISTINCT d.id) as nombre_devoirs
                FROM matieres m
                INNER JOIN enseignements ens ON m.id = ens.matiere_id
                INNER JOIN classes c ON ens.classe_id = c.id
                INNER JOIN filieres f ON c.filiere_id = f.id
                INNER JOIN groupes g ON c.id = g.classe_id
                INNER JOIN etudiants e ON g.id = e.groupe_id
                LEFT JOIN emploisdutemps edt ON m.id = edt.matiere_id
                LEFT JOIN devoirs d ON m.id = d.matiere_id
                WHERE e.utilisateur_id = ?
                GROUP BY m.id, m.nom, m.code, m.description, m.coefficient, f.id, f.nom
                ORDER BY m.nom
            ");
            $stmt->execute([$user_id]);
        } else {
            // Pour les admin/enseignants : toutes les matières
            $stmt = $pdo->prepare("
                SELECT m.id, m.nom, m.code, m.description, m.coefficient,
                       NULL as filiere_id, NULL as filiere_nom,
                       COUNT(DISTINCT edt.id) as nombre_cours_planifies,
                       COUNT(DISTINCT d.id) as nombre_devoirs
                FROM matieres m
                LEFT JOIN emploisdutemps edt ON m.id = edt.matiere_id
                LEFT JOIN devoirs d ON m.id = d.matiere_id
                GROUP BY m.id, m.nom, m.code, m.description, m.coefficient
                ORDER BY m.nom
            ");
            $stmt->execute();
        }
        
        $matieres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formater les données pour React
        $result = [];
        foreach ($matieres as $matiere) {
            $result[] = [
                'id' => (int)$matiere['id'],
                'nom' => $matiere['nom'],
                'code' => $matiere['code'],
                'description' => $matiere['description'],
                'coefficient' => $matiere['coefficient'] ? (float)$matiere['coefficient'] : null,
                'filiere_id' => $matiere['filiere_id'] ? (int)$matiere['filiere_id'] : null,
                'filiere_nom' => $matiere['filiere_nom'],
                'nombre_cours_planifies' => (int)$matiere['nombre_cours_planifies'],
                'nombre_devoirs' => (int)$matiere['nombre_devoirs']
            ];
        }
        
        // Log pour debug
        error_log("Matières récupérées pour utilisateur {$user_id} (rôle: {$role}): " . count($result) . " matières");

        // Retourner un format cohérent avec les autres APIs
        echo json_encode([
            'success' => true,
            'matieres' => $result,
            'total' => count($result),
            'message' => 'Matières récupérées avec succès',
            'user_role' => $role
        ]);
        
    } catch (Exception $e) {
        error_log("Erreur lors de la récupération des matières: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des matières: ' . $e->getMessage(),
            'matieres' => [],
            'total' => 0
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}
?>
