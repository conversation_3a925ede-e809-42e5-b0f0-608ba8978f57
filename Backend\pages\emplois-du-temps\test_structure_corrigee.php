<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🧪 TEST STRUCTURE CORRIGÉE - EMPLOIS DU TEMPS</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .json-block { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>";
    
    echo "<div class='success'>";
    echo "<h2>🧪 TEST : Structure Corrigée</h2>";
    echo "<p>Vérification que les jointures fonctionnent avec la vraie structure des tables</p>";
    echo "</div>";
    
    // Connexion à la base de données
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Connexion à la base de données réussie</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
        exit();
    }
    
    // 1. Test de la requête corrigée
    echo "<div class='step'>";
    echo "<h3>🔗 1. Test Jointure Corrigée</h3>";
    
    try {
        $stmt = $pdo->query("
            SELECT edt.id, edt.classe_id, edt.jour, edt.heure_debut, edt.heure_fin, 
                   edt.matiere_id, edt.enseignant_id,
                   COALESCE(c.nom, CONCAT('Classe ID ', edt.classe_id, ' (non trouvée)')) as classe_nom,
                   COALESCE(m.nom, CONCAT('Matière ID ', edt.matiere_id, ' (non trouvée)')) as matiere_nom,
                   COALESCE(e.nom_prenom, CONCAT('Enseignant ID ', edt.enseignant_id, ' (non trouvé)')) as enseignant_nom,
                   COALESCE(e.email, '') as enseignant_email,
                   COALESCE(e.specialite, '') as enseignant_specialite
            FROM EmploisDuTemps edt
            LEFT JOIN Classes c ON edt.classe_id = c.id
            LEFT JOIN Matieres m ON edt.matiere_id = m.id
            LEFT JOIN Enseignants e ON edt.enseignant_id = e.id
            ORDER BY edt.id DESC
            LIMIT 10
        ");
        $jointures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($jointures)) {
            echo "<p class='success'>✅ Requête exécutée avec succès !</p>";
            echo "<h4>📊 Résultats des jointures :</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Jour</th><th>Heure</th><th>Classe</th><th>Matière</th><th>Enseignant</th><th>Email</th><th>Spécialité</th></tr>";
            
            $problemes = 0;
            foreach ($jointures as $j) {
                $classe_ok = !strpos($j['classe_nom'], '(non trouvée)');
                $matiere_ok = !strpos($j['matiere_nom'], '(non trouvée)');
                $enseignant_ok = !strpos($j['enseignant_nom'], '(non trouvé)');
                
                if (!$classe_ok || !$matiere_ok || !$enseignant_ok) {
                    $problemes++;
                }
                
                echo "<tr>";
                echo "<td>{$j['id']}</td>";
                echo "<td>{$j['jour']}</td>";
                echo "<td>{$j['heure_debut']}-{$j['heure_fin']}</td>";
                echo "<td style='background:" . ($classe_ok ? '#d4edda' : '#f8d7da') . "'>{$j['classe_nom']}</td>";
                echo "<td style='background:" . ($matiere_ok ? '#d4edda' : '#f8d7da') . "'>{$j['matiere_nom']}</td>";
                echo "<td style='background:" . ($enseignant_ok ? '#d4edda' : '#f8d7da') . "'>{$j['enseignant_nom']}</td>";
                echo "<td>{$j['enseignant_email']}</td>";
                echo "<td>{$j['enseignant_specialite']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($problemes === 0) {
                echo "<p class='success'>🎉 PARFAIT ! Toutes les jointures fonctionnent !</p>";
            } else {
                echo "<p class='warning'>⚠️ $problemes problème(s) détecté(s) - certains IDs n'existent pas dans les tables liées</p>";
            }
            
        } else {
            echo "<p class='warning'>⚠️ Aucun emploi du temps trouvé</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur jointure : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. Test de l'API
    echo "<div class='step'>";
    echo "<h3>🔧 2. Test API Corrigée</h3>";
    
    $api_url = "http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_no_auth.php";
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>URL :</strong> <a href='$api_url' target='_blank'>$api_url</a></p>";
        echo "<p><strong>Code HTTP :</strong> $http_code</p>";
        
        if ($http_code === 200) {
            echo "<p class='success'>✅ API accessible</p>";
            
            $data = json_decode($response, true);
            if (is_array($data)) {
                echo "<p class='success'>✅ Format JSON valide</p>";
                echo "<p class='info'>📊 " . count($data) . " emploi(s) du temps retournés</p>";
                
                if (!empty($data)) {
                    echo "<h4>📅 Premier emploi du temps :</h4>";
                    $first = $data[0];
                    echo "<table>";
                    echo "<tr><th>Champ</th><th>Valeur</th></tr>";
                    foreach ($first as $key => $value) {
                        $is_good = true;
                        if ($key === 'classe_nom' && (strpos($value, 'non trouvée') || $value === 'Classe inconnue')) $is_good = false;
                        if ($key === 'matiere_nom' && (strpos($value, 'non trouvée') || $value === 'Matière inconnue')) $is_good = false;
                        if ($key === 'enseignant_nom' && (strpos($value, 'non trouvé') || $value === 'Enseignant inconnu')) $is_good = false;
                        
                        echo "<tr>";
                        echo "<td><strong>$key</strong></td>";
                        echo "<td style='background:" . ($is_good ? '#d4edda' : '#f8d7da') . "'>$value</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    // Vérifier si les noms sont corrects
                    $classe_ok = !strpos($first['classe_nom'], 'non trouvée') && $first['classe_nom'] !== 'Classe inconnue';
                    $matiere_ok = !strpos($first['matiere_nom'], 'non trouvée') && $first['matiere_nom'] !== 'Matière inconnue';
                    $enseignant_ok = !strpos($first['enseignant_nom'], 'non trouvé') && $first['enseignant_nom'] !== 'Enseignant inconnu';
                    
                    if ($classe_ok && $matiere_ok && $enseignant_ok) {
                        echo "<p class='success'>🎉 SUCCÈS TOTAL ! Les vrais noms s'affichent parfaitement !</p>";
                    } else {
                        echo "<p class='warning'>⚠️ Certains noms ne s'affichent pas correctement</p>";
                        if (!$classe_ok) echo "<p class='error'>❌ Problème avec le nom de la classe</p>";
                        if (!$matiere_ok) echo "<p class='error'>❌ Problème avec le nom de la matière</p>";
                        if (!$enseignant_ok) echo "<p class='error'>❌ Problème avec le nom de l'enseignant</p>";
                    }
                }
            } else {
                echo "<p class='error'>❌ Format JSON invalide</p>";
                echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
            }
        } else {
            echo "<p class='error'>❌ API non accessible (Code: $http_code)</p>";
            echo "<div class='json-block'>" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur test API : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. Test des APIs de référence
    echo "<div class='step'>";
    echo "<h3>📚 3. Test APIs de Référence</h3>";
    
    $apis = [
        'Classes' => 'http://localhost/Project_PFE/Backend/pages/classes/getClasses_no_auth.php',
        'Matières' => 'http://localhost/Project_PFE/Backend/pages/matieres/getMatieres_no_auth.php',
        'Enseignants' => 'http://localhost/Project_PFE/Backend/pages/enseignants/getEnseignants_no_auth.php'
    ];
    
    foreach ($apis as $nom => $url) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                $data = json_decode($response, true);
                if ($data && isset($data['success']) && $data['success']) {
                    $count = count($data[strtolower($nom)] ?? []);
                    echo "<p class='success'>✅ $nom : $count élément(s)</p>";
                } else {
                    echo "<p class='warning'>⚠️ $nom : Format inattendu</p>";
                }
            } else {
                echo "<p class='error'>❌ $nom : Erreur $http_code</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ $nom : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // 4. Instructions finales
    echo "<div class='step'>";
    echo "<h3>🎯 4. Actions Finales</h3>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
    echo "<h4>🚨 ÉTAPES SUIVANTES :</h4>";
    echo "<ol>";
    echo "<li><strong>Vider le cache React :</strong> Ctrl+F5</li>";
    echo "<li><strong>Redémarrer React :</strong> npm start</li>";
    echo "<li><strong>Tester l'interface :</strong> <a href='http://localhost:3000/emplois-du-temps' target='_blank'>http://localhost:3000/emplois-du-temps</a></li>";
    echo "<li><strong>Vérifier l'affichage :</strong> Les vrais noms doivent maintenant s'afficher</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='$api_url' target='_blank' class='btn btn-success'>📅 API Emplois</a>";
    echo "<a href='http://localhost/Project_PFE/Backend/pages/emplois-du-temps/diagnostic_jointures.php' target='_blank' class='btn btn-success'>🔍 Diagnostic</a>";
    echo "<a href='http://localhost:3000/emplois-du-temps' target='_blank' class='btn btn-danger'>⚛️ Interface React</a>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h4>🎉 TEST TERMINÉ !</h4>";
    echo "<p><strong>La structure a été corrigée pour correspondre à la vraie base de données.</strong></p>";
    echo "<p><strong>Les jointures devraient maintenant fonctionner parfaitement.</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR CRITIQUE</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
