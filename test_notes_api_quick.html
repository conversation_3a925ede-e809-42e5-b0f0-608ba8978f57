<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Rapide API Notes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .matiere-ok { background-color: #d4edda; }
        .matiere-missing { background-color: #f8d7da; }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <h1>🚀 Test Rapide API Notes - Problème Matières</h1>
    
    <div class="container">
        <h2>🧪 Tests API</h2>
        <button onclick="testNotesAPI('enseignant-token')">🧑‍🏫 Test Enseignant</button>
        <button onclick="testNotesAPI('admin-token')">👨‍💼 Test Admin</button>
        <button onclick="testNotesAPI('etudiant-token')">🎓 Test Étudiant</button>
        <button onclick="clearResults()">🗑️ Effacer</button>
    </div>

    <div id="test-results"></div>

    <script>
        const API_BASE = 'http://localhost/Project_PFE/Backend/pages/';
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testNotesAPI(token) {
            const resultsDiv = document.getElementById('test-results');
            
            // Créer le conteneur de résultats
            const resultContainer = document.createElement('div');
            resultContainer.className = 'container';
            resultContainer.innerHTML = `
                <h3>🔄 Test API Notes avec ${token}</h3>
                <div class="status info">Chargement en cours...</div>
            `;
            resultsDiv.appendChild(resultContainer);
            
            try {
                const response = await fetch(API_BASE + 'notes/api.php', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log(`🔍 Réponse Notes API (${token}):`, data);
                
                if (response.ok && data.success) {
                    const notes = data.data || [];
                    
                    // Analyser les données
                    const totalNotes = notes.length;
                    const notesAvecMatiere = notes.filter(n => n.matiere_nom && n.matiere_nom.trim() !== '');
                    const notesSansMatiere = notes.filter(n => !n.matiere_nom || n.matiere_nom.trim() === '');
                    
                    let statusClass = notesSansMatiere.length > 0 ? 'warning' : 'success';
                    let statusIcon = notesSansMatiere.length > 0 ? '⚠️' : '✅';
                    
                    let content = `
                        <h3>${statusIcon} Test API Notes - ${token}</h3>
                        <div class="status ${statusClass}">
                            📊 Total notes: ${totalNotes}<br>
                            ✅ Notes avec matière: ${notesAvecMatiere.length}<br>
                            ❌ Notes sans matière: ${notesSansMatiere.length}<br>
                            👤 Rôle: ${data.role || 'Non spécifié'}<br>
                            🔧 Permissions: ${JSON.stringify(data.permissions || {})}
                        </div>
                    `;
                    
                    if (totalNotes > 0) {
                        content += `
                            <h4>📋 Aperçu des Notes (5 premières)</h4>
                            <table>
                                <tr>
                                    <th>ID</th>
                                    <th>Étudiant</th>
                                    <th>Devoir</th>
                                    <th>Matière</th>
                                    <th>Note</th>
                                    <th>Date</th>
                                </tr>
                        `;
                        
                        notes.slice(0, 5).forEach(note => {
                            const matiereClass = note.matiere_nom ? 'matiere-ok' : 'matiere-missing';
                            const matiereText = note.matiere_nom || '❌ MANQUANT';
                            
                            content += `
                                <tr>
                                    <td>${note.id || 'N/A'}</td>
                                    <td>${note.etudiant_nom || 'N/A'}</td>
                                    <td>${note.devoir_titre || 'N/A'}</td>
                                    <td class="${matiereClass}"><strong>${matiereText}</strong></td>
                                    <td>${note.note || 'N/A'}</td>
                                    <td>${note.date_formatted || note.date_enregistrement || 'N/A'}</td>
                                </tr>
                            `;
                        });
                        
                        content += '</table>';
                        
                        if (notesSansMatiere.length > 0) {
                            content += `
                                <div class="status error">
                                    ⚠️ PROBLÈME DÉTECTÉ: ${notesSansMatiere.length} notes n'ont pas de nom de matière.<br>
                                    Cela peut indiquer un problème avec la jointure SQL ou des données manquantes.
                                </div>
                            `;
                        }
                        
                        // Afficher la structure de la première note
                        content += `
                            <h4>🔍 Structure de la première note</h4>
                            <pre>${JSON.stringify(notes[0], null, 2)}</pre>
                        `;
                        
                        // Analyser les champs disponibles
                        if (notes.length > 0) {
                            const champs = Object.keys(notes[0]);
                            const champsImportants = ['matiere_nom', 'etudiant_nom', 'devoir_titre', 'note', 'date_formatted'];
                            const champsManquants = champsImportants.filter(champ => !champs.includes(champ));
                            
                            if (champsManquants.length > 0) {
                                content += `
                                    <div class="status warning">
                                        ⚠️ Champs importants manquants: ${champsManquants.join(', ')}
                                    </div>
                                `;
                            }
                            
                            content += `
                                <div class="status info">
                                    📋 Champs disponibles: ${champs.join(', ')}
                                </div>
                            `;
                        }
                    } else {
                        content += `
                            <div class="status warning">
                                ⚠️ Aucune note trouvée pour ce rôle
                            </div>
                        `;
                    }
                    
                    resultContainer.innerHTML = content;
                    
                } else {
                    resultContainer.innerHTML = `
                        <h3>❌ Test API Notes - ${token}</h3>
                        <div class="status error">
                            Échec: HTTP ${response.status}<br>
                            Message: ${data.message || 'Erreur inconnue'}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
            } catch (error) {
                console.error(`❌ Erreur Notes API (${token}):`, error);
                resultContainer.innerHTML = `
                    <h3>❌ Test API Notes - ${token}</h3>
                    <div class="status error">
                        Erreur de connexion: ${error.message}
                    </div>
                `;
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `
                <div class="container">
                    <div class="status info">
                        🚀 Page de test chargée. Cliquez sur un bouton pour tester l'API Notes.
                    </div>
                </div>
            `;
        };
    </script>
</body>
</html>
