<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Simulation utilisateur pour test
$user_info = [
    'id' => 1,
    'role' => 'Admin'
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($pdo, $user_info) {
    $role = $user_info['role'];
    $user_id = $user_info['id'];
    
    if ($role === 'Admin') {
        // Admin peut voir tous les retards
        $stmt = $pdo->prepare("
            SELECT r.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Retards r 
            JOIN Etudiants e ON r.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id
            LEFT JOIN Enseignants ens ON r.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            ORDER BY r.date_retard DESC
        ");
        $stmt->execute();
    } elseif ($role === 'Enseignant') {
        // Enseignant peut voir et enregistrer les retards des étudiants de ses classes
        $stmt = $pdo->prepare("
            SELECT r.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Retards r 
            JOIN Etudiants e ON r.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id
            LEFT JOIN Enseignants ens ON r.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            JOIN Enseignements en ON en.enseignant_id = (
                SELECT id FROM Enseignants WHERE utilisateur_id = ?
            ) AND en.classe_id = (
                SELECT g.classe_id FROM Groupes g WHERE g.id = e.groupe_id
            )
            ORDER BY r.date_retard DESC
        ");
        $stmt->execute([$user_id]);
    } elseif ($role === 'Parent') {
        // Parent peut voir les retards de ses enfants
        $stmt = $pdo->prepare("
            SELECT r.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Retards r 
            JOIN Etudiants e ON r.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id
            LEFT JOIN Enseignants ens ON r.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            JOIN Parent_Etudiant pe ON e.id = pe.etudiant_id 
            JOIN Parents p ON pe.parent_id = p.id 
            WHERE p.utilisateur_id = ? 
            ORDER BY r.date_retard DESC
        ");
        $stmt->execute([$user_id]);
    } elseif ($role === 'Etudiant') {
        // Étudiant peut voir ses propres retards
        $stmt = $pdo->prepare("
            SELECT r.*, u.nom as etudiant_nom, u.email as etudiant_email,
                   m.nom as matiere_nom, ue.nom as enseignant_nom
            FROM Retards r 
            JOIN Etudiants e ON r.etudiant_id = e.id 
            JOIN Utilisateurs u ON e.utilisateur_id = u.id 
            LEFT JOIN Matieres m ON r.matiere_id = m.id
            LEFT JOIN Enseignants ens ON r.enseignant_id = ens.id
            LEFT JOIN Utilisateurs ue ON ens.utilisateur_id = ue.id
            WHERE e.utilisateur_id = ? 
            ORDER BY r.date_retard DESC
        ");
        $stmt->execute([$user_id]);
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
        return;
    }
    
    $retards = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($retards);
}

function handlePost($pdo, $user_info, $input) {
    if (!in_array($user_info['role'], ['Admin', 'Enseignant'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls l\'admin et les enseignants peuvent enregistrer des retards']);
        return;
    }
    
    if (!isset($input['etudiant_id'], $input['date_retard'], $input['duree_retard'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes']);
        return;
    }
    
    // Si c'est un enseignant, récupérer son ID
    $enseignant_id = null;
    if ($user_info['role'] === 'Enseignant') {
        $stmt = $pdo->prepare("SELECT id FROM Enseignants WHERE utilisateur_id = ?");
        $stmt->execute([$user_info['id']]);
        $enseignant = $stmt->fetch(PDO::FETCH_ASSOC);
        $enseignant_id = $enseignant ? $enseignant['id'] : null;
    } else {
        $enseignant_id = $input['enseignant_id'] ?? null;
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO Retards (etudiant_id, matiere_id, enseignant_id, date_retard, duree_retard, justification) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $input['etudiant_id'],
        $input['matiere_id'] ?? null,
        $enseignant_id,
        $input['date_retard'],
        $input['duree_retard'],
        $input['justification'] ?? null
    ]);
    
    echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
}

function handlePut($pdo, $user_info, $input) {
    if (!in_array($user_info['role'], ['Admin', 'Enseignant'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls l\'admin et les enseignants peuvent modifier des retards']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $fields = [];
    $values = [];
    
    if (isset($input['date_retard'])) {
        $fields[] = 'date_retard = ?';
        $values[] = $input['date_retard'];
    }
    if (isset($input['duree_retard'])) {
        $fields[] = 'duree_retard = ?';
        $values[] = $input['duree_retard'];
    }
    if (isset($input['justification'])) {
        $fields[] = 'justification = ?';
        $values[] = $input['justification'];
    }
    if (isset($input['matiere_id'])) {
        $fields[] = 'matiere_id = ?';
        $values[] = $input['matiere_id'];
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à modifier']);
        return;
    }
    
    $values[] = $input['id'];
    $sql = "UPDATE Retards SET " . implode(', ', $fields) . " WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($values);
    
    echo json_encode(['success' => true]);
}

function handleDelete($pdo, $user_info, $input) {
    if (!in_array($user_info['role'], ['Admin', 'Enseignant'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Seuls l\'admin et les enseignants peuvent supprimer des retards']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM Retards WHERE id = ?");
    $stmt->execute([$input['id']]);
    
    echo json_encode(['success' => true]);
}

function verifyToken($token) {
    // Implémentation simple de vérification de token
    // À adapter selon votre système d'authentification
    try {
        $decoded = base64_decode($token);
        $data = json_decode($decoded, true);
        
        if ($data && isset($data['id'], $data['role'])) {
            return $data;
        }
        return false;
    } catch (Exception $e) {
        return false;
    }
}
?>
