<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

// Récupération des informations utilisateur depuis la session ou token
session_start();
$user_info = [
    'id' => $_SESSION['user_id'] ?? 1,
    'role' => $_SESSION['user_role'] ?? 'Admin' // Pour test, changez selon votre système
];

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo, $user_info);
            break;
        case 'POST':
            handlePost($pdo, $user_info, $input);
            break;
        case 'PUT':
            handlePut($pdo, $user_info, $input);
            break;
        case 'DELETE':
            handleDelete($pdo, $user_info, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($pdo, $user_info) {
    $role = $user_info['role'];
    
    // Contrôle d'accès : Admin et Enseignant peuvent lire, Etudiant et Parent n'ont aucun accès
    if (!in_array($role, ['Admin', 'Enseignant'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Accès non autorisé']);
        return;
    }
    
    // Récupérer toutes les relations parent-étudiant avec les informations détaillées
    $stmt = $pdo->prepare("
        SELECT 
            pe.id,
            pe.parent_id,
            pe.etudiant_id,
            pe.lien_parente,
            up.nom as parent_nom,
            up.email as parent_email,
            p.telephone as parent_telephone,
            ue.nom as etudiant_nom,
            ue.email as etudiant_email
        FROM parent_etudiant pe
        JOIN parents p ON pe.parent_id = p.id
        JOIN utilisateurs up ON p.utilisateur_id = up.id
        JOIN etudiants e ON pe.etudiant_id = e.id
        JOIN utilisateurs ue ON e.utilisateur_id = ue.id
        ORDER BY up.nom, ue.nom
    ");
    $stmt->execute();
    
    $relations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($relations);
}

function handlePost($pdo, $user_info, $input) {
    // Seul l'Admin peut créer des relations
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'administrateur peut créer des relations parent-étudiant']);
        return;
    }
    
    // Validation des données
    if (!isset($input['parent_id'], $input['etudiant_id'], $input['lien_parente'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (parent_id, etudiant_id, lien_parente requis)']);
        return;
    }
    
    // Vérifier que les valeurs de lien_parente sont valides
    $liens_valides = ['Père', 'Mère', 'Tuteur', 'Autre'];
    if (!in_array($input['lien_parente'], $liens_valides)) {
        http_response_code(400);
        echo json_encode(['error' => 'Lien de parenté invalide. Valeurs autorisées: ' . implode(', ', $liens_valides)]);
        return;
    }
    
    // Vérifier que la relation n'existe pas déjà
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM parent_etudiant 
        WHERE parent_id = ? AND etudiant_id = ?
    ");
    $stmt->execute([$input['parent_id'], $input['etudiant_id']]);
    
    if ($stmt->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Cette relation parent-étudiant existe déjà']);
        return;
    }
    
    // Insérer la nouvelle relation
    $stmt = $pdo->prepare("
        INSERT INTO parent_etudiant (parent_id, etudiant_id, lien_parente) 
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([
        $input['parent_id'],
        $input['etudiant_id'],
        $input['lien_parente']
    ]);
    
    echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
}

function handlePut($pdo, $user_info, $input) {
    // Seul l'Admin peut modifier des relations
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'administrateur peut modifier des relations parent-étudiant']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $fields = [];
    $values = [];
    
    if (isset($input['parent_id'])) {
        $fields[] = 'parent_id = ?';
        $values[] = $input['parent_id'];
    }
    if (isset($input['etudiant_id'])) {
        $fields[] = 'etudiant_id = ?';
        $values[] = $input['etudiant_id'];
    }
    if (isset($input['lien_parente'])) {
        // Vérifier que la valeur est valide
        $liens_valides = ['Père', 'Mère', 'Tuteur', 'Autre'];
        if (!in_array($input['lien_parente'], $liens_valides)) {
            http_response_code(400);
            echo json_encode(['error' => 'Lien de parenté invalide']);
            return;
        }
        $fields[] = 'lien_parente = ?';
        $values[] = $input['lien_parente'];
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'Aucune donnée à modifier']);
        return;
    }
    
    // Vérifier l'unicité si parent_id ou etudiant_id sont modifiés
    if (isset($input['parent_id']) || isset($input['etudiant_id'])) {
        $parent_id = $input['parent_id'] ?? null;
        $etudiant_id = $input['etudiant_id'] ?? null;
        
        // Récupérer les valeurs actuelles si pas fournies
        if (!$parent_id || !$etudiant_id) {
            $stmt = $pdo->prepare("SELECT parent_id, etudiant_id FROM parent_etudiant WHERE id = ?");
            $stmt->execute([$input['id']]);
            $current = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $parent_id = $parent_id ?: $current['parent_id'];
            $etudiant_id = $etudiant_id ?: $current['etudiant_id'];
        }
        
        // Vérifier l'unicité
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM parent_etudiant 
            WHERE parent_id = ? AND etudiant_id = ? AND id != ?
        ");
        $stmt->execute([$parent_id, $etudiant_id, $input['id']]);
        
        if ($stmt->fetchColumn() > 0) {
            http_response_code(400);
            echo json_encode(['error' => 'Cette relation parent-étudiant existe déjà']);
            return;
        }
    }
    
    $values[] = $input['id'];
    $sql = "UPDATE parent_etudiant SET " . implode(', ', $fields) . " WHERE id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($values);
    
    echo json_encode(['success' => true]);
}

function handleDelete($pdo, $user_info, $input) {
    // Seul l'Admin peut supprimer des relations
    if ($user_info['role'] !== 'Admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Seul l\'administrateur peut supprimer des relations parent-étudiant']);
        return;
    }
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID manquant']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM parent_etudiant WHERE id = ?");
    $stmt->execute([$input['id']]);
    
    echo json_encode(['success' => true]);
}

// TODO: Intégrer avec votre système d'authentification existant
// function verifyToken($token) { ... }
?>
