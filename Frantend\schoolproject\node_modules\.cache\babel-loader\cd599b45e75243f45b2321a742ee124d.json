{"ast": null, "code": "/**\n * 🔒 SYSTÈME DE FILTRAGE DE SÉCURITÉ POUR LES DONNÉES ÉTUDIANTS\n * \n * Ce module garantit que chaque étudiant ne peut voir que ses propres informations\n * et les données liées à sa filière/classe uniquement.\n * \n * RÈGLES DE SÉCURITÉ :\n * - Étudiants : Accès en lecture seule à leurs propres données uniquement\n * - Matières : Filtrées par filière de l'étudiant\n * - Cours : Filtrés par classe/groupe de l'étudiant\n * - Notes/Absences/Retards/Diplômes : Données personnelles uniquement\n * - Admin/Enseignants : Accès complet à toutes les données\n */\n\n// ============================================================================\n// FONCTIONS DE VÉRIFICATION DES RÔLES\n// ============================================================================\n\nexport const isStudent = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'etudiant' || role === 'student';\n};\nexport const isAdmin = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'admin' || role === 'administrateur';\n};\nexport const isTeacher = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'enseignant' || role === 'teacher' || role === 'professeur';\n};\nexport const isParent = user => {\n  if (!user || !user.role) return false;\n  const role = user.role.toLowerCase();\n  return role === 'parent';\n};\nexport const canManageData = user => {\n  return isAdmin(user) || isTeacher(user);\n};\n\n// ============================================================================\n// FONCTION DE FILTRAGE GÉNÉRIQUE\n// ============================================================================\n\nexport const filterStudentData = (data, user, userField = 'etudiant_email') => {\n  if (!user || !Array.isArray(data)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner toutes les données\n  if (!isStudent(user)) {\n    return data;\n  }\n\n  // Filtrer les données pour ne garder que celles de l'étudiant connecté\n  return data.filter(item => {\n    const matchesEmail = item.etudiant_email === user.email;\n    const matchesUserId = item.utilisateur_id === user.id;\n    const matchesEtudiantUserId = item.etudiant_utilisateur_id === user.id;\n    const matchesEtudiantId = item.etudiant_id === user.etudiant_id;\n    return matchesEmail || matchesUserId || matchesEtudiantUserId || matchesEtudiantId;\n  });\n};\n\n// ============================================================================\n// FONCTIONS SPÉCIALISÉES PAR TYPE DE DONNÉES\n// ============================================================================\n\nexport const filterAbsences = (absences, user) => {\n  return filterStudentData(absences, user, 'etudiant_email');\n};\nexport const filterRetards = (retards, user) => {\n  return filterStudentData(retards, user, 'etudiant_email');\n};\nexport const filterNotes = (notes, user) => {\n  return filterStudentData(notes, user, 'etudiant_email');\n};\nexport const filterDiplomes = (diplomes, user) => {\n  return filterStudentData(diplomes, user, 'etudiant_email');\n};\nexport const filterCours = (cours, user) => {\n  // Pour les cours, on filtre par classe_id de l'étudiant\n  if (!user || !Array.isArray(cours)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner tous les cours\n  if (!isStudent(user)) {\n    return cours;\n  }\n\n  // Filtrer les cours par classe de l'étudiant\n  return cours.filter(coursItem => {\n    const matchesClasse = coursItem.classe_id === user.classe_id;\n    const matchesGroupe = coursItem.groupe_id === user.groupe_id;\n    const matchesEtudiant = coursItem.etudiant_email === user.email;\n    return matchesClasse || matchesGroupe || matchesEtudiant;\n  });\n};\nexport const filterMatieres = (matieres, user) => {\n  // Pour les matières, on filtre par filière de l'étudiant\n  if (!user || !Array.isArray(matieres)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner toutes les matières\n  if (!isStudent(user)) {\n    return matieres;\n  }\n\n  // Filtrer les matières par filière de l'étudiant\n  return matieres.filter(matiere => {\n    const matchesFiliere = matiere.filiere_id === user.filiere_id;\n    const matchesFiliereNom = matiere.filiere_nom === user.filiere_nom;\n    return matchesFiliere || matchesFiliereNom;\n  });\n};\nexport const filterDevoirs = (devoirs, user) => {\n  // Pour les devoirs, on filtre par classe/matière de l'étudiant\n  if (!user || !Array.isArray(devoirs)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner tous les devoirs\n  if (!isStudent(user)) {\n    return devoirs;\n  }\n\n  // Filtrer les devoirs par classe/matière de l'étudiant\n  return devoirs.filter(devoir => {\n    const matchesClasse = devoir.classe_id === user.classe_id;\n    const matchesGroupe = devoir.groupe_id === user.groupe_id;\n    const matchesFiliere = devoir.filiere_id === user.filiere_id;\n    return matchesClasse || matchesGroupe || matchesFiliere;\n  });\n};\nexport const filterEmploisDuTemps = (emplois, user) => {\n  // Pour les emplois du temps, on filtre par classe/groupe de l'étudiant\n  if (!user || !Array.isArray(emplois)) {\n    return [];\n  }\n\n  // Si l'utilisateur n'est pas un étudiant, retourner tous les emplois\n  if (!isStudent(user)) {\n    return emplois;\n  }\n\n  // Filtrer les emplois du temps par classe/groupe de l'étudiant\n  return emplois.filter(emploi => {\n    const matchesClasse = emploi.classe_id === user.classe_id;\n    const matchesGroupe = emploi.groupe_id === user.groupe_id;\n    return matchesClasse || matchesGroupe;\n  });\n};\n\n// ============================================================================\n// FONCTIONS DE LOGGING ET AUDIT DE SÉCURITÉ\n// ============================================================================\n\nexport const logSecurityEvent = (action, user, data) => {\n  const logEntry = {\n    timestamp: new Date().toISOString(),\n    action,\n    user: {\n      id: user === null || user === void 0 ? void 0 : user.id,\n      email: user === null || user === void 0 ? void 0 : user.email,\n      role: user === null || user === void 0 ? void 0 : user.role\n    },\n    data: {\n      id: data === null || data === void 0 ? void 0 : data.id,\n      type: data === null || data === void 0 ? void 0 : data.type,\n      total: data === null || data === void 0 ? void 0 : data.total,\n      filtered: data === null || data === void 0 ? void 0 : data.filtered\n    }\n  };\n  console.log('🔐 AUDIT SÉCURITÉ:', logEntry);\n\n  // En production, envoyer vers un système de logging centralisé\n  if (process.env.NODE_ENV === 'production') {\n    // sendToSecurityLog(logEntry);\n  }\n};\nexport const showSecurityAlert = (message = 'Accès refusé : Vous ne pouvez consulter que vos propres informations.') => {\n  console.warn('🚨 TENTATIVE D\\'ACCÈS NON AUTORISÉ DÉTECTÉE');\n  alert(message);\n};\n\n// ============================================================================\n// FONCTIONS D'AIDE POUR LA RÉCUPÉRATION DES INFORMATIONS ÉTUDIANT\n// ============================================================================\n\nexport const getStudentInfo = async (userId, token) => {\n  try {\n    const response = await fetch(`/api/student-info/${userId}`, {\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (response.ok) {\n      const data = await response.json();\n      return data.student;\n    }\n    return null;\n  } catch (error) {\n    console.error('Erreur lors de la récupération des informations étudiant:', error);\n    return null;\n  }\n};\n\n// ============================================================================\n// FONCTIONS DE VALIDATION DES PERMISSIONS\n// ============================================================================\n\nexport const canAccessData = (user, dataType, dataItem) => {\n  if (!user) return false;\n\n  // Admin et enseignants ont accès à tout\n  if (canManageData(user)) return true;\n\n  // Parents peuvent voir les données de leurs enfants\n  if (isParent(user)) {\n    return dataItem.parent_id === user.parent_id || dataItem.etudiant_parent_id === user.id;\n  }\n\n  // Étudiants ne peuvent voir que leurs propres données\n  if (isStudent(user)) {\n    return dataItem.etudiant_email === user.email || dataItem.utilisateur_id === user.id || dataItem.etudiant_id === user.etudiant_id;\n  }\n  return false;\n};\nexport const validateDataAccess = (user, data, dataType) => {\n  if (!isStudent(user)) return data;\n  const filteredData = data.filter(item => canAccessData(user, dataType, item));\n  if (filteredData.length !== data.length) {\n    logSecurityEvent(`${dataType.toUpperCase()}_ACCESS_FILTERED`, user, {\n      type: dataType,\n      total: data.length,\n      filtered: filteredData.length\n    });\n  }\n  return filteredData;\n};\n\n// Export par défaut pour faciliter l'importation\nexport default {\n  isStudent,\n  isAdmin,\n  isTeacher,\n  isParent,\n  canManageData,\n  filterStudentData,\n  filterAbsences,\n  filterRetards,\n  filterNotes,\n  filterDiplomes,\n  filterCours,\n  filterMatieres,\n  filterDevoirs,\n  filterEmploisDuTemps,\n  logSecurityEvent,\n  showSecurityAlert,\n  getStudentInfo,\n  canAccessData,\n  validateDataAccess\n};", "map": {"version": 3, "names": ["isStudent", "user", "role", "toLowerCase", "isAdmin", "<PERSON><PERSON><PERSON>er", "isParent", "canManageData", "filterStudentData", "data", "userField", "Array", "isArray", "filter", "item", "matchesEmail", "etudiant_email", "email", "matchesUserId", "utilisateur_id", "id", "matchesEtudiantUserId", "etudiant_utilisateur_id", "matchesEtudiantId", "etudiant_id", "filterAbsences", "absences", "filterRetards", "retards", "filterNotes", "notes", "filterDiplomes", "diplomes", "filterCours", "cours", "coursItem", "matchesClasse", "classe_id", "matchesGroupe", "groupe_id", "matchesEtudiant", "filterMatieres", "matieres", "matiere", "matchesFiliere", "filiere_id", "matchesFiliereNom", "filiere_nom", "filterDevoirs", "devoirs", "devoir", "filterEmploisDuTemps", "emp<PERSON><PERSON>", "emploi", "logSecurityEvent", "action", "logEntry", "timestamp", "Date", "toISOString", "type", "total", "filtered", "console", "log", "process", "env", "NODE_ENV", "showSecurityAlert", "message", "warn", "alert", "getStudentInfo", "userId", "token", "response", "fetch", "headers", "ok", "json", "student", "error", "canAccessData", "dataType", "dataItem", "parent_id", "etudiant_parent_id", "validateDataAccess", "filteredData", "length", "toUpperCase"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/utils/studentDataFilter.js"], "sourcesContent": ["/**\n * 🔒 SYSTÈME DE FILTRAGE DE SÉCURITÉ POUR LES DONNÉES ÉTUDIANTS\n * \n * Ce module garantit que chaque étudiant ne peut voir que ses propres informations\n * et les données liées à sa filière/classe uniquement.\n * \n * RÈGLES DE SÉCURITÉ :\n * - Étudiants : Accès en lecture seule à leurs propres données uniquement\n * - Matières : Filtrées par filière de l'étudiant\n * - Cours : Filtrés par classe/groupe de l'étudiant\n * - Notes/Absences/Retards/Diplômes : Données personnelles uniquement\n * - Admin/Enseignants : Accès complet à toutes les données\n */\n\n// ============================================================================\n// FONCTIONS DE VÉRIFICATION DES RÔLES\n// ============================================================================\n\nexport const isStudent = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'etudiant' || role === 'student';\n};\n\nexport const isAdmin = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'admin' || role === 'administrateur';\n};\n\nexport const isTeacher = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'enseignant' || role === 'teacher' || role === 'professeur';\n};\n\nexport const isParent = (user) => {\n    if (!user || !user.role) return false;\n    const role = user.role.toLowerCase();\n    return role === 'parent';\n};\n\nexport const canManageData = (user) => {\n    return isAdmin(user) || isTeacher(user);\n};\n\n// ============================================================================\n// FONCTION DE FILTRAGE GÉNÉRIQUE\n// ============================================================================\n\nexport const filterStudentData = (data, user, userField = 'etudiant_email') => {\n    if (!user || !Array.isArray(data)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner toutes les données\n    if (!isStudent(user)) {\n        return data;\n    }\n\n    // Filtrer les données pour ne garder que celles de l'étudiant connecté\n    return data.filter(item => {\n        const matchesEmail = item.etudiant_email === user.email;\n        const matchesUserId = item.utilisateur_id === user.id;\n        const matchesEtudiantUserId = item.etudiant_utilisateur_id === user.id;\n        const matchesEtudiantId = item.etudiant_id === user.etudiant_id;\n        \n        return matchesEmail || matchesUserId || matchesEtudiantUserId || matchesEtudiantId;\n    });\n};\n\n// ============================================================================\n// FONCTIONS SPÉCIALISÉES PAR TYPE DE DONNÉES\n// ============================================================================\n\nexport const filterAbsences = (absences, user) => {\n    return filterStudentData(absences, user, 'etudiant_email');\n};\n\nexport const filterRetards = (retards, user) => {\n    return filterStudentData(retards, user, 'etudiant_email');\n};\n\nexport const filterNotes = (notes, user) => {\n    return filterStudentData(notes, user, 'etudiant_email');\n};\n\nexport const filterDiplomes = (diplomes, user) => {\n    return filterStudentData(diplomes, user, 'etudiant_email');\n};\n\nexport const filterCours = (cours, user) => {\n    // Pour les cours, on filtre par classe_id de l'étudiant\n    if (!user || !Array.isArray(cours)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner tous les cours\n    if (!isStudent(user)) {\n        return cours;\n    }\n\n    // Filtrer les cours par classe de l'étudiant\n    return cours.filter(coursItem => {\n        const matchesClasse = coursItem.classe_id === user.classe_id;\n        const matchesGroupe = coursItem.groupe_id === user.groupe_id;\n        const matchesEtudiant = coursItem.etudiant_email === user.email;\n        \n        return matchesClasse || matchesGroupe || matchesEtudiant;\n    });\n};\n\nexport const filterMatieres = (matieres, user) => {\n    // Pour les matières, on filtre par filière de l'étudiant\n    if (!user || !Array.isArray(matieres)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner toutes les matières\n    if (!isStudent(user)) {\n        return matieres;\n    }\n\n    // Filtrer les matières par filière de l'étudiant\n    return matieres.filter(matiere => {\n        const matchesFiliere = matiere.filiere_id === user.filiere_id;\n        const matchesFiliereNom = matiere.filiere_nom === user.filiere_nom;\n        \n        return matchesFiliere || matchesFiliereNom;\n    });\n};\n\nexport const filterDevoirs = (devoirs, user) => {\n    // Pour les devoirs, on filtre par classe/matière de l'étudiant\n    if (!user || !Array.isArray(devoirs)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner tous les devoirs\n    if (!isStudent(user)) {\n        return devoirs;\n    }\n\n    // Filtrer les devoirs par classe/matière de l'étudiant\n    return devoirs.filter(devoir => {\n        const matchesClasse = devoir.classe_id === user.classe_id;\n        const matchesGroupe = devoir.groupe_id === user.groupe_id;\n        const matchesFiliere = devoir.filiere_id === user.filiere_id;\n        \n        return matchesClasse || matchesGroupe || matchesFiliere;\n    });\n};\n\nexport const filterEmploisDuTemps = (emplois, user) => {\n    // Pour les emplois du temps, on filtre par classe/groupe de l'étudiant\n    if (!user || !Array.isArray(emplois)) {\n        return [];\n    }\n\n    // Si l'utilisateur n'est pas un étudiant, retourner tous les emplois\n    if (!isStudent(user)) {\n        return emplois;\n    }\n\n    // Filtrer les emplois du temps par classe/groupe de l'étudiant\n    return emplois.filter(emploi => {\n        const matchesClasse = emploi.classe_id === user.classe_id;\n        const matchesGroupe = emploi.groupe_id === user.groupe_id;\n        \n        return matchesClasse || matchesGroupe;\n    });\n};\n\n// ============================================================================\n// FONCTIONS DE LOGGING ET AUDIT DE SÉCURITÉ\n// ============================================================================\n\nexport const logSecurityEvent = (action, user, data) => {\n    const logEntry = {\n        timestamp: new Date().toISOString(),\n        action,\n        user: { \n            id: user?.id, \n            email: user?.email, \n            role: user?.role \n        },\n        data: { \n            id: data?.id, \n            type: data?.type,\n            total: data?.total,\n            filtered: data?.filtered\n        }\n    };\n    \n    console.log('🔐 AUDIT SÉCURITÉ:', logEntry);\n    \n    // En production, envoyer vers un système de logging centralisé\n    if (process.env.NODE_ENV === 'production') {\n        // sendToSecurityLog(logEntry);\n    }\n};\n\nexport const showSecurityAlert = (message = 'Accès refusé : Vous ne pouvez consulter que vos propres informations.') => {\n    console.warn('🚨 TENTATIVE D\\'ACCÈS NON AUTORISÉ DÉTECTÉE');\n    alert(message);\n};\n\n// ============================================================================\n// FONCTIONS D'AIDE POUR LA RÉCUPÉRATION DES INFORMATIONS ÉTUDIANT\n// ============================================================================\n\nexport const getStudentInfo = async (userId, token) => {\n    try {\n        const response = await fetch(`/api/student-info/${userId}`, {\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        \n        if (response.ok) {\n            const data = await response.json();\n            return data.student;\n        }\n        \n        return null;\n    } catch (error) {\n        console.error('Erreur lors de la récupération des informations étudiant:', error);\n        return null;\n    }\n};\n\n// ============================================================================\n// FONCTIONS DE VALIDATION DES PERMISSIONS\n// ============================================================================\n\nexport const canAccessData = (user, dataType, dataItem) => {\n    if (!user) return false;\n    \n    // Admin et enseignants ont accès à tout\n    if (canManageData(user)) return true;\n    \n    // Parents peuvent voir les données de leurs enfants\n    if (isParent(user)) {\n        return dataItem.parent_id === user.parent_id || \n               dataItem.etudiant_parent_id === user.id;\n    }\n    \n    // Étudiants ne peuvent voir que leurs propres données\n    if (isStudent(user)) {\n        return dataItem.etudiant_email === user.email ||\n               dataItem.utilisateur_id === user.id ||\n               dataItem.etudiant_id === user.etudiant_id;\n    }\n    \n    return false;\n};\n\nexport const validateDataAccess = (user, data, dataType) => {\n    if (!isStudent(user)) return data;\n    \n    const filteredData = data.filter(item => canAccessData(user, dataType, item));\n    \n    if (filteredData.length !== data.length) {\n        logSecurityEvent(`${dataType.toUpperCase()}_ACCESS_FILTERED`, user, {\n            type: dataType,\n            total: data.length,\n            filtered: filteredData.length\n        });\n    }\n    \n    return filteredData;\n};\n\n// Export par défaut pour faciliter l'importation\nexport default {\n    isStudent,\n    isAdmin,\n    isTeacher,\n    isParent,\n    canManageData,\n    filterStudentData,\n    filterAbsences,\n    filterRetards,\n    filterNotes,\n    filterDiplomes,\n    filterCours,\n    filterMatieres,\n    filterDevoirs,\n    filterEmploisDuTemps,\n    logSecurityEvent,\n    showSecurityAlert,\n    getStudentInfo,\n    canAccessData,\n    validateDataAccess\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,OAAO,MAAMA,SAAS,GAAIC,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGD,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS;AACpD,CAAC;AAED,OAAO,MAAME,OAAO,GAAIH,IAAI,IAAK;EAC7B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGD,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,gBAAgB;AACxD,CAAC;AAED,OAAO,MAAMG,SAAS,GAAIJ,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGD,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY;AAC/E,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAIL,IAAI,IAAK;EAC9B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EACrC,MAAMA,IAAI,GAAGD,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;EACpC,OAAOD,IAAI,KAAK,QAAQ;AAC5B,CAAC;AAED,OAAO,MAAMK,aAAa,GAAIN,IAAI,IAAK;EACnC,OAAOG,OAAO,CAACH,IAAI,CAAC,IAAII,SAAS,CAACJ,IAAI,CAAC;AAC3C,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMO,iBAAiB,GAAGA,CAACC,IAAI,EAAER,IAAI,EAAES,SAAS,GAAG,gBAAgB,KAAK;EAC3E,IAAI,CAACT,IAAI,IAAI,CAACU,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IAC/B,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAACT,SAAS,CAACC,IAAI,CAAC,EAAE;IAClB,OAAOQ,IAAI;EACf;;EAEA;EACA,OAAOA,IAAI,CAACI,MAAM,CAACC,IAAI,IAAI;IACvB,MAAMC,YAAY,GAAGD,IAAI,CAACE,cAAc,KAAKf,IAAI,CAACgB,KAAK;IACvD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,cAAc,KAAKlB,IAAI,CAACmB,EAAE;IACrD,MAAMC,qBAAqB,GAAGP,IAAI,CAACQ,uBAAuB,KAAKrB,IAAI,CAACmB,EAAE;IACtE,MAAMG,iBAAiB,GAAGT,IAAI,CAACU,WAAW,KAAKvB,IAAI,CAACuB,WAAW;IAE/D,OAAOT,YAAY,IAAIG,aAAa,IAAIG,qBAAqB,IAAIE,iBAAiB;EACtF,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAME,cAAc,GAAGA,CAACC,QAAQ,EAAEzB,IAAI,KAAK;EAC9C,OAAOO,iBAAiB,CAACkB,QAAQ,EAAEzB,IAAI,EAAE,gBAAgB,CAAC;AAC9D,CAAC;AAED,OAAO,MAAM0B,aAAa,GAAGA,CAACC,OAAO,EAAE3B,IAAI,KAAK;EAC5C,OAAOO,iBAAiB,CAACoB,OAAO,EAAE3B,IAAI,EAAE,gBAAgB,CAAC;AAC7D,CAAC;AAED,OAAO,MAAM4B,WAAW,GAAGA,CAACC,KAAK,EAAE7B,IAAI,KAAK;EACxC,OAAOO,iBAAiB,CAACsB,KAAK,EAAE7B,IAAI,EAAE,gBAAgB,CAAC;AAC3D,CAAC;AAED,OAAO,MAAM8B,cAAc,GAAGA,CAACC,QAAQ,EAAE/B,IAAI,KAAK;EAC9C,OAAOO,iBAAiB,CAACwB,QAAQ,EAAE/B,IAAI,EAAE,gBAAgB,CAAC;AAC9D,CAAC;AAED,OAAO,MAAMgC,WAAW,GAAGA,CAACC,KAAK,EAAEjC,IAAI,KAAK;EACxC;EACA,IAAI,CAACA,IAAI,IAAI,CAACU,KAAK,CAACC,OAAO,CAACsB,KAAK,CAAC,EAAE;IAChC,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAAClC,SAAS,CAACC,IAAI,CAAC,EAAE;IAClB,OAAOiC,KAAK;EAChB;;EAEA;EACA,OAAOA,KAAK,CAACrB,MAAM,CAACsB,SAAS,IAAI;IAC7B,MAAMC,aAAa,GAAGD,SAAS,CAACE,SAAS,KAAKpC,IAAI,CAACoC,SAAS;IAC5D,MAAMC,aAAa,GAAGH,SAAS,CAACI,SAAS,KAAKtC,IAAI,CAACsC,SAAS;IAC5D,MAAMC,eAAe,GAAGL,SAAS,CAACnB,cAAc,KAAKf,IAAI,CAACgB,KAAK;IAE/D,OAAOmB,aAAa,IAAIE,aAAa,IAAIE,eAAe;EAC5D,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEzC,IAAI,KAAK;EAC9C;EACA,IAAI,CAACA,IAAI,IAAI,CAACU,KAAK,CAACC,OAAO,CAAC8B,QAAQ,CAAC,EAAE;IACnC,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAAC1C,SAAS,CAACC,IAAI,CAAC,EAAE;IAClB,OAAOyC,QAAQ;EACnB;;EAEA;EACA,OAAOA,QAAQ,CAAC7B,MAAM,CAAC8B,OAAO,IAAI;IAC9B,MAAMC,cAAc,GAAGD,OAAO,CAACE,UAAU,KAAK5C,IAAI,CAAC4C,UAAU;IAC7D,MAAMC,iBAAiB,GAAGH,OAAO,CAACI,WAAW,KAAK9C,IAAI,CAAC8C,WAAW;IAElE,OAAOH,cAAc,IAAIE,iBAAiB;EAC9C,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAME,aAAa,GAAGA,CAACC,OAAO,EAAEhD,IAAI,KAAK;EAC5C;EACA,IAAI,CAACA,IAAI,IAAI,CAACU,KAAK,CAACC,OAAO,CAACqC,OAAO,CAAC,EAAE;IAClC,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAACjD,SAAS,CAACC,IAAI,CAAC,EAAE;IAClB,OAAOgD,OAAO;EAClB;;EAEA;EACA,OAAOA,OAAO,CAACpC,MAAM,CAACqC,MAAM,IAAI;IAC5B,MAAMd,aAAa,GAAGc,MAAM,CAACb,SAAS,KAAKpC,IAAI,CAACoC,SAAS;IACzD,MAAMC,aAAa,GAAGY,MAAM,CAACX,SAAS,KAAKtC,IAAI,CAACsC,SAAS;IACzD,MAAMK,cAAc,GAAGM,MAAM,CAACL,UAAU,KAAK5C,IAAI,CAAC4C,UAAU;IAE5D,OAAOT,aAAa,IAAIE,aAAa,IAAIM,cAAc;EAC3D,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMO,oBAAoB,GAAGA,CAACC,OAAO,EAAEnD,IAAI,KAAK;EACnD;EACA,IAAI,CAACA,IAAI,IAAI,CAACU,KAAK,CAACC,OAAO,CAACwC,OAAO,CAAC,EAAE;IAClC,OAAO,EAAE;EACb;;EAEA;EACA,IAAI,CAACpD,SAAS,CAACC,IAAI,CAAC,EAAE;IAClB,OAAOmD,OAAO;EAClB;;EAEA;EACA,OAAOA,OAAO,CAACvC,MAAM,CAACwC,MAAM,IAAI;IAC5B,MAAMjB,aAAa,GAAGiB,MAAM,CAAChB,SAAS,KAAKpC,IAAI,CAACoC,SAAS;IACzD,MAAMC,aAAa,GAAGe,MAAM,CAACd,SAAS,KAAKtC,IAAI,CAACsC,SAAS;IAEzD,OAAOH,aAAa,IAAIE,aAAa;EACzC,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMgB,gBAAgB,GAAGA,CAACC,MAAM,EAAEtD,IAAI,EAAEQ,IAAI,KAAK;EACpD,MAAM+C,QAAQ,GAAG;IACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCJ,MAAM;IACNtD,IAAI,EAAE;MACFmB,EAAE,EAAEnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,EAAE;MACZH,KAAK,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,KAAK;MAClBf,IAAI,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC;IAChB,CAAC;IACDO,IAAI,EAAE;MACFW,EAAE,EAAEX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,EAAE;MACZwC,IAAI,EAAEnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI;MAChBC,KAAK,EAAEpD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoD,KAAK;MAClBC,QAAQ,EAAErD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD;IACpB;EACJ,CAAC;EAEDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAER,QAAQ,CAAC;;EAE3C;EACA,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACvC;EAAA;AAER,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,OAAO,GAAG,uEAAuE,KAAK;EACpHN,OAAO,CAACO,IAAI,CAAC,6CAA6C,CAAC;EAC3DC,KAAK,CAACF,OAAO,CAAC;AAClB,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAOC,MAAM,EAAEC,KAAK,KAAK;EACnD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqBH,MAAM,EAAE,EAAE;MACxDI,OAAO,EAAE;QACL,eAAe,EAAE,UAAUH,KAAK,EAAE;QAClC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAIC,QAAQ,CAACG,EAAE,EAAE;MACb,MAAMrE,IAAI,GAAG,MAAMkE,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,OAAOtE,IAAI,CAACuE,OAAO;IACvB;IAEA,OAAO,IAAI;EACf,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZlB,OAAO,CAACkB,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;IACjF,OAAO,IAAI;EACf;AACJ,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMC,aAAa,GAAGA,CAACjF,IAAI,EAAEkF,QAAQ,EAAEC,QAAQ,KAAK;EACvD,IAAI,CAACnF,IAAI,EAAE,OAAO,KAAK;;EAEvB;EACA,IAAIM,aAAa,CAACN,IAAI,CAAC,EAAE,OAAO,IAAI;;EAEpC;EACA,IAAIK,QAAQ,CAACL,IAAI,CAAC,EAAE;IAChB,OAAOmF,QAAQ,CAACC,SAAS,KAAKpF,IAAI,CAACoF,SAAS,IACrCD,QAAQ,CAACE,kBAAkB,KAAKrF,IAAI,CAACmB,EAAE;EAClD;;EAEA;EACA,IAAIpB,SAAS,CAACC,IAAI,CAAC,EAAE;IACjB,OAAOmF,QAAQ,CAACpE,cAAc,KAAKf,IAAI,CAACgB,KAAK,IACtCmE,QAAQ,CAACjE,cAAc,KAAKlB,IAAI,CAACmB,EAAE,IACnCgE,QAAQ,CAAC5D,WAAW,KAAKvB,IAAI,CAACuB,WAAW;EACpD;EAEA,OAAO,KAAK;AAChB,CAAC;AAED,OAAO,MAAM+D,kBAAkB,GAAGA,CAACtF,IAAI,EAAEQ,IAAI,EAAE0E,QAAQ,KAAK;EACxD,IAAI,CAACnF,SAAS,CAACC,IAAI,CAAC,EAAE,OAAOQ,IAAI;EAEjC,MAAM+E,YAAY,GAAG/E,IAAI,CAACI,MAAM,CAACC,IAAI,IAAIoE,aAAa,CAACjF,IAAI,EAAEkF,QAAQ,EAAErE,IAAI,CAAC,CAAC;EAE7E,IAAI0E,YAAY,CAACC,MAAM,KAAKhF,IAAI,CAACgF,MAAM,EAAE;IACrCnC,gBAAgB,CAAC,GAAG6B,QAAQ,CAACO,WAAW,CAAC,CAAC,kBAAkB,EAAEzF,IAAI,EAAE;MAChE2D,IAAI,EAAEuB,QAAQ;MACdtB,KAAK,EAAEpD,IAAI,CAACgF,MAAM;MAClB3B,QAAQ,EAAE0B,YAAY,CAACC;IAC3B,CAAC,CAAC;EACN;EAEA,OAAOD,YAAY;AACvB,CAAC;;AAED;AACA,eAAe;EACXxF,SAAS;EACTI,OAAO;EACPC,SAAS;EACTC,QAAQ;EACRC,aAAa;EACbC,iBAAiB;EACjBiB,cAAc;EACdE,aAAa;EACbE,WAAW;EACXE,cAAc;EACdE,WAAW;EACXQ,cAAc;EACdO,aAAa;EACbG,oBAAoB;EACpBG,gBAAgB;EAChBc,iBAAiB;EACjBI,cAAc;EACdU,aAAa;EACbK;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}