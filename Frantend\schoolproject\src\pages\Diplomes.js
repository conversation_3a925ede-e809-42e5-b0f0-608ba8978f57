import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import {
    filterDiplomes,
    canManageData,
    isStudent,
    logSecurityEvent
} from '../utils/studentDataFilter';
import '../css/Animations.css';
import '../css/Factures.css'; // Réutilisation des styles

const Diplomes = () => {
    const { user } = useContext(AuthContext);
    const [diplomes, setDiplomes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingDiplome, setEditingDiplome] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        titre: '',
        date_obtention: ''
    });

    useEffect(() => {
        fetchDiplomes();
        if (user?.role === 'Admin') {
            fetchEtudiants();
        }
    }, []);

    const fetchDiplomes = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setDiplomes(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des diplômes:', error);
            Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);

            if (response.data.success) {
                setEtudiants(response.data.etudiants);
                console.log('✅ Étudiants chargés:', response.data.etudiants.length);
            } else {
                console.error('❌ Erreur API étudiants:', response.data.error);
                setEtudiants([]);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des étudiants:', error);
            setEtudiants([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';
            const method = editingDiplome ? 'PUT' : 'POST';
            const data = editingDiplome ? { ...formData, id: editingDiplome.id } : formData;

            await axios({
                method,
                url,
                data,
                headers: { Authorization: `Bearer ${token}` }
            });

            Swal.fire('Succès', `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`, 'success');
            setShowModal(false);
            setEditingDiplome(null);
            resetForm();
            fetchDiplomes();
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (diplome) => {
        setEditingDiplome(diplome);
        setFormData({
            etudiant_id: diplome.etudiant_id,
            titre: diplome.titre,
            date_obtention: diplome.date_obtention
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {
                    headers: { Authorization: `Bearer ${token}` },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');
                fetchDiplomes();
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            titre: '',
            date_obtention: ''
        });
    };

    // FILTRAGE DE SÉCURITÉ : Les étudiants ne voient que leurs propres diplômes
    const securityFilteredDiplomes = filterDiplomes(diplomes, user);

    // Log de sécurité si des données ont été filtrées
    if (isStudent(user) && securityFilteredDiplomes.length !== diplomes.length) {
        logSecurityEvent('DIPLOME_ACCESS_FILTERED', user, {
            total: diplomes.length,
            filtered: securityFilteredDiplomes.length
        });
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des diplômes...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>{isStudent(user) ? 'Mes Diplômes' : 'Gestion des Diplômes'}</h1>
                {canManageData(user) && (
                    <button
                        className="btn btn-primary"
                        onClick={() => setShowModal(true)}
                    >
                        <img src="/plus.png" alt="Ajouter" /> Nouveau Diplôme
                    </button>
                )}
            </div>

            <div className="factures-grid">
                {diplomes.length === 0 ? (
                    <div className="no-data">
                        <img src="/result.png" alt="Aucun diplôme" />
                        <p>Aucun diplôme trouvé</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>Étudiant</th>
                                    <th>Titre du diplôme</th>
                                    <th>Date d'obtention</th>
                                    {canManageData(user) && <th>Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {securityFilteredDiplomes.map((diplome) => (
                                    <tr key={diplome.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{diplome.etudiant_nom}</strong>
                                                <small>{diplome.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{diplome.titre}</strong>
                                        </td>
                                        <td>
                                            {new Date(diplome.date_obtention).toLocaleDateString('fr-FR')}
                                        </td>
                                        {canManageData(user) && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(diplome)}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(diplome.id)}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Modal pour ajouter/modifier un diplôme */}
            {showModal && user?.role === 'Admin' && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'}</h3>
                            <button 
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingDiplome(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingDiplome}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.etudiant_id || etudiant.id} value={etudiant.etudiant_id || etudiant.id}>
                                            {etudiant.nom} {etudiant.prenom ? ` ${etudiant.prenom}` : ''} - {etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe'}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Titre du diplôme</label>
                                <input
                                    type="text"
                                    value={formData.titre}
                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}
                                    placeholder="Ex: Licence en Informatique"
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Date d'obtention</label>
                                <input
                                    type="date"
                                    value={formData.date_obtention}
                                    onChange={(e) => setFormData({...formData, date_obtention: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingDiplome ? 'Modifier' : 'Créer'}
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingDiplome(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Diplomes;
