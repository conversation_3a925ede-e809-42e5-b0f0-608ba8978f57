<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page Utilisateurs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .user-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Test de la Page Utilisateurs</h1>
            <p>Diagnostic complet de la fonctionnalité utilisateurs</p>
        </div>

        <?php
        // Test 1: Connexion à la base de données
        echo "<div class='test-section'>";
        echo "<h3>🔌 Test 1: Connexion à la base de données</h3>";
        try {
            $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Erreur de connexion : " . htmlspecialchars($e->getMessage()) . "</div>";
            exit();
        }
        echo "</div>";

        // Test 2: Vérification des tables
        echo "<div class='test-section'>";
        echo "<h3>📋 Test 2: Vérification des tables</h3>";
        $tables = ['Utilisateurs', 'Roles', 'Parents', 'Etudiants', 'Enseignants'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<div class='success'>✅ Table $table : {$result['count']} enregistrement(s)</div>";
            } catch (PDOException $e) {
                echo "<div class='error'>❌ Erreur table $table : " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
        echo "</div>";

        // Test 3: Test de l'API getUsers.php
        echo "<div class='test-section'>";
        echo "<h3>🌐 Test 3: API getUsers.php</h3>";
        
        $apiUrl = 'http://localhost/Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true';
        echo "<p><strong>URL testée:</strong> $apiUrl</p>";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            echo "<div class='error'>❌ Impossible d'accéder à l'API</div>";
            echo "<p>Vérifiez que le serveur web fonctionne et que l'URL est correcte.</p>";
        } else {
            $data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['success']) && $data['success']) {
                    echo "<div class='success'>✅ API fonctionne correctement</div>";
                    echo "<p>Nombre d'utilisateurs retournés: " . count($data['users']) . "</p>";
                } else {
                    echo "<div class='error'>❌ API retourne une erreur: " . ($data['error'] ?? 'Erreur inconnue') . "</div>";
                }
            } else {
                echo "<div class='error'>❌ Réponse API invalide (JSON malformé)</div>";
                echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
            }
        }
        echo "</div>";

        // Test 4: Affichage des utilisateurs
        echo "<div class='test-section'>";
        echo "<h3>👥 Test 4: Liste des utilisateurs</h3>";
        try {
            $stmt = $pdo->prepare("
                SELECT 
                    u.id,
                    u.nom,
                    u.email,
                    r.nom as role_nom
                FROM Utilisateurs u
                LEFT JOIN Roles r ON u.role_id = r.id
                ORDER BY u.nom
                LIMIT 10
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($users) > 0) {
                echo "<div class='success'>✅ " . count($users) . " utilisateur(s) trouvé(s)</div>";
                foreach ($users as $user) {
                    echo "<div class='user-card'>";
                    echo "<div>";
                    echo "<strong>{$user['nom']}</strong><br>";
                    echo "<small>{$user['email']} - {$user['role_nom']}</small>";
                    echo "</div>";
                    echo "<div>";
                    echo "<span class='btn btn-primary'>ID: {$user['id']}</span>";
                    echo "</div>";
                    echo "</div>";
                }
            } else {
                echo "<div class='error'>❌ Aucun utilisateur trouvé</div>";
            }
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Erreur lors de la récupération : " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        echo "</div>";

        // Test 5: Liens de navigation
        echo "<div class='test-section info'>";
        echo "<h3>🔗 Test 5: Navigation</h3>";
        echo "<p>Testez les liens suivants :</p>";
        echo "<a href='http://localhost:3000/utilisateurs' class='btn btn-primary' target='_blank'>🚀 Page React Utilisateurs</a>";
        echo "<a href='http://localhost:3000/login' class='btn btn-success' target='_blank'>🔐 Page de Connexion</a>";
        echo "<a href='getUsers.php?detailed=true' class='btn btn-danger' target='_blank'>📊 API Directe</a>";
        echo "</div>";

        // Instructions
        echo "<div class='test-section info'>";
        echo "<h3>💡 Instructions de dépannage</h3>";
        echo "<ol>";
        echo "<li><strong>Si l'API ne fonctionne pas :</strong> Vérifiez que le serveur Apache/Nginx est démarré</li>";
        echo "<li><strong>Si pas d'utilisateurs :</strong> Créez des utilisateurs via la page d'inscription</li>";
        echo "<li><strong>Si erreur de connexion :</strong> Vérifiez les paramètres de base de données</li>";
        echo "<li><strong>Si page React ne charge pas :</strong> Vérifiez que le serveur React (npm start) est démarré</li>";
        echo "<li><strong>Si erreur de permissions :</strong> Connectez-vous avec un compte admin/responsable</li>";
        echo "</ol>";
        echo "</div>";
        ?>
    </div>
</body>
</html>
