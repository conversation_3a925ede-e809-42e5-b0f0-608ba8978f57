<?php

// Autoriser les requêtes CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type");
header("Access-Control-Allow-Methods: PUT, OPTIONS");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'PUT') {
    // Récupérer les données JSON
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        echo json_encode(['error' => 'Données JSON invalides']);
        exit();
    }

    $userId = isset($data['id']) ? intval($data['id']) : null;
    $nom = isset($data['nom']) ? trim($data['nom']) : null;
    $email = isset($data['email']) ? trim($data['email']) : null;
    $telephone = isset($data['telephone']) ? trim($data['telephone']) : null;
    $adresse = isset($data['adresse']) ? trim($data['adresse']) : null;

    if (!$userId || !$nom || !$email) {
        echo json_encode(['error' => 'ID utilisateur, nom et email sont requis']);
        exit();
    }

    try {
        $pdo->beginTransaction();

        // Mettre à jour les informations de base de l'utilisateur
        $stmt = $pdo->prepare("
            UPDATE Utilisateurs 
            SET nom = :nom, email = :email 
            WHERE id = :id
        ");
        $stmt->execute([
            'nom' => $nom,
            'email' => $email,
            'id' => $userId
        ]);

        // Vérifier si l'utilisateur est un parent pour mettre à jour ses informations spécifiques
        $parentStmt = $pdo->prepare("SELECT id FROM Parents WHERE utilisateur_id = :user_id");
        $parentStmt->execute(['user_id' => $userId]);
        $parent = $parentStmt->fetch(PDO::FETCH_ASSOC);

        if ($parent && ($telephone !== null || $adresse !== null)) {
            // Mettre à jour les informations du parent
            $updateParentStmt = $pdo->prepare("
                UPDATE Parents 
                SET telephone = :telephone, adresse = :adresse 
                WHERE utilisateur_id = :user_id
            ");
            $updateParentStmt->execute([
                'telephone' => $telephone,
                'adresse' => $adresse,
                'user_id' => $userId
            ]);
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Informations utilisateur mises à jour avec succès'
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        echo json_encode(['error' => 'Erreur lors de la mise à jour : ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}

?>
