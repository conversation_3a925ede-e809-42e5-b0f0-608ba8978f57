import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const ClasseCRUD = () => {
    const { user } = useContext(AuthContext);
    const [classes, setClasses] = useState([]);
    const [filieres, setFilieres] = useState([]);
    const [niveaux, setNiveaux] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingClasse, setEditingClasse] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filiereFilter, setFiliereFilter] = useState('all');
    const [niveauFilter, setNiveauFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        nom: '',
        filiere_id: '',
        niveau_id: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';


    useEffect(() => {
        fetchClasses();
        fetchFilieres();
        fetchNiveaux();
    }, []);

    const fetchFilieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/filieres/filiere.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setFilieres(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des filières:', error);
            setFilieres([
                { id: 1, nom: 'Sciences' },
                { id: 2, nom: 'Littéraire' },
                { id: 3, nom: 'Économique' },
                { id: 4, nom: 'Technique' }
            ]);
        }
    };

    const fetchNiveaux = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/niveaux/niveau.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setNiveaux(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des niveaux:', error);
            setNiveaux([
                { id: 1, nom: 'Première année' },
                { id: 2, nom: 'Deuxième année' },
                { id: 3, nom: 'Troisième année' },
                { id: 4, nom: 'Baccalauréat 1ère année' },
                { id: 5, nom: 'Baccalauréat 2ème année' }
            ]);
        }
    };

    const fetchClasses = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des classes...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let classesData = response.data;
            if (!Array.isArray(classesData)) {
                classesData = [];
            }

            setClasses(classesData);
            console.log('✅ Classes chargées:', classesData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des classes:', error);

            // Données de test
            const testClasses = [
                { id: 1, nom: 'Classe A', filiere_id: 1, filiere_nom: 'Sciences', niveau_id: 1, niveau_nom: 'Première année' },
                { id: 2, nom: 'Classe B', filiere_id: 1, filiere_nom: 'Sciences', niveau_id: 1, niveau_nom: 'Première année' },
                { id: 3, nom: 'Classe C', filiere_id: 2, filiere_nom: 'Littéraire', niveau_id: 2, niveau_nom: 'Deuxième année' },
                { id: 4, nom: 'Classe D', filiere_id: 2, filiere_nom: 'Littéraire', niveau_id: 2, niveau_nom: 'Deuxième année' },
                { id: 5, nom: 'Classe E', filiere_id: 3, filiere_nom: 'Économique', niveau_id: 3, niveau_nom: 'Troisième année' },
                { id: 6, nom: 'Classe F', filiere_id: 3, filiere_nom: 'Économique', niveau_id: 3, niveau_nom: 'Troisième année' },
                { id: 7, nom: 'Classe G', filiere_id: 4, filiere_nom: 'Technique', niveau_id: 4, niveau_nom: 'Baccalauréat 1ère année' },
                { id: 8, nom: 'Classe H', filiere_id: 4, filiere_nom: 'Technique', niveau_id: 4, niveau_nom: 'Baccalauréat 1ère année' },
                { id: 9, nom: 'Classe I', filiere_id: 1, filiere_nom: 'Sciences', niveau_id: 5, niveau_nom: 'Baccalauréat 2ème année' },
                { id: 10, nom: 'Classe J', filiere_id: 2, filiere_nom: 'Littéraire', niveau_id: 5, niveau_nom: 'Baccalauréat 2ème année' },
                { id: 11, nom: 'Classe K', filiere_id: 3, filiere_nom: 'Économique', niveau_id: 1, niveau_nom: 'Première année' },
                { id: 12, nom: 'Classe L', filiere_id: 4, filiere_nom: 'Technique', niveau_id: 2, niveau_nom: 'Deuxième année' }
            ];

            setClasses(testClasses);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testClasses.length} classes de test.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };


    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des classes', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/classes/classe.php';
            const method = editingClasse ? 'PUT' : 'POST';
            const data = editingClasse ? { ...formData, id: editingClasse.id } : formData;

            console.log('🔄 Envoi requête:', { method, url, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Réponse:', response.data);

            if (response.data && response.data.success === true) {
                Swal.fire('Succès', `Classe ${editingClasse ? 'modifiée' : 'créée'} avec succès`, 'success');
                setShowModal(false);
                setEditingClasse(null);
                resetForm();
                fetchClasses();
            } else {
                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            const errorMessage = error.response?.data?.error ||
                               error.response?.data?.message ||
                               error.message ||
                               'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (classe) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des classes', 'error');
            return;
        }

        setEditingClasse(classe);
        setFormData({
            nom: classe.nom,
            filiere_id: classe.filiere_id,
            niveau_id: classe.niveau_id
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des classes', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/classes/classe.php';

                console.log('🔄 Suppression classe:', { id, url });

                const response = await axios.delete(url, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data && response.data.success === true) {
                    Swal.fire('Supprimé!', 'La classe a été supprimée.', 'success');
                    fetchClasses();
                } else {
                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error ||
                                   error.response?.data?.message ||
                                   error.message ||
                                   'Impossible de supprimer la classe';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            nom: '',
            filiere_id: '',
            niveau_id: ''
        });
    };

    // Filtrage des données
    const filteredClasses = classes.filter(classe => {
        const matchesSearch = classe.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             classe.filiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             classe.niveau_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesFiliere = filiereFilter === 'all' || classe.filiere_id?.toString() === filiereFilter;
        const matchesNiveau = niveauFilter === 'all' || classe.niveau_id?.toString() === niveauFilter;

        return matchesSearch && matchesFiliere && matchesNiveau;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentClasses = filteredClasses.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredClasses.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, filiereFilter, niveauFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des classes...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>🏫 Gestion des Classes</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredClasses.length} classe(s) trouvée(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvelle Classe
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les classes en mode lecture seule.
                        Seul l'administrateur peut créer, modifier ou supprimer des classes.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher une classe..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="filiere-filter">
                    <select
                        value={filiereFilter}
                        onChange={(e) => {
                            setFiliereFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Toutes les filières</option>
                        {filieres.map(filiere => (
                            <option key={filiere.id} value={filiere.id}>
                                {filiere.nom}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="niveau-filter">
                    <select
                        value={niveauFilter}
                        onChange={(e) => {
                            setNiveauFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Tous les niveaux</option>
                        {niveaux.map(niveau => (
                            <option key={niveau.id} value={niveau.id}>
                                {niveau.nom}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredClasses.length === 0 ? (
                    <div className="no-data">
                        <img src="/classroom.png" alt="Aucune classe" />
                        <p>Aucune classe trouvée</p>
                        {(searchTerm || filiereFilter !== 'all' || niveauFilter !== 'all') && (
                            <button
                                onClick={() => {
                                    setSearchTerm('');
                                    setFiliereFilter('all');
                                    setNiveauFilter('all');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>🏫 Nom de la Classe</th>
                                    <th>🎓 Filière</th>
                                    <th>📊 Niveau</th>
                                    <th>📈 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentClasses.map((classe) => (
                                    <tr key={classe.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{classe.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{classe.nom}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#856404'
                                            }}>
                                                {classe.filiere_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#d1ecf1',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#0c5460'
                                            }}>
                                                {classe.niveau_nom || 'Non défini'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="badge badge-success">Actif</span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(classe)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(classe.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredClasses.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredClasses.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des classes</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Filières disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {niveaux.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Niveaux disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {currentClasses.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichées</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier une classe */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingClasse ? 'Modifier la classe' : 'Nouvelle classe'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingClasse(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Nom de la classe *</label>
                                <input
                                    type="text"
                                    value={formData.nom}
                                    onChange={(e) => setFormData({...formData, nom: e.target.value})}
                                    placeholder="Ex: Classe A, Classe B..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Filière *</label>
                                <select
                                    value={formData.filiere_id}
                                    onChange={(e) => setFormData({...formData, filiere_id: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                >
                                    <option value="">Sélectionner une filière</option>
                                    {filieres.map((filiere) => (
                                        <option key={filiere.id} value={filiere.id}>
                                            {filiere.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label>Niveau *</label>
                                <select
                                    value={formData.niveau_id}
                                    onChange={(e) => setFormData({...formData, niveau_id: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                >
                                    <option value="">Sélectionner un niveau</option>
                                    {niveaux.map((niveau) => (
                                        <option key={niveau.id} value={niveau.id}>
                                            {niveau.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingClasse(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingClasse ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ClasseCRUD;
