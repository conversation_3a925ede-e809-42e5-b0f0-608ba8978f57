<?php
// Test de l'API étudiants
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

echo "<h1>🧪 Test API Étudiants</h1>";

try {
    // Connexion DB
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connexion DB réussie</p>";

    // Test de la requête
    $stmt = $pdo->query("
        SELECT 
            e.id,
            e.utilisateur_id,
            e.groupe_id,
            u.nom,
            u.email,
            g.nom as groupe_nom
        FROM etudiants e
        LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
        LEFT JOIN groupes g ON e.groupe_id = g.id
        ORDER BY e.id DESC
    ");
    
    $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ Requête exécutée avec succès</p>";
    echo "<p><strong>Nombre d'étudiants trouvés:</strong> " . count($etudiants) . "</p>";
    
    if (count($etudiants) > 0) {
        echo "<h2>📚 Premiers étudiants (avant transformation):</h2>";
        echo "<pre>" . json_encode(array_slice($etudiants, 0, 3), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
        // Transformation comme dans l'API
        foreach ($etudiants as &$etudiant) {
            $etudiant['etudiant_id'] = $etudiant['id'];
            $etudiant['prenom'] = ''; // Champ vide car pas dans la DB
            $etudiant['classe_nom'] = $etudiant['groupe_nom']; // Utiliser groupe comme classe
        }
        
        echo "<h2>📚 Premiers étudiants (après transformation):</h2>";
        echo "<pre>" . json_encode(array_slice($etudiants, 0, 3), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
        $response = [
            'success' => true,
            'etudiants' => $etudiants,
            'total' => count($etudiants)
        ];
        
        echo "<h2>🔄 Réponse API finale:</h2>";
        echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p style='color: orange;'>⚠️ Aucun étudiant trouvé dans la base de données</p>";
        
        // Vérifier les tables
        echo "<h2>🔍 Vérification des tables:</h2>";
        
        $tables = ['utilisateurs', 'etudiants', 'groupes'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                echo "<p><strong>$table:</strong> $count enregistrement(s)</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'><strong>$table:</strong> Erreur - " . $e->getMessage() . "</p>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur DB: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur générale: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='etudiant.php'>🔗 Tester l'API principale</a></p>";
echo "<p><a href='../../../Frantend/schoolproject/public/index.html'>🏠 Retour au frontend</a></p>";
?>
