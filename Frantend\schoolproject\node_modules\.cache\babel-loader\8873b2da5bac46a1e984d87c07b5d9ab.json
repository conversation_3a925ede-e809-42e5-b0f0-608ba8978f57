{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\Cours.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { filterCours, canManageData, isStudent, logSecurityEvent } from '../utils/studentDataFilter';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\nconst CoursCRUD = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [cours, setCours] = useState([]);\n  const [matieres, setMatieres] = useState([]);\n  const [classes, setClasses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingCours, setEditingCours] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [matiereFilter, setMatiereFilter] = useState('all');\n  const [classeFilter, setClasseFilter] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    titre: '',\n    description: '',\n    fichier_pdf: null,\n    date_publication: '',\n    matiere_id: '',\n    classe_id: ''\n  });\n\n  // Vérifier si l'utilisateur est Admin ou Enseignant avec notre système unifié\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'responsable';\n  const isTeacher = (user === null || user === void 0 ? void 0 : user.role) === 'Enseignant' || (user === null || user === void 0 ? void 0 : user.role) === 'enseignant' || (user === null || user === void 0 ? void 0 : user.role) === 'teacher';\n  const canManage = canManageData(user);\n  useEffect(() => {\n    fetchCours();\n    fetchMatieres();\n    fetchClasses();\n  }, []);\n  const fetchMatieres = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setMatieres(Array.isArray(response.data) ? response.data : []);\n    } catch (error) {\n      console.error('Erreur lors du chargement des matières:', error);\n      setMatieres([{\n        id: 1,\n        nom: 'Mathématiques'\n      }, {\n        id: 2,\n        nom: 'Physique'\n      }, {\n        id: 3,\n        nom: 'Français'\n      }, {\n        id: 4,\n        nom: 'Histoire'\n      }]);\n    }\n  };\n  const fetchClasses = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      setClasses(Array.isArray(response.data) ? response.data : []);\n    } catch (error) {\n      console.error('Erreur lors du chargement des classes:', error);\n      setClasses([{\n        id: 1,\n        nom: 'Classe A'\n      }, {\n        id: 2,\n        nom: 'Classe B'\n      }, {\n        id: 3,\n        nom: 'Classe C'\n      }, {\n        id: 4,\n        nom: 'Classe D'\n      }]);\n    }\n  };\n  const fetchCours = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des cours...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/cours/cours.php', {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      let coursData = response.data;\n      if (!Array.isArray(coursData)) {\n        coursData = [];\n      }\n      setCours(coursData);\n      console.log('✅ Cours chargés:', coursData.length, 'éléments');\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des cours:', error);\n\n      // Données de test avec fichiers PDF (compatible avec structure BDD réelle)\n      const testCours = [{\n        id: 1,\n        titre: 'Introduction aux Mathématiques',\n        description: 'Cours de base en mathématiques',\n        fichier_pdf: 'math_intro.pdf',\n        date_ajout: '2024-01-15',\n        date_publication: '2024-01-15',\n        // Pour compatibilité\n        matiere_id: 1,\n        matiere_nom: 'Mathématiques',\n        classe_id: 1,\n        // Valeur par défaut\n        classe_nom: 'Toutes classes',\n        // Valeur par défaut\n        taille_fichier: '2.5 MB'\n      }, {\n        id: 2,\n        titre: 'Les Forces en Physique',\n        description: 'Étude des forces et du mouvement',\n        fichier_pdf: 'physique_forces.pdf',\n        date_ajout: '2024-01-20',\n        date_publication: '2024-01-20',\n        matiere_id: 2,\n        matiere_nom: 'Physique',\n        classe_id: 1,\n        classe_nom: 'Toutes classes',\n        taille_fichier: '3.2 MB'\n      }, {\n        id: 3,\n        titre: 'Grammaire Française',\n        description: 'Les règles de grammaire essentielles',\n        fichier_pdf: 'francais_grammaire.pdf',\n        date_ajout: '2024-01-25',\n        date_publication: '2024-01-25',\n        matiere_id: 3,\n        matiere_nom: 'Français',\n        classe_id: 1,\n        classe_nom: 'Toutes classes',\n        taille_fichier: '1.8 MB'\n      }, {\n        id: 4,\n        titre: 'Histoire Moderne',\n        description: 'Les événements du 20ème siècle',\n        fichier_pdf: 'histoire_moderne.pdf',\n        date_ajout: '2024-02-01',\n        date_publication: '2024-02-01',\n        matiere_id: 4,\n        matiere_nom: 'Histoire',\n        classe_id: 1,\n        classe_nom: 'Toutes classes',\n        taille_fichier: '4.1 MB'\n      }, {\n        id: 5,\n        titre: 'Algèbre Avancée',\n        description: 'Concepts avancés en algèbre',\n        fichier_pdf: 'math_algebre.pdf',\n        date_ajout: '2024-02-05',\n        date_publication: '2024-02-05',\n        matiere_id: 1,\n        matiere_nom: 'Mathématiques',\n        classe_id: 1,\n        classe_nom: 'Toutes classes',\n        taille_fichier: '3.7 MB'\n      }, {\n        id: 6,\n        titre: 'Optique et Lumière',\n        description: 'Propriétés de la lumière',\n        fichier_pdf: 'physique_optique.pdf',\n        date_ajout: '2024-02-10',\n        date_publication: '2024-02-10',\n        matiere_id: 2,\n        matiere_nom: 'Physique',\n        classe_id: 1,\n        classe_nom: 'Toutes classes',\n        taille_fichier: '2.9 MB'\n      }];\n      setCours(testCours);\n      Swal.fire({\n        title: '🧪 Mode Test',\n        text: `Connexion API échouée. Utilisation de ${testCours.length} cours de test avec fichiers PDF.`,\n        icon: 'info',\n        timer: 3000,\n        showConfirmButton: false\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent créer/modifier des cours', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\n\n      // Validation côté frontend\n      if (!formData.titre.trim()) {\n        Swal.fire('Erreur', 'Le titre du cours est requis', 'error');\n        return;\n      }\n      if (!formData.matiere_id) {\n        Swal.fire('Erreur', 'Veuillez sélectionner une matière', 'error');\n        return;\n      }\n      if (!formData.classe_id) {\n        Swal.fire('Erreur', 'Veuillez sélectionner une classe', 'error');\n        return;\n      }\n      if (!formData.date_publication) {\n        Swal.fire('Erreur', 'Veuillez sélectionner une date de publication', 'error');\n        return;\n      }\n\n      // Validation du fichier PDF : requis seulement pour la création\n      if (!editingCours && !formData.fichier_pdf) {\n        Swal.fire('Erreur', 'Veuillez sélectionner un fichier PDF pour créer un nouveau cours', 'error');\n        return;\n      }\n\n      // Pour la modification, le fichier PDF est optionnel\n      if (editingCours && formData.fichier_pdf) {\n        console.log('📁 Nouveau fichier PDF sélectionné pour la modification:', formData.fichier_pdf.name);\n      } else if (editingCours && !formData.fichier_pdf) {\n        console.log('📁 Aucun nouveau fichier PDF - conservation du fichier existant');\n      }\n\n      // Validation de l'ID pour la modification\n      if (editingCours && (!editingCours.id || isNaN(editingCours.id))) {\n        Swal.fire('Erreur', 'ID du cours invalide pour la modification', 'error');\n        return;\n      }\n\n      // Créer FormData pour gérer l'upload de fichier\n      const formDataToSend = new FormData();\n      formDataToSend.append('titre', formData.titre.trim());\n      formDataToSend.append('description', formData.description.trim());\n      formDataToSend.append('matiere_id', formData.matiere_id);\n      formDataToSend.append('classe_id', formData.classe_id);\n      formDataToSend.append('date_publication', formData.date_publication);\n      if (editingCours) {\n        formDataToSend.append('id', editingCours.id);\n        formDataToSend.append('_method', 'PUT'); // Champ caché pour identifier PUT\n      }\n      if (formData.fichier_pdf) {\n        formDataToSend.append('fichier_pdf', formData.fichier_pdf);\n      }\n\n      // Logs détaillés pour debug\n      console.log('🔄 Envoi requête cours:', {\n        method: editingCours ? 'PUT (via POST)' : 'POST',\n        url,\n        titre: formData.titre,\n        matiere_id: formData.matiere_id,\n        classe_id: formData.classe_id,\n        date_publication: formData.date_publication,\n        editingId: editingCours === null || editingCours === void 0 ? void 0 : editingCours.id,\n        hasFile: !!formData.fichier_pdf,\n        fileSize: formData.fichier_pdf ? formData.fichier_pdf.size : 0,\n        token: token ? 'présent' : 'absent'\n      });\n\n      // Afficher le contenu du FormData pour debug\n      console.log('📦 FormData contents:');\n      for (let [key, value] of formDataToSend.entries()) {\n        if (value instanceof File) {\n          console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);\n        } else {\n          console.log(`  ${key}: ${value}`);\n        }\n      }\n\n      // Pour les modifications, utiliser POST avec _method=PUT car FormData ne fonctionne pas bien avec PUT\n      const response = await axios({\n        method: 'POST',\n        url,\n        data: formDataToSend,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'multipart/form-data'\n        },\n        timeout: 30000 // 30 secondes pour l'upload\n      });\n      console.log('✅ Réponse complète:', {\n        status: response.status,\n        data: response.data,\n        headers: response.headers\n      });\n      if (response.data && response.data.success === true) {\n        Swal.fire('Succès', `Cours ${editingCours ? 'modifié' : 'créé'} avec succès`, 'success');\n        setShowModal(false);\n        setEditingCours(null);\n        resetForm();\n        fetchCours();\n      } else {\n        var _response$data, _response$data2;\n        const errorMsg = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error) || ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || 'Erreur inconnue';\n        throw new Error(errorMsg);\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$config, _error$config2, _error$config3, _error$response3, _error$response3$data, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('❌ Erreur complète:', {\n        message: error.message,\n        response: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data,\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status,\n        config: {\n          method: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.method,\n          url: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url,\n          data: (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.data\n        }\n      });\n      let errorMessage = 'Une erreur est survenue';\n      if ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) {\n        errorMessage = error.response.data.error;\n      } else if ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) {\n        errorMessage = error.response.data.message;\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 413) {\n        errorMessage = 'Le fichier est trop volumineux (max 10MB)';\n      } else if (((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.status) === 415) {\n        errorMessage = 'Type de fichier non supporté (PDF uniquement)';\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      Swal.fire('Erreur', errorMessage, 'error');\n    }\n  };\n  const handleEdit = cours => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent modifier des cours', 'error');\n      return;\n    }\n\n    // Vérifier que le cours est valide\n    if (!cours || !cours.id) {\n      Swal.fire('Erreur', 'Cours invalide', 'error');\n      return;\n    }\n    console.log('✏️ Édition cours:', {\n      id: cours.id,\n      titre: cours.titre,\n      matiere_id: cours.matiere_id,\n      classe_id: cours.classe_id,\n      date_publication: cours.date_publication\n    });\n    setEditingCours(cours);\n    setFormData({\n      titre: cours.titre || '',\n      description: cours.description || '',\n      fichier_pdf: null,\n      // Ne pas pré-remplir le fichier\n      date_publication: cours.date_publication || '',\n      matiere_id: cours.matiere_id || '',\n      classe_id: cours.classe_id || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!canManage) {\n      Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent supprimer des cours', 'error');\n      return;\n    }\n\n    // Vérifier que l'ID est valide\n    if (!id || isNaN(id)) {\n      Swal.fire('Erreur', 'ID du cours invalide', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action supprimera également le fichier PDF associé!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\n        console.log('🔄 Suppression cours:', {\n          id: id,\n          type: typeof id,\n          url: url,\n          token: token ? 'présent' : 'absent'\n        });\n        const response = await axios({\n          method: 'DELETE',\n          url: url,\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: JSON.stringify({\n            id: parseInt(id)\n          }),\n          timeout: 10000\n        });\n        console.log('✅ Réponse suppression:', {\n          status: response.status,\n          data: response.data\n        });\n        if (response.data && response.data.success === true) {\n          Swal.fire('Supprimé!', 'Le cours et son fichier PDF ont été supprimés.', 'success');\n          fetchCours();\n        } else {\n          var _response$data3, _response$data4;\n          const errorMsg = ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.error) || ((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || 'Erreur lors de la suppression';\n          console.error('❌ Erreur dans la réponse:', response.data);\n          throw new Error(errorMsg);\n        }\n      } catch (error) {\n        var _error$response7, _error$response8, _error$response9, _error$response0, _error$response1, _error$response1$data, _error$response10, _error$response10$dat;\n        console.error('❌ Erreur suppression complète:', {\n          message: error.message,\n          response: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.data,\n          status: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.status,\n          id: id\n        });\n        let errorMessage = 'Impossible de supprimer le cours';\n        if (((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.status) === 404) {\n          errorMessage = 'Cours non trouvé';\n        } else if (((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status) === 403) {\n          errorMessage = 'Accès non autorisé';\n        } else if ((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.error) {\n          errorMessage = error.response.data.error;\n        } else if ((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        Swal.fire('Erreur', errorMessage, 'error');\n      }\n    }\n  };\n\n  // Fonction pour télécharger le PDF\n  const handleDownloadPDF = async cours => {\n    try {\n      var _response$headers$con;\n      const token = localStorage.getItem('token');\n\n      // Vérifier que le cours a un fichier PDF\n      if (!cours.fichier_pdf) {\n        Swal.fire('Erreur', 'Aucun fichier PDF associé à ce cours', 'error');\n        return;\n      }\n      const url = `http://localhost/Project_PFE/Backend/pages/cours/download.php?file=${encodeURIComponent(cours.fichier_pdf)}&cours_id=${cours.id}`;\n      console.log('📥 Téléchargement PDF:', {\n        titre: cours.titre,\n        fichier: cours.fichier_pdf,\n        url: url\n      });\n      const response = await axios({\n        method: 'GET',\n        url,\n        headers: {\n          Authorization: `Bearer ${token}`\n        },\n        responseType: 'blob',\n        timeout: 30000 // 30 secondes de timeout\n      });\n\n      // Vérifier que la réponse est bien un PDF\n      if (response.data.type !== 'application/pdf' && !((_response$headers$con = response.headers['content-type']) === null || _response$headers$con === void 0 ? void 0 : _response$headers$con.includes('application/pdf'))) {\n        console.error('❌ Type de fichier incorrect:', response.data.type);\n        Swal.fire('Erreur', 'Le fichier téléchargé n\\'est pas un PDF valide', 'error');\n        return;\n      }\n\n      // Créer un lien de téléchargement\n      const blob = new Blob([response.data], {\n        type: 'application/pdf'\n      });\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = `${cours.titre.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(downloadUrl);\n      console.log('✅ Téléchargement réussi:', cours.titre);\n      Swal.fire({\n        title: 'Téléchargement réussi!',\n        text: `Le fichier \"${cours.titre}.pdf\" a été téléchargé.`,\n        icon: 'success',\n        timer: 2000,\n        showConfirmButton: false\n      });\n    } catch (error) {\n      var _error$response11, _error$response12, _error$response13, _error$response14, _error$response15;\n      console.error('❌ Erreur téléchargement complète:', {\n        message: error.message,\n        response: (_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : _error$response11.data,\n        status: (_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : _error$response12.status,\n        cours: cours.titre\n      });\n      let errorMessage = 'Impossible de télécharger le fichier PDF';\n      if (((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : _error$response13.status) === 404) {\n        errorMessage = 'Fichier PDF non trouvé sur le serveur';\n      } else if (((_error$response14 = error.response) === null || _error$response14 === void 0 ? void 0 : _error$response14.status) === 403) {\n        errorMessage = 'Accès non autorisé au fichier PDF';\n      } else if (error.code === 'ECONNABORTED') {\n        errorMessage = 'Timeout - Le téléchargement a pris trop de temps';\n      } else if ((_error$response15 = error.response) === null || _error$response15 === void 0 ? void 0 : _error$response15.data) {\n        try {\n          const errorText = await error.response.data.text();\n          const errorData = JSON.parse(errorText);\n          errorMessage = errorData.error || errorMessage;\n        } catch (e) {\n          // Ignore parsing errors\n        }\n      }\n      Swal.fire('Erreur', errorMessage, 'error');\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      titre: '',\n      description: '',\n      fichier_pdf: null,\n      date_publication: '',\n      matiere_id: '',\n      classe_id: ''\n    });\n  };\n\n  // Fonction de test pour vérifier les données PUT\n  const testPUT = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/cours/test_put.php';\n\n      // Créer des données de test\n      const formDataToSend = new FormData();\n      formDataToSend.append('id', '1');\n      formDataToSend.append('titre', 'Test PUT');\n      formDataToSend.append('description', 'Test description');\n      formDataToSend.append('matiere_id', '1');\n      formDataToSend.append('classe_id', '1');\n      formDataToSend.append('date_publication', '2024-01-15');\n      console.log('🧪 Test PUT - Envoi des données...');\n      const response = await axios({\n        method: 'PUT',\n        url,\n        data: formDataToSend,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'multipart/form-data'\n        },\n        timeout: 10000\n      });\n      console.log('✅ Test PUT réussi:', response.data);\n      Swal.fire({\n        title: 'Test PUT Réussi',\n        html: `<pre>${JSON.stringify(response.data, null, 2)}</pre>`,\n        icon: 'success',\n        width: '80%'\n      });\n    } catch (error) {\n      console.error('❌ Test PUT échoué:', error);\n      Swal.fire('Erreur', 'Test PUT échoué: ' + (error.message || 'Erreur inconnue'), 'error');\n    }\n  };\n\n  // Fonction de test pour vérifier la connexion API\n  const testAPI = async () => {\n    try {\n      var _response$data5;\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\n      console.log('🧪 Test de connexion API...', {\n        url: url,\n        token: token ? 'présent' : 'absent'\n      });\n      const response = await axios.get(url, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n      console.log('✅ Test API réussi:', {\n        status: response.status,\n        dataLength: ((_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.length) || 0,\n        data: response.data\n      });\n      const coursCount = Array.isArray(response.data) ? response.data.length : 0;\n      Swal.fire('Succès', `API connectée ! ${coursCount} cours trouvés`, 'success');\n    } catch (error) {\n      var _error$response16, _error$response17, _error$response18, _error$response19;\n      console.error('❌ Test API échoué:', {\n        message: error.message,\n        response: (_error$response16 = error.response) === null || _error$response16 === void 0 ? void 0 : _error$response16.data,\n        status: (_error$response17 = error.response) === null || _error$response17 === void 0 ? void 0 : _error$response17.status,\n        code: error.code\n      });\n      let errorMessage = 'Connexion API échouée';\n      if (error.code === 'ECONNREFUSED') {\n        errorMessage = 'Serveur non accessible - Vérifiez que le serveur PHP fonctionne';\n      } else if (error.code === 'ECONNABORTED') {\n        errorMessage = 'Timeout - Le serveur met trop de temps à répondre';\n      } else if (((_error$response18 = error.response) === null || _error$response18 === void 0 ? void 0 : _error$response18.status) === 404) {\n        errorMessage = 'API non trouvée - Vérifiez l\\'URL du backend';\n      } else if (((_error$response19 = error.response) === null || _error$response19 === void 0 ? void 0 : _error$response19.status) === 500) {\n        errorMessage = 'Erreur serveur - Vérifiez les logs PHP';\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      Swal.fire('Erreur', errorMessage, 'error');\n    }\n  };\n\n  // Filtrage des données\n  const filteredCours = cours.filter(c => {\n    var _c$titre, _c$description, _c$matiere_nom, _c$classe_nom, _c$matiere_id, _c$classe_id;\n    const matchesSearch = ((_c$titre = c.titre) === null || _c$titre === void 0 ? void 0 : _c$titre.toLowerCase().includes(searchTerm.toLowerCase())) || ((_c$description = c.description) === null || _c$description === void 0 ? void 0 : _c$description.toLowerCase().includes(searchTerm.toLowerCase())) || ((_c$matiere_nom = c.matiere_nom) === null || _c$matiere_nom === void 0 ? void 0 : _c$matiere_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_c$classe_nom = c.classe_nom) === null || _c$classe_nom === void 0 ? void 0 : _c$classe_nom.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesMatiere = matiereFilter === 'all' || ((_c$matiere_id = c.matiere_id) === null || _c$matiere_id === void 0 ? void 0 : _c$matiere_id.toString()) === matiereFilter;\n    const matchesClasse = classeFilter === 'all' || ((_c$classe_id = c.classe_id) === null || _c$classe_id === void 0 ? void 0 : _c$classe_id.toString()) === classeFilter;\n    return matchesSearch && matchesMatiere && matchesClasse;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentCours = filteredCours.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredCours.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, matiereFilter, classeFilter]);\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 17\n      }\n    }, \"Chargement des cours...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 705,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCDA Gestion des Cours \"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 21\n    }\n  }, filteredCours.length, \" cours trouv\\xE9(s)\", totalPages > 1 && ` • Page ${currentPage}/${totalPages}`), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 21\n    }\n  }, canManage && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 33\n    }\n  }), \" Nouveau Cours\")))), !canManage && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez les cours en mode lecture seule. Seuls les administrateurs et enseignants peuvent cr\\xE9er, modifier ou supprimer des cours. Cliquez sur les liens PDF pour t\\xE9l\\xE9charger les cours.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    style: {\n      display: 'flex',\n      gap: '15px',\n      marginBottom: '20px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher un cours...\",\n    value: searchTerm,\n    onChange: e => {\n      setSearchTerm(e.target.value);\n      setCurrentPage(1);\n    },\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"matiere-filter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: matiereFilter,\n    onChange: e => {\n      setMatiereFilter(e.target.value);\n      setCurrentPage(1);\n    },\n    style: {\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px',\n      minWidth: '150px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 25\n    }\n  }, \"Toutes les mati\\xE8res\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 785,\n      columnNumber: 29\n    }\n  }, matiere.nom)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"classe-filter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 791,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: classeFilter,\n    onChange: e => {\n      setClasseFilter(e.target.value);\n      setCurrentPage(1);\n    },\n    style: {\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px',\n      minWidth: '150px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 25\n    }\n  }, \"Toutes les classes\"), classes.map(classe => /*#__PURE__*/React.createElement(\"option\", {\n    key: classe.id,\n    value: classe.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 807,\n      columnNumber: 29\n    }\n  }, classe.nom))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 13\n    }\n  }, filteredCours.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/pdf-icon.png\",\n    alt: \"Aucun cours\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 25\n    }\n  }, \"Aucun cours trouv\\xE9\"), (searchTerm || matiereFilter !== 'all' || classeFilter !== 'all') && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => {\n      setSearchTerm('');\n      setMatiereFilter('all');\n      setClasseFilter('all');\n    },\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 29\n    }\n  }, \"Effacer les filtres\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 836,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Titre du Cours\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCD6 Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 841,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDFEB Classe\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 842,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC4 Fichier PDF\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Taille\"), canManage && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 845,\n      columnNumber: 51\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 29\n    }\n  }, currentCours.map(c => /*#__PURE__*/React.createElement(\"tr\", {\n    key: c.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '4px',\n      fontSize: '0.9em',\n      fontWeight: 'bold'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 45\n    }\n  }, \"#\", c.id)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"student-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 49\n    }\n  }, c.titre), c.description && /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      display: 'block',\n      color: '#6c757d',\n      marginTop: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 53\n    }\n  }, c.description.length > 50 ? c.description.substring(0, 50) + '...' : c.description))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 878,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#fff3cd',\n      borderRadius: '4px',\n      fontSize: '0.9em',\n      color: '#856404'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 45\n    }\n  }, c.matiere_nom || 'Non définie')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 889,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#d4edda',\n      borderRadius: '4px',\n      fontSize: '0.9em',\n      color: '#155724'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 45\n    }\n  }, c.classe_nom || 'Non définie')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 900,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 901,\n      columnNumber: 45\n    }\n  }, new Date(c.date_publication).toLocaleDateString('fr-FR'))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-success\",\n    onClick: () => handleDownloadPDF(c),\n    title: \"T\\xE9l\\xE9charger le PDF\",\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '5px',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 45\n    }\n  }, \"\\uD83D\\uDCE5 PDF\")), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.8em',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 921,\n      columnNumber: 45\n    }\n  }, c.taille_fichier || 'N/A')), canManage && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 930,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(c),\n    title: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 931,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(c.id),\n    title: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 943,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginTop: '20px',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 958,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 965,\n      columnNumber: 21\n    }\n  }, \"\\u2B05\\uFE0F Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '8px 16px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 973,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 982,\n      columnNumber: 21\n    }\n  }, \"Suivant \\u27A1\\uFE0F\")), filteredCours.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-section\",\n    style: {\n      marginTop: '30px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 994,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#007bff',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1004,\n      columnNumber: 25\n    }\n  }, filteredCours.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1007,\n      columnNumber: 25\n    }\n  }, \"Total des cours\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1009,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#28a745',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1010,\n      columnNumber: 25\n    }\n  }, matieres.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1013,\n      columnNumber: 25\n    }\n  }, \"Mati\\xE8res disponibles\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1015,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#17a2b8',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1016,\n      columnNumber: 25\n    }\n  }, classes.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1019,\n      columnNumber: 25\n    }\n  }, \"Classes disponibles\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1021,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#ffc107',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1022,\n      columnNumber: 25\n    }\n  }, currentCours.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1025,\n      columnNumber: 25\n    }\n  }, \"Affich\\xE9s\"))), showModal && canManage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1032,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    style: {\n      maxWidth: '600px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1033,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1034,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1035,\n      columnNumber: 29\n    }\n  }, editingCours ? 'Modifier le cours' : 'Nouveau cours'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingCours(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1047,\n      columnNumber: 25\n    }\n  }, editingCours && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1050,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    style: {\n      margin: '0 0 10px 0',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDD Modification du cours #\", editingCours.id), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0',\n      fontSize: '14px',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1060,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC4 Fichier actuel: \", /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1061,\n      columnNumber: 60\n    }\n  }, editingCours.fichier_pdf || 'Aucun fichier'), editingCours.taille_fichier && ` (${editingCours.taille_fichier})`), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      fontSize: '12px',\n      color: '#666'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCA1 Laissez le champ fichier vide pour conserver le fichier actuel\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1070,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1071,\n      columnNumber: 33\n    }\n  }, \"Titre du cours *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    value: formData.titre,\n    onChange: e => setFormData({\n      ...formData,\n      titre: e.target.value\n    }),\n    placeholder: \"Ex: Introduction aux Math\\xE9matiques...\",\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1072,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1088,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1089,\n      columnNumber: 33\n    }\n  }, \"Description\"), /*#__PURE__*/React.createElement(\"textarea\", {\n    value: formData.description,\n    onChange: e => setFormData({\n      ...formData,\n      description: e.target.value\n    }),\n    placeholder: \"Description du cours...\",\n    rows: \"3\",\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      resize: 'vertical'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1090,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1106,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1107,\n      columnNumber: 33\n    }\n  }, \"Fichier PDF \", editingCours ? '(Optionnel - Laisser vide pour conserver le fichier actuel)' : '*'), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"file\",\n    accept: \".pdf\",\n    onChange: e => {\n      const file = e.target.files[0];\n      if (file) {\n        // Validation côté client\n        if (file.type !== 'application/pdf') {\n          Swal.fire('Erreur', 'Seuls les fichiers PDF sont acceptés', 'error');\n          e.target.value = '';\n          return;\n        }\n        if (file.size > 10 * 1024 * 1024) {\n          Swal.fire('Erreur', 'Le fichier ne doit pas dépasser 10MB', 'error');\n          e.target.value = '';\n          return;\n        }\n        console.log('📁 Fichier sélectionné:', {\n          name: file.name,\n          size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',\n          type: file.type\n        });\n      }\n      setFormData({\n        ...formData,\n        fichier_pdf: file\n      });\n    },\n    required: !editingCours,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1110,\n      columnNumber: 33\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1144,\n      columnNumber: 33\n    }\n  }, \"Formats accept\\xE9s: PDF uniquement. Taille max: 10MB\", editingCours && !formData.fichier_pdf && /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#17a2b8',\n      display: 'block',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1147,\n      columnNumber: 41\n    }\n  }, \"\\uD83D\\uDCC4 Fichier actuel: \", editingCours.fichier_pdf || 'Aucun fichier', editingCours.taille_fichier && ` (${editingCours.taille_fichier})`), formData.fichier_pdf && /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#28a745',\n      display: 'block',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1153,\n      columnNumber: 41\n    }\n  }, \"\\u2705 Nouveau fichier s\\xE9lectionn\\xE9: \", formData.fichier_pdf.name, \"(\", (formData.fichier_pdf.size / (1024 * 1024)).toFixed(2), \" MB)\"))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1161,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1162,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1163,\n      columnNumber: 37\n    }\n  }, \"Mati\\xE8re *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => setFormData({\n      ...formData,\n      matiere_id: e.target.value\n    }),\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1164,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1176,\n      columnNumber: 41\n    }\n  }, \"S\\xE9lectionner une mati\\xE8re\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1178,\n      columnNumber: 45\n    }\n  }, matiere.nom)))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1185,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1186,\n      columnNumber: 37\n    }\n  }, \"Classe *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.classe_id,\n    onChange: e => setFormData({\n      ...formData,\n      classe_id: e.target.value\n    }),\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1187,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1199,\n      columnNumber: 41\n    }\n  }, \"S\\xE9lectionner une classe\"), classes.map(classe => /*#__PURE__*/React.createElement(\"option\", {\n    key: classe.id,\n    value: classe.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1201,\n      columnNumber: 45\n    }\n  }, classe.nom))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1209,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1210,\n      columnNumber: 33\n    }\n  }, \"Date de publication *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_publication,\n    onChange: e => setFormData({\n      ...formData,\n      date_publication: e.target.value\n    }),\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1211,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1226,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingCours(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1227,\n      columnNumber: 33\n    }\n  }, \"Annuler\"), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1238,\n      columnNumber: 33\n    }\n  }, editingCours ? 'Modifier' : 'Créer'))))));\n};\nexport default CoursCRUD;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "filterCours", "canManageData", "isStudent", "logSecurityEvent", "CoursCRUD", "user", "cours", "setCours", "matieres", "set<PERSON>ati<PERSON>s", "classes", "setClasses", "loading", "setLoading", "showModal", "setShowModal", "editingCours", "setEditingCours", "searchTerm", "setSearchTerm", "matier<PERSON><PERSON><PERSON><PERSON>", "setMatiereFilter", "classeFilter", "setClasseFilter", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "titre", "description", "fichier_pdf", "date_publication", "matiere_id", "classe_id", "isAdmin", "role", "<PERSON><PERSON><PERSON>er", "canManage", "fetchCours", "fetchMatieres", "fetchClasses", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "Array", "isArray", "data", "error", "console", "id", "nom", "log", "coursData", "length", "testCours", "date_ajout", "matiere_nom", "classe_nom", "taille_fi<PERSON>er", "fire", "title", "text", "icon", "timer", "showConfirmButton", "handleSubmit", "e", "preventDefault", "url", "trim", "name", "isNaN", "formDataToSend", "FormData", "append", "method", "editingId", "hasFile", "fileSize", "size", "key", "value", "entries", "File", "timeout", "status", "success", "resetForm", "_response$data", "_response$data2", "errorMsg", "message", "Error", "_error$response", "_error$response2", "_error$config", "_error$config2", "_error$config3", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "config", "errorMessage", "handleEdit", "handleDelete", "result", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "type", "JSON", "stringify", "parseInt", "_response$data3", "_response$data4", "_error$response7", "_error$response8", "_error$response9", "_error$response0", "_error$response1", "_error$response1$data", "_error$response10", "_error$response10$dat", "handleDownloadPDF", "_response$headers$con", "encodeURIComponent", "<PERSON><PERSON><PERSON>", "responseType", "includes", "blob", "Blob", "downloadUrl", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "_error$response11", "_error$response12", "_error$response13", "_error$response14", "_error$response15", "code", "errorText", "errorData", "parse", "testPUT", "html", "width", "testAPI", "_response$data5", "dataLength", "coursCount", "_error$response16", "_error$response17", "_error$response18", "_error$response19", "filteredCours", "filter", "c", "_c$titre", "_c$description", "_c$matiere_nom", "_c$classe_nom", "_c$matiere_id", "_c$classe_id", "matchesSearch", "toLowerCase", "matchesMatiere", "toString", "matchesClasse", "indexOfLastItem", "indexOfFirstItem", "currentCours", "slice", "totalPages", "Math", "ceil", "paginate", "pageNumber", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gap", "onClick", "src", "alt", "padding", "backgroundColor", "borderRadius", "marginBottom", "border", "margin", "color", "flex", "placeholder", "onChange", "target", "min<PERSON><PERSON><PERSON>", "map", "matiere", "classe", "fontSize", "fontWeight", "marginTop", "substring", "Date", "toLocaleDateString", "alignItems", "justifyContent", "disabled", "gridTemplateColumns", "textAlign", "max<PERSON><PERSON><PERSON>", "onSubmit", "required", "rows", "resize", "accept", "file", "files", "toFixed"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/Cours.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\r\nimport { AuthContext } from '../context/AuthContext';\r\nimport axios from 'axios';\r\nimport Swal from 'sweetalert2';\r\nimport {\r\n    filterCours,\r\n    canManageData,\r\n    isStudent,\r\n    logSecurityEvent\r\n} from '../utils/studentDataFilter';\r\nimport '../css/Animations.css';\r\nimport '../css/Factures.css';\r\n\r\nconst CoursCRUD = () => {\r\n    const { user } = useContext(AuthContext);\r\n    const [cours, setCours] = useState([]);\r\n    const [matieres, setMatieres] = useState([]);\r\n    const [classes, setClasses] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [showModal, setShowModal] = useState(false);\r\n    const [editingCours, setEditingCours] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [matiereFilter, setMatiereFilter] = useState('all');\r\n    const [classeFilter, setClasseFilter] = useState('all');\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(10);\r\n    const [formData, setFormData] = useState({\r\n        titre: '',\r\n        description: '',\r\n        fichier_pdf: null,\r\n        date_publication: '',\r\n        matiere_id: '',\r\n        classe_id: ''\r\n    });\r\n\r\n    // Vérifier si l'utilisateur est Admin ou Enseignant avec notre système unifié\r\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';\r\n    const isTeacher = user?.role === 'Enseignant' || user?.role === 'enseignant' || user?.role === 'teacher';\r\n    const canManage = canManageData(user);\r\n\r\n    useEffect(() => {\r\n        fetchCours();\r\n        fetchMatieres();\r\n        fetchClasses();\r\n    }, []);\r\n\r\n    const fetchMatieres = async () => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n            setMatieres(Array.isArray(response.data) ? response.data : []);\r\n        } catch (error) {\r\n            console.error('Erreur lors du chargement des matières:', error);\r\n            setMatieres([\r\n                { id: 1, nom: 'Mathématiques' },\r\n                { id: 2, nom: 'Physique' },\r\n                { id: 3, nom: 'Français' },\r\n                { id: 4, nom: 'Histoire' }\r\n            ]);\r\n        }\r\n    };\r\n\r\n    const fetchClasses = async () => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n            setClasses(Array.isArray(response.data) ? response.data : []);\r\n        } catch (error) {\r\n            console.error('Erreur lors du chargement des classes:', error);\r\n            setClasses([\r\n                { id: 1, nom: 'Classe A' },\r\n                { id: 2, nom: 'Classe B' },\r\n                { id: 3, nom: 'Classe C' },\r\n                { id: 4, nom: 'Classe D' }\r\n            ]);\r\n        }\r\n    };\r\n\r\n    const fetchCours = async () => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            console.log('🔄 Chargement des cours...');\r\n\r\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/cours/cours.php', {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n\r\n            let coursData = response.data;\r\n            if (!Array.isArray(coursData)) {\r\n                coursData = [];\r\n            }\r\n\r\n            setCours(coursData);\r\n            console.log('✅ Cours chargés:', coursData.length, 'éléments');\r\n        } catch (error) {\r\n            console.error('❌ Erreur lors du chargement des cours:', error);\r\n\r\n            // Données de test avec fichiers PDF (compatible avec structure BDD réelle)\r\n            const testCours = [\r\n                {\r\n                    id: 1,\r\n                    titre: 'Introduction aux Mathématiques',\r\n                    description: 'Cours de base en mathématiques',\r\n                    fichier_pdf: 'math_intro.pdf',\r\n                    date_ajout: '2024-01-15',\r\n                    date_publication: '2024-01-15', // Pour compatibilité\r\n                    matiere_id: 1,\r\n                    matiere_nom: 'Mathématiques',\r\n                    classe_id: 1, // Valeur par défaut\r\n                    classe_nom: 'Toutes classes', // Valeur par défaut\r\n                    taille_fichier: '2.5 MB'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    titre: 'Les Forces en Physique',\r\n                    description: 'Étude des forces et du mouvement',\r\n                    fichier_pdf: 'physique_forces.pdf',\r\n                    date_ajout: '2024-01-20',\r\n                    date_publication: '2024-01-20',\r\n                    matiere_id: 2,\r\n                    matiere_nom: 'Physique',\r\n                    classe_id: 1,\r\n                    classe_nom: 'Toutes classes',\r\n                    taille_fichier: '3.2 MB'\r\n                },\r\n                {\r\n                    id: 3,\r\n                    titre: 'Grammaire Française',\r\n                    description: 'Les règles de grammaire essentielles',\r\n                    fichier_pdf: 'francais_grammaire.pdf',\r\n                    date_ajout: '2024-01-25',\r\n                    date_publication: '2024-01-25',\r\n                    matiere_id: 3,\r\n                    matiere_nom: 'Français',\r\n                    classe_id: 1,\r\n                    classe_nom: 'Toutes classes',\r\n                    taille_fichier: '1.8 MB'\r\n                },\r\n                {\r\n                    id: 4,\r\n                    titre: 'Histoire Moderne',\r\n                    description: 'Les événements du 20ème siècle',\r\n                    fichier_pdf: 'histoire_moderne.pdf',\r\n                    date_ajout: '2024-02-01',\r\n                    date_publication: '2024-02-01',\r\n                    matiere_id: 4,\r\n                    matiere_nom: 'Histoire',\r\n                    classe_id: 1,\r\n                    classe_nom: 'Toutes classes',\r\n                    taille_fichier: '4.1 MB'\r\n                },\r\n                {\r\n                    id: 5,\r\n                    titre: 'Algèbre Avancée',\r\n                    description: 'Concepts avancés en algèbre',\r\n                    fichier_pdf: 'math_algebre.pdf',\r\n                    date_ajout: '2024-02-05',\r\n                    date_publication: '2024-02-05',\r\n                    matiere_id: 1,\r\n                    matiere_nom: 'Mathématiques',\r\n                    classe_id: 1,\r\n                    classe_nom: 'Toutes classes',\r\n                    taille_fichier: '3.7 MB'\r\n                },\r\n                {\r\n                    id: 6,\r\n                    titre: 'Optique et Lumière',\r\n                    description: 'Propriétés de la lumière',\r\n                    fichier_pdf: 'physique_optique.pdf',\r\n                    date_ajout: '2024-02-10',\r\n                    date_publication: '2024-02-10',\r\n                    matiere_id: 2,\r\n                    matiere_nom: 'Physique',\r\n                    classe_id: 1,\r\n                    classe_nom: 'Toutes classes',\r\n                    taille_fichier: '2.9 MB'\r\n                }\r\n            ];\r\n\r\n            setCours(testCours);\r\n            Swal.fire({\r\n                title: '🧪 Mode Test',\r\n                text: `Connexion API échouée. Utilisation de ${testCours.length} cours de test avec fichiers PDF.`,\r\n                icon: 'info',\r\n                timer: 3000,\r\n                showConfirmButton: false\r\n            });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        if (!canManage) {\r\n            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent créer/modifier des cours', 'error');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\r\n\r\n            // Validation côté frontend\r\n            if (!formData.titre.trim()) {\r\n                Swal.fire('Erreur', 'Le titre du cours est requis', 'error');\r\n                return;\r\n            }\r\n\r\n            if (!formData.matiere_id) {\r\n                Swal.fire('Erreur', 'Veuillez sélectionner une matière', 'error');\r\n                return;\r\n            }\r\n\r\n            if (!formData.classe_id) {\r\n                Swal.fire('Erreur', 'Veuillez sélectionner une classe', 'error');\r\n                return;\r\n            }\r\n\r\n            if (!formData.date_publication) {\r\n                Swal.fire('Erreur', 'Veuillez sélectionner une date de publication', 'error');\r\n                return;\r\n            }\r\n\r\n            // Validation du fichier PDF : requis seulement pour la création\r\n            if (!editingCours && !formData.fichier_pdf) {\r\n                Swal.fire('Erreur', 'Veuillez sélectionner un fichier PDF pour créer un nouveau cours', 'error');\r\n                return;\r\n            }\r\n\r\n            // Pour la modification, le fichier PDF est optionnel\r\n            if (editingCours && formData.fichier_pdf) {\r\n                console.log('📁 Nouveau fichier PDF sélectionné pour la modification:', formData.fichier_pdf.name);\r\n            } else if (editingCours && !formData.fichier_pdf) {\r\n                console.log('📁 Aucun nouveau fichier PDF - conservation du fichier existant');\r\n            }\r\n\r\n            // Validation de l'ID pour la modification\r\n            if (editingCours && (!editingCours.id || isNaN(editingCours.id))) {\r\n                Swal.fire('Erreur', 'ID du cours invalide pour la modification', 'error');\r\n                return;\r\n            }\r\n\r\n            // Créer FormData pour gérer l'upload de fichier\r\n            const formDataToSend = new FormData();\r\n            formDataToSend.append('titre', formData.titre.trim());\r\n            formDataToSend.append('description', formData.description.trim());\r\n            formDataToSend.append('matiere_id', formData.matiere_id);\r\n            formDataToSend.append('classe_id', formData.classe_id);\r\n            formDataToSend.append('date_publication', formData.date_publication);\r\n\r\n            if (editingCours) {\r\n                formDataToSend.append('id', editingCours.id);\r\n                formDataToSend.append('_method', 'PUT'); // Champ caché pour identifier PUT\r\n            }\r\n\r\n            if (formData.fichier_pdf) {\r\n                formDataToSend.append('fichier_pdf', formData.fichier_pdf);\r\n            }\r\n\r\n            // Logs détaillés pour debug\r\n            console.log('🔄 Envoi requête cours:', {\r\n                method: editingCours ? 'PUT (via POST)' : 'POST',\r\n                url,\r\n                titre: formData.titre,\r\n                matiere_id: formData.matiere_id,\r\n                classe_id: formData.classe_id,\r\n                date_publication: formData.date_publication,\r\n                editingId: editingCours?.id,\r\n                hasFile: !!formData.fichier_pdf,\r\n                fileSize: formData.fichier_pdf ? formData.fichier_pdf.size : 0,\r\n                token: token ? 'présent' : 'absent'\r\n            });\r\n\r\n            // Afficher le contenu du FormData pour debug\r\n            console.log('📦 FormData contents:');\r\n            for (let [key, value] of formDataToSend.entries()) {\r\n                if (value instanceof File) {\r\n                    console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);\r\n                } else {\r\n                    console.log(`  ${key}: ${value}`);\r\n                }\r\n            }\r\n\r\n            // Pour les modifications, utiliser POST avec _method=PUT car FormData ne fonctionne pas bien avec PUT\r\n            const response = await axios({\r\n                method: 'POST',\r\n                url,\r\n                data: formDataToSend,\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'multipart/form-data'\r\n                },\r\n                timeout: 30000 // 30 secondes pour l'upload\r\n            });\r\n\r\n            console.log('✅ Réponse complète:', {\r\n                status: response.status,\r\n                data: response.data,\r\n                headers: response.headers\r\n            });\r\n\r\n            if (response.data && response.data.success === true) {\r\n                Swal.fire('Succès', `Cours ${editingCours ? 'modifié' : 'créé'} avec succès`, 'success');\r\n                setShowModal(false);\r\n                setEditingCours(null);\r\n                resetForm();\r\n                fetchCours();\r\n            } else {\r\n                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';\r\n                throw new Error(errorMsg);\r\n            }\r\n        } catch (error) {\r\n            console.error('❌ Erreur complète:', {\r\n                message: error.message,\r\n                response: error.response?.data,\r\n                status: error.response?.status,\r\n                config: {\r\n                    method: error.config?.method,\r\n                    url: error.config?.url,\r\n                    data: error.config?.data\r\n                }\r\n            });\r\n\r\n            let errorMessage = 'Une erreur est survenue';\r\n\r\n            if (error.response?.data?.error) {\r\n                errorMessage = error.response.data.error;\r\n            } else if (error.response?.data?.message) {\r\n                errorMessage = error.response.data.message;\r\n            } else if (error.response?.status === 413) {\r\n                errorMessage = 'Le fichier est trop volumineux (max 10MB)';\r\n            } else if (error.response?.status === 415) {\r\n                errorMessage = 'Type de fichier non supporté (PDF uniquement)';\r\n            } else if (error.message) {\r\n                errorMessage = error.message;\r\n            }\r\n\r\n            Swal.fire('Erreur', errorMessage, 'error');\r\n        }\r\n    };\r\n\r\n    const handleEdit = (cours) => {\r\n        if (!canManage) {\r\n            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent modifier des cours', 'error');\r\n            return;\r\n        }\r\n\r\n        // Vérifier que le cours est valide\r\n        if (!cours || !cours.id) {\r\n            Swal.fire('Erreur', 'Cours invalide', 'error');\r\n            return;\r\n        }\r\n\r\n        console.log('✏️ Édition cours:', {\r\n            id: cours.id,\r\n            titre: cours.titre,\r\n            matiere_id: cours.matiere_id,\r\n            classe_id: cours.classe_id,\r\n            date_publication: cours.date_publication\r\n        });\r\n\r\n        setEditingCours(cours);\r\n        setFormData({\r\n            titre: cours.titre || '',\r\n            description: cours.description || '',\r\n            fichier_pdf: null, // Ne pas pré-remplir le fichier\r\n            date_publication: cours.date_publication || '',\r\n            matiere_id: cours.matiere_id || '',\r\n            classe_id: cours.classe_id || ''\r\n        });\r\n        setShowModal(true);\r\n    };\r\n\r\n    const handleDelete = async (id) => {\r\n        if (!canManage) {\r\n            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent supprimer des cours', 'error');\r\n            return;\r\n        }\r\n\r\n        // Vérifier que l'ID est valide\r\n        if (!id || isNaN(id)) {\r\n            Swal.fire('Erreur', 'ID du cours invalide', 'error');\r\n            return;\r\n        }\r\n\r\n        const result = await Swal.fire({\r\n            title: 'Êtes-vous sûr?',\r\n            text: 'Cette action supprimera également le fichier PDF associé!',\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonColor: '#d33',\r\n            cancelButtonColor: '#3085d6',\r\n            confirmButtonText: 'Oui, supprimer!',\r\n            cancelButtonText: 'Annuler'\r\n        });\r\n\r\n        if (result.isConfirmed) {\r\n            try {\r\n                const token = localStorage.getItem('token');\r\n                const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\r\n\r\n                console.log('🔄 Suppression cours:', {\r\n                    id: id,\r\n                    type: typeof id,\r\n                    url: url,\r\n                    token: token ? 'présent' : 'absent'\r\n                });\r\n\r\n                const response = await axios({\r\n                    method: 'DELETE',\r\n                    url: url,\r\n                    headers: {\r\n                        Authorization: `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    },\r\n                    data: JSON.stringify({ id: parseInt(id) }),\r\n                    timeout: 10000\r\n                });\r\n\r\n                console.log('✅ Réponse suppression:', {\r\n                    status: response.status,\r\n                    data: response.data\r\n                });\r\n\r\n                if (response.data && response.data.success === true) {\r\n                    Swal.fire('Supprimé!', 'Le cours et son fichier PDF ont été supprimés.', 'success');\r\n                    fetchCours();\r\n                } else {\r\n                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';\r\n                    console.error('❌ Erreur dans la réponse:', response.data);\r\n                    throw new Error(errorMsg);\r\n                }\r\n            } catch (error) {\r\n                console.error('❌ Erreur suppression complète:', {\r\n                    message: error.message,\r\n                    response: error.response?.data,\r\n                    status: error.response?.status,\r\n                    id: id\r\n                });\r\n\r\n                let errorMessage = 'Impossible de supprimer le cours';\r\n\r\n                if (error.response?.status === 404) {\r\n                    errorMessage = 'Cours non trouvé';\r\n                } else if (error.response?.status === 403) {\r\n                    errorMessage = 'Accès non autorisé';\r\n                } else if (error.response?.data?.error) {\r\n                    errorMessage = error.response.data.error;\r\n                } else if (error.response?.data?.message) {\r\n                    errorMessage = error.response.data.message;\r\n                } else if (error.message) {\r\n                    errorMessage = error.message;\r\n                }\r\n\r\n                Swal.fire('Erreur', errorMessage, 'error');\r\n            }\r\n        }\r\n    };\r\n\r\n    // Fonction pour télécharger le PDF\r\n    const handleDownloadPDF = async (cours) => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n\r\n            // Vérifier que le cours a un fichier PDF\r\n            if (!cours.fichier_pdf) {\r\n                Swal.fire('Erreur', 'Aucun fichier PDF associé à ce cours', 'error');\r\n                return;\r\n            }\r\n\r\n            const url = `http://localhost/Project_PFE/Backend/pages/cours/download.php?file=${encodeURIComponent(cours.fichier_pdf)}&cours_id=${cours.id}`;\r\n\r\n            console.log('📥 Téléchargement PDF:', {\r\n                titre: cours.titre,\r\n                fichier: cours.fichier_pdf,\r\n                url: url\r\n            });\r\n\r\n            const response = await axios({\r\n                method: 'GET',\r\n                url,\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`\r\n                },\r\n                responseType: 'blob',\r\n                timeout: 30000 // 30 secondes de timeout\r\n            });\r\n\r\n            // Vérifier que la réponse est bien un PDF\r\n            if (response.data.type !== 'application/pdf' && !response.headers['content-type']?.includes('application/pdf')) {\r\n                console.error('❌ Type de fichier incorrect:', response.data.type);\r\n                Swal.fire('Erreur', 'Le fichier téléchargé n\\'est pas un PDF valide', 'error');\r\n                return;\r\n            }\r\n\r\n            // Créer un lien de téléchargement\r\n            const blob = new Blob([response.data], { type: 'application/pdf' });\r\n            const downloadUrl = window.URL.createObjectURL(blob);\r\n            const link = document.createElement('a');\r\n            link.href = downloadUrl;\r\n            link.download = `${cours.titre.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            link.remove();\r\n            window.URL.revokeObjectURL(downloadUrl);\r\n\r\n            console.log('✅ Téléchargement réussi:', cours.titre);\r\n            Swal.fire({\r\n                title: 'Téléchargement réussi!',\r\n                text: `Le fichier \"${cours.titre}.pdf\" a été téléchargé.`,\r\n                icon: 'success',\r\n                timer: 2000,\r\n                showConfirmButton: false\r\n            });\r\n\r\n        } catch (error) {\r\n            console.error('❌ Erreur téléchargement complète:', {\r\n                message: error.message,\r\n                response: error.response?.data,\r\n                status: error.response?.status,\r\n                cours: cours.titre\r\n            });\r\n\r\n            let errorMessage = 'Impossible de télécharger le fichier PDF';\r\n\r\n            if (error.response?.status === 404) {\r\n                errorMessage = 'Fichier PDF non trouvé sur le serveur';\r\n            } else if (error.response?.status === 403) {\r\n                errorMessage = 'Accès non autorisé au fichier PDF';\r\n            } else if (error.code === 'ECONNABORTED') {\r\n                errorMessage = 'Timeout - Le téléchargement a pris trop de temps';\r\n            } else if (error.response?.data) {\r\n                try {\r\n                    const errorText = await error.response.data.text();\r\n                    const errorData = JSON.parse(errorText);\r\n                    errorMessage = errorData.error || errorMessage;\r\n                } catch (e) {\r\n                    // Ignore parsing errors\r\n                }\r\n            }\r\n\r\n            Swal.fire('Erreur', errorMessage, 'error');\r\n        }\r\n    };\r\n\r\n    const resetForm = () => {\r\n        setFormData({\r\n            titre: '',\r\n            description: '',\r\n            fichier_pdf: null,\r\n            date_publication: '',\r\n            matiere_id: '',\r\n            classe_id: ''\r\n        });\r\n    };\r\n\r\n    // Fonction de test pour vérifier les données PUT\r\n    const testPUT = async () => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            const url = 'http://localhost/Project_PFE/Backend/pages/cours/test_put.php';\r\n\r\n            // Créer des données de test\r\n            const formDataToSend = new FormData();\r\n            formDataToSend.append('id', '1');\r\n            formDataToSend.append('titre', 'Test PUT');\r\n            formDataToSend.append('description', 'Test description');\r\n            formDataToSend.append('matiere_id', '1');\r\n            formDataToSend.append('classe_id', '1');\r\n            formDataToSend.append('date_publication', '2024-01-15');\r\n\r\n            console.log('🧪 Test PUT - Envoi des données...');\r\n\r\n            const response = await axios({\r\n                method: 'PUT',\r\n                url,\r\n                data: formDataToSend,\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'multipart/form-data'\r\n                },\r\n                timeout: 10000\r\n            });\r\n\r\n            console.log('✅ Test PUT réussi:', response.data);\r\n            Swal.fire({\r\n                title: 'Test PUT Réussi',\r\n                html: `<pre>${JSON.stringify(response.data, null, 2)}</pre>`,\r\n                icon: 'success',\r\n                width: '80%'\r\n            });\r\n\r\n        } catch (error) {\r\n            console.error('❌ Test PUT échoué:', error);\r\n            Swal.fire('Erreur', 'Test PUT échoué: ' + (error.message || 'Erreur inconnue'), 'error');\r\n        }\r\n    };\r\n\r\n    // Fonction de test pour vérifier la connexion API\r\n    const testAPI = async () => {\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';\r\n\r\n            console.log('🧪 Test de connexion API...', {\r\n                url: url,\r\n                token: token ? 'présent' : 'absent'\r\n            });\r\n\r\n            const response = await axios.get(url, {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                },\r\n                timeout: 10000\r\n            });\r\n\r\n            console.log('✅ Test API réussi:', {\r\n                status: response.status,\r\n                dataLength: response.data?.length || 0,\r\n                data: response.data\r\n            });\r\n\r\n            const coursCount = Array.isArray(response.data) ? response.data.length : 0;\r\n            Swal.fire('Succès', `API connectée ! ${coursCount} cours trouvés`, 'success');\r\n\r\n        } catch (error) {\r\n            console.error('❌ Test API échoué:', {\r\n                message: error.message,\r\n                response: error.response?.data,\r\n                status: error.response?.status,\r\n                code: error.code\r\n            });\r\n\r\n            let errorMessage = 'Connexion API échouée';\r\n\r\n            if (error.code === 'ECONNREFUSED') {\r\n                errorMessage = 'Serveur non accessible - Vérifiez que le serveur PHP fonctionne';\r\n            } else if (error.code === 'ECONNABORTED') {\r\n                errorMessage = 'Timeout - Le serveur met trop de temps à répondre';\r\n            } else if (error.response?.status === 404) {\r\n                errorMessage = 'API non trouvée - Vérifiez l\\'URL du backend';\r\n            } else if (error.response?.status === 500) {\r\n                errorMessage = 'Erreur serveur - Vérifiez les logs PHP';\r\n            } else if (error.message) {\r\n                errorMessage = error.message;\r\n            }\r\n\r\n            Swal.fire('Erreur', errorMessage, 'error');\r\n        }\r\n    };\r\n\r\n    // Filtrage des données\r\n    const filteredCours = cours.filter(c => {\r\n        const matchesSearch = c.titre?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                             c.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                             c.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                             c.classe_nom?.toLowerCase().includes(searchTerm.toLowerCase());\r\n\r\n        const matchesMatiere = matiereFilter === 'all' || c.matiere_id?.toString() === matiereFilter;\r\n        const matchesClasse = classeFilter === 'all' || c.classe_id?.toString() === classeFilter;\r\n\r\n        return matchesSearch && matchesMatiere && matchesClasse;\r\n    });\r\n\r\n    // Pagination\r\n    const indexOfLastItem = currentPage * itemsPerPage;\r\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\r\n    const currentCours = filteredCours.slice(indexOfFirstItem, indexOfLastItem);\r\n    const totalPages = Math.ceil(filteredCours.length / itemsPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    // Reset pagination when filters change\r\n    React.useEffect(() => {\r\n        setCurrentPage(1);\r\n    }, [searchTerm, matiereFilter, classeFilter]);\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"loading-container\">\r\n                <div className=\"spinner\"></div>\r\n                <p>Chargement des cours...</p>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"factures-container\">\r\n            <div className=\"page-header\">\r\n                <h1>📚 Gestion des Cours </h1>\r\n                <div className=\"header-info\">\r\n                    <span className=\"total-count\">\r\n                        {filteredCours.length} cours trouvé(s)\r\n                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}\r\n                    </span>\r\n                    <div style={{ display: 'flex', gap: '10px' }}>\r\n                        {canManage && (\r\n                            <button\r\n                                className=\"btn btn-primary\"\r\n                                onClick={() => setShowModal(true)}\r\n                            >\r\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouveau Cours\r\n                            </button>\r\n                        )}\r\n                        \r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Message d'information pour les non-gestionnaires */}\r\n            {!canManage && (\r\n                <div style={{\r\n                    padding: '15px',\r\n                    backgroundColor: '#e3f2fd',\r\n                    borderRadius: '8px',\r\n                    marginBottom: '20px',\r\n                    border: '1px solid #bbdefb'\r\n                }}>\r\n                    <p style={{ margin: '0', color: '#1976d2' }}>\r\n                        ℹ️ Vous consultez les cours en mode lecture seule.\r\n                        Seuls les administrateurs et enseignants peuvent créer, modifier ou supprimer des cours.\r\n                        Cliquez sur les liens PDF pour télécharger les cours.\r\n                    </p>\r\n                </div>\r\n            )}\r\n\r\n            {/* Filtres */}\r\n            <div className=\"filters-section\" style={{\r\n                display: 'flex',\r\n                gap: '15px',\r\n                marginBottom: '20px',\r\n                padding: '15px',\r\n                backgroundColor: '#f8f9fa',\r\n                borderRadius: '8px'\r\n            }}>\r\n                <div className=\"search-box\" style={{ flex: 1 }}>\r\n                    <input\r\n                        type=\"text\"\r\n                        placeholder=\"🔍 Rechercher un cours...\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => {\r\n                            setSearchTerm(e.target.value);\r\n                            setCurrentPage(1);\r\n                        }}\r\n                        style={{\r\n                            width: '100%',\r\n                            padding: '10px',\r\n                            border: '1px solid #ddd',\r\n                            borderRadius: '6px'\r\n                        }}\r\n                    />\r\n                </div>\r\n                <div className=\"matiere-filter\">\r\n                    <select\r\n                        value={matiereFilter}\r\n                        onChange={(e) => {\r\n                            setMatiereFilter(e.target.value);\r\n                            setCurrentPage(1);\r\n                        }}\r\n                        style={{\r\n                            padding: '10px',\r\n                            border: '1px solid #ddd',\r\n                            borderRadius: '6px',\r\n                            minWidth: '150px'\r\n                        }}\r\n                    >\r\n                        <option value=\"all\">Toutes les matières</option>\r\n                        {matieres.map(matiere => (\r\n                            <option key={matiere.id} value={matiere.id}>\r\n                                {matiere.nom}\r\n                            </option>\r\n                        ))}\r\n                    </select>\r\n                </div>\r\n                <div className=\"classe-filter\">\r\n                    <select\r\n                        value={classeFilter}\r\n                        onChange={(e) => {\r\n                            setClasseFilter(e.target.value);\r\n                            setCurrentPage(1);\r\n                        }}\r\n                        style={{\r\n                            padding: '10px',\r\n                            border: '1px solid #ddd',\r\n                            borderRadius: '6px',\r\n                            minWidth: '150px'\r\n                        }}\r\n                    >\r\n                        <option value=\"all\">Toutes les classes</option>\r\n                        {classes.map(classe => (\r\n                            <option key={classe.id} value={classe.id}>\r\n                                {classe.nom}\r\n                            </option>\r\n                        ))}\r\n                    </select>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"factures-grid\">\r\n                {filteredCours.length === 0 ? (\r\n                    <div className=\"no-data\">\r\n                        <img src=\"/pdf-icon.png\" alt=\"Aucun cours\" />\r\n                        <p>Aucun cours trouvé</p>\r\n                        {(searchTerm || matiereFilter !== 'all' || classeFilter !== 'all') && (\r\n                            <button\r\n                                onClick={() => {\r\n                                    setSearchTerm('');\r\n                                    setMatiereFilter('all');\r\n                                    setClasseFilter('all');\r\n                                }}\r\n                                className=\"btn btn-secondary\"\r\n                            >\r\n                                Effacer les filtres\r\n                            </button>\r\n                        )}\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"table-responsive\">\r\n                        <table className=\"table\">\r\n                            <thead>\r\n                                <tr>\r\n                                    <th>🆔 ID</th>\r\n                                    <th>📚 Titre du Cours</th>\r\n                                    <th>📖 Matière</th>\r\n                                    <th>🏫 Classe</th>\r\n                                    <th>📅 Date</th>\r\n                                    <th>📄 Fichier PDF</th>\r\n                                    <th>📊 Taille</th>\r\n                                    {canManage && <th>⚙️ Actions</th>}\r\n                                </tr>\r\n                            </thead>\r\n                            <tbody>\r\n                                {currentCours.map((c) => (\r\n                                    <tr key={c.id}>\r\n                                        <td>\r\n                                            <span style={{\r\n                                                padding: '4px 8px',\r\n                                                backgroundColor: '#e3f2fd',\r\n                                                borderRadius: '4px',\r\n                                                fontSize: '0.9em',\r\n                                                fontWeight: 'bold'\r\n                                            }}>\r\n                                                #{c.id}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td>\r\n                                            <div className=\"student-info\">\r\n                                                <strong>{c.titre}</strong>\r\n                                                {c.description && (\r\n                                                    <small style={{\r\n                                                        display: 'block',\r\n                                                        color: '#6c757d',\r\n                                                        marginTop: '4px'\r\n                                                    }}>\r\n                                                        {c.description.length > 50\r\n                                                            ? c.description.substring(0, 50) + '...'\r\n                                                            : c.description}\r\n                                                    </small>\r\n                                                )}\r\n                                            </div>\r\n                                        </td>\r\n                                        <td>\r\n                                            <span style={{\r\n                                                padding: '4px 8px',\r\n                                                backgroundColor: '#fff3cd',\r\n                                                borderRadius: '4px',\r\n                                                fontSize: '0.9em',\r\n                                                color: '#856404'\r\n                                            }}>\r\n                                                {c.matiere_nom || 'Non définie'}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td>\r\n                                            <span style={{\r\n                                                padding: '4px 8px',\r\n                                                backgroundColor: '#d4edda',\r\n                                                borderRadius: '4px',\r\n                                                fontSize: '0.9em',\r\n                                                color: '#155724'\r\n                                            }}>\r\n                                                {c.classe_nom || 'Non définie'}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td>\r\n                                            <span style={{ fontSize: '0.9em' }}>\r\n                                                {new Date(c.date_publication).toLocaleDateString('fr-FR')}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td>\r\n                                            <button\r\n                                                className=\"btn btn-sm btn-success\"\r\n                                                onClick={() => handleDownloadPDF(c)}\r\n                                                title=\"Télécharger le PDF\"\r\n                                                style={{\r\n                                                    display: 'flex',\r\n                                                    alignItems: 'center',\r\n                                                    gap: '5px',\r\n                                                    fontSize: '0.8em'\r\n                                                }}\r\n                                            >\r\n                                                📥 PDF\r\n                                            </button>\r\n                                        </td>\r\n                                        <td>\r\n                                            <span style={{\r\n                                                fontSize: '0.8em',\r\n                                                color: '#6c757d'\r\n                                            }}>\r\n                                                {c.taille_fichier || 'N/A'}\r\n                                            </span>\r\n                                        </td>\r\n                                        {canManage && (\r\n                                            <td>\r\n                                                <div className=\"action-buttons\">\r\n                                                    <button\r\n                                                        className=\"btn btn-sm btn-warning\"\r\n                                                        onClick={() => handleEdit(c)}\r\n                                                        title=\"Modifier\"\r\n                                                    >\r\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\r\n                                                    </button>\r\n                                                    <button\r\n                                                        className=\"btn btn-sm btn-danger\"\r\n                                                        onClick={() => handleDelete(c.id)}\r\n                                                        title=\"Supprimer\"\r\n                                                    >\r\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\r\n                                                    </button>\r\n                                                </div>\r\n                                            </td>\r\n                                        )}\r\n                                    </tr>\r\n                                ))}\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n                <div style={{\r\n                    display: 'flex',\r\n                    justifyContent: 'center',\r\n                    alignItems: 'center',\r\n                    marginTop: '20px',\r\n                    gap: '10px'\r\n                }}>\r\n                    <button\r\n                        className=\"btn btn-secondary\"\r\n                        onClick={() => paginate(currentPage - 1)}\r\n                        disabled={currentPage === 1}\r\n                    >\r\n                        ⬅️ Précédent\r\n                    </button>\r\n\r\n                    <span style={{\r\n                        padding: '8px 16px',\r\n                        backgroundColor: '#f8f9fa',\r\n                        borderRadius: '4px',\r\n                        fontSize: '14px'\r\n                    }}>\r\n                        Page {currentPage} sur {totalPages}\r\n                    </span>\r\n\r\n                    <button\r\n                        className=\"btn btn-secondary\"\r\n                        onClick={() => paginate(currentPage + 1)}\r\n                        disabled={currentPage === totalPages}\r\n                    >\r\n                        Suivant ➡️\r\n                    </button>\r\n                </div>\r\n            )}\r\n\r\n            {/* Statistiques */}\r\n            {filteredCours.length > 0 && (\r\n                <div className=\"stats-section\" style={{\r\n                    marginTop: '30px',\r\n                    padding: '20px',\r\n                    backgroundColor: '#f8f9fa',\r\n                    borderRadius: '8px',\r\n                    display: 'grid',\r\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\r\n                    gap: '15px'\r\n                }}>\r\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\r\n                        <h3 style={{ color: '#007bff', margin: '0' }}>\r\n                            {filteredCours.length}\r\n                        </h3>\r\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des cours</p>\r\n                    </div>\r\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\r\n                        <h3 style={{ color: '#28a745', margin: '0' }}>\r\n                            {matieres.length}\r\n                        </h3>\r\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Matières disponibles</p>\r\n                    </div>\r\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\r\n                        <h3 style={{ color: '#17a2b8', margin: '0' }}>\r\n                            {classes.length}\r\n                        </h3>\r\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Classes disponibles</p>\r\n                    </div>\r\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\r\n                        <h3 style={{ color: '#ffc107', margin: '0' }}>\r\n                            {currentCours.length}\r\n                        </h3>\r\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Modal pour ajouter/modifier un cours */}\r\n            {showModal && canManage && (\r\n                <div className=\"modal-overlay\">\r\n                    <div className=\"modal-content\" style={{ maxWidth: '600px' }}>\r\n                        <div className=\"modal-header\">\r\n                            <h3>{editingCours ? 'Modifier le cours' : 'Nouveau cours'}</h3>\r\n                            <button\r\n                                className=\"close-btn\"\r\n                                onClick={() => {\r\n                                    setShowModal(false);\r\n                                    setEditingCours(null);\r\n                                    resetForm();\r\n                                }}\r\n                            >\r\n                                <img src=\"/close.png\" alt=\"Fermer\" />\r\n                            </button>\r\n                        </div>\r\n                        <form onSubmit={handleSubmit}>\r\n                            {/* Affichage des informations du cours en modification */}\r\n                            {editingCours && (\r\n                                <div style={{\r\n                                    padding: '15px',\r\n                                    backgroundColor: '#e3f2fd',\r\n                                    borderRadius: '8px',\r\n                                    marginBottom: '20px',\r\n                                    border: '1px solid #bbdefb'\r\n                                }}>\r\n                                    <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>\r\n                                        📝 Modification du cours #{editingCours.id}\r\n                                    </h4>\r\n                                    <p style={{ margin: '5px 0', fontSize: '14px', color: '#1976d2' }}>\r\n                                        📄 Fichier actuel: <strong>{editingCours.fichier_pdf || 'Aucun fichier'}</strong>\r\n                                        {editingCours.taille_fichier && ` (${editingCours.taille_fichier})`}\r\n                                    </p>\r\n                                    <p style={{ margin: '5px 0 0 0', fontSize: '12px', color: '#666' }}>\r\n                                        💡 Laissez le champ fichier vide pour conserver le fichier actuel\r\n                                    </p>\r\n                                </div>\r\n                            )}\r\n\r\n                            <div className=\"form-group\">\r\n                                <label>Titre du cours *</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    value={formData.titre}\r\n                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}\r\n                                    placeholder=\"Ex: Introduction aux Mathématiques...\"\r\n                                    required\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '10px',\r\n                                        border: '1px solid #ced4da',\r\n                                        borderRadius: '4px',\r\n                                        fontSize: '14px'\r\n                                    }}\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label>Description</label>\r\n                                <textarea\r\n                                    value={formData.description}\r\n                                    onChange={(e) => setFormData({...formData, description: e.target.value})}\r\n                                    placeholder=\"Description du cours...\"\r\n                                    rows=\"3\"\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '10px',\r\n                                        border: '1px solid #ced4da',\r\n                                        borderRadius: '4px',\r\n                                        fontSize: '14px',\r\n                                        resize: 'vertical'\r\n                                    }}\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label>\r\n                                    Fichier PDF {editingCours ? '(Optionnel - Laisser vide pour conserver le fichier actuel)' : '*'}\r\n                                </label>\r\n                                <input\r\n                                    type=\"file\"\r\n                                    accept=\".pdf\"\r\n                                    onChange={(e) => {\r\n                                        const file = e.target.files[0];\r\n                                        if (file) {\r\n                                            // Validation côté client\r\n                                            if (file.type !== 'application/pdf') {\r\n                                                Swal.fire('Erreur', 'Seuls les fichiers PDF sont acceptés', 'error');\r\n                                                e.target.value = '';\r\n                                                return;\r\n                                            }\r\n                                            if (file.size > 10 * 1024 * 1024) {\r\n                                                Swal.fire('Erreur', 'Le fichier ne doit pas dépasser 10MB', 'error');\r\n                                                e.target.value = '';\r\n                                                return;\r\n                                            }\r\n                                            console.log('📁 Fichier sélectionné:', {\r\n                                                name: file.name,\r\n                                                size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',\r\n                                                type: file.type\r\n                                            });\r\n                                        }\r\n                                        setFormData({...formData, fichier_pdf: file});\r\n                                    }}\r\n                                    required={!editingCours}\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '10px',\r\n                                        border: '1px solid #ced4da',\r\n                                        borderRadius: '4px',\r\n                                        fontSize: '14px'\r\n                                    }}\r\n                                />\r\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\r\n                                    Formats acceptés: PDF uniquement. Taille max: 10MB\r\n                                    {editingCours && !formData.fichier_pdf && (\r\n                                        <span style={{ color: '#17a2b8', display: 'block', marginTop: '5px' }}>\r\n                                            📄 Fichier actuel: {editingCours.fichier_pdf || 'Aucun fichier'}\r\n                                            {editingCours.taille_fichier && ` (${editingCours.taille_fichier})`}\r\n                                        </span>\r\n                                    )}\r\n                                    {formData.fichier_pdf && (\r\n                                        <span style={{ color: '#28a745', display: 'block', marginTop: '5px' }}>\r\n                                            ✅ Nouveau fichier sélectionné: {formData.fichier_pdf.name}\r\n                                            ({(formData.fichier_pdf.size / (1024 * 1024)).toFixed(2)} MB)\r\n                                        </span>\r\n                                    )}\r\n                                </small>\r\n                            </div>\r\n\r\n                            <div style={{ display: 'flex', gap: '15px' }}>\r\n                                <div className=\"form-group\" style={{ flex: 1 }}>\r\n                                    <label>Matière *</label>\r\n                                    <select\r\n                                        value={formData.matiere_id}\r\n                                        onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}\r\n                                        required\r\n                                        style={{\r\n                                            width: '100%',\r\n                                            padding: '10px',\r\n                                            border: '1px solid #ced4da',\r\n                                            borderRadius: '4px',\r\n                                            fontSize: '14px'\r\n                                        }}\r\n                                    >\r\n                                        <option value=\"\">Sélectionner une matière</option>\r\n                                        {matieres.map((matiere) => (\r\n                                            <option key={matiere.id} value={matiere.id}>\r\n                                                {matiere.nom}\r\n                                            </option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div className=\"form-group\" style={{ flex: 1 }}>\r\n                                    <label>Classe *</label>\r\n                                    <select\r\n                                        value={formData.classe_id}\r\n                                        onChange={(e) => setFormData({...formData, classe_id: e.target.value})}\r\n                                        required\r\n                                        style={{\r\n                                            width: '100%',\r\n                                            padding: '10px',\r\n                                            border: '1px solid #ced4da',\r\n                                            borderRadius: '4px',\r\n                                            fontSize: '14px'\r\n                                        }}\r\n                                    >\r\n                                        <option value=\"\">Sélectionner une classe</option>\r\n                                        {classes.map((classe) => (\r\n                                            <option key={classe.id} value={classe.id}>\r\n                                                {classe.nom}\r\n                                            </option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label>Date de publication *</label>\r\n                                <input\r\n                                    type=\"date\"\r\n                                    value={formData.date_publication}\r\n                                    onChange={(e) => setFormData({...formData, date_publication: e.target.value})}\r\n                                    required\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '10px',\r\n                                        border: '1px solid #ced4da',\r\n                                        borderRadius: '4px',\r\n                                        fontSize: '14px'\r\n                                    }}\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    className=\"btn btn-secondary\"\r\n                                    onClick={() => {\r\n                                        setShowModal(false);\r\n                                        setEditingCours(null);\r\n                                        resetForm();\r\n                                    }}\r\n                                >\r\n                                    Annuler\r\n                                </button>\r\n                                <button type=\"submit\" className=\"btn btn-primary\">\r\n                                    {editingCours ? 'Modifier' : 'Créer'}\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CoursCRUD;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SACIC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,gBAAgB,QACb,4BAA4B;AACnC,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAGT,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACrCmC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,aAAa;EAChG,MAAMC,SAAS,GAAG,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,YAAY,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,YAAY,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,SAAS;EACxG,MAAME,SAAS,GAAGrC,aAAa,CAACI,IAAI,CAAC;EAErCV,SAAS,CAAC,MAAM;IACZ4C,UAAU,CAAC,CAAC;IACZC,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC,iEAAiE,EAAE;QAChGC,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MACFjC,WAAW,CAACwC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACM,IAAI,CAAC,GAAGN,QAAQ,CAACM,IAAI,GAAG,EAAE,CAAC;IAClE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D3C,WAAW,CAAC,CACR;QAAE6C,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAgB,CAAC,EAC/B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,EAC1B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,EAC1B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,CAC7B,CAAC;IACN;EACJ,CAAC;EAED,MAAMd,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC,+DAA+D,EAAE;QAC9FC,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MACF/B,UAAU,CAACsC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACM,IAAI,CAAC,GAAGN,QAAQ,CAACM,IAAI,GAAG,EAAE,CAAC;IACjE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DzC,UAAU,CAAC,CACP;QAAE2C,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,EAC1B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,EAC1B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,EAC1B;QAAED,EAAE,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAW,CAAC,CAC7B,CAAC;IACN;EACJ,CAAC;EAED,MAAMhB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CS,OAAO,CAACG,GAAG,CAAC,4BAA4B,CAAC;MAEzC,MAAMX,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC,4DAA4D,EAAE;QAC3FC,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAIe,SAAS,GAAGZ,QAAQ,CAACM,IAAI;MAC7B,IAAI,CAACF,KAAK,CAACC,OAAO,CAACO,SAAS,CAAC,EAAE;QAC3BA,SAAS,GAAG,EAAE;MAClB;MAEAlD,QAAQ,CAACkD,SAAS,CAAC;MACnBJ,OAAO,CAACG,GAAG,CAAC,kBAAkB,EAAEC,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC;IACjE,CAAC,CAAC,OAAON,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;;MAE9D;MACA,MAAMO,SAAS,GAAG,CACd;QACIL,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,gCAAgC;QACvCC,WAAW,EAAE,gCAAgC;QAC7CC,WAAW,EAAE,gBAAgB;QAC7B6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAAE;QAChCC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,eAAe;QAC5B3B,SAAS,EAAE,CAAC;QAAE;QACd4B,UAAU,EAAE,gBAAgB;QAAE;QAC9BC,cAAc,EAAE;MACpB,CAAC,EACD;QACIT,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,kCAAkC;QAC/CC,WAAW,EAAE,qBAAqB;QAClC6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAC9BC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,UAAU;QACvB3B,SAAS,EAAE,CAAC;QACZ4B,UAAU,EAAE,gBAAgB;QAC5BC,cAAc,EAAE;MACpB,CAAC,EACD;QACIT,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE,sCAAsC;QACnDC,WAAW,EAAE,wBAAwB;QACrC6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAC9BC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,UAAU;QACvB3B,SAAS,EAAE,CAAC;QACZ4B,UAAU,EAAE,gBAAgB;QAC5BC,cAAc,EAAE;MACpB,CAAC,EACD;QACIT,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,gCAAgC;QAC7CC,WAAW,EAAE,sBAAsB;QACnC6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAC9BC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,UAAU;QACvB3B,SAAS,EAAE,CAAC;QACZ4B,UAAU,EAAE,gBAAgB;QAC5BC,cAAc,EAAE;MACpB,CAAC,EACD;QACIT,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,6BAA6B;QAC1CC,WAAW,EAAE,kBAAkB;QAC/B6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAC9BC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,eAAe;QAC5B3B,SAAS,EAAE,CAAC;QACZ4B,UAAU,EAAE,gBAAgB;QAC5BC,cAAc,EAAE;MACpB,CAAC,EACD;QACIT,EAAE,EAAE,CAAC;QACLzB,KAAK,EAAE,oBAAoB;QAC3BC,WAAW,EAAE,0BAA0B;QACvCC,WAAW,EAAE,sBAAsB;QACnC6B,UAAU,EAAE,YAAY;QACxB5B,gBAAgB,EAAE,YAAY;QAC9BC,UAAU,EAAE,CAAC;QACb4B,WAAW,EAAE,UAAU;QACvB3B,SAAS,EAAE,CAAC;QACZ4B,UAAU,EAAE,gBAAgB;QAC5BC,cAAc,EAAE;MACpB,CAAC,CACJ;MAEDxD,QAAQ,CAACoD,SAAS,CAAC;MACnB5D,IAAI,CAACiE,IAAI,CAAC;QACNC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,yCAAyCP,SAAS,CAACD,MAAM,mCAAmC;QAClGS,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,IAAI;QACXC,iBAAiB,EAAE;MACvB,CAAC,CAAC;IACN,CAAC,SAAS;MACNxD,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMyD,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAClC,SAAS,EAAE;MACZvC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,2EAA2E,EAAE,OAAO,CAAC;MACzG;IACJ;IAEA,IAAI;MACA,MAAMtB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM6B,GAAG,GAAG,4DAA4D;;MAExE;MACA,IAAI,CAAC9C,QAAQ,CAACE,KAAK,CAAC6C,IAAI,CAAC,CAAC,EAAE;QACxB3E,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,8BAA8B,EAAE,OAAO,CAAC;QAC5D;MACJ;MAEA,IAAI,CAACrC,QAAQ,CAACM,UAAU,EAAE;QACtBlC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,mCAAmC,EAAE,OAAO,CAAC;QACjE;MACJ;MAEA,IAAI,CAACrC,QAAQ,CAACO,SAAS,EAAE;QACrBnC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,kCAAkC,EAAE,OAAO,CAAC;QAChE;MACJ;MAEA,IAAI,CAACrC,QAAQ,CAACK,gBAAgB,EAAE;QAC5BjC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,+CAA+C,EAAE,OAAO,CAAC;QAC7E;MACJ;;MAEA;MACA,IAAI,CAAChD,YAAY,IAAI,CAACW,QAAQ,CAACI,WAAW,EAAE;QACxChC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,kEAAkE,EAAE,OAAO,CAAC;QAChG;MACJ;;MAEA;MACA,IAAIhD,YAAY,IAAIW,QAAQ,CAACI,WAAW,EAAE;QACtCsB,OAAO,CAACG,GAAG,CAAC,0DAA0D,EAAE7B,QAAQ,CAACI,WAAW,CAAC4C,IAAI,CAAC;MACtG,CAAC,MAAM,IAAI3D,YAAY,IAAI,CAACW,QAAQ,CAACI,WAAW,EAAE;QAC9CsB,OAAO,CAACG,GAAG,CAAC,iEAAiE,CAAC;MAClF;;MAEA;MACA,IAAIxC,YAAY,KAAK,CAACA,YAAY,CAACsC,EAAE,IAAIsB,KAAK,CAAC5D,YAAY,CAACsC,EAAE,CAAC,CAAC,EAAE;QAC9DvD,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,2CAA2C,EAAE,OAAO,CAAC;QACzE;MACJ;;MAEA;MACA,MAAMa,cAAc,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACrCD,cAAc,CAACE,MAAM,CAAC,OAAO,EAAEpD,QAAQ,CAACE,KAAK,CAAC6C,IAAI,CAAC,CAAC,CAAC;MACrDG,cAAc,CAACE,MAAM,CAAC,aAAa,EAAEpD,QAAQ,CAACG,WAAW,CAAC4C,IAAI,CAAC,CAAC,CAAC;MACjEG,cAAc,CAACE,MAAM,CAAC,YAAY,EAAEpD,QAAQ,CAACM,UAAU,CAAC;MACxD4C,cAAc,CAACE,MAAM,CAAC,WAAW,EAAEpD,QAAQ,CAACO,SAAS,CAAC;MACtD2C,cAAc,CAACE,MAAM,CAAC,kBAAkB,EAAEpD,QAAQ,CAACK,gBAAgB,CAAC;MAEpE,IAAIhB,YAAY,EAAE;QACd6D,cAAc,CAACE,MAAM,CAAC,IAAI,EAAE/D,YAAY,CAACsC,EAAE,CAAC;QAC5CuB,cAAc,CAACE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;MAC7C;MAEA,IAAIpD,QAAQ,CAACI,WAAW,EAAE;QACtB8C,cAAc,CAACE,MAAM,CAAC,aAAa,EAAEpD,QAAQ,CAACI,WAAW,CAAC;MAC9D;;MAEA;MACAsB,OAAO,CAACG,GAAG,CAAC,yBAAyB,EAAE;QACnCwB,MAAM,EAAEhE,YAAY,GAAG,gBAAgB,GAAG,MAAM;QAChDyD,GAAG;QACH5C,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBI,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BF,gBAAgB,EAAEL,QAAQ,CAACK,gBAAgB;QAC3CiD,SAAS,EAAEjE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsC,EAAE;QAC3B4B,OAAO,EAAE,CAAC,CAACvD,QAAQ,CAACI,WAAW;QAC/BoD,QAAQ,EAAExD,QAAQ,CAACI,WAAW,GAAGJ,QAAQ,CAACI,WAAW,CAACqD,IAAI,GAAG,CAAC;QAC9D1C,KAAK,EAAEA,KAAK,GAAG,SAAS,GAAG;MAC/B,CAAC,CAAC;;MAEF;MACAW,OAAO,CAACG,GAAG,CAAC,uBAAuB,CAAC;MACpC,KAAK,IAAI,CAAC6B,GAAG,EAAEC,KAAK,CAAC,IAAIT,cAAc,CAACU,OAAO,CAAC,CAAC,EAAE;QAC/C,IAAID,KAAK,YAAYE,IAAI,EAAE;UACvBnC,OAAO,CAACG,GAAG,CAAC,KAAK6B,GAAG,UAAUC,KAAK,CAACX,IAAI,KAAKW,KAAK,CAACF,IAAI,SAAS,CAAC;QACrE,CAAC,MAAM;UACH/B,OAAO,CAACG,GAAG,CAAC,KAAK6B,GAAG,KAAKC,KAAK,EAAE,CAAC;QACrC;MACJ;;MAEA;MACA,MAAMzC,QAAQ,GAAG,MAAM/C,KAAK,CAAC;QACzBkF,MAAM,EAAE,MAAM;QACdP,GAAG;QACHtB,IAAI,EAAE0B,cAAc;QACpB9B,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB,CAAC;QACD+C,OAAO,EAAE,KAAK,CAAC;MACnB,CAAC,CAAC;MAEFpC,OAAO,CAACG,GAAG,CAAC,qBAAqB,EAAE;QAC/BkC,MAAM,EAAE7C,QAAQ,CAAC6C,MAAM;QACvBvC,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBJ,OAAO,EAAEF,QAAQ,CAACE;MACtB,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACwC,OAAO,KAAK,IAAI,EAAE;QACjD5F,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,SAAShD,YAAY,GAAG,SAAS,GAAG,MAAM,cAAc,EAAE,SAAS,CAAC;QACxFD,YAAY,CAAC,KAAK,CAAC;QACnBE,eAAe,CAAC,IAAI,CAAC;QACrB2E,SAAS,CAAC,CAAC;QACXrD,UAAU,CAAC,CAAC;MAChB,CAAC,MAAM;QAAA,IAAAsD,cAAA,EAAAC,eAAA;QACH,MAAMC,QAAQ,GAAG,EAAAF,cAAA,GAAAhD,QAAQ,CAACM,IAAI,cAAA0C,cAAA,uBAAbA,cAAA,CAAezC,KAAK,OAAA0C,eAAA,GAAIjD,QAAQ,CAACM,IAAI,cAAA2C,eAAA,uBAAbA,eAAA,CAAeE,OAAO,KAAI,iBAAiB;QACpF,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAAC;MAC7B;IACJ,CAAC,CAAC,OAAO3C,KAAK,EAAE;MAAA,IAAA8C,eAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACZvD,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAE;QAChC4C,OAAO,EAAE5C,KAAK,CAAC4C,OAAO;QACtBnD,QAAQ,GAAAqD,eAAA,GAAE9C,KAAK,CAACP,QAAQ,cAAAqD,eAAA,uBAAdA,eAAA,CAAgB/C,IAAI;QAC9BuC,MAAM,GAAAS,gBAAA,GAAE/C,KAAK,CAACP,QAAQ,cAAAsD,gBAAA,uBAAdA,gBAAA,CAAgBT,MAAM;QAC9BmB,MAAM,EAAE;UACJ7B,MAAM,GAAAoB,aAAA,GAAEhD,KAAK,CAACyD,MAAM,cAAAT,aAAA,uBAAZA,aAAA,CAAcpB,MAAM;UAC5BP,GAAG,GAAA4B,cAAA,GAAEjD,KAAK,CAACyD,MAAM,cAAAR,cAAA,uBAAZA,cAAA,CAAc5B,GAAG;UACtBtB,IAAI,GAAAmD,cAAA,GAAElD,KAAK,CAACyD,MAAM,cAAAP,cAAA,uBAAZA,cAAA,CAAcnD;QACxB;MACJ,CAAC,CAAC;MAEF,IAAI2D,YAAY,GAAG,yBAAyB;MAE5C,KAAAP,gBAAA,GAAInD,KAAK,CAACP,QAAQ,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpD,IAAI,cAAAqD,qBAAA,uBAApBA,qBAAA,CAAsBpD,KAAK,EAAE;QAC7B0D,YAAY,GAAG1D,KAAK,CAACP,QAAQ,CAACM,IAAI,CAACC,KAAK;MAC5C,CAAC,MAAM,KAAAqD,gBAAA,GAAIrD,KAAK,CAACP,QAAQ,cAAA4D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtD,IAAI,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,EAAE;QACtCc,YAAY,GAAG1D,KAAK,CAACP,QAAQ,CAACM,IAAI,CAAC6C,OAAO;MAC9C,CAAC,MAAM,IAAI,EAAAW,gBAAA,GAAAvD,KAAK,CAACP,QAAQ,cAAA8D,gBAAA,uBAAdA,gBAAA,CAAgBjB,MAAM,MAAK,GAAG,EAAE;QACvCoB,YAAY,GAAG,2CAA2C;MAC9D,CAAC,MAAM,IAAI,EAAAF,gBAAA,GAAAxD,KAAK,CAACP,QAAQ,cAAA+D,gBAAA,uBAAdA,gBAAA,CAAgBlB,MAAM,MAAK,GAAG,EAAE;QACvCoB,YAAY,GAAG,+CAA+C;MAClE,CAAC,MAAM,IAAI1D,KAAK,CAAC4C,OAAO,EAAE;QACtBc,YAAY,GAAG1D,KAAK,CAAC4C,OAAO;MAChC;MAEAjG,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE8C,YAAY,EAAE,OAAO,CAAC;IAC9C;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIzG,KAAK,IAAK;IAC1B,IAAI,CAACgC,SAAS,EAAE;MACZvC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,qEAAqE,EAAE,OAAO,CAAC;MACnG;IACJ;;IAEA;IACA,IAAI,CAAC1D,KAAK,IAAI,CAACA,KAAK,CAACgD,EAAE,EAAE;MACrBvD,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC;MAC9C;IACJ;IAEAX,OAAO,CAACG,GAAG,CAAC,mBAAmB,EAAE;MAC7BF,EAAE,EAAEhD,KAAK,CAACgD,EAAE;MACZzB,KAAK,EAAEvB,KAAK,CAACuB,KAAK;MAClBI,UAAU,EAAE3B,KAAK,CAAC2B,UAAU;MAC5BC,SAAS,EAAE5B,KAAK,CAAC4B,SAAS;MAC1BF,gBAAgB,EAAE1B,KAAK,CAAC0B;IAC5B,CAAC,CAAC;IAEFf,eAAe,CAACX,KAAK,CAAC;IACtBsB,WAAW,CAAC;MACRC,KAAK,EAAEvB,KAAK,CAACuB,KAAK,IAAI,EAAE;MACxBC,WAAW,EAAExB,KAAK,CAACwB,WAAW,IAAI,EAAE;MACpCC,WAAW,EAAE,IAAI;MAAE;MACnBC,gBAAgB,EAAE1B,KAAK,CAAC0B,gBAAgB,IAAI,EAAE;MAC9CC,UAAU,EAAE3B,KAAK,CAAC2B,UAAU,IAAI,EAAE;MAClCC,SAAS,EAAE5B,KAAK,CAAC4B,SAAS,IAAI;IAClC,CAAC,CAAC;IACFnB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiG,YAAY,GAAG,MAAO1D,EAAE,IAAK;IAC/B,IAAI,CAAChB,SAAS,EAAE;MACZvC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,sEAAsE,EAAE,OAAO,CAAC;MACpG;IACJ;;IAEA;IACA,IAAI,CAACV,EAAE,IAAIsB,KAAK,CAACtB,EAAE,CAAC,EAAE;MAClBvD,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,sBAAsB,EAAE,OAAO,CAAC;MACpD;IACJ;IAEA,MAAMiD,MAAM,GAAG,MAAMlH,IAAI,CAACiE,IAAI,CAAC;MAC3BC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,2DAA2D;MACjEC,IAAI,EAAE,SAAS;MACf+C,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIL,MAAM,CAACM,WAAW,EAAE;MACpB,IAAI;QACA,MAAM7E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM6B,GAAG,GAAG,4DAA4D;QAExEpB,OAAO,CAACG,GAAG,CAAC,uBAAuB,EAAE;UACjCF,EAAE,EAAEA,EAAE;UACNkE,IAAI,EAAE,OAAOlE,EAAE;UACfmB,GAAG,EAAEA,GAAG;UACR/B,KAAK,EAAEA,KAAK,GAAG,SAAS,GAAG;QAC/B,CAAC,CAAC;QAEF,MAAMG,QAAQ,GAAG,MAAM/C,KAAK,CAAC;UACzBkF,MAAM,EAAE,QAAQ;UAChBP,GAAG,EAAEA,GAAG;UACR1B,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDS,IAAI,EAAEsE,IAAI,CAACC,SAAS,CAAC;YAAEpE,EAAE,EAAEqE,QAAQ,CAACrE,EAAE;UAAE,CAAC,CAAC;UAC1CmC,OAAO,EAAE;QACb,CAAC,CAAC;QAEFpC,OAAO,CAACG,GAAG,CAAC,wBAAwB,EAAE;UAClCkC,MAAM,EAAE7C,QAAQ,CAAC6C,MAAM;UACvBvC,IAAI,EAAEN,QAAQ,CAACM;QACnB,CAAC,CAAC;QAEF,IAAIN,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACwC,OAAO,KAAK,IAAI,EAAE;UACjD5F,IAAI,CAACiE,IAAI,CAAC,WAAW,EAAE,gDAAgD,EAAE,SAAS,CAAC;UACnFzB,UAAU,CAAC,CAAC;QAChB,CAAC,MAAM;UAAA,IAAAqF,eAAA,EAAAC,eAAA;UACH,MAAM9B,QAAQ,GAAG,EAAA6B,eAAA,GAAA/E,QAAQ,CAACM,IAAI,cAAAyE,eAAA,uBAAbA,eAAA,CAAexE,KAAK,OAAAyE,eAAA,GAAIhF,QAAQ,CAACM,IAAI,cAAA0E,eAAA,uBAAbA,eAAA,CAAe7B,OAAO,KAAI,+BAA+B;UAClG3C,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEP,QAAQ,CAACM,IAAI,CAAC;UACzD,MAAM,IAAI8C,KAAK,CAACF,QAAQ,CAAC;QAC7B;MACJ,CAAC,CAAC,OAAO3C,KAAK,EAAE;QAAA,IAAA0E,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QACZhF,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAE;UAC5C4C,OAAO,EAAE5C,KAAK,CAAC4C,OAAO;UACtBnD,QAAQ,GAAAiF,gBAAA,GAAE1E,KAAK,CAACP,QAAQ,cAAAiF,gBAAA,uBAAdA,gBAAA,CAAgB3E,IAAI;UAC9BuC,MAAM,GAAAqC,gBAAA,GAAE3E,KAAK,CAACP,QAAQ,cAAAkF,gBAAA,uBAAdA,gBAAA,CAAgBrC,MAAM;UAC9BpC,EAAE,EAAEA;QACR,CAAC,CAAC;QAEF,IAAIwD,YAAY,GAAG,kCAAkC;QAErD,IAAI,EAAAkB,gBAAA,GAAA5E,KAAK,CAACP,QAAQ,cAAAmF,gBAAA,uBAAdA,gBAAA,CAAgBtC,MAAM,MAAK,GAAG,EAAE;UAChCoB,YAAY,GAAG,kBAAkB;QACrC,CAAC,MAAM,IAAI,EAAAmB,gBAAA,GAAA7E,KAAK,CAACP,QAAQ,cAAAoF,gBAAA,uBAAdA,gBAAA,CAAgBvC,MAAM,MAAK,GAAG,EAAE;UACvCoB,YAAY,GAAG,oBAAoB;QACvC,CAAC,MAAM,KAAAoB,gBAAA,GAAI9E,KAAK,CAACP,QAAQ,cAAAqF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/E,IAAI,cAAAgF,qBAAA,uBAApBA,qBAAA,CAAsB/E,KAAK,EAAE;UACpC0D,YAAY,GAAG1D,KAAK,CAACP,QAAQ,CAACM,IAAI,CAACC,KAAK;QAC5C,CAAC,MAAM,KAAAgF,iBAAA,GAAIhF,KAAK,CAACP,QAAQ,cAAAuF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBjF,IAAI,cAAAkF,qBAAA,uBAApBA,qBAAA,CAAsBrC,OAAO,EAAE;UACtCc,YAAY,GAAG1D,KAAK,CAACP,QAAQ,CAACM,IAAI,CAAC6C,OAAO;QAC9C,CAAC,MAAM,IAAI5C,KAAK,CAAC4C,OAAO,EAAE;UACtBc,YAAY,GAAG1D,KAAK,CAAC4C,OAAO;QAChC;QAEAjG,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE8C,YAAY,EAAE,OAAO,CAAC;MAC9C;IACJ;EACJ,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAG,MAAOhI,KAAK,IAAK;IACvC,IAAI;MAAA,IAAAiI,qBAAA;MACA,MAAM7F,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,IAAI,CAACtC,KAAK,CAACyB,WAAW,EAAE;QACpBhC,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,sCAAsC,EAAE,OAAO,CAAC;QACpE;MACJ;MAEA,MAAMS,GAAG,GAAG,sEAAsE+D,kBAAkB,CAAClI,KAAK,CAACyB,WAAW,CAAC,aAAazB,KAAK,CAACgD,EAAE,EAAE;MAE9ID,OAAO,CAACG,GAAG,CAAC,wBAAwB,EAAE;QAClC3B,KAAK,EAAEvB,KAAK,CAACuB,KAAK;QAClB4G,OAAO,EAAEnI,KAAK,CAACyB,WAAW;QAC1B0C,GAAG,EAAEA;MACT,CAAC,CAAC;MAEF,MAAM5B,QAAQ,GAAG,MAAM/C,KAAK,CAAC;QACzBkF,MAAM,EAAE,KAAK;QACbP,GAAG;QACH1B,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK;QAClC,CAAC;QACDgG,YAAY,EAAE,MAAM;QACpBjD,OAAO,EAAE,KAAK,CAAC;MACnB,CAAC,CAAC;;MAEF;MACA,IAAI5C,QAAQ,CAACM,IAAI,CAACqE,IAAI,KAAK,iBAAiB,IAAI,GAAAe,qBAAA,GAAC1F,QAAQ,CAACE,OAAO,CAAC,cAAc,CAAC,cAAAwF,qBAAA,uBAAhCA,qBAAA,CAAkCI,QAAQ,CAAC,iBAAiB,CAAC,GAAE;QAC5GtF,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEP,QAAQ,CAACM,IAAI,CAACqE,IAAI,CAAC;QACjEzH,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,gDAAgD,EAAE,OAAO,CAAC;QAC9E;MACJ;;MAEA;MACA,MAAM4E,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAChG,QAAQ,CAACM,IAAI,CAAC,EAAE;QAAEqE,IAAI,EAAE;MAAkB,CAAC,CAAC;MACnE,MAAMsB,WAAW,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACpD,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,WAAW;MACvBI,IAAI,CAACI,QAAQ,GAAG,GAAGhJ,KAAK,CAACuB,KAAK,CAAC0H,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,MAAM;MAClEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZR,IAAI,CAACS,MAAM,CAAC,CAAC;MACbZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,WAAW,CAAC;MAEvCzF,OAAO,CAACG,GAAG,CAAC,0BAA0B,EAAElD,KAAK,CAACuB,KAAK,CAAC;MACpD9B,IAAI,CAACiE,IAAI,CAAC;QACNC,KAAK,EAAE,wBAAwB;QAC/BC,IAAI,EAAE,eAAe5D,KAAK,CAACuB,KAAK,yBAAyB;QACzDsC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI;QACXC,iBAAiB,EAAE;MACvB,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAyG,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MACZ5G,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAE;QAC/C4C,OAAO,EAAE5C,KAAK,CAAC4C,OAAO;QACtBnD,QAAQ,GAAAgH,iBAAA,GAAEzG,KAAK,CAACP,QAAQ,cAAAgH,iBAAA,uBAAdA,iBAAA,CAAgB1G,IAAI;QAC9BuC,MAAM,GAAAoE,iBAAA,GAAE1G,KAAK,CAACP,QAAQ,cAAAiH,iBAAA,uBAAdA,iBAAA,CAAgBpE,MAAM;QAC9BpF,KAAK,EAAEA,KAAK,CAACuB;MACjB,CAAC,CAAC;MAEF,IAAIiF,YAAY,GAAG,0CAA0C;MAE7D,IAAI,EAAAiD,iBAAA,GAAA3G,KAAK,CAACP,QAAQ,cAAAkH,iBAAA,uBAAdA,iBAAA,CAAgBrE,MAAM,MAAK,GAAG,EAAE;QAChCoB,YAAY,GAAG,uCAAuC;MAC1D,CAAC,MAAM,IAAI,EAAAkD,iBAAA,GAAA5G,KAAK,CAACP,QAAQ,cAAAmH,iBAAA,uBAAdA,iBAAA,CAAgBtE,MAAM,MAAK,GAAG,EAAE;QACvCoB,YAAY,GAAG,mCAAmC;MACtD,CAAC,MAAM,IAAI1D,KAAK,CAAC8G,IAAI,KAAK,cAAc,EAAE;QACtCpD,YAAY,GAAG,kDAAkD;MACrE,CAAC,MAAM,KAAAmD,iBAAA,GAAI7G,KAAK,CAACP,QAAQ,cAAAoH,iBAAA,uBAAdA,iBAAA,CAAgB9G,IAAI,EAAE;QAC7B,IAAI;UACA,MAAMgH,SAAS,GAAG,MAAM/G,KAAK,CAACP,QAAQ,CAACM,IAAI,CAACe,IAAI,CAAC,CAAC;UAClD,MAAMkG,SAAS,GAAG3C,IAAI,CAAC4C,KAAK,CAACF,SAAS,CAAC;UACvCrD,YAAY,GAAGsD,SAAS,CAAChH,KAAK,IAAI0D,YAAY;QAClD,CAAC,CAAC,OAAOvC,CAAC,EAAE;UACR;QAAA;MAER;MAEAxE,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE8C,YAAY,EAAE,OAAO,CAAC;IAC9C;EACJ,CAAC;EAED,MAAMlB,SAAS,GAAGA,CAAA,KAAM;IACpBhE,WAAW,CAAC;MACRC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,IAAI;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE;IACf,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMoI,OAAO,GAAG,MAAAA,CAAA,KAAY;IACxB,IAAI;MACA,MAAM5H,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM6B,GAAG,GAAG,+DAA+D;;MAE3E;MACA,MAAMI,cAAc,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACrCD,cAAc,CAACE,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC;MAChCF,cAAc,CAACE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC;MAC1CF,cAAc,CAACE,MAAM,CAAC,aAAa,EAAE,kBAAkB,CAAC;MACxDF,cAAc,CAACE,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC;MACxCF,cAAc,CAACE,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;MACvCF,cAAc,CAACE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC;MAEvD1B,OAAO,CAACG,GAAG,CAAC,oCAAoC,CAAC;MAEjD,MAAMX,QAAQ,GAAG,MAAM/C,KAAK,CAAC;QACzBkF,MAAM,EAAE,KAAK;QACbP,GAAG;QACHtB,IAAI,EAAE0B,cAAc;QACpB9B,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB,CAAC;QACD+C,OAAO,EAAE;MACb,CAAC,CAAC;MAEFpC,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAEX,QAAQ,CAACM,IAAI,CAAC;MAChDpD,IAAI,CAACiE,IAAI,CAAC;QACNC,KAAK,EAAE,iBAAiB;QACxBsG,IAAI,EAAE,QAAQ9C,IAAI,CAACC,SAAS,CAAC7E,QAAQ,CAACM,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;QAC5DgB,IAAI,EAAE,SAAS;QACfqG,KAAK,EAAE;MACX,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOpH,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CrD,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,mBAAmB,IAAIZ,KAAK,CAAC4C,OAAO,IAAI,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5F;EACJ,CAAC;;EAED;EACA,MAAMyE,OAAO,GAAG,MAAAA,CAAA,KAAY;IACxB,IAAI;MAAA,IAAAC,eAAA;MACA,MAAMhI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM6B,GAAG,GAAG,4DAA4D;MAExEpB,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAE;QACvCiB,GAAG,EAAEA,GAAG;QACR/B,KAAK,EAAEA,KAAK,GAAG,SAAS,GAAG;MAC/B,CAAC,CAAC;MAEF,MAAMG,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC2B,GAAG,EAAE;QAClC1B,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB,CAAC;QACD+C,OAAO,EAAE;MACb,CAAC,CAAC;MAEFpC,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAE;QAC9BkC,MAAM,EAAE7C,QAAQ,CAAC6C,MAAM;QACvBiF,UAAU,EAAE,EAAAD,eAAA,GAAA7H,QAAQ,CAACM,IAAI,cAAAuH,eAAA,uBAAbA,eAAA,CAAehH,MAAM,KAAI,CAAC;QACtCP,IAAI,EAAEN,QAAQ,CAACM;MACnB,CAAC,CAAC;MAEF,MAAMyH,UAAU,GAAG3H,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACM,IAAI,CAAC,GAAGN,QAAQ,CAACM,IAAI,CAACO,MAAM,GAAG,CAAC;MAC1E3D,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,mBAAmB4G,UAAU,gBAAgB,EAAE,SAAS,CAAC;IAEjF,CAAC,CAAC,OAAOxH,KAAK,EAAE;MAAA,IAAAyH,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MACZ3H,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAE;QAChC4C,OAAO,EAAE5C,KAAK,CAAC4C,OAAO;QACtBnD,QAAQ,GAAAgI,iBAAA,GAAEzH,KAAK,CAACP,QAAQ,cAAAgI,iBAAA,uBAAdA,iBAAA,CAAgB1H,IAAI;QAC9BuC,MAAM,GAAAoF,iBAAA,GAAE1H,KAAK,CAACP,QAAQ,cAAAiI,iBAAA,uBAAdA,iBAAA,CAAgBpF,MAAM;QAC9BwE,IAAI,EAAE9G,KAAK,CAAC8G;MAChB,CAAC,CAAC;MAEF,IAAIpD,YAAY,GAAG,uBAAuB;MAE1C,IAAI1D,KAAK,CAAC8G,IAAI,KAAK,cAAc,EAAE;QAC/BpD,YAAY,GAAG,iEAAiE;MACpF,CAAC,MAAM,IAAI1D,KAAK,CAAC8G,IAAI,KAAK,cAAc,EAAE;QACtCpD,YAAY,GAAG,mDAAmD;MACtE,CAAC,MAAM,IAAI,EAAAiE,iBAAA,GAAA3H,KAAK,CAACP,QAAQ,cAAAkI,iBAAA,uBAAdA,iBAAA,CAAgBrF,MAAM,MAAK,GAAG,EAAE;QACvCoB,YAAY,GAAG,8CAA8C;MACjE,CAAC,MAAM,IAAI,EAAAkE,iBAAA,GAAA5H,KAAK,CAACP,QAAQ,cAAAmI,iBAAA,uBAAdA,iBAAA,CAAgBtF,MAAM,MAAK,GAAG,EAAE;QACvCoB,YAAY,GAAG,wCAAwC;MAC3D,CAAC,MAAM,IAAI1D,KAAK,CAAC4C,OAAO,EAAE;QACtBc,YAAY,GAAG1D,KAAK,CAAC4C,OAAO;MAChC;MAEAjG,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE8C,YAAY,EAAE,OAAO,CAAC;IAC9C;EACJ,CAAC;;EAED;EACA,MAAMmE,aAAa,GAAG3K,KAAK,CAAC4K,MAAM,CAACC,CAAC,IAAI;IAAA,IAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,YAAA;IACpC,MAAMC,aAAa,GAAG,EAAAN,QAAA,GAAAD,CAAC,CAACtJ,KAAK,cAAAuJ,QAAA,uBAAPA,QAAA,CAASO,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACzH,UAAU,CAACyK,WAAW,CAAC,CAAC,CAAC,OAAAN,cAAA,GAC1DF,CAAC,CAACrJ,WAAW,cAAAuJ,cAAA,uBAAbA,cAAA,CAAeM,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACzH,UAAU,CAACyK,WAAW,CAAC,CAAC,CAAC,OAAAL,cAAA,GAC/DH,CAAC,CAACtH,WAAW,cAAAyH,cAAA,uBAAbA,cAAA,CAAeK,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACzH,UAAU,CAACyK,WAAW,CAAC,CAAC,CAAC,OAAAJ,aAAA,GAC/DJ,CAAC,CAACrH,UAAU,cAAAyH,aAAA,uBAAZA,aAAA,CAAcI,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACzH,UAAU,CAACyK,WAAW,CAAC,CAAC,CAAC;IAEnF,MAAMC,cAAc,GAAGxK,aAAa,KAAK,KAAK,IAAI,EAAAoK,aAAA,GAAAL,CAAC,CAAClJ,UAAU,cAAAuJ,aAAA,uBAAZA,aAAA,CAAcK,QAAQ,CAAC,CAAC,MAAKzK,aAAa;IAC5F,MAAM0K,aAAa,GAAGxK,YAAY,KAAK,KAAK,IAAI,EAAAmK,YAAA,GAAAN,CAAC,CAACjJ,SAAS,cAAAuJ,YAAA,uBAAXA,YAAA,CAAaI,QAAQ,CAAC,CAAC,MAAKvK,YAAY;IAExF,OAAOoK,aAAa,IAAIE,cAAc,IAAIE,aAAa;EAC3D,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGvK,WAAW,GAAGE,YAAY;EAClD,MAAMsK,gBAAgB,GAAGD,eAAe,GAAGrK,YAAY;EACvD,MAAMuK,YAAY,GAAGhB,aAAa,CAACiB,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACpB,aAAa,CAACvH,MAAM,GAAGhC,YAAY,CAAC;EAEjE,MAAM4K,QAAQ,GAAIC,UAAU,IAAK9K,cAAc,CAAC8K,UAAU,CAAC;;EAE3D;EACA9M,KAAK,CAACE,SAAS,CAAC,MAAM;IAClB8B,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACP,UAAU,EAAEE,aAAa,EAAEE,YAAY,CAAC,CAAC;EAE7C,IAAIV,OAAO,EAAE;IACT,oBACInB,KAAA,CAAA2J,aAAA;MAAKoD,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BrN,KAAA,CAAA2J,aAAA;MAAKoD,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BrN,KAAA,CAAA2J,aAAA;MAAAqD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,yBAA0B,CAC5B,CAAC;EAEd;EAEA,oBACIrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iCAAyB,CAAC,eAC9BrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBrN,KAAA,CAAA2J,aAAA;IAAMoD,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB7B,aAAa,CAACvH,MAAM,EAAC,qBACtB,EAACyI,UAAU,GAAG,CAAC,IAAI,WAAW3K,WAAW,IAAI2K,UAAU,EACrD,CAAC,eACP1M,KAAA,CAAA2J,aAAA;IAAK2D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCxK,SAAS,iBACN7C,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,iBAAiB;IAC3BU,OAAO,EAAEA,CAAA,KAAMnM,YAAY,CAAC,IAAI,CAAE;IAAA0L,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCrN,KAAA,CAAA2J,aAAA;IAAK+D,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,kBACjC,CAGX,CACJ,CACJ,CAAC,EAGL,CAACxK,SAAS,iBACP7C,KAAA,CAAA2J,aAAA;IAAK2D,KAAK,EAAE;MACRM,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACZ,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErN,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sNAI1C,CACF,CACR,eAGDrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,iBAAiB;IAACO,KAAK,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXO,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE;IAClB,CAAE;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAE,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CrN,KAAA,CAAA2J,aAAA;IACI5B,IAAI,EAAC,MAAM;IACXqG,WAAW,EAAC,qCAA2B;IACvCvI,KAAK,EAAEpE,UAAW;IAClB4M,QAAQ,EAAGvJ,CAAC,IAAK;MACbpD,aAAa,CAACoD,CAAC,CAACwJ,MAAM,CAACzI,KAAK,CAAC;MAC7B7D,cAAc,CAAC,CAAC,CAAC;IACrB,CAAE;IACFsL,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE;IAClB,CAAE;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACNrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BrN,KAAA,CAAA2J,aAAA;IACI9D,KAAK,EAAElE,aAAc;IACrB0M,QAAQ,EAAGvJ,CAAC,IAAK;MACblD,gBAAgB,CAACkD,CAAC,CAACwJ,MAAM,CAACzI,KAAK,CAAC;MAChC7D,cAAc,CAAC,CAAC,CAAC;IACrB,CAAE;IACFsL,KAAK,EAAE;MACHM,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE,KAAK;MACnBS,QAAQ,EAAE;IACd,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrN,KAAA,CAAA2J,aAAA;IAAQ9D,KAAK,EAAC,KAAK;IAAAmH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wBAA2B,CAAC,EAC/CtM,QAAQ,CAACyN,GAAG,CAACC,OAAO,iBACjBzO,KAAA,CAAA2J,aAAA;IAAQ/D,GAAG,EAAE6I,OAAO,CAAC5K,EAAG;IAACgC,KAAK,EAAE4I,OAAO,CAAC5K,EAAG;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtCoB,OAAO,CAAC3K,GACL,CACX,CACG,CACP,CAAC,eACN9D,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrN,KAAA,CAAA2J,aAAA;IACI9D,KAAK,EAAEhE,YAAa;IACpBwM,QAAQ,EAAGvJ,CAAC,IAAK;MACbhD,eAAe,CAACgD,CAAC,CAACwJ,MAAM,CAACzI,KAAK,CAAC;MAC/B7D,cAAc,CAAC,CAAC,CAAC;IACrB,CAAE;IACFsL,KAAK,EAAE;MACHM,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE,KAAK;MACnBS,QAAQ,EAAE;IACd,CAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrN,KAAA,CAAA2J,aAAA;IAAQ9D,KAAK,EAAC,KAAK;IAAAmH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAA0B,CAAC,EAC9CpM,OAAO,CAACuN,GAAG,CAACE,MAAM,iBACf1O,KAAA,CAAA2J,aAAA;IAAQ/D,GAAG,EAAE8I,MAAM,CAAC7K,EAAG;IAACgC,KAAK,EAAE6I,MAAM,CAAC7K,EAAG;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCqB,MAAM,CAAC5K,GACJ,CACX,CACG,CACP,CACJ,CAAC,eAEN9D,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB7B,aAAa,CAACvH,MAAM,KAAK,CAAC,gBACvBjE,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBrN,KAAA,CAAA2J,aAAA;IAAK+D,GAAG,EAAC,eAAe;IAACC,GAAG,EAAC,aAAa;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC7CrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,uBAAqB,CAAC,EACxB,CAAC5L,UAAU,IAAIE,aAAa,KAAK,KAAK,IAAIE,YAAY,KAAK,KAAK,kBAC7D7B,KAAA,CAAA2J,aAAA;IACI8D,OAAO,EAAEA,CAAA,KAAM;MACX/L,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,KAAK,CAAC;MACvBE,eAAe,CAAC,KAAK,CAAC;IAC1B,CAAE;IACFiL,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,qBAEO,CAEX,CAAC,gBAENrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BrN,KAAA,CAAA2J,aAAA;IAAOoD,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,eACdrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,6BAAqB,CAAC,eAC1BrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eACnBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAkB,CAAC,eACvBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EACjBxK,SAAS,iBAAI7C,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAChC,CACD,CAAC,eACRrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKb,YAAY,CAACgC,GAAG,CAAE9C,CAAC,iBAChB1L,KAAA,CAAA2J,aAAA;IAAI/D,GAAG,EAAE8F,CAAC,CAAC7H,EAAG;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACVrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MACTM,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB,CAAE;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACE,EAAC3B,CAAC,CAAC7H,EACF,CACN,CAAC,eACL7D,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS3B,CAAC,CAACtJ,KAAc,CAAC,EACzBsJ,CAAC,CAACrJ,WAAW,iBACVrC,KAAA,CAAA2J,aAAA;IAAO2D,KAAK,EAAE;MACVC,OAAO,EAAE,OAAO;MAChBW,KAAK,EAAE,SAAS;MAChBW,SAAS,EAAE;IACf,CAAE;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG3B,CAAC,CAACrJ,WAAW,CAAC4B,MAAM,GAAG,EAAE,GACpByH,CAAC,CAACrJ,WAAW,CAACyM,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GACtCpD,CAAC,CAACrJ,WACL,CAEV,CACL,CAAC,eACLrC,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MACTM,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE,OAAO;MACjBT,KAAK,EAAE;IACX,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG3B,CAAC,CAACtH,WAAW,IAAI,aAChB,CACN,CAAC,eACLpE,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MACTM,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE,OAAO;MACjBT,KAAK,EAAE;IACX,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG3B,CAAC,CAACrH,UAAU,IAAI,aACf,CACN,CAAC,eACLrE,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MAAEqB,QAAQ,EAAE;IAAQ,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B,IAAI0B,IAAI,CAACrD,CAAC,CAACnJ,gBAAgB,CAAC,CAACyM,kBAAkB,CAAC,OAAO,CACtD,CACN,CAAC,eACLhP,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,wBAAwB;IAClCU,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC6C,CAAC,CAAE;IACpClH,KAAK,EAAC,0BAAoB;IAC1B8I,KAAK,EAAE;MACHC,OAAO,EAAE,MAAM;MACf0B,UAAU,EAAE,QAAQ;MACpBzB,GAAG,EAAE,KAAK;MACVmB,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,kBAEO,CACR,CAAC,eACLrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MACTqB,QAAQ,EAAE,OAAO;MACjBT,KAAK,EAAE;IACX,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG3B,CAAC,CAACpH,cAAc,IAAI,KACnB,CACN,CAAC,EACJzB,SAAS,iBACN7C,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BrN,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,wBAAwB;IAClCU,OAAO,EAAEA,CAAA,KAAMnG,UAAU,CAACoE,CAAC,CAAE;IAC7BlH,KAAK,EAAC,UAAU;IAAAwI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhBrN,KAAA,CAAA2J,aAAA;IAAK+D,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTrN,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,uBAAuB;IACjCU,OAAO,EAAEA,CAAA,KAAMlG,YAAY,CAACmE,CAAC,CAAC7H,EAAE,CAAE;IAClCW,KAAK,EAAC,WAAW;IAAAwI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjBrN,KAAA,CAAA2J,aAAA;IAAK+D,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLX,UAAU,GAAG,CAAC,iBACX1M,KAAA,CAAA2J,aAAA;IAAK2D,KAAK,EAAE;MACRC,OAAO,EAAE,MAAM;MACf2B,cAAc,EAAE,QAAQ;MACxBD,UAAU,EAAE,QAAQ;MACpBJ,SAAS,EAAE,MAAM;MACjBrB,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErN,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,mBAAmB;IAC7BU,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC9K,WAAW,GAAG,CAAC,CAAE;IACzCoN,QAAQ,EAAEpN,WAAW,KAAK,CAAE;IAAAiL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B,8BAEO,CAAC,eAETrN,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MACTM,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OACM,EAACtL,WAAW,EAAC,OAAK,EAAC2K,UACtB,CAAC,eAEP1M,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,mBAAmB;IAC7BU,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC9K,WAAW,GAAG,CAAC,CAAE;IACzCoN,QAAQ,EAAEpN,WAAW,KAAK2K,UAAW;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,sBAEO,CACP,CACR,EAGA7B,aAAa,CAACvH,MAAM,GAAG,CAAC,iBACrBjE,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAACO,KAAK,EAAE;MAClCuB,SAAS,EAAE,MAAM;MACjBjB,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBP,OAAO,EAAE,MAAM;MACf6B,mBAAmB,EAAE,sCAAsC;MAC3D5B,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE+B,SAAS,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDrN,KAAA,CAAA2J,aAAA;IAAI2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC7B,aAAa,CAACvH,MACf,CAAC,eACLjE,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iBAAkB,CACtE,CAAC,eACNrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE+B,SAAS,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDrN,KAAA,CAAA2J,aAAA;IAAI2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCtM,QAAQ,CAACkD,MACV,CAAC,eACLjE,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yBAAuB,CAC3E,CAAC,eACNrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE+B,SAAS,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDrN,KAAA,CAAA2J,aAAA;IAAI2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCpM,OAAO,CAACgD,MACT,CAAC,eACLjE,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qBAAsB,CAC1E,CAAC,eACNrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE+B,SAAS,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDrN,KAAA,CAAA2J,aAAA;IAAI2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCb,YAAY,CAACvI,MACd,CAAC,eACLjE,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAW,CAC/D,CACJ,CACR,EAGAhM,SAAS,IAAIwB,SAAS,iBACnB7C,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAACO,KAAK,EAAE;MAAEgC,QAAQ,EAAE;IAAQ,CAAE;IAAAtC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxDrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK9L,YAAY,GAAG,mBAAmB,GAAG,eAAoB,CAAC,eAC/DvB,KAAA,CAAA2J,aAAA;IACIoD,SAAS,EAAC,WAAW;IACrBU,OAAO,EAAEA,CAAA,KAAM;MACXnM,YAAY,CAAC,KAAK,CAAC;MACnBE,eAAe,CAAC,IAAI,CAAC;MACrB2E,SAAS,CAAC,CAAC;IACf,CAAE;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrN,KAAA,CAAA2J,aAAA;IAAK+D,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNrN,KAAA,CAAA2J,aAAA;IAAM4F,QAAQ,EAAE1K,YAAa;IAAAmI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAExB9L,YAAY,iBACTvB,KAAA,CAAA2J,aAAA;IAAK2D,KAAK,EAAE;MACRM,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACZ,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACErN,KAAA,CAAA2J,aAAA;IAAI2D,KAAK,EAAE;MAAEW,MAAM,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCACzB,EAAC9L,YAAY,CAACsC,EACxC,CAAC,eACL7D,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,OAAO;MAAEU,QAAQ,EAAE,MAAM;MAAET,KAAK,EAAE;IAAU,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAC5C,eAAArN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS9L,YAAY,CAACe,WAAW,IAAI,eAAwB,CAAC,EAChFf,YAAY,CAAC+C,cAAc,IAAI,KAAK/C,YAAY,CAAC+C,cAAc,GACjE,CAAC,eACJtE,KAAA,CAAA2J,aAAA;IAAG2D,KAAK,EAAE;MAAEW,MAAM,EAAE,WAAW;MAAEU,QAAQ,EAAE,MAAM;MAAET,KAAK,EAAE;IAAO,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6EAEjE,CACF,CACR,eAEDrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kBAAuB,CAAC,eAC/BrN,KAAA,CAAA2J,aAAA;IACI5B,IAAI,EAAC,MAAM;IACXlC,KAAK,EAAE3D,QAAQ,CAACE,KAAM;IACtBiM,QAAQ,EAAGvJ,CAAC,IAAK3C,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,KAAK,EAAE0C,CAAC,CAACwJ,MAAM,CAACzI;IAAK,CAAC,CAAE;IACnEuI,WAAW,EAAC,0CAAuC;IACnDoB,QAAQ;IACRlC,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,aAAkB,CAAC,eAC1BrN,KAAA,CAAA2J,aAAA;IACI9D,KAAK,EAAE3D,QAAQ,CAACG,WAAY;IAC5BgM,QAAQ,EAAGvJ,CAAC,IAAK3C,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,WAAW,EAAEyC,CAAC,CAACwJ,MAAM,CAACzI;IAAK,CAAC,CAAE;IACzEuI,WAAW,EAAC,yBAAyB;IACrCqB,IAAI,EAAC,GAAG;IACRnC,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE,MAAM;MAChBe,MAAM,EAAE;IACZ,CAAE;IAAA1C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,cACS,EAAC9L,YAAY,GAAG,6DAA6D,GAAG,GACzF,CAAC,eACRvB,KAAA,CAAA2J,aAAA;IACI5B,IAAI,EAAC,MAAM;IACX4H,MAAM,EAAC,MAAM;IACbtB,QAAQ,EAAGvJ,CAAC,IAAK;MACb,MAAM8K,IAAI,GAAG9K,CAAC,CAACwJ,MAAM,CAACuB,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACN;QACA,IAAIA,IAAI,CAAC7H,IAAI,KAAK,iBAAiB,EAAE;UACjCzH,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,sCAAsC,EAAE,OAAO,CAAC;UACpEO,CAAC,CAACwJ,MAAM,CAACzI,KAAK,GAAG,EAAE;UACnB;QACJ;QACA,IAAI+J,IAAI,CAACjK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;UAC9BrF,IAAI,CAACiE,IAAI,CAAC,QAAQ,EAAE,sCAAsC,EAAE,OAAO,CAAC;UACpEO,CAAC,CAACwJ,MAAM,CAACzI,KAAK,GAAG,EAAE;UACnB;QACJ;QACAjC,OAAO,CAACG,GAAG,CAAC,yBAAyB,EAAE;UACnCmB,IAAI,EAAE0K,IAAI,CAAC1K,IAAI;UACfS,IAAI,EAAE,CAACiK,IAAI,CAACjK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEmK,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UACpD/H,IAAI,EAAE6H,IAAI,CAAC7H;QACf,CAAC,CAAC;MACN;MACA5F,WAAW,CAAC;QAAC,GAAGD,QAAQ;QAAEI,WAAW,EAAEsN;MAAI,CAAC,CAAC;IACjD,CAAE;IACFJ,QAAQ,EAAE,CAACjO,YAAa;IACxB+L,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACFrN,KAAA,CAAA2J,aAAA;IAAO2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAES,QAAQ,EAAE;IAAO,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uDAElD,EAAC9L,YAAY,IAAI,CAACW,QAAQ,CAACI,WAAW,iBAClCtC,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAEX,OAAO,EAAE,OAAO;MAAEsB,SAAS,EAAE;IAAM,CAAE;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAChD,EAAC9L,YAAY,CAACe,WAAW,IAAI,eAAe,EAC9Df,YAAY,CAAC+C,cAAc,IAAI,KAAK/C,YAAY,CAAC+C,cAAc,GAC9D,CACT,EACApC,QAAQ,CAACI,WAAW,iBACjBtC,KAAA,CAAA2J,aAAA;IAAM2D,KAAK,EAAE;MAAEY,KAAK,EAAE,SAAS;MAAEX,OAAO,EAAE,OAAO;MAAEsB,SAAS,EAAE;IAAM,CAAE;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CACpC,EAACnL,QAAQ,CAACI,WAAW,CAAC4C,IAAI,EAAC,GACzD,EAAC,CAAChD,QAAQ,CAACI,WAAW,CAACqD,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEmK,OAAO,CAAC,CAAC,CAAC,EAAC,MACvD,CAEP,CACN,CAAC,eAEN9P,KAAA,CAAA2J,aAAA;IAAK2D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAE,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,cAAgB,CAAC,eACxBrN,KAAA,CAAA2J,aAAA;IACI9D,KAAK,EAAE3D,QAAQ,CAACM,UAAW;IAC3B6L,QAAQ,EAAGvJ,CAAC,IAAK3C,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEM,UAAU,EAAEsC,CAAC,CAACwJ,MAAM,CAACzI;IAAK,CAAC,CAAE;IACxE2J,QAAQ;IACRlC,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrN,KAAA,CAAA2J,aAAA;IAAQ9D,KAAK,EAAC,EAAE;IAAAmH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDtM,QAAQ,CAACyN,GAAG,CAAEC,OAAO,iBAClBzO,KAAA,CAAA2J,aAAA;IAAQ/D,GAAG,EAAE6I,OAAO,CAAC5K,EAAG;IAACgC,KAAK,EAAE4I,OAAO,CAAC5K,EAAG;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtCoB,OAAO,CAAC3K,GACL,CACX,CACG,CACP,CAAC,eAEN9D,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAE,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,UAAe,CAAC,eACvBrN,KAAA,CAAA2J,aAAA;IACI9D,KAAK,EAAE3D,QAAQ,CAACO,SAAU;IAC1B4L,QAAQ,EAAGvJ,CAAC,IAAK3C,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEO,SAAS,EAAEqC,CAAC,CAACwJ,MAAM,CAACzI;IAAK,CAAC,CAAE;IACvE2J,QAAQ;IACRlC,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrN,KAAA,CAAA2J,aAAA;IAAQ9D,KAAK,EAAC,EAAE;IAAAmH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4BAA+B,CAAC,EAChDpM,OAAO,CAACuN,GAAG,CAAEE,MAAM,iBAChB1O,KAAA,CAAA2J,aAAA;IAAQ/D,GAAG,EAAE8I,MAAM,CAAC7K,EAAG;IAACgC,KAAK,EAAE6I,MAAM,CAAC7K,EAAG;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCqB,MAAM,CAAC5K,GACJ,CACX,CACG,CACP,CACJ,CAAC,eAEN9D,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrN,KAAA,CAAA2J,aAAA;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,uBAA4B,CAAC,eACpCrN,KAAA,CAAA2J,aAAA;IACI5B,IAAI,EAAC,MAAM;IACXlC,KAAK,EAAE3D,QAAQ,CAACK,gBAAiB;IACjC8L,QAAQ,EAAGvJ,CAAC,IAAK3C,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,gBAAgB,EAAEuC,CAAC,CAACwJ,MAAM,CAACzI;IAAK,CAAC,CAAE;IAC9E2J,QAAQ;IACRlC,KAAK,EAAE;MACHvC,KAAK,EAAE,MAAM;MACb6C,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBa,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENrN,KAAA,CAAA2J,aAAA;IAAKoD,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrN,KAAA,CAAA2J,aAAA;IACI5B,IAAI,EAAC,QAAQ;IACbgF,SAAS,EAAC,mBAAmB;IAC7BU,OAAO,EAAEA,CAAA,KAAM;MACXnM,YAAY,CAAC,KAAK,CAAC;MACnBE,eAAe,CAAC,IAAI,CAAC;MACrB2E,SAAS,CAAC,CAAC;IACf,CAAE;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,SAEO,CAAC,eACTrN,KAAA,CAAA2J,aAAA;IAAQ5B,IAAI,EAAC,QAAQ;IAACgF,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C9L,YAAY,GAAG,UAAU,GAAG,OACzB,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAeZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}