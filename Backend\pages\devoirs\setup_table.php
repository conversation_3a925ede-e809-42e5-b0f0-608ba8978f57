<?php
/**
 * Script pour créer/vérifier la table Devoirs
 * Utilisation: Accéder via navigateur ou ligne de commande
 */

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once('../../config/db.php');

$result = [
    'status' => 'Setup Table Devoirs',
    'timestamp' => date('Y-m-d H:i:s'),
    'operations' => []
];

try {
    // 1. Vérifier si la table Devoirs existe
    $checkTable = $pdo->query("SHOW TABLES LIKE 'Devoirs'");
    $tableExists = $checkTable->rowCount() > 0;
    
    if ($tableExists) {
        $result['operations'][] = [
            'action' => 'check_table',
            'status' => 'OK',
            'message' => 'Table Devoirs existe déjà'
        ];
        
        // Vérifier la structure de la table
        $columns = $pdo->query("DESCRIBE Devoirs")->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        $result['operations'][] = [
            'action' => 'check_structure',
            'status' => 'OK',
            'message' => 'Structure de la table',
            'columns' => $columnNames
        ];
        
        // Vérifier si la colonne taille_fichier existe
        if (!in_array('taille_fichier', $columnNames)) {
            $pdo->exec("ALTER TABLE Devoirs ADD COLUMN taille_fichier VARCHAR(50) AFTER fichier_pdf");
            $result['operations'][] = [
                'action' => 'add_column',
                'status' => 'OK',
                'message' => 'Colonne taille_fichier ajoutée'
            ];
        }
        
    } else {
        // Créer la table Devoirs selon la structure exacte fournie
        $createTableSQL = "
            CREATE TABLE Devoirs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matiere_id INT NOT NULL,
                classe_id INT NOT NULL,
                titre VARCHAR(255) NOT NULL,
                description TEXT,
                date_remise DATE,
                fichier_pdf VARCHAR(255),
                FOREIGN KEY (matiere_id) REFERENCES Matieres(id),
                FOREIGN KEY (classe_id) REFERENCES Classes(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        
        $result['operations'][] = [
            'action' => 'create_table',
            'status' => 'OK',
            'message' => 'Table Devoirs créée avec succès'
        ];
    }
    
    // 2. Vérifier les tables de référence
    $checkMatieres = $pdo->query("SHOW TABLES LIKE 'Matieres'")->rowCount() > 0;
    $checkClasses = $pdo->query("SHOW TABLES LIKE 'Classes'")->rowCount() > 0;
    
    $result['operations'][] = [
        'action' => 'check_references',
        'status' => ($checkMatieres && $checkClasses) ? 'OK' : 'WARNING',
        'message' => 'Vérification des tables de référence',
        'details' => [
            'Matieres' => $checkMatieres ? 'existe' : 'manquante',
            'Classes' => $checkClasses ? 'existe' : 'manquante'
        ]
    ];
    
    // 3. Créer le dossier uploads si nécessaire
    $uploadDir = '../../uploads/devoirs/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
        $result['operations'][] = [
            'action' => 'create_upload_dir',
            'status' => 'OK',
            'message' => 'Dossier uploads/devoirs créé',
            'path' => realpath($uploadDir)
        ];
    } else {
        $result['operations'][] = [
            'action' => 'check_upload_dir',
            'status' => 'OK',
            'message' => 'Dossier uploads/devoirs existe',
            'path' => realpath($uploadDir),
            'writable' => is_writable($uploadDir) ? 'oui' : 'non'
        ];
    }
    
    // 4. Insérer des données de test si la table est vide
    $countDevoirs = $pdo->query("SELECT COUNT(*) FROM Devoirs")->fetchColumn();
    
    if ($countDevoirs == 0 && $checkMatieres && $checkClasses) {
        // Récupérer quelques matières et classes pour les données de test
        $matieres = $pdo->query("SELECT id FROM Matieres LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);
        $classes = $pdo->query("SELECT id FROM Classes LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($matieres) && !empty($classes)) {
            $testData = [
                [
                    'titre' => 'Exercices de Mathématiques',
                    'description' => 'Résoudre les équations du chapitre 3',
                    'date_remise' => '2024-02-15',
                    'matiere_id' => $matieres[0],
                    'classe_id' => $classes[0]
                ],
                [
                    'titre' => 'Dissertation de Français',
                    'description' => 'Analyse littéraire sur "Le Petit Prince"',
                    'date_remise' => '2024-02-20',
                    'matiere_id' => $matieres[1] ?? $matieres[0],
                    'classe_id' => $classes[1] ?? $classes[0]
                ],
                [
                    'titre' => 'Expérience de Physique',
                    'description' => 'Rapport sur l\'électricité statique',
                    'date_remise' => '2024-02-25',
                    'matiere_id' => $matieres[2] ?? $matieres[0],
                    'classe_id' => $classes[2] ?? $classes[0]
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO Devoirs (titre, description, date_remise, matiere_id, classe_id) 
                VALUES (:titre, :description, :date_remise, :matiere_id, :classe_id)
            ");
            
            $insertedCount = 0;
            foreach ($testData as $data) {
                if ($stmt->execute($data)) {
                    $insertedCount++;
                }
            }
            
            $result['operations'][] = [
                'action' => 'insert_test_data',
                'status' => 'OK',
                'message' => "$insertedCount devoirs de test insérés"
            ];
        }
    } else {
        $result['operations'][] = [
            'action' => 'check_data',
            'status' => 'OK',
            'message' => "$countDevoirs devoirs trouvés dans la table"
        ];
    }
    
    // 5. Résumé final
    $allOk = true;
    foreach ($result['operations'] as $op) {
        if ($op['status'] === 'ERROR') {
            $allOk = false;
            break;
        }
    }
    
    $result['final_status'] = $allOk ? 'SUCCESS' : 'ERROR';
    $result['message'] = $allOk ? 'Configuration des devoirs terminée avec succès' : 'Certaines opérations ont échoué';
    
} catch (PDOException $e) {
    $result['operations'][] = [
        'action' => 'database_error',
        'status' => 'ERROR',
        'message' => 'Erreur de base de données: ' . $e->getMessage()
    ];
    $result['final_status'] = 'ERROR';
    $result['message'] = 'Erreur lors de la configuration';
}

echo json_encode($result, JSON_PRETTY_PRINT);
?>
