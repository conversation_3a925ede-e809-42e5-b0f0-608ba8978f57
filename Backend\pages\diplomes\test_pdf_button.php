<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bouton PDF - Diplômes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .test-button {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .diplome-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: transform 0.2s ease;
        }
        .diplome-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Test du Bouton PDF - Diplômes</h1>
            <p>Diagnostic et test de la fonctionnalité de génération PDF</p>
        </div>

        <?php
        // Connexion à la base de données
        try {
            $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "<div class='status success'>✅ Connexion à la base de données réussie</div>";
        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Erreur de connexion : " . htmlspecialchars($e->getMessage()) . "</div>";
            exit();
        }

        // Vérifier l'existence des fichiers PDF
        $pdfFiles = [
            'generateSimplePDF.php' => 'Générateur PDF Simple',
            'generateProfessionalPDF.php' => 'Générateur PDF Professionnel'
        ];

        echo "<h3>📁 Vérification des fichiers PDF</h3>";
        foreach ($pdfFiles as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='status success'>✅ $description ($file) - Fichier trouvé</div>";
            } else {
                echo "<div class='status error'>❌ $description ($file) - Fichier manquant</div>";
            }
        }

        // Récupérer les diplômes
        try {
            $stmt = $pdo->prepare("
                SELECT 
                    d.id,
                    d.titre,
                    d.date_obtention,
                    u.nom as etudiant_nom,
                    u.email as etudiant_email
                FROM Diplomes d
                JOIN Etudiants e ON d.etudiant_id = e.id
                JOIN Utilisateurs u ON e.utilisateur_id = u.id
                ORDER BY d.date_obtention DESC
                LIMIT 5
            ");
            $stmt->execute();
            $diplomes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h3>🎓 Diplômes disponibles pour test</h3>";
            
            if (count($diplomes) > 0) {
                echo "<div class='status success'>✅ " . count($diplomes) . " diplôme(s) trouvé(s)</div>";
                
                foreach ($diplomes as $diplome) {
                    echo "<div class='diplome-card'>";
                    echo "<h4>🏆 {$diplome['titre']}</h4>";
                    echo "<p><strong>Étudiant:</strong> {$diplome['etudiant_nom']} ({$diplome['etudiant_email']})</p>";
                    echo "<p><strong>Date:</strong> " . date('d/m/Y', strtotime($diplome['date_obtention'])) . "</p>";
                    echo "<div style='margin-top: 15px;'>";
                    echo "<a href='generateSimplePDF.php?diplome_id={$diplome['id']}' target='_blank' class='test-button'>📄 Test PDF Simple</a>";
                    if (file_exists('generateProfessionalPDF.php')) {
                        echo "<a href='generateProfessionalPDF.php?id={$diplome['id']}' target='_blank' class='test-button'>🎨 Test PDF Pro</a>";
                    }
                    echo "</div>";
                    echo "</div>";
                }
            } else {
                echo "<div class='status error'>❌ Aucun diplôme trouvé dans la base de données</div>";
                echo "<p>Créez d'abord des diplômes via l'interface React pour pouvoir tester la génération PDF.</p>";
            }

        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Erreur lors de la récupération des diplômes : " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>

        <h3>🔗 Liens utiles</h3>
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000/diplomes" class="test-button">🎓 Interface React Diplômes</a>
            <a href="demo_professional_interface.php" class="test-button">🎨 Interface Demo</a>
        </div>

        <div class="status info">
            <h4>💡 Instructions de test</h4>
            <ol>
                <li>Cliquez sur un bouton "Test PDF" ci-dessus</li>
                <li>Le PDF devrait s'ouvrir dans un nouvel onglet</li>
                <li>Si ça ne fonctionne pas, vérifiez la console du navigateur</li>
                <li>Testez aussi depuis l'interface React principale</li>
            </ol>
        </div>
    </div>
</body>
</html>
