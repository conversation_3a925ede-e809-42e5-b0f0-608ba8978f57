<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chargement Matières - Notes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .select-test {
            margin: 15px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Chargement Matières pour Notes</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>Ce test simule le chargement des matières comme dans la page NotesUnified.js</p>
        <button onclick="testMatieresLoading()">🔍 Tester Chargement Matières</button>
        <button onclick="testSelectPopulation()">📋 Tester Population Select</button>
        <button onclick="clearResults()">🧹 Effacer Résultats</button>
    </div>

    <div id="results"></div>

    <div class="select-test">
        <h3>🎯 Test du Select des Matières</h3>
        <label for="matiere-select">Sélectionner une matière :</label>
        <select id="matiere-select">
            <option value="">Chargement des matières...</option>
        </select>
        <p id="select-status">Status: En attente de test</p>
    </div>

    <script>
        let matieres = [];

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('matiere-select').innerHTML = '<option value="">Chargement des matières...</option>';
            document.getElementById('select-status').textContent = 'Status: En attente de test';
        }

        async function testMatieresLoading() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section info"><h3>🔄 Test en cours...</h3></div>';
            
            try {
                console.log('🔄 Simulation du chargement des matières...');
                
                // Test de l'API getMatieres_no_auth.php (nouvelle API utilisée)
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/getMatieres_no_auth.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('📊 Réponse API getMatieres_no_auth:', data);
                
                let testResult = '';
                let testClass = 'error';
                
                if (response.ok) {
                    if (data.success && data.matieres) {
                        matieres = data.matieres;
                        testResult = `
                            <h3>✅ Test Réussi - API getMatieres_no_auth.php</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Success:</strong> ${data.success}</p>
                            <p><strong>Nombre de matières:</strong> ${data.matieres.length}</p>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <h4>Première matière (exemple):</h4>
                            <pre>${data.matieres.length > 0 ? JSON.stringify(data.matieres[0], null, 2) : 'Aucune matière'}</pre>
                        `;
                        testClass = 'success';
                    } else if (Array.isArray(data)) {
                        matieres = data;
                        testResult = `
                            <h3>⚠️ Format Alternatif Détecté</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Type:</strong> Array direct</p>
                            <p><strong>Nombre de matières:</strong> ${data.length}</p>
                            <h4>Première matière (exemple):</h4>
                            <pre>${data.length > 0 ? JSON.stringify(data[0], null, 2) : 'Aucune matière'}</pre>
                        `;
                        testClass = 'warning';
                    } else {
                        testResult = `
                            <h3>❌ Format Inattendu</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Propriété success:</strong> ${data.hasOwnProperty('success') ? data.success : 'Non présente'}</p>
                            <p><strong>Propriété matieres:</strong> ${data.hasOwnProperty('matieres') ? 'Présente' : 'Non présente'}</p>
                            <h4>Données complètes:</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                    }
                } else {
                    testResult = `
                        <h3>❌ Erreur HTTP</h3>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
                resultsDiv.innerHTML = `<div class="test-section ${testClass}">${testResult}</div>`;
                
            } catch (error) {
                console.error('❌ Erreur:', error);
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Erreur de Connexion</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <p><strong>Cause possible:</strong> Serveur backend non démarré ou problème CORS</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        function testSelectPopulation() {
            const selectElement = document.getElementById('matiere-select');
            const statusElement = document.getElementById('select-status');
            
            if (matieres.length === 0) {
                statusElement.textContent = 'Status: ❌ Aucune matière chargée. Lancez d\'abord le test de chargement.';
                statusElement.style.color = 'red';
                return;
            }
            
            // Vider le select
            selectElement.innerHTML = '';
            
            // Ajouter l'option par défaut
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Sélectionner une matière...';
            selectElement.appendChild(defaultOption);
            
            // Ajouter les matières
            matieres.forEach(matiere => {
                const option = document.createElement('option');
                option.value = matiere.id;
                option.textContent = `📖 ${matiere.nom} | Filière: ${matiere.filiere_nom || 'N/A'}`;
                selectElement.appendChild(option);
            });
            
            statusElement.textContent = `Status: ✅ ${matieres.length} matières chargées dans le select`;
            statusElement.style.color = 'green';
            
            // Ajouter un event listener pour tester la sélection
            selectElement.onchange = function() {
                if (this.value) {
                    const selectedMatiere = matieres.find(m => m.id == this.value);
                    statusElement.textContent = `Status: ✅ Matière sélectionnée: ${selectedMatiere.nom}`;
                } else {
                    statusElement.textContent = `Status: ⚪ Aucune matière sélectionnée`;
                }
            };
        }
    </script>
</body>
</html>
