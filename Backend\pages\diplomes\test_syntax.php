<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    echo "<h1>🔧 TEST DE SYNTAXE - IMPRESSION AUTOMATIQUE</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
        .test-button:hover { background: #0056b3; color: white; text-decoration: none; }
    </style>";
    
    echo "<div class='info'>";
    echo "<h2>✅ Erreur de Syntaxe Corrigée</h2>";
    echo "<p>Le problème était causé par un conflit de guillemets dans le JavaScript :</p>";
    echo "<ul>";
    echo "<li>❌ <strong>Avant :</strong> Guillemets simples dans JavaScript à l'intérieur d'une chaîne PHP avec guillemets simples</li>";
    echo "<li>✅ <strong>Après :</strong> Guillemets doubles dans JavaScript pour éviter les conflits</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test de syntaxe PHP
    echo "<div class='step'>";
    echo "<h3>🔍 Test de Syntaxe PHP</h3>";
    
    $syntaxError = false;
    $errorMessage = '';
    
    // Vérifier la syntaxe du fichier generateSimplePDF.php
    $filePath = __DIR__ . '/generateSimplePDF.php';
    
    if (file_exists($filePath)) {
        echo "<p class='info'>📁 Fichier trouvé : generateSimplePDF.php</p>";
        
        // Test de syntaxe avec php -l
        $output = [];
        $returnCode = 0;
        
        exec("php -l \"$filePath\" 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "<p class='success'>✅ Syntaxe PHP valide</p>";
            echo "<p style='color: #666;'>Résultat : " . implode(' ', $output) . "</p>";
        } else {
            echo "<p class='error'>❌ Erreur de syntaxe PHP détectée</p>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars(implode("\n", $output));
            echo "</pre>";
            $syntaxError = true;
            $errorMessage = implode("\n", $output);
        }
    } else {
        echo "<p class='error'>❌ Fichier generateSimplePDF.php non trouvé</p>";
        $syntaxError = true;
        $errorMessage = "Fichier non trouvé";
    }
    echo "</div>";
    
    // Test de chargement
    echo "<div class='step'>";
    echo "<h3>🌐 Test de Chargement</h3>";
    
    if (!$syntaxError) {
        echo "<p class='info'>Test de chargement du fichier avec un ID de diplôme...</p>";
        
        // Essayer de charger le fichier avec un paramètre de test
        $testUrl = "http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?id=1";
        
        echo "<p><strong>URL de test :</strong> <a href='$testUrl' target='_blank'>$testUrl</a></p>";
        
        // Test avec cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "<p class='error'>❌ Erreur cURL : $error</p>";
        } elseif ($httpCode === 200) {
            echo "<p class='success'>✅ Fichier se charge correctement (HTTP $httpCode)</p>";
            
            // Vérifier si le JavaScript est présent
            if (strpos($response, 'console.log("Automatic diploma print script initialized")') !== false) {
                echo "<p class='success'>✅ Script JavaScript d'impression automatique détecté</p>";
            } else {
                echo "<p class='warning'>⚠️ Script JavaScript d'impression non détecté dans la réponse</p>";
            }
            
            // Vérifier la structure HTML
            if (strpos($response, '<div class="diploma-container">') !== false) {
                echo "<p class='success'>✅ Structure HTML du diplôme détectée</p>";
            } else {
                echo "<p class='warning'>⚠️ Structure HTML du diplôme non détectée</p>";
            }
            
        } else {
            echo "<p class='error'>❌ Erreur HTTP : Code $httpCode</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠️ Test de chargement ignoré à cause de l'erreur de syntaxe</p>";
    }
    echo "</div>";
    
    // Correction appliquée
    echo "<div class='step'>";
    echo "<h3>🔧 Correction Appliquée</h3>";
    
    echo "<h4>❌ Problème Original</h4>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "Parse error: syntax error, unexpected identifier \"Cannot\", expecting \";\"";
    echo "</pre>";
    
    echo "<h4>🔍 Cause du Problème</h4>";
    echo "<p>Conflit de guillemets dans le JavaScript intégré au PHP :</p>";
    echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
    echo "// Dans une chaîne PHP avec guillemets simples\n";
    echo "echo 'console.log('Cannot close window');';\n";
    echo "//            ^                    ^\n";
    echo "//            Conflit de guillemets simples";
    echo "</pre>";
    
    echo "<h4>✅ Solution Appliquée</h4>";
    echo "<p>Remplacement des guillemets simples par des guillemets doubles dans le JavaScript :</p>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo "// Avant (problématique)\n";
    echo "console.log('Cannot close window');\n";
    echo "console.log('Opening print dialog...');\n\n";
    echo "// Après (corrigé)\n";
    echo "console.log(\"Cannot close window\");\n";
    echo "console.log(\"Opening print dialog...\");";
    echo "</pre>";
    echo "</div>";
    
    // Tests recommandés
    echo "<div class='step'>";
    echo "<h3>🧪 Tests Recommandés</h3>";
    
    if (!$syntaxError) {
        echo "<h4>✅ Fichier Prêt pour les Tests</h4>";
        echo "<p>Le fichier generateSimplePDF.php est maintenant syntaxiquement correct. Vous pouvez :</p>";
        
        echo "<div style='margin: 15px 0;'>";
        echo "<a href='test_auto_print.php' class='test-button'>📊 Page de Test Complète</a>";
        echo "<a href='generateSimplePDF.php?id=1' target='_blank' class='test-button'>🖨️ Test Direct (ID=1)</a>";
        echo "<a href='http://localhost:3000/diplomes' class='test-button'>🎓 Interface Diplômes</a>";
        echo "</div>";
        
        echo "<h4>🎯 Processus de Test</h4>";
        echo "<ol>";
        echo "<li><strong>Test de syntaxe :</strong> ✅ Réussi</li>";
        echo "<li><strong>Test de chargement :</strong> Cliquez sur 'Test Direct' ci-dessus</li>";
        echo "<li><strong>Test d'impression :</strong> Vérifiez que la fenêtre d'impression s'ouvre automatiquement</li>";
        echo "<li><strong>Test de fermeture :</strong> Vérifiez que la page se ferme après impression/annulation</li>";
        echo "</ol>";
        
    } else {
        echo "<h4>❌ Erreur Persistante</h4>";
        echo "<p class='error'>Il reste une erreur de syntaxe à corriger :</p>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($errorMessage);
        echo "</pre>";
        echo "<p>Veuillez vérifier le fichier generateSimplePDF.php manuellement.</p>";
    }
    echo "</div>";
    
    // Résumé
    echo "<div class='step'>";
    echo "<h3>📋 Résumé</h3>";
    
    if (!$syntaxError) {
        echo "<p class='success' style='font-size: 18px;'>🎉 PROBLÈME RÉSOLU !</p>";
        echo "<p>✅ L'erreur de syntaxe a été corrigée avec succès</p>";
        echo "<p>✅ Le fichier generateSimplePDF.php fonctionne maintenant correctement</p>";
        echo "<p>✅ L'impression automatique des diplômes est opérationnelle</p>";
        
        echo "<h4>🚀 Prochaines Étapes</h4>";
        echo "<ul>";
        echo "<li>Testez l'impression automatique avec un diplôme réel</li>";
        echo "<li>Vérifiez que la fenêtre se ferme automatiquement</li>";
        echo "<li>Testez sur différents navigateurs si nécessaire</li>";
        echo "</ul>";
        
    } else {
        echo "<p class='error' style='font-size: 18px;'>❌ ERREUR PERSISTANTE</p>";
        echo "<p>Il reste des problèmes de syntaxe à résoudre dans le fichier.</p>";
        echo "<p>Contactez le développeur pour une correction manuelle.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR</h2>";
    echo "<p><strong>Message :</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
