-- ============================================================================
-- MISE À JOUR DE LA TABLE MESSAGES POUR LES FONCTIONNALITÉS AVANCÉES
-- ============================================================================

-- Supprimer la table existante si elle existe
DROP TABLE IF EXISTS `messages`;

-- Créer la nouvelle table messages avec toutes les fonctionnalités avancées
CREATE TABLE `messages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `expediteur_id` INT NOT NULL,                       -- ID de l'expéditeur du message (utilisateur connecté)
    `destinataire_id` INT NOT NULL,                     -- ID du destinataire du message
    
    `message` TEXT NOT NULL,                            -- Contenu textuel du message
    `date_envoi` DATETIME DEFAULT CURRENT_TIMESTAMP,    -- Date et heure d'envoi du message
    
    `lu` BOOLEAN DEFAULT FALSE,                         -- Indique si le message a été lu (FALSE = non lu, TRUE = lu)
    
    -- Nouvelles colonnes pour la modification des messages
    `modifie` BOOLEAN DEFAULT FALSE,                    -- Indique si le message a été modifié après son envoi
    `date_modification` DATETIME NULL DEFAULT NULL,     -- Date et heure de la dernière modification
    
    -- Nouvelles colonnes pour la suppression flexible
    `supprime_par_expediteur` BOOLEAN DEFAULT FALSE,    -- TRUE si le message a été supprimé côté expéditeur uniquement
    `supprime_par_destinataire` BOOLEAN DEFAULT FALSE,  -- TRUE si le message a été supprimé côté destinataire uniquement
    
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Clés étrangères
    FOREIGN KEY (`expediteur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`destinataire_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Index pour optimiser les performances
    INDEX `idx_expediteur_id` (`expediteur_id`),
    INDEX `idx_destinataire_id` (`destinataire_id`),
    INDEX `idx_date_envoi` (`date_envoi`),
    INDEX `idx_lu` (`lu`),
    INDEX `idx_modifie` (`modifie`),
    INDEX `idx_supprime_expediteur` (`supprime_par_expediteur`),
    INDEX `idx_supprime_destinataire` (`supprime_par_destinataire`),
    INDEX `idx_conversation` (`expediteur_id`, `destinataire_id`, `date_envoi`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- DONNÉES DE TEST POUR LA MESSAGERIE
-- ============================================================================

-- Insérer quelques messages de test (optionnel)
INSERT INTO `messages` (`expediteur_id`, `destinataire_id`, `message`, `date_envoi`, `lu`) VALUES
(1, 2, 'Bonjour, comment allez-vous ?', NOW() - INTERVAL 2 DAY, TRUE),
(2, 1, 'Très bien merci ! Et vous ?', NOW() - INTERVAL 2 DAY + INTERVAL 30 MINUTE, TRUE),
(1, 3, 'Réunion prévue demain à 14h', NOW() - INTERVAL 1 DAY, FALSE),
(3, 1, 'Parfait, je serai présent', NOW() - INTERVAL 1 DAY + INTERVAL 1 HOUR, FALSE);

-- ============================================================================
-- VÉRIFICATION DE LA STRUCTURE
-- ============================================================================

-- Afficher la structure de la table
DESCRIBE `messages`;

-- Compter les messages
SELECT COUNT(*) as total_messages FROM `messages`;

-- Afficher les contraintes de clés étrangères
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'messages' 
AND CONSTRAINT_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;
